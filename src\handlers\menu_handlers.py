# handlers/menu_handlers.py
"""
معالجات القوائم - TradingTelegram
تحتوي على جميع دوال عرض القوائم والواجهات المنقولة من main.py
"""

import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from telegram.constants import ParseMode

# إعداد السجل
logger = logging.getLogger(__name__)

# متغيرات عامة (سيتم تعيينها من main.py)
subscription_system = None
ENHANCED_ANALYSIS_EXPLANATION = None

def set_dependencies(sub_system, enhanced_explanation):
    """تعيين التبعيات المطلوبة من main.py"""
    global subscription_system, ENHANCED_ANALYSIS_EXPLANATION
    subscription_system = sub_system
    ENHANCED_ANALYSIS_EXPLANATION = enhanced_explanation

async def show_language_selection(update: Update, context: CallbackContext):
    """عرض اختيار اللغة للمستخدم الجديد"""
    try:
        user_id = str(update.effective_user.id)

        # إنشاء أزرار اختيار اللغة
        keyboard = [
            [InlineKeyboardButton("🇸🇦 العربية", callback_data='set_initial_lang_ar')],
            [InlineKeyboardButton("🇬🇧 English", callback_data='set_initial_lang_en')]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        # إرسال رسالة اختيار اللغة
        await update.message.reply_text(
            "🌐 اختر لغتك المفضلة | Choose your preferred language",
            reply_markup=reply_markup
        )
    except Exception as e:
        logger.error(f"خطأ في عرض اختيار اللغة: {str(e)}")
        await update.message.reply_text("❌ حدث خطأ أثناء عرض اختيار اللغة. الرجاء المحاولة مرة أخرى لاحقًا.")

async def show_terms_and_conditions(update: Update, context: CallbackContext, lang='ar'):
    """عرض الشروط والأحكام للمستخدم"""
    try:
        user_id = str(update.effective_user.id)
        logger.info(f"جاري إعداد أزرار الشروط والأحكام للمستخدم {user_id} باللغة {lang}")

        # التحقق من إعدادات المستخدم لمعرفة ما إذا كان مستخدماً جديداً
        # استخدام subscription_system العام المُعرف في أعلى الملف
        if subscription_system is None:
            # إذا لم يتم تعيين subscription_system، نحاول الحصول عليه
            try:
                from services.subscription_system import get_subscription_system
                subscription_system_local = get_subscription_system()
                user_settings = subscription_system_local.get_user_settings(user_id) if subscription_system_local else {}
            except Exception as e:
                logger.error(f"خطأ في الحصول على subscription_system: {str(e)}")
                user_settings = {}
        else:
            user_settings = subscription_system.get_user_settings(user_id)
        is_new_user = not user_settings.get('terms_accepted', False)

        # إنشاء الأزرار حسب حالة المستخدم
        if is_new_user:
            # للمستخدمين الجدد: أزرار الموافقة والرفض
            agree_button_text = "✅ أوافق على الشروط والأحكام" if lang == 'ar' else "✅ I Agree to Terms"
            decline_button_text = "❌ لا أوافق" if lang == 'ar' else "❌ I Disagree"

            keyboard = [
                [InlineKeyboardButton(agree_button_text, callback_data='terms_agree')],
                [InlineKeyboardButton(decline_button_text, callback_data='terms_decline')]
            ]
        else:
            # للمستخدمين الذين وافقوا بالفعل: زر العودة فقط
            back_button_text = "🔙 العودة للقائمة الرئيسية" if lang == 'ar' else "🔙 Back to Main Menu"
            keyboard = [
                [InlineKeyboardButton(back_button_text, callback_data='back_to_main')]
            ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        # رسالة ترحيبية للمستخدمين الجدد
        welcome_message = ""
        if is_new_user:
            if lang == 'ar':
                welcome_message = """🎉 **مرحباً بك في بوت التحليل الفني للعملات الرقمية!**

قبل البدء، يرجى قراءة الشروط والأحكام التالية بعناية والموافقة عليها:

"""
            else:
                welcome_message = """🎉 **Welcome to the Cryptocurrency Technical Analysis Bot!**

Before starting, please carefully read the following terms and conditions and agree to them:

"""

        # محتوى الشروط والأحكام باللغة العربية
        if lang == 'ar':
            terms_content = """📜 الشروط والأحكام

⚠️ إخلاء المسؤولية القانوني

🔹 طبيعة الخدمة:
• هذا البوت يقدم تحليلات فنية فقط وليست نصائح استثمارية
• جميع التحليلات تعتمد على بيانات تاريخية وقد لا تعكس الأداء المستقبلي
• لا نقدم استشارات مالية أو توصيات استثمارية

🔹 المسؤولية الشخصية:
• أنت المسؤول الوحيد عن جميع قراراتك الاستثمارية
• استخدام البوت يعني موافقتك الكاملة على هذه الشروط
• جميع قرارات التداول تتم على مسؤوليتك الشخصية الكاملة

🔹 إخلاء المسؤولية:
• نحن لا نتحمل أي مسؤولية عن خسائر مالية
• قد تحدث أخطاء في البيانات أو التحليلات
• لا نضمن دقة أو اكتمال المعلومات المقدمة
• لا نتحمل مسؤولية انقطاع الخدمة أو الأعطال الفنية

🔒 شروط الاستخدام:

🔹 الاستخدام المسموح:
• البوت مخصص للتحليل الفني فقط
• يمكن استخدامه كأداة مساعدة في اتخاذ القرارات
• مناسب للأغراض التعليمية والبحثية

🔹 الاستخدام المحظور:
• مشاركة حساب الاشتراك مع الآخرين
• بيع أو إعادة توزيع المحتوى
• استخدام البوت لأغراض تجارية دون إذن
• نسخ أو تقليد الخدمة

🔹 سياسة الاشتراك:
• الاشتراك غير قابل للاسترداد
• يمكن إلغاء الاشتراك في أي وقت
• لا يوجد دعم فني متاح

⚠️ تحذيرات مهمة:

🚨 مخاطر التداول:
تداول العملات الرقمية ينطوي على مخاطر عالية جداً. قد تخسر أكثر مما يمكنك تحمل خسارته. لا تستثمر أموالاً لا يمكنك تحمل خسارتها.

✅ الموافقة:
بالموافقة على هذه الشروط، فإنك تقر بفهمك وقبولك لجميع البنود المذكورة أعلاه وتتحمل المسؤولية الكاملة عن استخدام الخدمة.
            """
        else:
            # محتوى الشروط والأحكام باللغة الإنجليزية
            terms_content = """📜 Terms and Conditions

⚠️ Legal Disclaimer

🔹 Nature of Service:
• This bot provides technical analysis only, not investment advice
• All analyses are based on historical data and may not reflect future performance
• We do not provide financial consulting or investment recommendations

🔹 Personal Responsibility:
• You are solely responsible for all your investment decisions
• Using the bot means your complete agreement to these terms
• All trading decisions are made at your complete personal responsibility

🔹 Disclaimer of Liability:
• We do not bear any responsibility for financial losses
• Errors may occur in data or analysis
• We do not guarantee accuracy or completeness of provided information
• We are not responsible for service interruptions or technical failures

🔒 Terms of Use:

🔹 Permitted Use:
• Bot is intended for technical analysis only
• Can be used as an aid in decision making
• Suitable for educational and research purposes

🔹 Prohibited Use:
• Sharing subscription account with others
• Selling or redistributing content
• Using the bot for commercial purposes without permission
• Copying or replicating the service

🔹 Subscription Policy:
• Subscription is non-refundable
• Can cancel subscription at any time
• No technical support is available

⚠️ Important Warnings:

🚨 Trading Risks:
Trading cryptocurrencies involves very high risks. You may lose more than you can afford to lose. Do not invest money you cannot afford to lose.

✅ Agreement:
By agreeing to these terms, you acknowledge your understanding and acceptance of all the above clauses and take full responsibility for using the service.
            """

        # دمج الرسالة الترحيبية والمحتوى
        full_terms_text = f"{welcome_message}{terms_content}"

        # إرسال رسالة الشروط والأحكام
        # التحقق من نوع التحديث (message أو callback_query)
        if update.callback_query:
            # إذا كان من callback_query، نستخدم edit_message_text أو send_message
            try:
                await update.callback_query.edit_message_text(
                    text=full_terms_text,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.MARKDOWN
                )
                logger.info(f"✅ تم عرض الشروط والأحكام للمستخدم {user_id} بنجاح (تحديث الرسالة)")
            except Exception as edit_error:
                logger.warning(f"فشل في تحديث الرسالة، إرسال رسالة جديدة: {str(edit_error)}")
                await update.callback_query.message.reply_text(
                    text=full_terms_text,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.MARKDOWN
                )
                logger.info(f"✅ تم عرض الشروط والأحكام للمستخدم {user_id} بنجاح (رسالة جديدة)")
        else:
            # إذا كان من message عادية (للمستخدمين الجدد)
            await update.message.reply_text(
                full_terms_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            logger.info(f"✅ تم عرض الشروط والأحكام للمستخدم الجديد {user_id} بنجاح")

        # إضافة سجل خاص للمستخدمين الجدد
        if is_new_user:
            logger.info(f"🆕 عرض الشروط والأحكام المفصلة للمستخدم الجديد {user_id} باللغة {lang}")
        else:
            logger.info(f"📜 عرض الشروط والأحكام من القائمة الرئيسية للمستخدم {user_id} باللغة {lang}")

    except Exception as e:
        logger.error(f"❌ خطأ في عرض الشروط والأحكام للمستخدم {user_id}: {str(e)}")
        try:
            error_message = "❌ حدث خطأ أثناء عرض الشروط والأحكام. الرجاء المحاولة مرة أخرى لاحقًا." if lang == 'ar' else "❌ An error occurred while displaying terms and conditions. Please try again later."

            # التعامل مع نوع التحديث المناسب
            if update.callback_query:
                await update.callback_query.answer(error_message, show_alert=True)
                logger.info(f"تم إرسال رسالة خطأ للمستخدم {user_id} عبر callback_query")
            elif update.message:
                await update.message.reply_text(error_message)
                logger.info(f"تم إرسال رسالة خطأ للمستخدم {user_id} عبر message")
            else:
                logger.error(f"لا يمكن إرسال رسالة خطأ للمستخدم {user_id}: لا يوجد كائن رسالة أو callback_query متاح")
        except Exception as inner_e:
            logger.error(f"خطأ في إرسال رسالة الخطأ للمستخدم {user_id}: {str(inner_e)}")

async def show_enhanced_settings(update: Update, context: CallbackContext):
    """عرض إعدادات النظام المحسن"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        subscription_system_to_use = subscription_system
        if subscription_system_to_use is None:
            try:
                from services.subscription_system import get_subscription_system
                subscription_system_to_use = get_subscription_system()
            except Exception as e:
                logger.error(f"خطأ في الحصول على subscription_system: {str(e)}")
                await update.callback_query.answer(
                    "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                    show_alert=True
                )
                return

        settings = subscription_system_to_use.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # التحقق من حالة الاشتراك أو الأيام المجانية النشطة
        is_subscribed = subscription_system_to_use.is_subscribed_sync(user_id)
        has_free_day = False
        try:
            if hasattr(subscription_system_to_use, 'free_day_system') and subscription_system_to_use.free_day_system:
                has_free_day = subscription_system_to_use.free_day_system.has_active_free_day(user_id)
        except Exception as e:
            logger.warning(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(e)}")

        if not is_subscribed and not has_free_day:
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط أو في الأيام المجانية" if lang == 'ar' else
                "This feature is available for subscribers only or on free days",
                show_alert=True
            )
            return

        current_style = settings.get('trading_style', 'day_trading')
        current_analysis_type = settings.get('analysis_type', 'enhanced')

        styles_ar = {
            'scalping': 'المضاربة السريعة',
            'day_trading': 'التداول اليومي',
            'swing_trading': 'التداول المتأرجح',
            'position': 'الاستثمار طويل المدى'
        }

        analysis_types_ar = {
            'traditional': 'التحليل التقليدي',
            'ai': 'الذكاء الاصطناعي',
            'enhanced': 'النظام المحسن'
        }

        if lang == 'ar':
            settings_text = f"""⚙️ **إعدادات النظام المحسن**

🎯 **نمط التحليل الحالي:** {styles_ar.get(current_style, current_style)}
🧠 **نوع التحليل الحالي:** {analysis_types_ar.get(current_analysis_type, current_analysis_type)}

اختر الإعداد الذي تريد تغييره:"""
        else:
            settings_text = f"""⚙️ **Enhanced System Settings**

🎯 **Current Analysis Style:** {current_style.replace('_', ' ').title()}
🧠 **Current Analysis Type:** {current_analysis_type.replace('_', ' ').title()}

Choose the setting you want to change:"""

        keyboard = [
            [InlineKeyboardButton(
                "🎯 تغيير نمط التحليل" if lang == 'ar' else "🎯 Change Analysis Style",
                callback_data='show_trading_style_options'
            )],
            [InlineKeyboardButton(
                "🧠 تغيير نوع التحليل" if lang == 'ar' else "🧠 Change Analysis Type",
                callback_data='show_analysis_type_options'
            )],
            [InlineKeyboardButton(
                "🔄 إعادة تعيين الإعدادات" if lang == 'ar' else "🔄 Reset Settings",
                callback_data='reset_enhanced_settings'
            )],
            [InlineKeyboardButton(
                "🔙 العودة للقائمة الرئيسية" if lang == 'ar' else "🔙 Back to Main Menu",
                callback_data='back_to_main'
            )]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            text=settings_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض إعدادات النظام المحسن: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ" if lang == 'ar' else "❌ An error occurred",
            show_alert=True
        )

async def show_trading_style_options(update: Update, context: CallbackContext):
    """عرض خيارات أنماط التحليل"""
    try:
        user_id = str(update.effective_user.id)

        # الحصول على subscription_system
        subscription_system_to_use = subscription_system
        if subscription_system_to_use is None:
            try:
                from services.subscription_system import get_subscription_system
                subscription_system_to_use = get_subscription_system()
            except Exception as e:
                logger.error(f"خطأ في الحصول على subscription_system: {str(e)}")
                await update.callback_query.answer(
                    "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                    show_alert=True
                )
                return

        settings = subscription_system_to_use.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # التحقق من حالة الاشتراك أو الأيام المجانية النشطة
        is_subscribed = subscription_system_to_use.is_subscribed_sync(user_id)
        has_free_day = False
        try:
            if hasattr(subscription_system_to_use, 'free_day_system') and subscription_system_to_use.free_day_system:
                has_free_day = subscription_system_to_use.free_day_system.has_active_free_day(user_id)
        except Exception as e:
            logger.warning(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(e)}")

        if not is_subscribed and not has_free_day:
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط أو في الأيام المجانية" if lang == 'ar' else
                "This feature is available for subscribers only or on free days",
                show_alert=True
            )
            return

        current_style = settings.get('trading_style', 'day_trading')

        # إنشاء نص الخيارات
        if lang == 'ar':
            options_text = "🎯 اختر نمط التحليل المفضل:\n\n"

            options_text += "📈 **المضاربة السريعة**\n"
            options_text += "• المدة: دقائق إلى ساعات قليلة\n"
            options_text += "• الهدف: أرباح سريعة من تحركات صغيرة\n"
            options_text += "• المؤشرات: مؤشر القوة النسبية، ستوكاستيك، بولينجر باندز\n"
            options_text += "• مناسب للمتداولين ذوي الخبرة\n\n"

            options_text += "📊 **التداول اليومي**\n"
            options_text += "• المدة: ساعات إلى يوم واحد\n"
            options_text += "• الهدف: الاستفادة من تحركات الأسعار اليومية\n"
            options_text += "• المؤشرات: الماكد، المتوسط المتحرك الأسي، حجم التداول\n"
            options_text += "• مناسب للمبتدئين والمتوسطين\n\n"

            options_text += "🌊 **التداول المتأرجح**\n"
            options_text += "• المدة: أيام إلى أسابيع\n"
            options_text += "• الهدف: التقاط تحركات متوسطة المدى\n"
            options_text += "• المؤشرات: فيبوناتشي، الدعم والمقاومة\n"
            options_text += "• مناسب للمتداولين بوقت محدود\n\n"

            options_text += "🏛️ **الاستثمار طويل المدى**\n"
            options_text += "• المدة: أسابيع إلى شهور\n"
            options_text += "• الهدف: الاستفادة من الاتجاهات الكبيرة\n"
            options_text += "• المؤشرات: المتوسطات المتحركة، خطوط الاتجاه\n"
            options_text += "• مناسب للمستثمرين الصبورين\n\n"

            options_text += f"**النمط الحالي:** {current_style}"
        else:
            options_text = "🎯 Choose your preferred analysis style:\n\n"

            options_text += "📈 **Scalping**\n"
            options_text += "• Duration: Minutes to few hours\n"
            options_text += "• Goal: Quick profits from small movements\n"
            options_text += "• Indicators: RSI, Stochastic, Bollinger Bands\n"
            options_text += "• Suitable for experienced traders\n\n"

            options_text += "📊 **Day Trading**\n"
            options_text += "• Duration: Hours to one day\n"
            options_text += "• Goal: Benefit from daily price movements\n"
            options_text += "• Indicators: MACD, EMA, Volume\n"
            options_text += "• Suitable for beginners and intermediates\n\n"

            options_text += "🌊 **التداول المتأرجح**\n"
            options_text += "• Duration: Days to weeks\n"
            options_text += "• Goal: Capture medium-term movements\n"
            options_text += "• Indicators: Fibonacci, Support/Resistance\n"
            options_text += "• Suitable for traders with limited time\n\n"

            options_text += "🏛️ **Position Trading**\n"
            options_text += "• Duration: Weeks to months\n"
            options_text += "• Goal: Benefit from major trends\n"
            options_text += "• Indicators: Moving Averages, Trend Lines\n"
            options_text += "• Suitable for patient investors\n\n"

            options_text += f"**Current Style:** {current_style}"

        # إنشاء الأزرار
        keyboard = [
            [InlineKeyboardButton(
                "📈 المضاربة السريعة" if lang == 'ar' else "📈 Scalping",
                callback_data='set_trading_style_scalping'
            )],
            [InlineKeyboardButton(
                "📊 التداول اليومي" if lang == 'ar' else "📊 Day Trading",
                callback_data='set_trading_style_day_trading'
            )],
            [InlineKeyboardButton(
                "🌊 التداول المتأرجح" if lang == 'ar' else "🌊 Swing Trading",
                callback_data='set_trading_style_swing_trading'
            )],
            [InlineKeyboardButton(
                "🏛️ الاستثمار طويل المدى" if lang == 'ar' else "🏛️ Position Trading",
                callback_data='set_trading_style_position'
            )],
            [InlineKeyboardButton(
                "🔙 العودة للإعدادات" if lang == 'ar' else "🔙 Back to Settings",
                callback_data='enhanced_settings'
            )]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            text=options_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض خيارات أنماط التحليل: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ" if lang == 'ar' else "❌ An error occurred",
            show_alert=True
        )

async def show_analysis_type_options(update: Update, context: CallbackContext):
    """عرض خيارات أنواع التحليل"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        subscription_system_to_use = subscription_system
        if subscription_system_to_use is None:
            try:
                from services.subscription_system import get_subscription_system
                subscription_system_to_use = get_subscription_system()
            except Exception as e:
                logger.error(f"خطأ في الحصول على subscription_system: {str(e)}")
                await update.callback_query.answer(
                    "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                    show_alert=True
                )
                return

        settings = subscription_system_to_use.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # التحقق من حالة الاشتراك أو الأيام المجانية النشطة
        is_subscribed = subscription_system_to_use.is_subscribed_sync(user_id)
        has_free_day = False
        try:
            if hasattr(subscription_system_to_use, 'free_day_system') and subscription_system_to_use.free_day_system:
                has_free_day = subscription_system_to_use.free_day_system.has_active_free_day(user_id)
        except Exception as e:
            logger.warning(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(e)}")

        if not is_subscribed and not has_free_day:
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط أو في الأيام المجانية" if lang == 'ar' else
                "This feature is available for subscribers only or on free days",
                show_alert=True
            )
            return

        current_analysis_type = settings.get('analysis_type', 'enhanced')

        # إنشاء نص الخيارات
        if lang == 'ar':
            options_text = "🧠 اختر نوع التحليل المفضل:\n\n"

            options_text += "📊 **التحليل التقليدي**\n"
            options_text += "• سريع ومباشر\n"
            options_text += "• يستخدم مؤشرات أساسية\n"
            options_text += "• مناسب للمبتدئين\n\n"

            options_text += "🤖 **تحليل الذكاء الاصطناعي**\n"
            options_text += "• تحليل متقدم بـ Gemini AI\n"
            options_text += "• يتطلب مفتاح Gemini API\n"
            options_text += "• دقة عالية\n\n"

            options_text += "🚀 **النظام المحسن**\n"
            options_text += "• تحليل متعدد الإطارات الزمنية\n"
            options_text += "• أعلى دقة ممكنة\n"
            options_text += "• يتطلب مفاتيح API متعددة\n\n"

            options_text += f"**النوع الحالي:** {current_analysis_type}"
        else:
            options_text = "🧠 Choose your preferred analysis type:\n\n"

            options_text += "📊 **Traditional Analysis**\n"
            options_text += "• Fast and direct\n"
            options_text += "• Uses basic indicators\n"
            options_text += "• Suitable for beginners\n\n"

            options_text += "🤖 **AI Analysis**\n"
            options_text += "• Advanced analysis with Gemini AI\n"
            options_text += "• Requires Gemini API key\n"
            options_text += "• High accuracy\n\n"

            options_text += "🚀 **Enhanced System**\n"
            options_text += "• Multi-timeframe analysis\n"
            options_text += "• Highest possible accuracy\n"
            options_text += "• Requires multiple API keys\n\n"

            options_text += f"**Current Type:** {current_analysis_type}"

        # إنشاء الأزرار
        keyboard = [
            [InlineKeyboardButton(
                "📊 التحليل التقليدي" if lang == 'ar' else "📊 Traditional Analysis",
                callback_data='set_analysis_type_traditional'
            )],
            [InlineKeyboardButton(
                "🤖 تحليل الذكاء الاصطناعي" if lang == 'ar' else "🤖 AI Analysis",
                callback_data='set_analysis_type_ai'
            )],
            [InlineKeyboardButton(
                "🚀 النظام المحسن" if lang == 'ar' else "🚀 Enhanced System",
                callback_data='set_analysis_type_enhanced'
            )],
            [InlineKeyboardButton(
                "🔙 العودة للإعدادات" if lang == 'ar' else "🔙 Back to Settings",
                callback_data='enhanced_settings'
            )]
        ]

        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            text=options_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض خيارات أنواع التحليل: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ" if lang == 'ar' else "❌ An error occurred",
            show_alert=True
        )

async def show_enhanced_analysis_explanation(update: Update, context: CallbackContext):
    """عرض شرح مفصل لميزة التحليل المحسن"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        subscription_system_to_use = subscription_system
        if subscription_system_to_use is None:
            try:
                from services.subscription_system import get_subscription_system
                subscription_system_to_use = get_subscription_system()
            except Exception as e:
                logger.error(f"خطأ في الحصول على subscription_system: {str(e)}")
                await update.callback_query.answer(
                    "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                    show_alert=True
                )
                return

        settings = subscription_system_to_use.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # الحصول على النص المناسب حسب اللغة
        explanation_text = ENHANCED_ANALYSIS_EXPLANATION.get(lang, ENHANCED_ANALYSIS_EXPLANATION['ar']) if ENHANCED_ANALYSIS_EXPLANATION else "النص غير متوفر"

        # إنشاء الأزرار
        keyboard = []

        # زر تجربة التحليل المحسن
        if lang == 'ar':
            keyboard.append([InlineKeyboardButton("🚀 جرب التحليل المحسن", callback_data="enhanced_analysis_menu")])
            keyboard.append([InlineKeyboardButton("📊 مقارنة مع التحليل العادي", callback_data="compare_analysis_types")])
            keyboard.append([InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")])
        else:
            keyboard.append([InlineKeyboardButton("🚀 Try Enhanced Analysis", callback_data="enhanced_analysis_menu")])
            keyboard.append([InlineKeyboardButton("📊 Compare with Regular Analysis", callback_data="compare_analysis_types")])
            keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # محاولة إرسال الرسالة مع Markdown أولاً، وإذا فشل نستخدم HTML
        try:
            await update.callback_query.edit_message_text(
                text=explanation_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as markdown_error:
            logger.warning(f"فشل في إرسال الرسالة بتنسيق Markdown، محاولة HTML: {str(markdown_error)}")
            try:
                await update.callback_query.edit_message_text(
                    text=explanation_text,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
            except Exception as html_error:
                logger.warning(f"فشل في إرسال الرسالة بتنسيق HTML، إرسال نص عادي: {str(html_error)}")
                await update.callback_query.edit_message_text(
                    text=explanation_text,
                    reply_markup=reply_markup
                )

    except Exception as e:
        logger.error(f"خطأ في عرض شرح التحليل المحسن: {str(e)}")
        error_message = "❌ حدث خطأ أثناء عرض الشرح" if lang == 'ar' else "❌ An error occurred while displaying explanation"
        if update.callback_query:
            await update.callback_query.answer(text=error_message, show_alert=True)
        else:
            await update.message.reply_text(error_message)

async def show_analysis_comparison(update: Update, context: CallbackContext):
    """عرض مقارنة بين التحليل العادي والمحسن"""
    try:
        user_id = str(update.effective_user.id)

        # الحصول على subscription_system
        subscription_system_to_use = subscription_system
        if subscription_system_to_use is None:
            try:
                from services.subscription_system import get_subscription_system
                subscription_system_to_use = get_subscription_system()
            except Exception as e:
                logger.error(f"خطأ في الحصول على subscription_system: {str(e)}")
                await update.callback_query.answer(
                    "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                    show_alert=True
                )
                return

        settings = subscription_system_to_use.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        if lang == 'ar':
            comparison_text = """
📊 **مقارنة شاملة بين أنواع التحليل**

## 🔍 **التحليل العادي (التقليدي)**

### ✅ **المميزات:**
• سريع ومباشر
• يستخدم مؤشرات أساسية
• مناسب للمبتدئين
• لا يحتاج موارد كثيرة

### ❌ **العيوب:**
• دقة محدودة (~60-70%)
• تحليل إطار زمني واحد فقط
• قد يفوت إشارات مهمة
• لا يكشف التناقضات

---

## 🚀 **التحليل المحسن**

### ✅ **المميزات:**
• دقة عالية جداً (~85-95%)
• تحليل متعدد الإطارات الزمنية
• كشف التناقضات والإشارات الخاطئة
• تقييم شامل للمخاطر
• توصيات مخصصة حسب نمط التداول
• نظام تأكيد الإشارات

### ❌ **العيوب:**
• يحتاج وقت أطول قليلاً
• يتطلب مفاتيح API
• أكثر تعقيداً للمبتدئين

---

## 📈 **مقارنة الأداء:**

| المعيار | التحليل العادي | التحليل المحسن |
|---------|---------------|----------------|
| **الدقة** | 60-70% | 85-95% |
| **الإطارات الزمنية** | 1 | 5-7 |
| **المؤشرات** | 5-8 | 20+ |
| **كشف الإشارات الخاطئة** | ❌ | ✅ |
| **تقييم المخاطر** | أساسي | شامل |
| **التخصيص** | محدود | كامل |

---

## 🎯 **التوصية:**

• **للمبتدئين:** ابدأ بالتحليل العادي
• **للمتقدمين:** استخدم التحليل المحسن
• **للمحترفين:** التحليل المحسن مع تخصيص كامل
            """
        else:
            comparison_text = """
📊 **Comprehensive Comparison Between Analysis Types**

## 🔍 **Regular Analysis (Traditional)**

### ✅ **Advantages:**
• Fast and direct
• Uses basic indicators
• Suitable for beginners
• Doesn't require many resources

### ❌ **Disadvantages:**
• Limited accuracy (~60-70%)
• Single timeframe analysis only
• May miss important signals
• Doesn't detect conflicts

---

## 🚀 **Enhanced Analysis**

### ✅ **Advantages:**
• Very high accuracy (~85-95%)
• Multi-timeframe analysis
• Detects conflicts and false signals
• Comprehensive risk assessment
• Customized recommendations by trading style
• Signal confirmation system

### ❌ **Disadvantages:**
• Takes slightly longer
• Requires API keys
• More complex for beginners

---

## 📈 **Performance Comparison:**

| Criteria | Regular Analysis | Enhanced Analysis |
|----------|------------------|-------------------|
| **Accuracy** | 60-70% | 85-95% |
| **Timeframes** | 1 | 5-7 |
| **Indicators** | 5-8 | 20+ |
| **False Signal Detection** | ❌ | ✅ |
| **Risk Assessment** | Basic | Comprehensive |
| **Customization** | Limited | Complete |

---

## 🎯 **Recommendation:**

• **For Beginners:** Start with regular analysis
• **For Advanced:** Use enhanced analysis
• **For Professionals:** Enhanced analysis with full customization
            """

        # إنشاء الأزرار
        keyboard = []
        if lang == 'ar':
            keyboard.append([InlineKeyboardButton("🚀 جرب التحليل المحسن", callback_data="enhanced_analysis_menu")])
            keyboard.append([InlineKeyboardButton("📊 التحليل العادي", callback_data="regular_analysis_menu")])
            keyboard.append([InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")])
        else:
            keyboard.append([InlineKeyboardButton("🚀 Try Enhanced Analysis", callback_data="enhanced_analysis_menu")])
            keyboard.append([InlineKeyboardButton("📊 Regular Analysis", callback_data="regular_analysis_menu")])
            keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # محاولة إرسال الرسالة مع Markdown أولاً، وإذا فشل نستخدم HTML
        try:
            await update.callback_query.edit_message_text(
                text=comparison_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as markdown_error:
            logger.warning(f"فشل في إرسال المقارنة بتنسيق Markdown، محاولة HTML: {str(markdown_error)}")
            try:
                await update.callback_query.edit_message_text(
                    text=comparison_text,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
            except Exception as html_error:
                logger.warning(f"فشل في إرسال المقارنة بتنسيق HTML، إرسال نص عادي: {str(html_error)}")
                await update.callback_query.edit_message_text(
                    text=comparison_text,
                    reply_markup=reply_markup
                )

    except Exception as e:
        logger.error(f"خطأ في عرض مقارنة التحليل: {str(e)}")
        error_message = "❌ حدث خطأ أثناء عرض المقارنة" if lang == 'ar' else "❌ An error occurred while displaying comparison"
        if update.callback_query:
            await update.callback_query.answer(text=error_message, show_alert=True)
        else:
            await update.message.reply_text(error_message)

async def show_upgrade_info(update: Update, context: CallbackContext):
    """عرض معلومات الترقية"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        subscription_system_to_use = subscription_system
        if subscription_system_to_use is None:
            try:
                from services.subscription_system import get_subscription_system
                subscription_system_to_use = get_subscription_system()
            except Exception as e:
                logger.error(f"خطأ في الحصول على subscription_system: {str(e)}")
                await update.callback_query.answer(
                    "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                    show_alert=True
                )
                return

        settings = subscription_system_to_use.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # التحقق من حالة الاشتراك
        if subscription_system_to_use.is_subscribed_sync(user_id):
            await update.callback_query.answer(
                "أنت مشترك بالفعل!" if lang == 'ar' else "You are already subscribed!",
                show_alert=True
            )
            return

        # عرض معلومات الترقية المحدثة
        if lang == 'ar':
            message_text = """🚀 *مميزات بوت التحليل الفني*

📊 *النسخة المجانية:*
• 3 تحليلات يومياً
• تنبيه سعري واحد يومياً
• التحليل الفني التقليدي
• المؤشرات الأساسية
• نظام اليوم المجاني الأسبوعي

💎 *النسخة المدفوعة:*
• تحليلات غير محدودة
• تنبيهات سعرية غير محدودة
• التحليل المحسن بالذكاء الاصطناعي
• تحليل متعدد الإطارات الزمنية
• استراتيجيات التداول المخصصة
• توقعات الأسعار الذكية
• إدارة المخاطر المتقدمة
• إعداد API للمنصات المتعددة
• ميزة التعلم مع الذكاء الاصطناعي

💰 *سعر الاشتراك:*
• 5 دولار أمريكي أسبوعياً
• الدفع عبر PayPal فقط
• تفعيل فوري بعد الدفع

⚠️ *ملاحظات مهمة:*
• لا يوجد دعم فني متاح
• اتبع التعليمات بعناية عند الدفع
• الاشتراك غير قابل للاسترداد
• تأكد من إكمال عملية الدفع بالكامل"""
        else:
            message_text = """🚀 *Technical Analysis Bot Features*

📊 *Free Version:*
• 3 analyses per day
• 1 price alert per day
• Traditional technical analysis
• Basic indicators
• Weekly free day system

💎 *Premium Version:*
• Unlimited analyses
• Unlimited price alerts
• AI-enhanced analysis
• Multi-timeframe analysis
• Custom trading strategies
• Smart price predictions
• Advanced risk management
• Multi-platform API setup
• AI Learning feature

💰 *Subscription Price:*
• $5 USD weekly
• PayPal payment only
• Instant activation after payment

⚠️ *Important Notes:*
• No technical support available
• Follow payment instructions carefully
• Subscription is non-refundable
• Ensure complete payment process"""

        # إنشاء الأزرار
        keyboard = []

        if lang == 'ar':
            keyboard.extend([
                [InlineKeyboardButton("💳 الدفع عبر PayPal", callback_data="payment_paypal")],
                [InlineKeyboardButton("🎁 إعداد اليوم المجاني", callback_data="manage_free_day")],
                [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
            ])
        else:
            keyboard.extend([
                [InlineKeyboardButton("💳 Pay via PayPal", callback_data="payment_paypal")],
                [InlineKeyboardButton("🎁 Setup Free Day", callback_data="manage_free_day")],
                [InlineKeyboardButton("🔙 Back to Main Menu", callback_data="back_to_main")]
            ])

        reply_markup = InlineKeyboardMarkup(keyboard)

        # محاولة تحديث الرسالة أولاً، وإذا فشل إرسال رسالة جديدة
        try:
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as edit_error:
            logger.warning(f"فشل في تحديث الرسالة، إرسال رسالة جديدة: {str(edit_error)}")
            await update.callback_query.message.reply_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )

    except Exception as e:
        logger.error(f"خطأ في عرض معلومات الترقية: {str(e)}")
        error_message = "❌ حدث خطأ أثناء عرض معلومات الترقية" if lang == 'ar' else "❌ An error occurred while displaying upgrade info"
        if update.callback_query:
            await update.callback_query.answer(text=error_message, show_alert=True)
        else:
            await update.message.reply_text(error_message)
