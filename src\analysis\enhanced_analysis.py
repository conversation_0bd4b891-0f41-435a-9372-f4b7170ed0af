"""
وحدة التحليل المحسن - Enhanced Analysis Module
تحتوي على جميع دوال التحليل المحسن المنقولة من main.py

المرحلة الثامنة من خطة التقسيم
الدوال المنقولة:
- analyze_symbol_enhanced()
- get_comprehensive_analysis()
- show_enhanced_analysis_menu()
- show_enhanced_stats()
- compare_trading_styles()
- refresh_analysis()
- show_analysis_type_settings()
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

# استيرادات Telegram
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode
from telegram.ext import CallbackContext

# استيرادات التحليل
from analysis.analysis_helpers import create_analysis_text, get_analysis_type_name
from analysis.gemini_analysis import (
    get_user_api_client, get_price_prediction, 
    get_trading_strategy, get_multi_timeframe_analysis, 
    create_comprehensive_report
)

# استيرادات الأدوات المساعدة
from utils import get_text, sanitize_telegram_text, validate_markdown_entities

# إعداد السجل
logger = logging.getLogger(__name__)

# متغيرات عامة سيتم تهيئتها من main.py
subscription_system = None
enhanced_analyzer = None
api_manager = None
db = None
ca = None  # CryptoAnalysis instance
user_states = None

def initialize_enhanced_analysis(
    subscription_sys, enhanced_analyzer_instance, api_mgr, 
    database, crypto_analysis, user_states_dict, 
    show_main_menu_func, show_api_instructions_func,
    analyze_symbol_func
):
    """تهيئة وحدة التحليل المحسن مع التبعيات المطلوبة"""
    global subscription_system, enhanced_analyzer, api_manager, db, ca, user_states
    global show_main_menu, show_api_instructions, analyze_symbol
    
    subscription_system = subscription_sys
    enhanced_analyzer = enhanced_analyzer_instance
    api_manager = api_mgr
    db = database
    ca = crypto_analysis
    user_states = user_states_dict
    show_main_menu = show_main_menu_func
    show_api_instructions = show_api_instructions_func
    analyze_symbol = analyze_symbol_func
    
    logger.info("✅ تم تهيئة وحدة التحليل المحسن بنجاح")


async def show_enhanced_stats(update: Update, context: CallbackContext):
    """عرض إحصائيات النظام المحسن"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # التحقق من توفر النظام المحسن
        if enhanced_analyzer is None:
            await update.callback_query.answer(
                "النظام المحسن غير متاح حالياً" if lang == 'ar' else
                "Enhanced system not available",
                show_alert=True
            )
            return

        # جمع إحصائيات النظام المحسن
        try:
            stats = await enhanced_analyzer.get_system_stats()
            
            if lang == 'ar':
                stats_text = f"""📊 **إحصائيات النظام المحسن**

🔍 **إحصائيات التحليل:**
• إجمالي التحليلات: {stats.get('total_analyses', 0)}
• التحليلات الناجحة: {stats.get('successful_analyses', 0)}
• معدل النجاح: {stats.get('success_rate', 0):.1f}%

⚡ **الأداء:**
• متوسط وقت التحليل: {stats.get('avg_analysis_time', 0):.2f}ث
• الذاكرة المستخدمة: {stats.get('memory_usage', 0):.1f}MB
• حالة النظام: {'🟢 نشط' if stats.get('system_status') == 'active' else '🔴 غير نشط'}

📈 **الإطارات الزمنية:**
• الإطارات المدعومة: {stats.get('supported_timeframes', 0)}
• الإطارات النشطة: {stats.get('active_timeframes', 0)}

🎯 **دقة التوقعات:**
• دقة التوقعات قصيرة المدى: {stats.get('short_term_accuracy', 0):.1f}%
• دقة التوقعات متوسطة المدى: {stats.get('medium_term_accuracy', 0):.1f}%
• دقة التوقعات طويلة المدى: {stats.get('long_term_accuracy', 0):.1f}%

🔄 **آخر تحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            else:
                stats_text = f"""📊 **Enhanced System Statistics**

🔍 **Analysis Statistics:**
• Total Analyses: {stats.get('total_analyses', 0)}
• Successful Analyses: {stats.get('successful_analyses', 0)}
• Success Rate: {stats.get('success_rate', 0):.1f}%

⚡ **Performance:**
• Average Analysis Time: {stats.get('avg_analysis_time', 0):.2f}s
• Memory Usage: {stats.get('memory_usage', 0):.1f}MB
• System Status: {'🟢 Active' if stats.get('system_status') == 'active' else '🔴 Inactive'}

📈 **Timeframes:**
• Supported Timeframes: {stats.get('supported_timeframes', 0)}
• Active Timeframes: {stats.get('active_timeframes', 0)}

🎯 **Prediction Accuracy:**
• Short-term Accuracy: {stats.get('short_term_accuracy', 0):.1f}%
• Medium-term Accuracy: {stats.get('medium_term_accuracy', 0):.1f}%
• Long-term Accuracy: {stats.get('long_term_accuracy', 0):.1f}%

🔄 **Last Updated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

        except Exception as e:
            logger.warning(f"فشل في الحصول على إحصائيات النظام المحسن: {str(e)}")
            stats_text = (
                "📊 **إحصائيات النظام المحسن**\n\n"
                "⚠️ لا يمكن الحصول على الإحصائيات حالياً\n"
                "يرجى المحاولة مرة أخرى لاحقاً" if lang == 'ar' else
                "📊 **Enhanced System Statistics**\n\n"
                "⚠️ Cannot retrieve statistics at the moment\n"
                "Please try again later"
            )

        # إنشاء أزرار الإحصائيات
        keyboard = [
            [InlineKeyboardButton(
                "🔄 تحديث الإحصائيات" if lang == 'ar' else "🔄 Refresh Stats",
                callback_data='enhanced_stats'
            )],
            [InlineKeyboardButton(
                "🗑️ مسح التخزين المؤقت" if lang == 'ar' else "🗑️ Clear Cache",
                callback_data='clear_enhanced_cache'
            )],
            [InlineKeyboardButton(
                "🔙 العودة" if lang == 'ar' else "🔙 Back",
                callback_data='enhanced_analysis_menu'
            )]
        ]

        await update.callback_query.edit_message_text(
            text=stats_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"خطأ في عرض إحصائيات النظام المحسن: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ" if lang == 'ar' else "❌ An error occurred",
            show_alert=True
        )


async def show_enhanced_analysis_menu(update: Update, context: CallbackContext):
    """عرض قائمة النظام المحسن"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        if subscription_system is None:
            logger.error("subscription_system غير متوفر")
            await update.callback_query.answer(
                "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                show_alert=True
            )
            return

        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # التحقق من حالة الاشتراك والأيام المجانية
        subscription_details = subscription_system.get_subscription_status(user_id, full_details=True)
        is_premium = subscription_details['is_active'] if subscription_details else False
        is_free_day = subscription_details.get('is_free_day', False) if subscription_details else False

        # للمستخدمين غير المشتركين أو الذين ليس لديهم يوم مجاني: التحليل التقليدي تلقائياً
        if not (is_premium or is_free_day):
            # توجيه المستخدم مباشرة لإدخال رمز العملة للتحليل التقليدي
            await update.callback_query.message.reply_text(
                "أدخل رمز العملة للتحليل:" if lang == 'ar' else "Enter currency symbol for analysis:",
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')
                ]])
            )
            
            # تعيين حالة انتظار رمز العملة
            if user_states is not None:
                user_states[user_id] = {'state': 'waiting_for_symbol', 'analysis_mode': 'regular'}
            return

        # التحقق من توفر النظام المحسن
        if enhanced_analyzer is None:
            await update.callback_query.answer(
                "النظام المحسن غير متاح حالياً" if lang == 'ar' else
                "Enhanced system not available",
                show_alert=True
            )
            return

        # للمستخدمين المشتركين أو الذين لديهم يوم مجاني: عرض القائمة الكاملة مع الإعدادات

        # إنشاء نص القائمة
        current_analysis_type = settings.get('analysis_type', 'ai')
        current_trading_style = settings.get('trading_style', 'day_trading')

        if lang == 'ar':
            menu_text = f"""🚀 **التحليل المحسن الموحد**

🔄 **نظام جديد:** تحليل شامل يجمع جميع الأنماط
📊 **الأنماط المدمجة:** المضاربة السريعة + التداول اليومي + التداول المتأرجح + الاستثمار طويل المدى
⚡ **مزايا:** تحليل فوري شامل بنقرة واحدة
🎯 **الدقة:** أعلى دقة ممكنة من دمج جميع الاستراتيجيات

✨ **الجديد:** لا حاجة لاختيار نمط - النظام يحلل كل شيء تلقائياً!

🚀 **ابدأ التحليل:**
أدخل رمز العملة للحصول على تحليل محسن شامل"""
        else:
            menu_text = f"""🚀 **Unified Enhanced Analysis**

🔄 **New System:** Comprehensive analysis combining all patterns
📊 **Integrated Patterns:** Scalping + Day Trading + Swing Trading + Position Trading
⚡ **Benefits:** Instant comprehensive analysis with one click
🎯 **Accuracy:** Highest possible accuracy from combining all strategies

✨ **New:** No need to choose pattern - system analyzes everything automatically!

🚀 **Start Analysis:**
Enter currency symbol for comprehensive enhanced analysis"""

        # إنشاء الأزرار للنظام الموحد
        keyboard = [
            [InlineKeyboardButton(
                "🚀 بدء التحليل الموحد" if lang == 'ar' else "🚀 Start Unified Analysis",
                callback_data='start_enhanced_analysis'
            )],
            [InlineKeyboardButton(
                "📊 إحصائيات النظام" if lang == 'ar' else "📊 System Statistics",
                callback_data='enhanced_stats'
            )],
            [InlineKeyboardButton(
                "ℹ️ شرح النظام الموحد" if lang == 'ar' else "ℹ️ About Unified System",
                callback_data='explain_unified_system'
            )],
            [InlineKeyboardButton(
                "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                callback_data='back_to_main'
            )]
        ]

        await update.callback_query.edit_message_text(
            text=menu_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"خطأ في عرض قائمة التحليل المحسن: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ" if lang == 'ar' else "❌ An error occurred",
            show_alert=True
        )


def get_trading_style_name(style: str, lang: str) -> str:
    """الحصول على اسم نمط التداول باللغة المحددة"""
    styles = {
        'ar': {
            'scalping': 'المضاربة السريعة',
            'day_trading': 'التداول اليومي',
            'swing_trading': 'التداول المتأرجح',
            'position': 'الاستثمار طويل المدى'
        },
        'en': {
            'scalping': 'Scalping',
            'day_trading': 'Day Trading',
            'swing_trading': 'Swing Trading',
            'position': 'Position Trading'
        }
    }
    return styles.get(lang, styles['ar']).get(style, style)


def get_analysis_type_name(analysis_type: str, lang: str) -> str:
    """الحصول على اسم نوع التحليل باللغة المحددة"""
    types = {
        'ar': {
            'traditional': 'التحليل التقليدي',
            'ai': 'الذكاء الاصطناعي',
            'enhanced': 'التحليل المحسن',
            'comprehensive': 'التحليل الشامل'
        },
        'en': {
            'traditional': 'Traditional Analysis',
            'ai': 'AI Analysis',
            'enhanced': 'Enhanced Analysis',
            'comprehensive': 'Comprehensive Analysis'
        }
    }
    return types.get(lang, types['ar']).get(analysis_type, analysis_type)


async def analyze_symbol_enhanced(update: Update, context: CallbackContext, symbol: str, message=None, target_currency=None):
    """تحليل رمز العملة باستخدام النظام المحسن مع الرسم البياني"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        if subscription_system is None:
            logger.error("subscription_system غير متوفر في analyze_symbol_enhanced")
            error_text = "❌ حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً."
            if message:
                await message.edit_text(error_text)
            else:
                await update.message.reply_text(error_text)
            return

        settings = subscription_system.get_user_settings(user_id)
        if not settings:
            logger.warning(f"لم يتم العثور على إعدادات للمستخدم {user_id}")
            settings = {'lang': 'ar'}  # إعدادات افتراضية

        lang = settings.get('lang', 'ar')

        # التحقق من حالة الاشتراك
        is_subscribed = subscription_system.is_subscribed_sync(user_id)
        if not is_subscribed:
            await update.message.reply_text(
                "التحليل المحسن متاح للمشتركين فقط" if lang == 'ar' else "Enhanced analysis is available for subscribers only"
            )
            return

        # إرسال رسالة انتظار
        wait_message = await update.message.reply_text(
            "🔄 جاري تحليل العملة باستخدام النظام المحسن..." if lang == 'ar' else "🔄 Analyzing currency using enhanced system..."
        )

        # الحصول على بيانات السوق لإنشاء الرسم البياني
        market_data = await ca.get_market_data(symbol, user_id=user_id, lang=lang)

        # استخدام التحليل المحسن مباشرة
        analysis_text = await create_analysis_text(symbol, market_data, lang, user_id, analysis_type='enhanced')

        # حذف رسالة الانتظار
        await wait_message.delete()

        # إرسال التحليل
        keyboard = [
            [InlineKeyboardButton(
                "🔄 تحديث التحليل" if lang == 'ar' else "🔄 Refresh Analysis",
                callback_data=f'refresh_enhanced_{symbol}'
            )],
            [InlineKeyboardButton(
                "📊 مقارنة أنماط التداول" if lang == 'ar' else "📊 Compare Trading Styles",
                callback_data=f'compare_styles_{symbol}'
            )],
            [InlineKeyboardButton(
                "🔙 العودة" if lang == 'ar' else "🔙 Back",
                callback_data='enhanced_analysis_menu'
            )]
        ]

        # تطبيق الحل المبهر لتنسيق النص
        try:
            from utils.utils import fix_bold_formatting
            analysis_text = fix_bold_formatting(analysis_text, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة fix_bold_formatting")

        # إرسال التحليل مع معالجة أخطاء Markdown المتدرجة
        try:
            await update.message.reply_text(
                analysis_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN,
                disable_web_page_preview=True
            )
        except Exception as markdown_error:
            logger.warning(f"فشل في إرسال التحليل المحسن مع Markdown: {markdown_error}")
            # محاولة إرسال بدون تنسيق Markdown
            try:
                await update.message.reply_text(
                    analysis_text,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    disable_web_page_preview=True
                )
            except Exception as plain_error:
                logger.error(f"فشل في إرسال التحليل المحسن حتى بدون Markdown: {plain_error}")
                # إرسال رسالة خطأ بسيطة
                await update.message.reply_text(
                    "عذراً، حدث خطأ في عرض التحليل. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                    "Sorry, an error occurred displaying the analysis. Please try again.",
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

    except Exception as e:
        logger.error(f"خطأ في التحليل المحسن للعملة {symbol}: {str(e)}")
        try:
            await wait_message.edit_text(
                "❌ حدث خطأ أثناء التحليل" if lang == 'ar' else "❌ Error occurred during analysis"
            )
        except:
            await update.message.reply_text(
                "❌ حدث خطأ أثناء التحليل" if lang == 'ar' else "❌ Error occurred during analysis"
            )


async def compare_trading_styles(update: Update, context: CallbackContext, symbol: str):
    """مقارنة أنماط التداول المختلفة للعملة"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # التحقق من توفر النظام المحسن
        if enhanced_analyzer is None:
            await update.callback_query.answer(
                "النظام المحسن غير متاح حالياً" if lang == 'ar' else
                "Enhanced system not available",
                show_alert=True
            )
            return

        # إرسال رسالة انتظار
        wait_message = await update.callback_query.message.reply_text(
            "جاري مقارنة أنماط التداول المختلفة..." if lang == 'ar' else
            "Comparing different trading styles..."
        )

        try:
            # تشغيل مقارنة أنماط التداول
            comparison_result = await enhanced_analyzer.compare_trading_styles(symbol, user_id)

            if comparison_result and comparison_result.get('comparison_results'):
                # حذف رسالة الانتظار
                await wait_message.delete()

                # إنشاء نص المقارنة
                comparison_text = f"📊 **مقارنة أنماط التداول لـ {symbol}**\n\n"

                styles_ar = {
                    'scalping': 'المضاربة السريعة',
                    'day_trading': 'التداول اليومي',
                    'swing_trading': 'التداول المتأرجح',
                    'position': 'الاستثمار طويل المدى'
                }

                results = comparison_result['comparison_results']
                best_style = comparison_result.get('best_style', 'day_trading')

                for style, result in results.items():
                    style_name = styles_ar.get(style, style)
                    is_best = "⭐ " if style == best_style else ""

                    comparison_text += f"{is_best}**{style_name}**:\n"
                    comparison_text += f"• التوصية: {result.get('recommendation', 'غير محدد')}\n"
                    comparison_text += f"• مستوى الثقة: {result.get('confidence', 'غير محدد')}\n"
                    comparison_text += f"• المخاطر: {result.get('risk_level', 'غير محدد')}\n"
                    comparison_text += f"• القوة: {result.get('strength', 0)}/100\n\n"

                comparison_text += f"🏆 **أفضل نمط تداول**: {styles_ar.get(best_style, best_style)}\n"

                # إنشاء أزرار التفاعل
                keyboard = [
                    [InlineKeyboardButton(
                        f"اختيار {styles_ar.get(best_style, best_style)}" if lang == 'ar' else f"Select {best_style}",
                        callback_data=f'set_trading_style_{best_style}'
                    )],
                    [InlineKeyboardButton(
                        "🔄 تحديث المقارنة" if lang == 'ar' else "🔄 Refresh Comparison",
                        callback_data=f'compare_styles_{symbol}'
                    )],
                    [InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back",
                        callback_data='back_to_main'
                    )]
                ]

                # تطبيق الحل المبهر لتنسيق النص
                try:
                    from utils.utils import fix_bold_formatting
                    comparison_text = fix_bold_formatting(comparison_text, lang)
                except ImportError:
                    logger.warning("لم يتم العثور على دالة fix_bold_formatting")

                # إرسال المقارنة مع معالجة أخطاء Markdown المتدرجة
                try:
                    await update.callback_query.message.reply_text(
                        comparison_text,
                        reply_markup=InlineKeyboardMarkup(keyboard),
                        parse_mode=ParseMode.MARKDOWN,
                        disable_web_page_preview=True
                    )
                except Exception as markdown_error:
                    logger.warning(f"فشل في إرسال مقارنة أنماط التداول مع Markdown: {markdown_error}")
                    # محاولة إرسال بدون تنسيق Markdown
                    try:
                        await update.callback_query.message.reply_text(
                            comparison_text,
                            reply_markup=InlineKeyboardMarkup(keyboard),
                            disable_web_page_preview=True
                        )
                    except Exception as plain_error:
                        logger.error(f"فشل في إرسال مقارنة أنماط التداول حتى بدون Markdown: {plain_error}")
                        # إرسال رسالة خطأ بسيطة
                        await update.callback_query.message.reply_text(
                            "عذراً، حدث خطأ في عرض المقارنة. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                            "Sorry, an error occurred displaying the comparison. Please try again.",
                            reply_markup=InlineKeyboardMarkup(keyboard)
                        )
            else:
                await wait_message.edit_text(
                    "❌ فشل في مقارنة أنماط التداول" if lang == 'ar' else
                    "❌ Failed to compare trading styles"
                )

        except Exception as e:
            logger.error(f"خطأ في مقارنة أنماط التداول: {str(e)}")
            await wait_message.edit_text(
                "❌ حدث خطأ أثناء مقارنة أنماط التداول" if lang == 'ar' else
                "❌ Error occurred while comparing trading styles"
            )

    except Exception as e:
        logger.error(f"خطأ في دالة مقارنة أنماط التداول: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ" if lang == 'ar' else "❌ An error occurred",
            show_alert=True
        )


async def refresh_analysis(update: Update, context: CallbackContext, symbol: str):
    """تحديث التحليل"""
    try:
        # التحقق من توفر analyze_symbol
        if analyze_symbol is None:
            logger.error("analyze_symbol غير متوفر في refresh_analysis")
            await update.callback_query.answer("❌ حدث خطأ في النظام", show_alert=True)
            return

        # تحديث التحليل
        await analyze_symbol(update, context, symbol, message=update.callback_query.message)
    except Exception as e:
        logger.error(f"Error refreshing analysis: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ. الرجاء المحاولة مرة أخرى")


async def show_analysis_type_settings(update: Update, context: CallbackContext):
    """عرض إعدادات نوع التحليل"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        if subscription_system is None:
            logger.error("subscription_system غير متوفر")
            await update.callback_query.answer(
                "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                show_alert=True
            )
            return

        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'
        current_type = settings.get('analysis_type', 'ai') if settings else 'ai'

        # التحقق من حالة الاشتراك
        subscription_details = subscription_system.get_subscription_status(user_id, full_details=True)
        is_premium = subscription_details['is_active'] if subscription_details else False
        is_free_day = subscription_details.get('is_free_day', False) if subscription_details else False

        # التحقق من أن المستخدم مشترك أو لديه يوم مجاني
        if not (is_premium or is_free_day):
            error_message = (
                "⚠️ إعدادات التحليل متاحة للمشتركين أو من لديهم يوم مجاني فقط" if lang == 'ar' else
                "⚠️ Analysis settings are available for subscribers or free day users only"
            )
            if update.callback_query:
                await update.callback_query.answer(error_message, show_alert=True)
                await show_main_menu(update, context)
            else:
                await update.message.reply_text(error_message)
            return

        is_subscribed = subscription_system.is_subscribed_sync(user_id)
        has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini') if api_manager else False

        # إنشاء نص الإعدادات
        if lang == 'ar':
            settings_text = "⚙️ **إعدادات نوع التحليل**\n\n"
            settings_text += f"النوع الحالي: **{get_analysis_type_name(current_type, lang)}**\n\n"
            settings_text += "اختر نوع التحليل المفضل:\n\n"

            settings_text += "📊 **التحليل التقليدي**: تحليل أساسي بالمؤشرات الفنية\n"
            settings_text += "🤖 **تحليل الذكاء الاصطناعي**: تحليل متقدم بـ Gemini AI\n"
            settings_text += "🚀 **التحليل المحسن**: تحليل متعدد الإطارات الزمنية\n"
        else:
            settings_text = "⚙️ **Analysis Type Settings**\n\n"
            settings_text += f"Current type: **{get_analysis_type_name(current_type, lang)}**\n\n"
            settings_text += "Choose your preferred analysis type:\n\n"

            settings_text += "📊 **Traditional Analysis**: Basic analysis with technical indicators\n"
            settings_text += "🤖 **AI Analysis**: Advanced analysis with Gemini AI\n"
            settings_text += "🚀 **Enhanced Analysis**: Multi-timeframe analysis\n"

        # إنشاء الأزرار
        keyboard = []

        # زر التحليل التقليدي (متاح للجميع)
        traditional_text = "📊 التحليل التقليدي" if lang == 'ar' else "📊 Traditional Analysis"
        if current_type == 'traditional':
            traditional_text += " ✓"
        keyboard.append([InlineKeyboardButton(traditional_text, callback_data='set_analysis_type_traditional')])

        # زر تحليل الذكاء الاصطناعي (للمشتركين مع Gemini API)
        if is_subscribed:
            ai_text = "🤖 تحليل الذكاء الاصطناعي" if lang == 'ar' else "🤖 AI Analysis"
            if current_type == 'ai':
                ai_text += " ✓"
            if not has_gemini_api:
                ai_text += " (يتطلب Gemini API)" if lang == 'ar' else " (Requires Gemini API)"
            keyboard.append([InlineKeyboardButton(ai_text, callback_data='set_analysis_type_ai')])

            # زر التحليل المحسن (للمشتركين فقط)
            enhanced_text = "🚀 التحليل المحسن" if lang == 'ar' else "🚀 Enhanced Analysis"
            if current_type == 'enhanced':
                enhanced_text += " ✓"
            keyboard.append([InlineKeyboardButton(enhanced_text, callback_data='set_analysis_type_enhanced')])

        # زر العودة للتحليل المحسن
        keyboard.append([InlineKeyboardButton(
            "🔙 العودة للتحليل المحسن" if lang == 'ar' else "🔙 Back to Enhanced Analysis",
            callback_data='enhanced_analysis_menu'
        )])

        # زر العودة للقائمة الرئيسية
        keyboard.append([InlineKeyboardButton(
            "🏠 القائمة الرئيسية" if lang == 'ar' else "🏠 Main Menu",
            callback_data='back_to_main'
        )])

        # إرسال الرسالة
        if update.callback_query:
            await update.callback_query.edit_message_text(
                text=settings_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                text=settings_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )

    except Exception as e:
        logger.error(f"خطأ في عرض إعدادات نوع التحليل: {str(e)}")
        error_message = "❌ حدث خطأ أثناء عرض الإعدادات" if lang == 'ar' else "❌ Error displaying settings"
        if update.callback_query:
            await update.callback_query.answer(error_message, show_alert=True)
        else:
            await update.message.reply_text(error_message)


async def get_comprehensive_analysis(update: Update, context: CallbackContext, symbol: str):
    """
    الحصول على تقرير تحليلي متكامل يشمل:
    1. التنبؤات السعرية
    2. تحليل متعدد الأطر الزمنية
    3. استراتيجية التداول
    4. تقرير شامل مدمج بالذكاء الاصطناعي
    """
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        if subscription_system is None:
            logger.error("subscription_system غير متوفر")
            await update.message.reply_text("❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً")
            return

        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # استخدام العملة المخصصة إذا كانت متوفرة
        custom_currencies = settings.get('currencies', [])
        if custom_currencies:
            target_currency = custom_currencies[0]
        else:
            target_currency = 'USD'  # العملة الافتراضية

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # إرسال رسالة انتظار
        wait_message = await update.callback_query.message.reply_text(
            "🔬 جاري إنشاء التقرير التحليلي المتكامل...\n\n" +
            "⏳ هذا قد يستغرق بضع دقائق لجمع وتحليل جميع البيانات" if lang == 'ar' else
            "🔬 Creating comprehensive analysis report...\n\n" +
            "⏳ This may take a few minutes to collect and analyze all data"
        )

        # الحصول على بيانات السوق
        market_data = await ca.get_market_data(symbol, target_currency=target_currency, user_id=user_id, lang=lang)

        if not market_data:
            await wait_message.edit_text(
                f"لم يتم العثور على بيانات لـ {symbol}" if lang == 'ar' else
                f"Could not find data for {symbol}"
            )
            return

        # الحصول على نموذج Gemini للمستخدم
        gemini_model = await get_user_api_client(user_id, 'gemini')

        if not gemini_model:
            await wait_message.edit_text(
                "يرجى إضافة مفتاح Gemini API الخاص بك أولاً. انتقل إلى إعدادات API من القائمة الرئيسية." if lang == 'ar' else
                "Please add your Gemini API key first. Go to API Setup from the main menu."
            )
            await show_api_instructions(update, context, 'gemini', lang)
            return

        # تحديث رسالة الانتظار
        await wait_message.edit_text(
            "📊 المرحلة 1/4: جاري تحليل التنبؤات السعرية..." if lang == 'ar' else
            "📊 Step 1/4: Analyzing price predictions..."
        )

        # 1. الحصول على التنبؤات السعرية
        price_predictions = await get_price_prediction(gemini_model, market_data, lang)

        if not price_predictions:
            logger.warning("فشل في الحصول على التنبؤات السعرية")
            price_predictions = "لم يتم الحصول على تنبؤات سعرية" if lang == 'ar' else "Price predictions not available"

        # تحديث رسالة الانتظار
        await wait_message.edit_text(
            "🔍 المرحلة 2/4: جاري تحليل الأطر الزمنية المتعددة..." if lang == 'ar' else
            "🔍 Step 2/4: Analyzing multiple timeframes..."
        )

        # 2. الحصول على تحليل متعدد الأطر الزمنية
        # جمع بيانات الإطارات الزمنية المختلفة
        timeframes = {
            '1h': None,
            '4h': market_data,  # استخدام البيانات التي تم جلبها بالفعل
            '1d': None,
            '1w': None
        }

        try:
            timeframes['1h'] = await ca.get_market_data(symbol, interval='1h', target_currency=target_currency)
            timeframes['1d'] = await ca.get_market_data(symbol, interval='1d', target_currency=target_currency)
            timeframes['1w'] = await ca.get_market_data(symbol, interval='1w', target_currency=target_currency)
        except Exception as e:
            logger.warning(f"لم يتم الحصول على جميع الإطارات الزمنية: {str(e)}")

        # تصفية الإطارات الزمنية التي تم الحصول عليها بنجاح
        valid_timeframes = {k: v for k, v in timeframes.items() if v is not None}

        multi_timeframe_analysis = None
        if len(valid_timeframes) >= 2:
            multi_timeframe_analysis = await get_multi_timeframe_analysis(gemini_model, market_data, valid_timeframes, lang)

        if not multi_timeframe_analysis:
            logger.warning("فشل في الحصول على تحليل متعدد الأطر الزمنية")
            multi_timeframe_analysis = "لم يتم الحصول على تحليل متعدد الأطر الزمنية" if lang == 'ar' else "Multi-timeframe analysis not available"

        # تحديث رسالة الانتظار
        await wait_message.edit_text(
            "📈 المرحلة 3/4: جاري إنشاء استراتيجية التداول..." if lang == 'ar' else
            "📈 Step 3/4: Creating trading strategy..."
        )

        # 3. الحصول على استراتيجية التداول
        trading_strategy = await get_trading_strategy(gemini_model, market_data, lang)

        if not trading_strategy:
            logger.warning("فشل في الحصول على استراتيجية التداول")
            trading_strategy = "لم يتم الحصول على استراتيجية تداول" if lang == 'ar' else "Trading strategy not available"

        # تحديث رسالة الانتظار
        await wait_message.edit_text(
            "🤖 المرحلة 4/4: جاري دمج التحليلات وإنشاء التقرير النهائي..." if lang == 'ar' else
            "🤖 Step 4/4: Combining analyses and creating final report..."
        )

        # 4. دمج جميع النتائج في تقرير شامل
        comprehensive_report = await create_comprehensive_report(
            gemini_model,
            symbol,
            market_data,
            price_predictions,
            multi_timeframe_analysis,
            trading_strategy,
            lang
        )

        if not comprehensive_report:
            await wait_message.edit_text(
                "حدث خطأ أثناء إنشاء التقرير التحليلي المتكامل" if lang == 'ar' else
                "An error occurred while creating the comprehensive analysis report"
            )
            return

        # تنظيف وتحقق من صحة التقرير قبل الإرسال
        comprehensive_report = sanitize_telegram_text(comprehensive_report)

        # التحقق من صحة entities
        if not validate_markdown_entities(comprehensive_report):
            logger.warning("تم اكتشاف مشاكل في تنسيق التقرير، سيتم إرساله كنص عادي")
            # إزالة جميع تنسيقات Markdown كحل احتياطي
            import re
            comprehensive_report = re.sub(r'\*\*([^*]*?)\*\*', r'\1', comprehensive_report)
            comprehensive_report = re.sub(r'\*([^*]*?)\*', r'\1', comprehensive_report)
            comprehensive_report = re.sub(r'#{1,6}\s*([^#\n]*)', r'\1', comprehensive_report)
            parse_mode = None
        else:
            parse_mode = 'Markdown'

        # التحقق من طول التقرير وتقليصه إذا لزم الأمر
        max_telegram_length = 4000  # حد أقصى آمن لتيليجرام

        if len(comprehensive_report) > max_telegram_length:
            logger.warning(f"التقرير طويل جداً ({len(comprehensive_report)} حرف)، سيتم تقليصه")
            # تقليص التقرير بدلاً من تقسيمه
            comprehensive_report = comprehensive_report[:max_telegram_length-200] + "\n\n⚠️ تم اختصار التقرير لتجنب التقسيم إلى صفحات متعددة"

        # حذف رسالة الانتظار
        await wait_message.delete()

        # إنشاء أزرار التحكم
        keyboard = [
            [
                InlineKeyboardButton("🔄 تحديث التقرير" if lang == 'ar' else "🔄 Refresh Report",
                                   callback_data=f'comprehensive_analysis_{symbol}')
            ],
            [
                InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back",
                                   callback_data='back_to_main')
            ]
        ]

        # تطبيق الحل المبهر لتنسيق النص
        try:
            from utils.utils import fix_bold_formatting
            comprehensive_report = fix_bold_formatting(comprehensive_report, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة fix_bold_formatting")

        # إرسال التقرير كرسالة واحدة مع معالجة أخطاء التحليل المتدرجة
        try:
            await update.callback_query.message.reply_text(
                text=comprehensive_report,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN,
                disable_web_page_preview=True
            )
        except Exception as send_error:
            # إذا فشل الإرسال مع Markdown، أرسل كنص عادي
            if "can't parse entities" in str(send_error).lower() or "parse" in str(send_error).lower():
                logger.warning("فشل إرسال التقرير مع Markdown، سيتم إرساله كنص عادي")
                # إزالة جميع تنسيقات Markdown
                import re
                clean_report = re.sub(r'\*\*([^*]*?)\*\*', r'\1', comprehensive_report)
                clean_report = re.sub(r'\*([^*]*?)\*', r'\1', clean_report)
                clean_report = re.sub(r'#{1,6}\s*([^#\n]*)', r'\1', clean_report)

                await update.callback_query.message.reply_text(
                    text=clean_report,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode=None
                )
            elif "Message_too_long" in str(send_error) or "message is too long" in str(send_error).lower():
                # إذا كان التقرير لا يزال طويلاً جداً، قم بتقليصه أكثر
                logger.warning("التقرير لا يزال طويلاً جداً، سيتم تقليصه أكثر")
                short_report = comprehensive_report[:3500] + "\n\n⚠️ تم اختصار التقرير بسبب قيود طول الرسالة"

                await update.callback_query.message.reply_text(
                    text=short_report,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode=None
                )
            else:
                # إعادة رفع الخطأ إذا لم يكن متعلق بالتحليل
                raise send_error

    except Exception as e:
        logger.error(f"خطأ في الحصول على التقرير التحليلي المتكامل: {str(e)}")

        # معالجة خاصة لأخطاء مختلفة
        if "Message_too_long" in str(e) or "message is too long" in str(e).lower():
            error_message = (
                "⚠️ التقرير طويل جداً. جاري إعادة المحاولة بتنسيق مختصر..." if lang == 'ar' else
                "⚠️ Report too long. Retrying with condensed format..."
            )
        elif "can't parse entities" in str(e).lower() or "parse" in str(e).lower():
            error_message = (
                "⚠️ مشكلة في تنسيق التقرير. تم إرسال نسخة مبسطة." if lang == 'ar' else
                "⚠️ Report formatting issue. Simplified version sent."
            )
        else:
            error_message = (
                f"حدث خطأ: {str(e)}" if lang == 'ar' else
                f"An error occurred: {str(e)}"
            )

        try:
            await wait_message.edit_text(error_message)
        except:
            try:
                await update.callback_query.message.reply_text(error_message)
            except:
                await update.callback_query.answer(
                    "حدث خطأ أثناء معالجة الطلب" if lang == 'ar' else
                    "An error occurred while processing the request",
                    show_alert=True
                )
