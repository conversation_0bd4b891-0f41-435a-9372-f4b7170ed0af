"""
نظام حماية API متقدم - Advanced API Security System
يوفر حماية شاملة لجميع نقاط API مع تقنيات أمان متطورة

الميزات:
- Rate Limiting متقدم
- حماية من DDoS
- تشفير البيانات الحساسة
- مصادقة متعددة العوامل
- تسجيل الأنشطة المشبوهة
- حماية من SQL Injection و XSS
"""

import asyncio
import hashlib
import hmac
import time
import json
import logging
from typing import Dict, List, Optional, Callable, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import ipaddress
import re
import secrets
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import jwt
from functools import wraps
# تم حذف Redis نهائياً واستبداله بتخزين مؤقت محلي محسن
REDIS_AVAILABLE = False
from firebase_admin import firestore

logger = logging.getLogger(__name__)

@dataclass
class SecurityEvent:
    """حدث أمني"""
    event_type: str
    user_id: str
    ip_address: str
    endpoint: str
    timestamp: datetime
    severity: str  # low, medium, high, critical
    details: Dict[str, Any]
    blocked: bool = False

@dataclass
class RateLimitRule:
    """قاعدة تحديد المعدل"""
    endpoint: str
    max_requests: int
    time_window: int  # بالثواني
    user_specific: bool = True
    ip_specific: bool = True

class AdvancedAPISecurityManager:
    """مدير الأمان المتقدم لـ API"""
    
    def __init__(self, db: firestore.Client, encryption_key: str = None):
        """
        تهيئة نظام الأمان المتقدم
        
        Args:
            db: قاعدة بيانات Firestore
            encryption_key: مفتاح التشفير
        """
        self.db = db
        self.encryption_key = encryption_key or Fernet.generate_key()
        self.cipher = Fernet(self.encryption_key)
        
        # تتبع الطلبات والمعدلات
        self.request_counts: Dict[str, deque] = defaultdict(lambda: deque())
        self.blocked_ips: Dict[str, datetime] = {}
        self.blocked_users: Dict[str, datetime] = {}
        
        # قواعد Rate Limiting
        self.rate_limit_rules: Dict[str, RateLimitRule] = {}
        self._setup_default_rate_limits()
        
        # قائمة IP المحظورة والمسموحة
        self.ip_whitelist: set = set()
        self.ip_blacklist: set = set()
        
        # أنماط الهجمات المعروفة - محسنة لتجنب الإيجابيات الخاطئة
        self.attack_patterns = {
            'sql_injection': [
                r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\s+\w+\s+(FROM|INTO|TABLE|DATABASE))",
                r"(\b(UNION\s+SELECT|OR\s+\d+\s*=\s*\d+|AND\s+\d+\s*=\s*\d+))",
                r"(';|\";\s*--|--\s*$|\*/\s*$)",
                r"(\b(EXEC|EXECUTE|SP_|XP_)\s*\()"
            ],
            'xss': [
                r"<script[^>]*>.*?</script>",
                r"javascript\s*:",
                r"on(click|load|error|focus|blur)\s*=",
                r"<iframe[^>]*src\s*="
            ],
            'path_traversal': [
                r"\.\./.*\.\./",
                r"\.\.\\.*\.\.\\",
                r"%2e%2e%2f.*%2e%2e%2f",
                r"%2e%2e%5c.*%2e%2e%5c"
            ],
            'command_injection': [
                r"(\b(cat|ls|pwd|whoami|id|uname)\s+[/\w])",
                r"(\||\|\||&&|;)\s*(cat|ls|rm|mv|cp|chmod|chown|curl|wget)",
                r"(\$\(|\$\{|`)[^)}`]*\s*(cat|ls|rm|mv|cp|chmod|chown)"
            ]
        }
        
        # إحصائيات الأمان
        self.security_stats = {
            'total_requests': 0,
            'blocked_requests': 0,
            'suspicious_activities': 0,
            'rate_limit_violations': 0,
            'attack_attempts': defaultdict(int),
            'unique_attackers': set()
        }
        
        # تهيئة التخزين المؤقت المحلي المحسن
        try:
            from utils.local_cache import LocalCache
            self.cache = LocalCache(max_memory_mb=50)  # 50MB للأمان
            logger.info("تم تهيئة نظام التخزين المؤقت المحلي المحسن")
        except ImportError:
            self.cache = {}
            logger.warning("فشل في تحميل نظام التخزين المؤقت المحسن، سيتم استخدام قاموس بسيط")
        
        logger.info("تم تهيئة نظام الأمان المتقدم لـ API")

    async def initialize_security_rules(self):
        """
        تهيئة قواعد الأمان من قاعدة البيانات
        """
        try:
            # تحميل قواعد Rate Limiting المخصصة
            rules_ref = self.db.collection('security_rules').document('rate_limits')
            rules_doc = rules_ref.get()

            if rules_doc.exists:
                custom_rules = rules_doc.to_dict()
                for endpoint, rule_data in custom_rules.items():
                    if isinstance(rule_data, dict):
                        self.rate_limit_rules[endpoint] = RateLimitRule(
                            endpoint=endpoint,
                            max_requests=rule_data.get('max_requests', 10),
                            time_window=rule_data.get('time_window', 60),
                            user_specific=rule_data.get('user_specific', True),
                            ip_specific=rule_data.get('ip_specific', True)
                        )

            # تحميل قوائم IP المحظورة والمسموحة
            ip_lists_ref = self.db.collection('security_rules').document('ip_lists')
            ip_lists_doc = ip_lists_ref.get()

            if ip_lists_doc.exists:
                ip_data = ip_lists_doc.to_dict()
                self.ip_whitelist.update(ip_data.get('whitelist', []))
                self.ip_blacklist.update(ip_data.get('blacklist', []))

            # تحميل أنماط الهجمات المخصصة
            patterns_ref = self.db.collection('security_rules').document('attack_patterns')
            patterns_doc = patterns_ref.get()

            if patterns_doc.exists:
                custom_patterns = patterns_doc.to_dict()
                for attack_type, patterns in custom_patterns.items():
                    if isinstance(patterns, list):
                        self.attack_patterns[attack_type] = patterns

            logger.info("✅ تم تهيئة قواعد الأمان بنجاح")

        except Exception as e:
            logger.error(f"خطأ في تهيئة قواعد الأمان: {str(e)}")
            logger.info("سيتم استخدام القواعد الافتراضية")

    def _setup_default_rate_limits(self):
        """إعداد قواعد Rate Limiting الافتراضية"""
        default_rules = [
            RateLimitRule("/api/analyze", 10, 60, True, True),  # 10 طلبات في الدقيقة
            RateLimitRule("/api/alerts", 5, 60, True, True),    # 5 طلبات في الدقيقة
            RateLimitRule("/api/subscription", 3, 300, True, True),  # 3 طلبات في 5 دقائق
            RateLimitRule("/api/payment", 2, 600, True, True),  # 2 طلبات في 10 دقائق
            RateLimitRule("/api/login", 5, 300, False, True),   # 5 محاولات تسجيل دخول في 5 دقائق
            RateLimitRule("*", 100, 60, False, True)            # حد عام: 100 طلب في الدقيقة لكل IP
        ]
        
        for rule in default_rules:
            self.rate_limit_rules[rule.endpoint] = rule
    
    async def validate_request(self, 
                             endpoint: str, 
                             user_id: str = None, 
                             ip_address: str = None,
                             headers: Dict[str, str] = None,
                             data: Dict[str, Any] = None) -> Tuple[bool, str, Dict[str, Any]]:
        """
        التحقق من صحة وأمان الطلب
        
        Args:
            endpoint: نقطة النهاية
            user_id: معرف المستخدم
            ip_address: عنوان IP
            headers: رؤوس الطلب
            data: بيانات الطلب
            
        Returns:
            tuple: (مسموح, سبب الرفض, معلومات إضافية)
        """
        try:
            self.security_stats['total_requests'] += 1
            
            # 1. فحص IP المحظور
            if await self._is_ip_blocked(ip_address):
                await self._log_security_event(
                    "blocked_ip_access", user_id, ip_address, endpoint,
                    "high", {"reason": "IP في القائمة المحظورة"}
                )
                return False, "IP محظور", {"block_duration": "permanent"}
            
            # 2. فحص المستخدم المحظور
            if await self._is_user_blocked(user_id):
                await self._log_security_event(
                    "blocked_user_access", user_id, ip_address, endpoint,
                    "high", {"reason": "مستخدم محظور"}
                )
                return False, "مستخدم محظور", {"block_duration": "permanent"}
            
            # 3. فحص Rate Limiting
            rate_limit_result = await self._check_rate_limit(endpoint, user_id, ip_address)
            if not rate_limit_result[0]:
                await self._log_security_event(
                    "rate_limit_violation", user_id, ip_address, endpoint,
                    "medium", {"reason": rate_limit_result[1]}
                )
                self.security_stats['rate_limit_violations'] += 1
                return False, rate_limit_result[1], rate_limit_result[2]
            
            # 4. فحص أنماط الهجمات (مع استثناءات للبيانات المشروعة)
            # تخطي فحص الهجمات لنقاط النهاية الآمنة في Telegram
            safe_endpoints = ['button_click', 'telegram_callback', 'menu_navigation']
            if endpoint not in safe_endpoints:
                attack_check = await self._detect_attack_patterns(data, headers)
                if attack_check[0]:
                    await self._log_security_event(
                        "attack_attempt", user_id, ip_address, endpoint,
                        "critical", {"attack_type": attack_check[1], "details": attack_check[2]}
                    )
                    self.security_stats['attack_attempts'][attack_check[1]] += 1
                    self.security_stats['unique_attackers'].add(ip_address)

                    # حظر مؤقت للمهاجم
                    await self._temporary_block_ip(ip_address, minutes=30)
                    return False, f"محاولة هجوم مكتشفة: {attack_check[1]}", {"attack_type": attack_check[1]}
            
            # 5. فحص صحة البيانات
            data_validation = await self._validate_data_integrity(data)
            if not data_validation[0]:
                await self._log_security_event(
                    "data_validation_failure", user_id, ip_address, endpoint,
                    "medium", {"reason": data_validation[1]}
                )
                return False, data_validation[1], {}
            
            # 6. فحص الرؤوس المشبوهة
            headers_check = await self._validate_headers(headers)
            if not headers_check[0]:
                await self._log_security_event(
                    "suspicious_headers", user_id, ip_address, endpoint,
                    "low", {"reason": headers_check[1]}
                )
                # لا نحظر بناءً على الرؤوس فقط، لكن نسجل
            
            # الطلب آمن
            return True, "طلب آمن", {}
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من أمان الطلب: {str(e)}")
            return False, "خطأ في التحقق من الأمان", {"error": str(e)}
    
    async def _is_ip_blocked(self, ip_address: str) -> bool:
        """فحص ما إذا كان IP محظور"""
        if not ip_address:
            return False
        
        # فحص القائمة السوداء
        if ip_address in self.ip_blacklist:
            return True
        
        # فحص الحظر المؤقت
        if ip_address in self.blocked_ips:
            if datetime.now() < self.blocked_ips[ip_address]:
                return True
            else:
                # انتهت مدة الحظر
                del self.blocked_ips[ip_address]
        
        # فحص قاعدة البيانات للحظر الدائم
        try:
            blocked_ip_ref = self.db.collection('blocked_ips').document(ip_address)
            blocked_ip_doc = blocked_ip_ref.get()
            
            if blocked_ip_doc.exists:
                block_data = blocked_ip_doc.to_dict()
                if block_data.get('permanent', False):
                    self.ip_blacklist.add(ip_address)
                    return True
                
                # فحص الحظر المؤقت من قاعدة البيانات
                expires_at = block_data.get('expires_at')
                if expires_at and datetime.fromisoformat(expires_at) > datetime.now():
                    return True
        except Exception as e:
            logger.warning(f"خطأ في فحص IP المحظور: {str(e)}")
        
        return False
    
    async def _is_user_blocked(self, user_id: str) -> bool:
        """فحص ما إذا كان المستخدم محظور"""
        if not user_id:
            return False
        
        # فحص الحظر المؤقت المحلي
        if user_id in self.blocked_users:
            if datetime.now() < self.blocked_users[user_id]:
                return True
            else:
                del self.blocked_users[user_id]
        
        # فحص قاعدة البيانات
        try:
            banned_user_ref = self.db.collection('banned_users').document(user_id)
            banned_user_doc = banned_user_ref.get()
            
            if banned_user_doc.exists:
                ban_data = banned_user_doc.to_dict()
                if ban_data.get('is_banned', False):
                    return True
        except Exception as e:
            logger.warning(f"خطأ في فحص المستخدم المحظور: {str(e)}")
        
        return False
    
    async def _check_rate_limit(self, endpoint: str, user_id: str, ip_address: str) -> Tuple[bool, str, Dict]:
        """فحص حدود المعدل"""
        current_time = time.time()
        
        # العثور على القاعدة المناسبة
        rule = self.rate_limit_rules.get(endpoint) or self.rate_limit_rules.get("*")
        if not rule:
            return True, "", {}
        
        # إنشاء مفاتيح التتبع
        keys = []
        if rule.user_specific and user_id:
            keys.append(f"user:{user_id}:{endpoint}")
        if rule.ip_specific and ip_address:
            keys.append(f"ip:{ip_address}:{endpoint}")
        
        if not keys:
            keys.append(f"global:{endpoint}")
        
        # فحص كل مفتاح
        for key in keys:
            if key not in self.request_counts:
                self.request_counts[key] = deque()
            
            request_times = self.request_counts[key]
            
            # إزالة الطلبات القديمة
            cutoff_time = current_time - rule.time_window
            while request_times and request_times[0] < cutoff_time:
                request_times.popleft()
            
            # فحص الحد الأقصى
            if len(request_times) >= rule.max_requests:
                remaining_time = int(request_times[0] + rule.time_window - current_time)
                return False, f"تم تجاوز حد المعدل. حاول مرة أخرى خلال {remaining_time} ثانية", {
                    "retry_after": remaining_time,
                    "limit": rule.max_requests,
                    "window": rule.time_window
                }
            
            # إضافة الطلب الحالي
            request_times.append(current_time)
        
        return True, "", {}
    
    async def _detect_attack_patterns(self, data: Dict[str, Any], headers: Dict[str, str]) -> Tuple[bool, str, Dict]:
        """كشف أنماط الهجمات"""
        if not data and not headers:
            return False, "", {}

        # تحويل البيانات إلى نص للفحص
        text_to_check = ""
        if data:
            text_to_check += json.dumps(data, ensure_ascii=False)
        if headers:
            text_to_check += " ".join(headers.values())

        text_to_check = text_to_check.lower()

        # استثناءات خاصة لبيانات Telegram callback_data المشروعة
        telegram_safe_patterns = [
            'enhanced_analysis_menu',
            'analysis_type_settings',
            'trading_style_',
            'enhanced_analyze',
            'start_enhanced_analysis',
            'main_menu',
            'back_to_main',
            'compare_analysis_types',
            'show_analysis_type_settings'
        ]

        # فحص إذا كانت البيانات تحتوي على أنماط Telegram المشروعة فقط
        if data and 'callback_data' in data:
            callback_data = data.get('callback_data', '').lower()
            if any(safe_pattern in callback_data for safe_pattern in telegram_safe_patterns):
                # تخطي فحص الهجمات للبيانات المشروعة
                return False, "", {}

        # فحص كل نوع هجوم
        for attack_type, patterns in self.attack_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text_to_check, re.IGNORECASE):
                    return True, attack_type, {
                        "pattern": pattern,
                        "matched_text": text_to_check[:200]  # أول 200 حرف فقط
                    }

        return False, "", {}
    
    async def _validate_data_integrity(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """التحقق من سلامة البيانات"""
        if not data:
            return True, ""
        
        try:
            # فحص حجم البيانات
            data_size = len(json.dumps(data))
            if data_size > 1024 * 1024:  # 1MB
                return False, "حجم البيانات كبير جداً"
            
            # فحص عمق التداخل
            def check_depth(obj, current_depth=0, max_depth=10):
                if current_depth > max_depth:
                    return False
                if isinstance(obj, dict):
                    return all(check_depth(v, current_depth + 1, max_depth) for v in obj.values())
                elif isinstance(obj, list):
                    return all(check_depth(item, current_depth + 1, max_depth) for item in obj)
                return True
            
            if not check_depth(data):
                return False, "تداخل البيا��ات عميق جداً"
            
            # فحص الأحرف المشبوهة (مع استثناءات للبيانات المشروعة)
            suspicious_chars = ['<', '>', '&', ';', '|', '`', '$']
            data_str = json.dumps(data)

            # استثناءات للبيانات المشروعة
            safe_contexts = [
                'telegram_callback',
                'button_click',
                'menu_navigation',
                'analysis_result',
                'user_message'
            ]

            # فحص السياق لتحديد ما إذا كانت الأحرف مشروعة
            is_safe_context = any(context in data_str.lower() for context in safe_contexts)

            for char in suspicious_chars:
                if char in data_str and not is_safe_context:
                    # تسجيل تحذير فقط للسياقات غير الآمنة
                    logger.warning(f"حرف مشبوه في البيانات: {char}")
                    break  # تسجيل تحذير واحد فقط لتجنب الإزعاج
            
            return True, ""
            
        except Exception as e:
            return False, f"خطأ في التحقق من البيانات: {str(e)}"
    
    async def _validate_headers(self, headers: Dict[str, str]) -> Tuple[bool, str]:
        """التحقق من صحة الرؤوس"""
        if not headers:
            return True, ""
        
        suspicious_headers = [
            'x-forwarded-for',
            'x-real-ip',
            'x-cluster-client-ip',
            'x-forwarded',
            'forwarded-for',
            'forwarded'
        ]
        
        # فحص الرؤوس المشبوهة
        for header_name, header_value in headers.items():
            header_name_lower = header_name.lower()
            
            # فحص محاولات تزوير IP
            if header_name_lower in suspicious_headers:
                if ',' in header_value or ';' in header_value:
                    return False, f"رأس مشبوه: {header_name}"
            
            # فحص User-Agent المشبوه
            if header_name_lower == 'user-agent':
                suspicious_agents = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget']
                if any(agent in header_value.lower() for agent in suspicious_agents):
                    return False, "User-Agent مشبوه"
        
        return True, ""
    
    async def _temporary_block_ip(self, ip_address: str, minutes: int = 30):
        """حظر IP مؤقتاً"""
        if not ip_address:
            return
        
        block_until = datetime.now() + timedelta(minutes=minutes)
        self.blocked_ips[ip_address] = block_until
        
        # حفظ في قاعدة البيانات
        try:
            blocked_ip_ref = self.db.collection('blocked_ips').document(ip_address)
            blocked_ip_ref.set({
                'ip_address': ip_address,
                'blocked_at': datetime.now().isoformat(),
                'expires_at': block_until.isoformat(),
                'reason': 'محاولة هجوم مكتشفة',
                'permanent': False
            })
        except Exception as e:
            logger.error(f"خطأ ف�� حفظ IP المحظور: {str(e)}")
        
        logger.warning(f"تم حظر IP {ip_address} مؤقتاً لمدة {minutes} دقيقة")
    
    async def _log_security_event(self, event_type: str, user_id: str, ip_address: str, 
                                 endpoint: str, severity: str, details: Dict[str, Any]):
        """تسجيل حدث أمني"""
        try:
            event = SecurityEvent(
                event_type=event_type,
                user_id=user_id or "unknown",
                ip_address=ip_address or "unknown",
                endpoint=endpoint,
                timestamp=datetime.now(),
                severity=severity,
                details=details,
                blocked=severity in ['high', 'critical']
            )
            
            # حفظ في قاعدة البيانات
            security_events_ref = self.db.collection('security_events').document()
            security_events_ref.set(asdict(event))
            
            # تحديث الإحصائيات
            if severity in ['medium', 'high', 'critical']:
                self.security_stats['suspicious_activities'] += 1
            
            if event.blocked:
                self.security_stats['blocked_requests'] += 1
            
            # تسجيل في السجل
            log_message = f"حدث أمني: {event_type} | المستخدم: {user_id} | IP: {ip_address} | الخطورة: {severity}"
            if severity == 'critical':
                logger.critical(log_message)
            elif severity == 'high':
                logger.error(log_message)
            elif severity == 'medium':
                logger.warning(log_message)
            else:
                logger.info(log_message)
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الحدث الأمني: {str(e)}")
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """تشفير البيانات الحساسة"""
        try:
            encrypted_data = self.cipher.encrypt(data.encode())
            return base64.b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"خطأ في تشفير البيانات: {str(e)}")
            return data
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات الحساسة"""
        try:
            decoded_data = base64.b64decode(encrypted_data.encode())
            decrypted_data = self.cipher.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"خطأ في فك تشفير البيانات: {str(e)}")
            return encrypted_data
    
    def generate_secure_token(self, user_id: str, expires_in_hours: int = 24) -> str:
        """إنشاء رمز آمن للمصادقة"""
        try:
            payload = {
                'user_id': user_id,
                'exp': datetime.utcnow() + timedelta(hours=expires_in_hours),
                'iat': datetime.utcnow(),
                'jti': secrets.token_hex(16)  # معرف فريد للرمز
            }
            
            token = jwt.encode(payload, self.encryption_key, algorithm='HS256')
            return token
        except Exception as e:
            logger.error(f"خطأ في إنشاء الرمز الآمن: {str(e)}")
            return None
    
    def verify_secure_token(self, token: str) -> Tuple[bool, Dict[str, Any]]:
        """التحقق من صحة الرمز الآمن"""
        try:
            payload = jwt.decode(token, self.encryption_key, algorithms=['HS256'])
            return True, payload
        except jwt.ExpiredSignatureError:
            return False, {"error": "انتهت صلاحية الرمز"}
        except jwt.InvalidTokenError:
            return False, {"error": "رمز غير صالح"}
        except Exception as e:
            logger.error(f"خطأ في التحقق من الرمز: {str(e)}")
            return False, {"error": str(e)}
    
    def get_security_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الأمان"""
        total_requests = self.security_stats['total_requests']
        blocked_requests = self.security_stats['blocked_requests']
        
        return {
            "total_requests": total_requests,
            "blocked_requests": blocked_requests,
            "block_rate": f"{(blocked_requests / max(total_requests, 1)) * 100:.2f}%",
            "suspicious_activities": self.security_stats['suspicious_activities'],
            "rate_limit_violations": self.security_stats['rate_limit_violations'],
            "attack_attempts": dict(self.security_stats['attack_attempts']),
            "unique_attackers": len(self.security_stats['unique_attackers']),
            "blocked_ips_count": len(self.blocked_ips),
            "blocked_users_count": len(self.blocked_users),
            "rate_limit_rules": len(self.rate_limit_rules)
        }
    
    async def cleanup_expired_blocks(self):
        """تنظيف الحظر المنتهي الصلاحية"""
        current_time = datetime.now()
        
        # تنظيف IP المحظورة مؤقتاً
        expired_ips = [ip for ip, expires_at in self.blocked_ips.items() if current_time >= expires_at]
        for ip in expired_ips:
            del self.blocked_ips[ip]
        
        # تنظيف المستخدمين المحظورين مؤقتاً
        expired_users = [user for user, expires_at in self.blocked_users.items() if current_time >= expires_at]
        for user in expired_users:
            del self.blocked_users[user]
        
        if expired_ips or expired_users:
            logger.info(f"تم تنظيف {len(expired_ips)} IP و {len(expired_users)} مستخدم من الحظر المنتهي الصلاحية")


# دالة decorator للحماية التلقائية
def secure_endpoint(rate_limit: RateLimitRule = None):
    """Decorator لحماية نقاط API تلقائياً"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # استخراج معلومات الطلب
            # هذا يحتاج تخصيص حسب إطار العمل المستخدم
            endpoint = func.__name__
            user_id = kwargs.get('user_id')
            ip_address = kwargs.get('ip_address')
            headers = kwargs.get('headers', {})
            data = kwargs.get('data', {})
            
            # إن��اء مثيل الأمان (يجب تمريره من التطبيق الرئيسي)
            security_manager = kwargs.get('security_manager')
            if not security_manager:
                logger.warning("لم يتم تمرير مدير الأمان للـ decorator")
                return await func(*args, **kwargs)
            
            # التحقق من الأمان
            is_valid, reason, details = await security_manager.validate_request(
                endpoint, user_id, ip_address, headers, data
            )
            
            if not is_valid:
                return {"error": reason, "details": details}, 403
            
            # تنفيذ الدالة الأصلية
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


# إنشاء مثيل عام للاستخدام
def create_security_manager(db: firestore.Client, encryption_key: str = None) -> AdvancedAPISecurityManager:
    """إنشاء مدير أمان متقدم"""
    return AdvancedAPISecurityManager(db, encryption_key)