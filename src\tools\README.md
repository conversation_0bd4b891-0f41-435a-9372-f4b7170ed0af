# أدوات نظام الأخبار الذكي

هذا المجلد يحتوي على أدوات مساعدة لتشخيص وإصلاح واختبار نظام الأخبار الذكي في البوت.

## الأدوات المتوفرة

### 1. أداة التشخيص (`news_system_diagnostics.py`)

تقوم بفحص شامل لنظام الأخبار الذكي وتشخيص المشاكل.

**الاستخدام:**
```bash
cd src/tools
python news_system_diagnostics.py
```

**ما تفحصه:**
- حالة مكونات النظام (نظام الأخبار، الإشعارات، الجدولة، التخزين المؤقت)
- إعدادات اللغة للمستخدمين
- نظام الإشعارات والتنبيهات
- مفاتيح API (Gemini, Binance)
- حالة قاعدة البيانات

**النتائج:**
- تقرير مفصل عن حالة النظام
- قائمة بالمشاكل المكتشفة
- توصيات للإصلاح

### 2. أداة الاختبار (`test_news_system.py`)

تختبر جميع وظائف نظام الأخبار الذكي.

**الاستخدام:**
```bash
cd src/tools
python test_news_system.py
```

**الاختبارات المشمولة:**
- تهيئة النظام
- تحديد اللغة للمستخدمين
- تحليل الأخبار بالذكاء الاصطناعي
- نظام الترجمة
- إرسال الإشعارات
- النظام التلقائي

**النتائج:**
- تقرير مفصل عن نتائج الاختبارات
- إحصائيات النجاح والفشل
- توصيات للتحسين

### 3. أداة الإصلاح (`fix_news_system.py`)

تصلح المشاكل الشائعة في نظام الأخبار تلقائياً.

**الاستخدام:**
```bash
cd src/tools
python fix_news_system.py
```

**الإصلاحات المشمولة:**
- تهيئة الأنظمة المفقودة
- إصلاح إعدادات اللغة للمستخدمين
- إنشاء تفضيلات الإشعارات المفقودة
- تنظيف البيانات القديمة
- إعادة تشغيل النظام التلقائي

**النتائج:**
- تقرير مفصل عن الإصلاحات المطبقة
- إحصائيات النجاح والفشل

### 4. أداة إصلاح الترميز (`fix_encoding_issues.py`)

تبحث عن وتصلح مشاكل الترميز في جميع ملفات البوت.

**الاستخدام:**
```bash
cd src/tools
python fix_encoding_issues.py
```

**ما تصلحه:**
- الرموز التعبيرية المعطوبة (��)
- الكلمات العربية المعطوبة
- مشاكل ترميز UTF-8

### 5. إصلاح سريع لرسائل اليوم المجاني (`quick_fix_free_day_encoding.py`)

إصلاح سريع ومركز لمشاكل ترميز رسائل اليوم المجاني.

**الاستخدام:**
```bash
cd src/tools
python quick_fix_free_day_encoding.py
```

**يصلح:**
- رسالة "انتهى اليوم المجاني المؤقت"
- الرموز التعبيرية المعطوبة في الرسائل
- الكلمات العربية المعطوبة

### 6. اختبار رسائل اليوم المجاني (`test_free_day_messages.py`)

يختبر عرض رسائل اليوم المجاني للتأكد من عدم وجود مشاكل ترميز.

**الاستخدام:**
```bash
cd src/tools
python test_free_day_messages.py
```

## سير العمل الموصى به

### عند مواجهة مشاكل في نظام الأخبار:

1. **التشخيص أولاً:**
   ```bash
   python news_system_diagnostics.py
   ```
   
2. **الإصلاح التلقائي:**
   ```bash
   python fix_news_system.py
   ```
   
3. **اختبار النظام:**
   ```bash
   python test_news_system.py
   ```

### للصيانة الدورية:

1. تشغيل التشخيص أسبوعياً
2. تشغيل الإصلاح عند الحاجة
3. تشغيل الاختبار بعد أي تحديثات

## المشاكل الشائعة وحلولها

### 1. لا يتم إرسال الأخبار للمستخدمين

**الأسباب المحتملة:**
- نظام الإشعارات غير مهيأ
- مفتاح Gemini API مفقود
- إعدادات اللغة غير صحيحة

**الحل:**
```bash
python fix_news_system.py
```

### 2. الأخبار لا تُرسل باللغة الصحيحة

**الأسباب المحتملة:**
- إعدادات اللغة للمستخدمين غير محفوظة
- نظام الترجمة لا يعمل

**الحل:**
1. تشغيل أداة الإصلاح لإصلاح إعدادات اللغة
2. فحص نظام الترجمة بأداة الاختبار

### 3. النظام التلقائي لا يعمل

**الأسباب المحتملة:**
- النظام غير مهيأ
- مشاكل في الجدولة
- مفاتيح API مفقودة

**الحل:**
```bash
python fix_news_system.py
```

### 4. تحليل الأخبار بالذكاء الاصطناعي لا يعمل

**الأسباب المحتملة:**
- مفتاح Gemini API غير صحيح أو مفقود
- مشاكل في الاتصال بالإنترنت
- حدود API مستنفدة

**الحل:**
1. فحص مفتاح Gemini API
2. التأكد من الاتصال بالإنترنت
3. فحص حدود API

### 5. رسائل اليوم المجاني تظهر رموز معطوبة (��)

**الأسباب المحتملة:**
- مشاكل في ترميز UTF-8
- الرموز التعبيرية معطوبة في ملفات الترجمة
- مشاكل في حفظ الملفات

**الحل:**
```bash
python quick_fix_free_day_encoding.py
```

أو للإصلاح الشامل:
```bash
python fix_encoding_issues.py
```

## متطلبات التشغيل

- Python 3.8+
- جميع مكتبات البوت مثبتة
- اتصال بقاعدة البيانات
- مفتاح Gemini API (للاختبار الكامل)

## ملاحظات مهمة

1. **الأمان:** هذه الأدوات تتعامل مع قاعدة البيانات مباشرة، تأكد من عمل نسخة احتياطية قبل التشغيل
2. **الاختبار:** أداة الاختبار قد ترسل رسالة اختبار للمطور
3. **الإصلاح:** أداة الإصلاح تقوم بتعديلات على قاعدة البيانات
4. **التسجيل:** جميع الأدوات تسجل أنشطتها في ملفات السجل

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
```
تأكد من:
- ملف .env يحتوي على إعدادات قاعدة البيانات الصحيحة
- الاتصال بالإنترنت متوفر
- صلاحيات الوصول لقاعدة البيانات صحيحة
```

### خطأ في استيراد الوحدات
```
تأكد من:
- تشغيل الأدوات من مجلد src/tools
- جميع المكتبات مثبتة
- مسار Python صحيح
```

### خطأ في مفاتيح API
```
تأكد من:
- مفتاح Gemini API صحيح ومفعل
- المفتاح محفوظ في متغيرات البيئة أو قاعدة البيانات
- حدود API لم تستنفد
```

## الدعم

في حالة مواجهة مشاكل:
1. راجع ملفات السجل
2. تشغيل أداة التشخيص للحصول على تفاصيل المشكلة
3. استخدام أداة الإصلاح للحلول التلقائية
4. التواصل مع فريق التطوير مع تفاصيل المشكلة
