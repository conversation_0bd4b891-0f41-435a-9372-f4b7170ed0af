"""
دوال تهيئة النظام - النسخة المحدثة
"""

import logging
import os
from services.system_settings import system_settings
from config import SystemConfig

# استيراد الدوال الموسعة من الملف الجديد
from .system_initialization_extended import (
    initialize_firestore,
    check_firestore_connection,
    initialize_analysis_modules as initialize_analysis_modules_extended,
    migrate_config_to_database,
    initialize_system_extended
)

logger = logging.getLogger(__name__)

async def initialize_system():
    """تهيئة النظام الأساسي - النسخة المحدثة"""
    try:
        logger.info("🔄 جاري تهيئة النظام...")

        # تهيئة الإعدادات الأساسية
        await _initialize_basic_settings()

        # نقل الإعدادات من ملف التكوين إلى قاعدة البيانات
        await migrate_config_to_database(system_settings)

        # تهيئة المكونات الأساسية
        await _initialize_core_components()

        logger.info("✅ تم تهيئة النظام بنجاح")
        return True

    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة النظام: {str(e)}")
        return False

async def _initialize_basic_settings():
    """تهيئة الإعدادات الأساسية"""
    try:
        # التحقق من وجود الإعدادات الأساسية
        required_settings = [
            "BOT_TOKEN",
            "DEVELOPER_ID"
        ]
        
        for setting in required_settings:
            value = system_settings.get(setting, sensitive=True)
            if not value:
                # محاولة الحصول على القيمة من متغيرات البيئة
                env_value = os.environ.get(setting)
                if env_value:
                    system_settings.set(setting, env_value, sensitive=True)
                    logger.info(f"✅ تم تعيين {setting} من متغيرات البيئة")
                else:
                    logger.warning(f"⚠️ لم يتم العثور على {setting}")
        
        logger.info("✅ تم تهيئة الإعدادات الأساسية")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة الإعدادات الأساسية: {str(e)}")
        raise

async def _initialize_core_components():
    """تهيئة المكونات الأساسية"""
    try:
        # تهيئة خدمات التحليل
        await _initialize_analysis_modules()

        logger.info("✅ تم تهيئة المكونات الأساسية")

    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة المكونات الأساسية: {str(e)}")
        raise

async def _initialize_analysis_modules():
    """تهيئة وحدات التحليل - النسخة البسيطة للتوافق مع الكود القديم"""
    try:
        # تهيئة خدمة الإدارة
        from services.admin_service import initialize_admin_service
        initialize_admin_service(None, SystemConfig)  # سيتم تمرير db لاحقاً

        # ملاحظة: تم تعطيل تهيئة الخدمات التي تحتاج معاملات خاصة هنا
        # مثل alert_service, user_management, backup_service, api_management
        # لأنها تحتاج معاملات خاصة وسيتم تهيئتها في النسخة الموسعة

        logger.info("✅ تم تهيئة وحدات التحليل (النسخة البسيطة)")

    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة وحدات التحليل: {str(e)}")
        raise

def initialize_analysis_modules(
    subscription_system=None,
    api_manager=None,
    enhanced_analyzer=None,
    db=None,
    user_states=None,
    get_text=None,
    create_analysis_text=None,
    load_user_settings=None,
    save_user_settings=None,
    specialized_handlers=None,
    delete_message_after_delay=None,
    binance_manager=None,
    ca=None,
    show_main_menu=None,
    show_api_instructions=None,
    analyze_symbol=None
):
    """تهيئة وحدات التحليل (نسخة متزامنة محدثة)"""
    try:
        if all([subscription_system, api_manager, enhanced_analyzer, db, user_states, get_text]):
            # استخدام النسخة الموسعة مع جميع المعاملات
            return initialize_analysis_modules_extended(
                subscription_system,
                api_manager,
                enhanced_analyzer,
                db,
                user_states,
                get_text,
                create_analysis_text,
                load_user_settings,
                save_user_settings,
                specialized_handlers,
                delete_message_after_delay,
                binance_manager,
                ca,
                show_main_menu,
                show_api_instructions,
                analyze_symbol
            )
        else:
            # النسخة البسيطة للتوافق مع الكود القديم
            from analysis.integration_wrapper import EnhancedAnalysisWrapper
            # إنشاء كائن محلي بدلاً من استخدام global
            enhanced_analyzer_local = EnhancedAnalysisWrapper()

            logger.info("✅ تم تهيئة وحدات التحليل (النسخة البسيطة)")
            return True

    except Exception as e:
        logger.error(f"❌ خطأ في تهيئة وحدات التحليل: {str(e)}")
        return False

# إعادة تصدير الدوال المهمة للاستخدام الخارجي
__all__ = [
    'initialize_system',
    'initialize_analysis_modules',
    'initialize_firestore',
    'check_firestore_connection',
    'migrate_config_to_database',
    'initialize_system_extended'
]
