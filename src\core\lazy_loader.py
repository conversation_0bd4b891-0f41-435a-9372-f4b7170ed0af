"""
⚡ نظام التحميل الذكي (<PERSON><PERSON> Loader) - Trading Telegram Bot
===========================================================

نظام متقدم للتحميل الذكي للوحدات حسب الحاجة.
يحسن الأداء ويقلل استهلاك الذاكرة عند بدء التشغيل.

الميزات:
- تحميل الوحدات عند أول استخدام
- تخزين مؤقت ذكي للوحدات المحملة
- إدارة دورة حياة الوحدات
- مراقبة الاستخدام والأداء
- تنظيف تلقائي للذاكرة

المؤلف: Augment Agent
الإصدار: 1.0.0
التاريخ: ديسمبر 2024
"""

import logging
import time
import weakref
import threading
import gc
from typing import Any, Dict, Optional, Callable, Union
from functools import wraps
import sys
import importlib

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class LazyModule:
    """
    وحدة تحميل ذكي
    
    تحمل الوحدة فقط عند أول استخدام لها
    """
    
    def __init__(self, module_name: str, import_path: str = None):
        self.module_name = module_name
        self.import_path = import_path or module_name
        self._module = None
        self._loading = False
        self._load_time = None
        self._access_count = 0
        self._last_access = None
        self.lock = threading.RLock()
    
    def __getattr__(self, name: str) -> Any:
        """تحميل الوحدة عند أول وصول لأي خاصية"""
        with self.lock:
            if self._module is None and not self._loading:
                self._load_module()
            
            self._access_count += 1
            self._last_access = time.time()
            
            if self._module is not None:
                return getattr(self._module, name)
            else:
                raise AttributeError(f"الوحدة {self.module_name} غير متاحة أو فشل تحميلها")
    
    def _load_module(self):
        """تحميل الوحدة الفعلي"""
        self._loading = True
        start_time = time.time()
        
        try:
            logger.debug(f"🔄 تحميل الوحدة: {self.module_name}")
            
            # محاولة تحميل الوحدة
            if self.import_path in sys.modules:
                self._module = sys.modules[self.import_path]
            else:
                self._module = importlib.import_module(self.import_path)
            
            self._load_time = time.time() - start_time
            logger.debug(f"✅ تم تحميل {self.module_name} في {self._load_time:.3f} ثانية")
            
        except Exception as e:
            logger.error(f"❌ فشل في تحميل الوحدة {self.module_name}: {e}")
            self._module = None
        finally:
            self._loading = False
    
    @property
    def is_loaded(self) -> bool:
        """التحقق من تحميل الوحدة"""
        return self._module is not None
    
    @property
    def load_time(self) -> Optional[float]:
        """وقت تحميل الوحدة"""
        return self._load_time
    
    @property
    def access_count(self) -> int:
        """عدد مرات الوصول للوحدة"""
        return self._access_count
    
    @property
    def last_access(self) -> Optional[float]:
        """آخر وقت وصول للوحدة"""
        return self._last_access

class LazyLoader:
    """
    مدير التحميل الذكي
    
    يدير مجموعة من الوحدات ويحملها حسب الحاجة
    """
    
    def __init__(self):
        self.modules: Dict[str, LazyModule] = {}
        self.stats: Dict[str, Any] = {}
        self.lock = threading.RLock()
        self._cleanup_interval = 300  # 5 دقائق
        self._last_cleanup = time.time()
    
    def register_module(self, name: str, import_path: str = None) -> LazyModule:
        """
        تسجيل وحدة للتحميل الذكي
        
        Args:
            name: اسم الوحدة
            import_path: مسار الاستيراد (اختياري)
            
        Returns:
            LazyModule: كائن الوحدة الذكية
        """
        with self.lock:
            lazy_module = LazyModule(name, import_path)
            self.modules[name] = lazy_module
            logger.debug(f"📝 تم تسجيل الوحدة للتحميل الذكي: {name}")
            return lazy_module
    
    def get_module(self, name: str) -> Optional[LazyModule]:
        """
        الحصول على وحدة مسجلة
        
        Args:
            name: اسم الوحدة
            
        Returns:
            LazyModule: الوحدة أو None
        """
        return self.modules.get(name)
    
    def preload_critical(self, critical_modules: list):
        """
        تحميل مسبق للوحدات الحرجة
        
        Args:
            critical_modules: قائمة بأسماء الوحدات الحرجة
        """
        logger.info(f"🚀 بدء التحميل المسبق لـ {len(critical_modules)} وحدة حرجة...")
        
        loaded_count = 0
        start_time = time.time()
        
        for module_name in critical_modules:
            if module_name in self.modules:
                try:
                    # الوصول لأي خاصية لتحفيز التحميل
                    lazy_module = self.modules[module_name]
                    _ = lazy_module.__dict__  # تحفيز التحميل
                    if lazy_module.is_loaded:
                        loaded_count += 1
                except Exception as e:
                    logger.warning(f"⚠️ فشل في التحميل المسبق لـ {module_name}: {e}")
        
        total_time = time.time() - start_time
        logger.info(f"✅ تم التحميل المسبق لـ {loaded_count}/{len(critical_modules)} وحدة في {total_time:.2f} ثانية")
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        إحصائيات الاستخدام
        
        Returns:
            dict: إحصائيات مفصلة
        """
        with self.lock:
            total_modules = len(self.modules)
            loaded_modules = sum(1 for module in self.modules.values() if module.is_loaded)
            total_accesses = sum(module.access_count for module in self.modules.values())
            
            # أكثر الوحدات استخداماً
            most_used = sorted(
                self.modules.items(),
                key=lambda x: x[1].access_count,
                reverse=True
            )[:5]
            
            # أسرع الوحدات تحميلاً
            fastest_loading = sorted(
                [(name, module) for name, module in self.modules.items() if module.load_time],
                key=lambda x: x[1].load_time
            )[:5]
            
            return {
                'summary': {
                    'total_modules': total_modules,
                    'loaded_modules': loaded_modules,
                    'loading_percentage': (loaded_modules / total_modules * 100) if total_modules > 0 else 0,
                    'total_accesses': total_accesses
                },
                'most_used_modules': [
                    {
                        'name': name,
                        'access_count': module.access_count,
                        'last_access': module.last_access
                    }
                    for name, module in most_used
                ],
                'fastest_loading': [
                    {
                        'name': name,
                        'load_time': module.load_time
                    }
                    for name, module in fastest_loading
                ],
                'memory_info': {
                    'loaded_modules_count': loaded_modules,
                    'cache_size': len([m for m in self.modules.values() if m.is_loaded])
                }
            }
    
    def cleanup_unused(self, max_idle_time: float = 1800):  # 30 دقيقة
        """
        تنظيف الوحدات غير المستخدمة
        
        Args:
            max_idle_time: أقصى وقت خمول بالثواني
        """
        current_time = time.time()
        
        # تحقق من الحاجة للتنظيف
        if current_time - self._last_cleanup < self._cleanup_interval:
            return
        
        with self.lock:
            cleaned_count = 0
            
            for name, module in list(self.modules.items()):
                if (module.is_loaded and 
                    module.last_access and 
                    current_time - module.last_access > max_idle_time):
                    
                    try:
                        # إزالة المرجع للوحدة
                        module._module = None
                        cleaned_count += 1
                        logger.debug(f"🧹 تم تنظيف الوحدة غير المستخدمة: {name}")
                    except Exception as e:
                        logger.warning(f"⚠️ فشل في تنظيف الوحدة {name}: {e}")
            
            if cleaned_count > 0:
                gc.collect()  # تنظيف الذاكرة
                logger.info(f"🧹 تم تنظيف {cleaned_count} وحدة غير مستخدمة")
            
            self._last_cleanup = current_time
    
    def force_reload(self, module_name: str) -> bool:
        """
        إعادة تحميل وحدة بالقوة
        
        Args:
            module_name: اسم الوحدة
            
        Returns:
            bool: نجح إعادة التحميل أم لا
        """
        if module_name not in self.modules:
            return False
        
        with self.lock:
            try:
                module = self.modules[module_name]
                
                # إزالة من sys.modules إذا كانت موجودة
                if module.import_path in sys.modules:
                    del sys.modules[module.import_path]
                
                # إعادة تعيين الوحدة
                module._module = None
                module._load_time = None
                
                # تحميل جديد
                module._load_module()
                
                logger.info(f"🔄 تم إعادة تحميل الوحدة: {module_name}")
                return module.is_loaded
                
            except Exception as e:
                logger.error(f"❌ فشل في إعادة تحميل الوحدة {module_name}: {e}")
                return False
    
    def get_module_info(self, module_name: str) -> Optional[Dict[str, Any]]:
        """
        معلومات مفصلة عن وحدة
        
        Args:
            module_name: اسم الوحدة
            
        Returns:
            dict: معلومات الوحدة
        """
        if module_name not in self.modules:
            return None
        
        module = self.modules[module_name]
        return {
            'name': module.module_name,
            'import_path': module.import_path,
            'is_loaded': module.is_loaded,
            'load_time': module.load_time,
            'access_count': module.access_count,
            'last_access': module.last_access,
            'idle_time': time.time() - module.last_access if module.last_access else None
        }

# إنشاء مثيل عام للتحميل الذكي
lazy_loader = LazyLoader()

def lazy_import(module_name: str, import_path: str = None):
    """
    ديكوريتر للاستيراد الذكي
    
    Args:
        module_name: اسم الوحدة
        import_path: مسار الاستيراد
        
    Returns:
        LazyModule: الوحدة الذكية
    """
    return lazy_loader.register_module(module_name, import_path)

def get_lazy_loader() -> LazyLoader:
    """الحصول على مدير التحميل الذكي العام"""
    return lazy_loader
