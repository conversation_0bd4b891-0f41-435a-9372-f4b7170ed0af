"""
إصلاح مشكلة اليوم المجاني للمستخدمين الجدد

هذا الملف يحل المشكلة التي تحدث عندما:
1. يتم حذف جدول المستخدمين وإعادة إنشاؤه
2. المستخدمون الجدد لا يحصلون على اليوم المجاني بشكل صحيح
3. البوت يخبر المستخدم أنه حصل على هدية يوم مجاني لكن لا يتم تفعيله

الحلول المطبقة:
1. تحسين دالة منح اليوم المجاني للمستخدمين الجدد
2. إضافة تفعيل فوري لليوم المجاني إذا كان اليوم المناسب
3. تحسين التحقق من حالة اليوم المجاني في نظام الاشتراكات
4. إضافة دالة لإصلاح المستخدمين الموجودين
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def fix_free_day_for_existing_users():
    """
    إصلاح مشكلة اليوم المجاني للمستخدمين الموجودين
    """
    try:
        # استيراد المكونات المطلوبة
        from integrations.firebase_init import initialize_firebase
        from services.free_day_system import FreeDaySystem
        from services.subscription_system import SubscriptionSystem
        
        # تهيئة قاعدة البيانات
        db = initialize_firebase()
        if not db:
            logger.error("فشل في تهيئة قاعدة البيانات")
            return False
            
        # تهيئة الأنظمة
        free_day_system = FreeDaySystem(db)
        subscription_system = SubscriptionSystem(db, free_day_system)
        
        logger.info("🔧 بدء إصلاح مشكلة اليوم المجاني للمستخدمين الموجودين...")
        
        # الحصول على جميع المستخدمين
        users_ref = db.collection('users')
        users = users_ref.get()
        
        fixed_count = 0
        total_count = 0
        
        for user_doc in users:
            try:
                user_id = user_doc.id
                user_data = user_doc.to_dict()
                
                # تجاهل وثائق ا��بيانات الوصفية
                if user_id.startswith('_'):
                    continue
                    
                total_count += 1
                
                # التحقق من أن المستخدم غير مشترك
                is_subscribed = subscription_system.is_subscribed_sync(user_id)
                if is_subscribed:
                    logger.info(f"تخطي المستخدم المشترك {user_id}")
                    continue
                
                # التحقق من وجود إعدادات اليوم المجاني
                if 'free_day_of_week' not in user_data:
                    # إضافة إعدادات اليوم المجاني الافتراضية
                    user_doc.reference.update({
                        'free_day_of_week': 0,  # الاثنين
                        'last_free_day_used': None,
                        'is_free_day_active': False
                    })
                    logger.info(f"تم إضافة إعدادات اليوم المجاني للمستخدم {user_id}")
                    fixed_count += 1
                
                # التحقق من إمكانية تفعيل اليوم المجاني اليوم
                if free_day_system.is_today_free_day(user_id):
                    is_active = free_day_system.is_free_day_active(user_id)
                    if not is_active:
                        # تفعيل اليوم المجاني
                        await free_day_system.activate_free_day(user_id)
                        logger.info(f"تم تفعيل اليوم المجاني للمستخدم {user_id}")
                        fixed_count += 1
                
            except Exception as e:
                logger.error(f"خطأ في إصلاح المستخدم {user_doc.id}: {str(e)}")
                continue
        
        logger.info(f"✅ تم إصلاح {fixed_count} مستخدم من أصل {total_count} مستخدم")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إصلاح مشكلة اليوم المجاني: {str(e)}")
        return False

async def grant_welcome_free_day_to_all():
    """
    منح يوم مجاني ترحيبي لجميع المستخدمين غير المشتركين
    """
    try:
        # استيراد المكونات المطلوبة
        from integrations.firebase_init import initialize_firebase
        from services.free_day_system import FreeDaySystem
        from services.subscription_system import SubscriptionSystem
        
        # تهيئة قاعدة البيانات
        db = initialize_firebase()
        if not db:
            logger.error("فشل في تهيئة قاعدة البيانات")
            return False
            
        # تهيئة الأنظمة
        free_day_system = FreeDaySystem(db)
        subscription_system = SubscriptionSystem(db, free_day_system)
        
        logger.info("🎁 بدء منح يوم مجاني ترحيبي لجميع المستخدمين غير المشتركين...")
        
        # الحصول على جميع المستخدمين
        users_ref = db.collection('users')
        users = users_ref.get()
        
        granted_count = 0
        total_count = 0
        skipped_count = 0
        
        for user_doc in users:
            try:
                user_id = user_doc.id
                user_data = user_doc.to_dict()
                
                # تجاهل وثائق البيانات الوصفية
                if user_id.startswith('_'):
                    continue
                    
                total_count += 1
                
                # التحقق من أن المستخدم غير مشترك
                is_subscribed = subscription_system.is_subscribed_sync(user_id)
                if is_subscribed:
                    skipped_count += 1
                    continue
                
                # الت��قق من وجود يوم مجاني نشط بالفعل
                has_active_free_day = free_day_system.has_active_free_day(user_id)
                if has_active_free_day:
                    logger.info(f"المستخدم {user_id} لديه يوم مجاني نشط بالفعل")
                    skipped_count += 1
                    continue
                
                # منح يوم مجاني ترحيبي (24 ساعة)
                if free_day_system.grant_free_day(user_id, 24):
                    logger.info(f"تم منح يوم مجاني ترحيبي للمستخدم {user_id}")
                    granted_count += 1
                else:
                    logger.warning(f"فشل في منح يوم مجاني للمستخدم {user_id}")
                
            except Exception as e:
                logger.error(f"خطأ في منح يوم مجاني للمستخدم {user_doc.id}: {str(e)}")
                continue
        
        logger.info(f"✅ تم منح يوم مجاني لـ {granted_count} مستخدم، تم تخطي {skipped_count} مستخدم، من أصل {total_count} مستخدم")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في منح يوم مجاني ترحيبي: {str(e)}")
        return False

async def test_free_day_system():
    """
    اختبار نظام اليوم المجاني للتأكد من عمله بشكل صحيح
    """
    try:
        # استيراد المكونات المطلوبة
        from integrations.firebase_init import initialize_firebase
        from services.free_day_system import FreeDaySystem
        from services.subscription_system import SubscriptionSystem
        
        # تهيئة قاعدة البيانات
        db = initialize_firebase()
        if not db:
            logger.error("فشل في تهيئة قاعدة البيانات")
            return False
            
        # تهيئة الأنظمة
        free_day_system = FreeDaySystem(db)
        subscription_system = SubscriptionSystem(db, free_day_system)
        
        logger.info("🧪 بدء اختبار نظام اليوم المجاني...")
        
        # اختبار مستخدم وهمي
        test_user_id = "test_user_123456789"
        
        # 1. اختبار الحصول على حالة اليوم المجاني
        status = free_day_system.get_user_free_day_status(test_user_id)
        logger.info(f"حالة اليوم المجاني للمستخدم التجريبي: {status}")
        
        # 2. اختبار منح يوم مجاني
        grant_result = free_day_system.grant_free_day(test_user_id, 1)  # ساعة واحدة للاختبار
        logger.info(f"نتيجة منح يوم مجاني: {grant_result}")
        
        # 3. اختبار التحقق من اليوم المجاني النشط
        has_active = free_day_system.has_active_free_day(test_user_id)
        logger.info(f"هل لديه يوم مجاني نشط؟ {has_active}")
        
        # 4. اختبار نظام الاشتراكات
        is_subscribed = subscription_system.is_subscribed_sync(test_user_id)
        logger.info(f"هل مشترك؟ {is_subscribed}")
        
        # 5. تنظيف المستخدم التجريبي
        try:
            db.collection('users').document(test_user_id).delete()
            logger.info("تم حذف المستخدم التجريبي")
        except:
            pass
        
        logger.info("✅ تم اختبار نظام اليوم المجاني بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في اختبار نظام اليوم المجاني: {str(e)}")
        return False

async def check_user_free_day_status(user_id: str):
    """
    فحص حالة اليوم المجاني لمستخدم محدد
    """
    try:
        # استيراد المكونات المطلوبة
        from integrations.firebase_init import initialize_firebase
        from services.free_day_system import FreeDaySystem
        from services.subscription_system import SubscriptionSystem
        
        # تهيئة قاعدة البيانات
        db = initialize_firebase()
        if not db:
            logger.error("فشل في تهيئة قاعدة البيانات")
            return False
            
        # تهيئة الأنظمة
        free_day_system = FreeDaySystem(db)
        subscription_system = SubscriptionSystem(db, free_day_system)
        
        logger.info(f"🔍 فحص حالة اليوم المجاني للمستخدم {user_id}...")
        
        # الحصول على بيانات المستخدم
        user_ref = db.collection('users').document(user_id)
        user_doc = user_ref.get()
        
        if not user_doc.exists:
            logger.warning(f"المستخدم {user_id} غير موجود في قاعدة البيانات")
            return False
        
        user_data = user_doc.to_dict()
        logger.info(f"بيانات المستخدم: {user_data}")
        
        # فحص حالة اليوم المجاني
        status = free_day_system.get_user_free_day_status(user_id)
        logger.info(f"حالة اليوم المجاني: {status}")
        
        # فحص اليوم المجاني النشط
        has_active = free_day_system.has_active_free_day(user_id)
        logger.info(f"هل لديه يوم مجاني نشط؟ {has_active}")
        
        # فحص حالة الاشتراك
        is_subscribed = subscription_system.is_subscribed_sync(user_id)
        logger.info(f"هل مشترك؟ {is_subscribed}")
        
        # فحص الاستخدام المجاني
        free_usage = subscription_system.get_free_usage(user_id)
        logger.info(f"الاستخدام المجاني: {free_usage}")
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في فحص حالة المستخدم {user_id}: {str(e)}")
        return False

async def main():
    """
    الدالة الرئيسية لتشغيل الإصلاحات
    """
    print("🔧 أداة إصلاح مشكلة اليوم المجاني")
    print("=" * 50)
    
    while True:
        print("\nاختر العملية المطلوبة:")
        print("1. إصلاح اليوم المجاني للمستخدمين الموجودين")
        print("2. منح يوم مجاني ترحيبي لجميع المستخدمين")
        print("3. اختبار نظام اليوم المجاني")
        print("4. فحص حالة مستخدم م��دد")
        print("5. الخروج")
        
        choice = input("\nأدخل رقم الخيار: ").strip()
        
        if choice == "1":
            print("\n🔧 جاري إصلاح اليوم المجاني للمستخدمين الموجودين...")
            result = await fix_free_day_for_existing_users()
            if result:
                print("✅ تم الإصلاح بنجاح!")
            else:
                print("❌ فشل في الإصلاح!")
                
        elif choice == "2":
            print("\n🎁 جاري منح يوم مجاني ترحيبي لجميع المستخدمين...")
            result = await grant_welcome_free_day_to_all()
            if result:
                print("✅ تم منح اليوم المجاني بنجاح!")
            else:
                print("❌ فشل في منح اليوم المجاني!")
                
        elif choice == "3":
            print("\n🧪 جاري اختبار نظام اليوم المجاني...")
            result = await test_free_day_system()
            if result:
                print("✅ تم الاختبار بنجاح!")
            else:
                print("❌ فشل في الاختبار!")
                
        elif choice == "4":
            user_id = input("أدخل معرف المستخدم: ").strip()
            if user_id:
                print(f"\n🔍 جاري فحص حالة المستخدم {user_id}...")
                result = await check_user_free_day_status(user_id)
                if result:
                    print("✅ تم الفحص بنجاح!")
                else:
                    print("❌ فشل في الفحص!")
            else:
                print("❌ معرف المستخدم مطلوب!")
                
        elif choice == "5":
            print("👋 وداعاً!")
            break
            
        else:
            print("❌ خيار غير صحيح!")

if __name__ == "__main__":
    asyncio.run(main())