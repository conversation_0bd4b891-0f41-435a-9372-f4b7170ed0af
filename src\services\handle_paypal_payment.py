import logging
from datetime import datetime, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode
from telegram.ext import CallbackContext

# Configure logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

async def handle_paypal_payment(update: Update, context: CallbackContext):
    """معالجة الدفع عبر PayPal"""
    try:
        # التحقق من وجود update.callback_query
        if not hasattr(update, 'callback_query') or not update.callback_query:
            logger.error("تم استدعاء handle_paypal_payment بدون callback_query")
            raise ValueError("callback_query غير موجود")

        user_id = str(update.effective_user.id)
        callback_query = update.callback_query

        # استيراد المكتبات اللازمة
        from firebase_admin import firestore
        from services.system_settings import system_settings

        # الحصول على قاعدة البيانات
        db = firestore.client()

        # الحصول على إعدادات المستخدم
        user_settings_ref = db.collection('user_settings').document(user_id)
        user_settings_doc = user_settings_ref.get()

        if user_settings_doc.exists:
            settings = user_settings_doc.to_dict()
            lang = settings.get('lang', 'ar')
        else:
            lang = 'ar'

        # التحقق من وجود معاملات معلقة للمستخدم قبل إنشاء معاملة جديدة
        from google.cloud.firestore_v1.base_query import FieldFilter

        # البحث عن معاملات معلقة للمستخدم
        transactions_ref = db.collection('transactions')
        firestore_query = transactions_ref.where(
            filter=FieldFilter("user_id", "==", user_id)
        ).where(
            filter=FieldFilter("status", "==", "pending")
        ).where(
            filter=FieldFilter("payment_method", "==", "paypal")
        )

        pending_transactions = list(firestore_query.get())

        # إذا كان هناك معاملات معلقة، نستخدم أحدث معاملة بدلاً من إنشاء معاملة جديدة
        if pending_transactions:
            # ترتيب المعاملات حسب تاريخ الإنشاء (الأحدث أولاً)
            pending_transactions.sort(key=lambda x: x.to_dict().get('created_at', ''), reverse=True)

            # استخدام أحدث معاملة
            transaction_ref = pending_transactions[0].reference
            transaction_id = transaction_ref.id
            transaction_data = pending_transactions[0].to_dict()

            # تحديث وقت انتهاء الصلاحية للمعاملة الحالية
            transaction_ref.update({
                'expires_at': (datetime.now() + timedelta(hours=1)).isoformat()  # تمديد صلاحية المعاملة
            })

            logger.info(f"تم العثور على معاملة معلقة للمستخدم {user_id} بمعرف {transaction_id}، سيتم استخدامها بدلاً من إنشاء معاملة جديدة")
        else:
            # إنشاء معاملة جديدة
            transaction_data = {
                'user_id': user_id,
                'amount': 5.0,  # USD
                'created_at': datetime.now().isoformat(),
                'status': 'pending',
                'used': False,
                'expires_at': (datetime.now() + timedelta(hours=1)).isoformat(),
                'verification_attempts': 0,
                'payment_method': 'paypal'
            }

            # حفظ المعاملة في Firestore
            transaction_ref = db.collection('transactions').document()
            transaction_ref.set(transaction_data)
            transaction_id = transaction_ref.id

        # إنشاء رابط دفع ديناميكي باستخدام PayPal API
        from integrations.paypal_payment import PayPalAPI

        # الحصول على بيانات اعتماد PayPal من الإعدادات
        client_id = system_settings.get("PAYPAL_CLIENT_ID", sensitive=True)
        client_secret = system_settings.get("PAYPAL_CLIENT_SECRET", sensitive=True)
        is_sandbox = system_settings.get("PAYPAL_SANDBOX_MODE", False)

        if not client_id or not client_secret:
            logger.error("بيانات اعتماد PayPal غير متوفرة")
            await callback_query.answer("❌ خطأ في إعدادات PayPal. الرجاء التواصل مع المطور.", show_alert=True)
            return

        # إنشاء كائن PayPalAPI
        paypal_api = PayPalAPI(client_id, client_secret, is_sandbox)

        # التحقق مما إذا كانت المعاملة تحتوي على معرف طلب ورابط دفع
        order_id = transaction_data.get('order_id')
        paypal_link = transaction_data.get('payment_link')

        # إذا لم يكن هناك معرف طلب أو رابط دفع، نقوم بإنشاء طلب جديد
        if not order_id or not paypal_link:
            # إنشاء طلب دفع جديد مع تفعيل خيار الدفع كضيف
            # المستخدم لا يحتاج لحساب PayPal - يمكنه الدفع بالبطاقة مباشرة
            order_result = await paypal_api.create_order(
                amount=5.0,
                currency="USD",
                user_id=user_id,
                description=f"اشتراك أسبوعي في بوت التحليل الفني - المستخدم: {user_id}",
                allow_guest_checkout=True  # تفعيل خيار الدفع كضيف بدون تسجيل
            )

            if not order_result:
                logger.error("فشل في إنشاء طلب دفع PayPal")
                await callback_query.answer("❌ فشل في إنشاء طلب دفع. الرجاء المحاولة مرة أخرى.", show_alert=True)
                return

            # الحصول على رابط الدفع من نتيجة إنشاء الطلب
            paypal_link = None
            for link in order_result.get("links", []):
                if link.get("rel") == "approve":
                    paypal_link = link.get("href")
                    break

            if not paypal_link:
                logger.error("لم يتم العثور على رابط الدفع في نتيجة إنشاء الطلب")
                await callback_query.answer("❌ فشل في إنشاء رابط الدفع. الرجاء المحاولة مرة أخرى.", show_alert=True)
                return

            # تحديث المعاملة برقم الطلب
            transaction_ref.update({
                'order_id': order_result.get("id"),
                'payment_link': paypal_link
            })

            # تحديث المتغيرات المحلية
            order_id = order_result.get("id")
        else:
            logger.info(f"استخدام طلب دفع موجود للمستخدم {user_id} بمعرف {order_id}")

        # إنشاء رسالة الدفع
        payment_message = (
            "💳 *الدفع عبر PayPal*\n\n"
            "1️⃣ *تفاصيل الدفع:*\n"
            "• المبلغ: 5 USD\n"
            "• مدة الاشتراك: أسبوع واحد\n"
            "• معرف المعاملة: `{transaction_id}`\n\n"
            "2️⃣ *خطوات الدفع:*\n"
            "• اضغط على زر 'الدفع عبر PayPal' أدناه\n"
            "• ستفتح صفحة PayPal الرسمية\n"
            "• يمكنك الدفع باستخدام حساب PayPal أو بطاقة ائتمان مباشرة (بدون تسجيل دخول)\n"
            "• للدفع بالبطاقة مباشرة، اختر 'الدفع ببطاقة الائتمان' أو 'Pay with Debit or Credit Card'\n"
            "• أكمل عملية الدفع بالكامل\n"
            "• بعد إكمال الدفع، عد إلى البوت واضغط على 'تحقق من الدفع'\n\n"
            "3️⃣ *ملاحظات هامة:*\n"
            "• سيتم تفعيل اشتراكك تلقائياً بعد التحقق من الدفع\n"
            "• قد يستغرق تأكيد الدفع بضع دقائق\n"
            "• الاشتراك غير قابل للاسترداد\n"
            "• معرف المستخدم الخاص بك ({user_id}) مضمن تلقائياً في الطلب\n"
            "• ⚠️ لا يوجد دعم فني - اتبع التعليمات بعناية\n"
        ).format(user_id=user_id, transaction_id=transaction_id) if lang == 'ar' else (
            "💳 *Payment via PayPal*\n\n"
            "1️⃣ *Payment Details:*\n"
            "• Amount: 5 USD\n"
            "• Subscription Period: One Week\n"
            "• Transaction ID: `{transaction_id}`\n\n"
            "2️⃣ *Payment Steps:*\n"
            "• Click the 'Pay with PayPal' button below\n"
            "• Official PayPal page will open\n"
            "• You can pay using PayPal account or credit card directly (no login required)\n"
            "• For direct card payment, select 'Pay with Debit or Credit Card'\n"
            "• Complete the payment process fully\n"
            "• After completing payment, return to the bot and click 'Verify Payment'\n\n"
            "3️⃣ *Important Notes:*\n"
            "• Your subscription will be activated automatically after payment verification\n"
            "• Payment confirmation may take a few minutes\n"
            "• Subscription is non-refundable\n"
            "• Your user ID ({user_id}) is automatically included in the order\n"
            "• ⚠️ No technical support available - follow instructions carefully\n"
        ).format(user_id=user_id, transaction_id=transaction_id)

        # إنشاء أزرار الدفع
        keyboard = [
            [InlineKeyboardButton(
                "💳 الدفع عبر PayPal" if lang == 'ar' else "💳 Pay with PayPal",
                url=paypal_link
            )],
            [InlineKeyboardButton(
                "✅ تحقق من الدفع" if lang == 'ar' else "✅ Verify Payment",
                callback_data=f'verify_payment_{transaction_id}'
            )],
            [InlineKeyboardButton(
                "🔙 رجوع" if lang == 'ar' else "🔙 Back",
                callback_data='back_to_main'
            )]
        ]

        # إرسال رسالة الدفع
        await callback_query.edit_message_text(
            text=payment_message,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

        logger.info(f"تم إنشاء معاملة دفع جديدة للمستخدم {user_id} بمعرف {transaction_id}")

    except Exception as e:
        logger.error(f"خطأ في معالجة الدفع عبر PayPal: {str(e)}")
        # إعادة رفع الاستثناء ليتم التعامل معه في main.py
        raise
