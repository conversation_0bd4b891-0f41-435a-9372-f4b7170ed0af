"""
خدمة النسخ الاحتياطي - TradingTelegram Bot

هذا الملف يحتوي على جميع دوال وكلاسات النسخ الاحتياطي:
- SecureBackupSystem: نظام النسخ الاحتياطي المشفر
- GitHubBackup: إدارة النسخ الاحتياطي على GitHub
- perform_backup: تنفيذ النسخ الاحتياطي
- backup_subscription_data: نسخ احتياطي لبيانات الاشتراكات

تم نقل هذه الدوال من main.py في إطار مشروع تقسيم الملف الرئيسي
"""

import os
import time
import json
import base64
import hashlib
from datetime import datetime, timedelta
from typing import Optional

import aiohttp
from cryptography.fernet import Fernet
from telegram import Update
from telegram.ext import CallbackContext
from telegram.constants import ParseMode

# استيراد التبعيات المطلوبة
from utils.system_helpers import encrypt_file
from analysis.analysis_helpers import generate_stats_report

# متغيرات عامة - سيتم تعيينها من main.py
db = None
logger = None
SystemConfig = None
GITHUB_TOKEN = None
GITHUB_REPO = None
GITHUB_OWNER = None


def initialize_backup_service(firestore_db, system_logger, system_config):
    """تهيئة خدمة النسخ الاحتياطي مع التبعيات المطلوبة"""
    global db, logger, SystemConfig, GITHUB_TOKEN, GITHUB_REPO, GITHUB_OWNER
    
    db = firestore_db
    logger = system_logger
    SystemConfig = system_config
    GITHUB_TOKEN = system_config.GITHUB['token']
    GITHUB_REPO = system_config.GITHUB['repo']
    GITHUB_OWNER = system_config.GITHUB['owner']
    
    logger.info("✅ تم تهيئة خدمة النسخ الاحتياطي بنجاح")


class SecureBackupSystem:
    """نظام نسخ احتياطي آمن مع تشفير متقدم"""
    def __init__(self):
        self.encryption_key = None
        self.key_rotation_interval = timedelta(days=7)
        self.last_key_rotation = None

    async def _rotate_encryption_key(self):
        new_key = Fernet.generate_key()
        key_data = {
            'key': new_key.decode(),
            'created_at': datetime.now().isoformat(),
            'previous_key': self.encryption_key.decode() if self.encryption_key else None
        }

        keys_ref = db.collection('backup_keys').document('current')
        keys_ref.set(key_data)

        self.encryption_key = new_key
        self.last_key_rotation = datetime.now()

    async def create_secure_backup(self, data: dict) -> bool:
        try:
            if not self.encryption_key or (
                self.last_key_rotation and
                datetime.now() - self.last_key_rotation >= self.key_rotation_interval
            ):
                await self._rotate_encryption_key()

            # تشفير البيانات
            f = Fernet(self.encryption_key)
            encrypted_data = f.encrypt(json.dumps(data).encode())

            # إضافة تشفير إضافي للبيانات الحساسة
            if 'payment_info' in data or 'user_data' in data:
                salt = os.urandom(16)
                kdf = hashlib.pbkdf2_hmac('sha256', self.encryption_key, salt, 100000)
                additional_key = base64.urlsafe_b64encode(kdf)
                f_additional = Fernet(additional_key)
                encrypted_data = f_additional.encrypt(encrypted_data)
            else:
                salt = None

            # إنشاء البيانات الوصفية
            metadata = {
                'timestamp': datetime.now().isoformat(),
                'checksum': hashlib.sha256(encrypted_data).hexdigest(),
                'salt': base64.b64encode(salt).decode() if salt else None,
                'key_id': db.collection('backup_keys').document('current').id
            }

            # حفظ النسخة الاحتياطية في Firestore
            backup_ref = db.collection('backups').document()
            backup_ref.set({
                'encrypted_data': base64.b64encode(encrypted_data).decode(),
                'metadata': metadata
            })

            return True

        except Exception as e:
            logger.error(f"Error creating secure backup: {str(e)}")
            return False


class GitHubBackup:
    """فئة لإدارة النسخ الاحتياطي على GitHub"""
    def __init__(self):
        self.token = GITHUB_TOKEN
        self.repo = GITHUB_REPO
        self.owner = GITHUB_OWNER
        self.headers = {
            'Authorization': f'token {self.token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        self.base_url = 'https://api.github.com'
        self.last_backup_hash = None

    async def ensure_backup_folder_exists(self) -> bool:
        """التحقق من وجود مجلد النسخ الاحتياطي وإنشائه إذا لم يكن موجوداً"""
        try:
            # التحقق من وجود المجلد
            url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/backups'

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 404:
                        # المجلد غير موجود، نقوم بإنشائه
                        create_url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/backups/.gitkeep'
                        payload = {
                            'message': 'Create backups folder',
                            'content': base64.b64encode(b'').decode(),  # ملف فارغ
                            'branch': 'main'
                        }

                        async with session.put(create_url, headers=self.headers, json=payload) as create_response:
                            if create_response.status == 201:
                                logger.info("✅ تم إنشاء مجلد النسخ الاحتياطي بنجاح")
                                return True
                            else:
                                logger.error(f"❌ فشل في إنشاء مجلد النسخ الاحتياطي: {await create_response.text()}")
                                return False
                    elif response.status == 200:
                        # المجلد موجود بالفعل
                        return True
                    else:
                        logger.error(f"❌ خطأ في التحقق من وجود مجلد النسخ الاحتياطي: {await response.text()}")
                        return False

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من مجلد النسخ الاحتياطي: {str(e)}")
            return False

    async def upload_to_github(self, data: dict) -> bool:
        """رفع النسخة الاحتياطية إلى GitHub"""
        try:
            # التأكد من وجود مجلد النسخ الاحتياطي
            if not await self.ensure_backup_folder_exists():
                logger.error("❌ فشل في التحقق من/إنشاء مجلد النسخ الاحتياطي")
                return False

            # تشفير البيانات
            key = Fernet.generate_key()
            f = Fernet(key)
            encrypted_data = f.encrypt(json.dumps(data).encode())

            # إنشاء اسم الملف
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f'backup_{current_time}.enc'

            # رفع الملف
            path = 'backups'
            url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/{path}/{filename}'

            content = base64.b64encode(encrypted_data).decode()
            payload = {
                'message': f'Automated backup - {current_time}',
                'content': content,
                'branch': 'main'
            }

            async with aiohttp.ClientSession() as session:
                async with session.put(url, headers=self.headers, json=payload) as response:
                    if response.status == 201:
                        logger.info(f"✅ تم رفع النسخة الاحتياطية بنجاح: {filename}")

                        # حفظ مفتاح التشفير في Firestore
                        keys_ref = db.collection('backup_keys').document(filename)
                        keys_ref.set({
                            'key': key.decode(),
                            'created_at': datetime.now().isoformat()
                        })

                        return True
                    else:
                        logger.error(f"❌ خطأ في رفع الملف: {await response.text()}")
                        return False

        except Exception as e:
            logger.error(f"❌ خطأ في رفع النسخة الاحتياطية: {str(e)}")
            return False

    async def cleanup_old_backups(self):
        """حذف النسخ الاحتياطية القديمة (أكثر من أسبوع)"""
        try:
            # جلب قائمة الملفات
            path = 'backups'
            url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/{path}'

            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=self.headers) as response:
                    if response.status == 200:
                        files = await response.json()
                        week_ago = datetime.now() - timedelta(days=7)

                        for file in files:
                            if file['type'] == 'file' and file['name'].startswith('backup_'):
                                # استخراج التاريخ من اسم الملف
                                try:
                                    file_date = datetime.strptime(
                                        file['name'].split('_')[1].split('.')[0],
                                        "%Y%m%d%H%M%S"
                                    )
                                    if file_date < week_ago:
                                        # حذف الملف
                                        delete_url = f'{self.base_url}/repos/{self.owner}/{self.repo}/contents/{file["path"]}'
                                        payload = {
                                            'message': f'Cleanup old backup - {file["name"]}',
                                            'sha': file['sha'],
                                            'branch': 'main'
                                        }

                                        async with session.delete(delete_url, headers=self.headers, json=payload) as del_response:
                                            if del_response.status == 200:
                                                logger.info(f"تم حذف النسخة الاحتياطية القديمة: {file['name']}")

                                                # حذف مفتاح التشفير
                                                keys_ref = db.collection('backup_keys').document(file['name'])
                                                keys_ref.delete()
                                            else:
                                                logger.error(f"خطأ في حذف الملف: {await del_response.text()}")

                                except ValueError:
                                    continue

        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")

    async def check_changes(self) -> bool:
        """التحقق من وجود تغييرات تتطلب نسخة احتياطية جديدة"""
        # هذه دالة مؤقتة - يجب تنفيذها حسب منطق التطبيق
        # حالياً ترجع True دائماً لضمان عمل النسخ الاحتياطي
        return True

    async def create_backup(self) -> Optional[dict]:
        """إنشاء بيانات النسخة الاحتياطية"""
        try:
            backup_data = {
                'timestamp': datetime.now().isoformat(),
                'users': [],
                'subscriptions': [],
                'transactions': [],
                'settings': []
            }

            # نسخ بيانات المستخدمين
            users_ref = db.collection('users')
            for user_doc in users_ref.get():
                if not user_doc.id.startswith('_'):  # تجاهل الوثائق الوصفية
                    backup_data['users'].append({
                        'id': user_doc.id,
                        'data': user_doc.to_dict()
                    })

            # نسخ بيانات الاشتراكات
            subscriptions_ref = db.collection('subscriptions')
            for sub_doc in subscriptions_ref.get():
                if not sub_doc.id.startswith('_'):
                    backup_data['subscriptions'].append({
                        'id': sub_doc.id,
                        'data': sub_doc.to_dict()
                    })

            # نسخ بيانات المعاملات
            transactions_ref = db.collection('transactions')
            for trans_doc in transactions_ref.get():
                if not trans_doc.id.startswith('_'):
                    backup_data['transactions'].append({
                        'id': trans_doc.id,
                        'data': trans_doc.to_dict()
                    })

            # نسخ الإعدادات
            settings_ref = db.collection('_system')
            for setting_doc in settings_ref.get():
                backup_data['settings'].append({
                    'id': setting_doc.id,
                    'data': setting_doc.to_dict()
                })

            logger.info(f"تم إنشاء بيانات النسخة الاحتياطية: {len(backup_data['users'])} مستخدم، {len(backup_data['subscriptions'])} اشتراك، {len(backup_data['transactions'])} معاملة")
            return backup_data

        except Exception as e:
            logger.error(f"خطأ في إنشاء بيانات النسخة الاحتياطية: {str(e)}")
            return None


# تهيئة الكائنات العامة
secure_backup = None
github_backup = None


def get_backup_instances():
    """الحصول على كائنات النسخ الاحتياطي"""
    global secure_backup, github_backup

    if secure_backup is None:
        secure_backup = SecureBackupSystem()

    if github_backup is None:
        github_backup = GitHubBackup()

    return secure_backup, github_backup


async def perform_backup(update: Update = None, context: CallbackContext = None):
    """تنفيذ النسخ الاحتياطي إذا كانت هناك تغييرات"""
    try:
        # الحصول على كائن GitHub backup
        _, github_backup_instance = get_backup_instances()

        # التحقق من أن المستخدم هو المطور إذا تم استدعاء الدالة من أمر
        if update and str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            logger.warning(f"محاولة غير مصرح بها لإنشاء نسخة احتياطية من قبل المستخدم {update.effective_user.id}")
            return

        # التحقق من وجود تغييرات
        if await github_backup_instance.check_changes():
            # إنشاء النسخة الاحتياطية
            backup_data = await github_backup_instance.create_backup()
            if backup_data:
                # رفع النسخة الاحتياطية
                if await github_backup_instance.upload_to_github(backup_data):
                    # تنظيف النسخ القديمة
                    await github_backup_instance.cleanup_old_backups()

                    # إرسال تأكيد للمطور
                    success_message = "✅ تم إنشاء نسخة احتياطية جديدة بنجاح"

                    if context and context.bot:
                        await context.bot.send_message(
                            chat_id=SystemConfig.DEVELOPER_ID,
                            text=success_message
                        )

                    if update:
                        await update.message.reply_text(success_message)

                    logger.info("تم إنشاء نسخة احتياطية جديدة بنجاح")
                    return True
        else:
            # لا توجد تغييرات تتطلب نسخة احتياطية جديدة
            if update:
                await update.message.reply_text("ℹ️ لا توجد تغييرات تتطلب نسخة احتياطية جديدة")

        return False

    except Exception as e:
        error_message = f"⚠️ خطأ في النسخ الاحتياطي: {str(e)}"
        logger.error(f"خطأ في تنفيذ النسخ الاحتياطي: {str(e)}")

        if context and context.bot:
            await context.bot.send_message(
                chat_id=SystemConfig.DEVELOPER_ID,
                text=error_message
            )

        if update:
            await update.message.reply_text(error_message)

        return False


async def backup_subscription_data(context: CallbackContext):
    """نسخ احتياطي لبيانات الاشتراكات"""
    try:
        # التحقق من وجود الملف
        if not os.path.exists('subscription_data.json'):
            logger.error("ملف البيانات غير موجود")
            await context.bot.send_message(
                chat_id=7839527436,  # معرف المالك
                text="⚠️ تنبيه: ملف بيانات الاشتراكات غير موجود!"
            )
            return

        # إنشاء مفتاح تشفير
        key = Fernet.generate_key()

        # إنشاء نسخة احتياطية مع الوقت الحالي
        current_time = time.strftime("%Y%m%d_%H%M%S")
        backup_filename = f'subscription_data_{current_time}.enc'

        # تشفير الملف
        encrypted_data = encrypt_file('subscription_data.json', key)
        if encrypted_data is None:
            await context.bot.send_message(
                chat_id=7839527436,  # معرف المالك
                text="⚠️ حدث خطأ أثناء تشفير الملف"
            )
            return

        with open(backup_filename, 'w') as f:
            f.write(encrypted_data)

        # إرسال الملف المشفر إلى المالك
        with open(backup_filename, 'rb') as file:
            await context.bot.send_document(
                chat_id=7839527436,  # معرف المالك
                document=file,
                caption=f"🔐 نسخة احتياطية مشفرة - {current_time}\n⚠️ هذا الملف مشفر ولا يمكن قراءته إلا بواسطة النظام"
            )

        # إرسال مفتاح التشفير في رسالة منفصلة
        await context.bot.send_message(
            chat_id=7839527436,  # معرف المالك
            text=f"🔑 مفتاح التشفير (يرجى الاحتفاظ به بأمان):\n`{key.decode()}`",
            parse_mode=ParseMode.MARKDOWN
        )

        # إرسال التقرير
        report = await generate_stats_report()
        await context.bot.send_message(
            chat_id=7839527436,  # معرف المالك
            text=report,
            parse_mode=ParseMode.MARKDOWN
        )

        # حذف الملف المؤقت
        os.remove(backup_filename)
        logger.info(f"تم إنشاء وإرسال نسخة احتياطية مشفرة بنجاح - {current_time}")

    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        await context.bot.send_message(
            chat_id=7839527436,  # معرف المالك
            text=f"⚠️ خطأ في إنشاء النسخة الاحتياطية: {str(e)}"
        )
