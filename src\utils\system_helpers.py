"""
دوال مساعدة للنظام - تم نقلها من main.py
تحتوي على دوال مستقلة للمساعدة في العمليات الأساسية للنظام
"""

import os
import logging
import aiohttp
import base64
from cryptography.fernet import Fernet

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

def get_env_var(name, default=None):
    """
    دالة مساعدة للحصول على متغيرات البيئة
    
    Args:
        name: اسم المتغير البيئي
        default: القيمة الافتراضية إذا لم يتم العثور على المتغير
        
    Returns:
        قيمة المتغير البيئي أو القيمة الافتراضية
        
    Raises:
        ValueError: إذا لم يتم العثور على المتغير ولم يتم تحديد قيمة افتراضية
    """
    # أولاً، نحاول الحصول على القيمة من system_settings
    try:
        from services.system_settings import system_settings
        value = system_settings.get(name, None)
    except ImportError:
        value = None
        
    if value is None:
        # إذا لم نجد القيمة في system_settings، نحاول الحصول عليها من متغيرات البيئة
        value = os.getenv(name)

    if not value and default is None:
        raise ValueError(f"المتغير البيئي {name} مطلوب")
    return value or default


def encrypt_file(file_path: str, key: bytes) -> str:
    """
    تشفير محتويات الملف
    
    Args:
        file_path: مسار الملف المراد تشفيره
        key: مفتاح التشفير
        
    Returns:
        البيانات المشفرة مُرمزة بـ base64، أو None في حالة الخطأ
    """
    try:
        with open(file_path, 'rb') as file:
            data = file.read()
        f = Fernet(key)
        encrypted_data = f.encrypt(data)
        return base64.b64encode(encrypted_data).decode()
    except Exception as e:
        logger.error(f"خطأ في تشفير الملف: {str(e)}")
        return None


async def ping_url(url: str, timeout: int = 10) -> bool:
    """
    تشغيل رابط محدد والتحقق من استجابته

    Args:
        url: الرابط المراد تشغيله
        timeout: مهلة الانتظار بالثواني

    Returns:
        True إذا كان الرابط يعمل، False خلاف ذلك
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=timeout) as response:
                status = response.status
                logger.info(f"🔄 تم تشغيل الرابط: {url} - الاستجابة: {status}")
                return status == 200
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل الرابط {url}: {str(e)}")
        return False


async def ping_koyeb_app():
    """
    دالة لتشغيل رابط Koyeb (تم تعطيلها)
    
    تم تعطيل هذه الدالة لأنها لم تعد ضرورية مع إعدادات Koyeb الجديدة
    
    Returns:
        True دائماً (لأن الدالة معطلة)
    """
    logger.info("🔄 تم تعطيل تشغيل رابط Koyeb لأنه لم يعد ضروريًا")
    return True
