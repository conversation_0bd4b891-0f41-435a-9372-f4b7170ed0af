"""
ملف التكامل مع النظام الحالي
Integration Wrapper for Enhanced Multi-Timeframe Analysis

هذا الملف يوفر واجهة سهلة للتكامل مع النظام الحالي
ويحافظ على التوافق مع الكود الموجود
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional

# استيراد النظام المحسن
from .enhanced_analyzer import EnhancedMultiTimeframeAnalyzer, TradingStyle

# إعداد السجل
logger = logging.getLogger(__name__)

class EnhancedAnalysisWrapper:
    """واجهة التكامل مع النظام المحسن"""
    
    def __init__(self, binance_manager=None, api_manager=None):
        self.enhanced_analyzer = EnhancedMultiTimeframeAnalyzer(binance_manager, api_manager)
        self.binance_manager = binance_manager
        self.api_manager = api_manager
    
    async def analyze_crypto_enhanced(self, symbol: str, user_id: str = None,
                                    trading_style: str = None, lang: str = 'ar') -> Dict[str, Any]:
        """
        تحليل محسن للعملة الرقمية - نظام موحد يجمع جميع الأنماط

        Args:
            symbol: رمز العملة (مثل BTCUSDT)
            user_id: معرف المستخدم
            trading_style: معامل محفوظ للتوافق (لا يُستخدم في النظام الموحد)
            lang: لغة التحليل (ar أو en)

        Returns:
            نتائج التحليل المحسن الموحد
        """
        try:
            logger.info(f"🔄 بدء التحليل المحسن الموحد لـ {symbol}")

            # استخدام النظام الموحد الجديد (لا يحتاج نمط تداول محدد)
            # النظام الجديد يجمع جميع الأنماط الأربعة تلقائياً
            analysis_result = await self.enhanced_analyzer.analyze_symbol(
                symbol=symbol,
                trading_style=None,  # النظام الموحد لا يحتاج نمط محدد
                user_id=user_id
            )

            # تحويل النتائج إلى تنسيق متوافق مع النظام الحالي
            compatible_result = self._convert_to_compatible_format(analysis_result, lang)

            # إضافة معلومات النظام الموحد
            compatible_result['analysis_type'] = 'unified_comprehensive'
            compatible_result['patterns_included'] = ['scalping', 'day_trading', 'swing_trading', 'position_trading']

            logger.info(f"✅ اكتمل التحليل المحسن الموحد لـ {symbol}")
            return compatible_result

        except Exception as e:
            logger.error(f"خطأ في التحليل المحسن الموحد لـ {symbol}: {str(e)}")
            return self._create_fallback_response(symbol, str(e), lang)
    
    def _convert_to_compatible_format(self, enhanced_result: Dict[str, Any], lang: str = 'ar') -> Dict[str, Any]:
        """تحويل نتائج التحليل المحسن إلى تنسيق متوافق"""
        try:
            if not enhanced_result.get('success', True):
                return enhanced_result
            
            final_rec = enhanced_result.get('final_recommendation', {})
            
            # تحويل إلى التنسيق المتوقع من النظام الحالي
            compatible_result = {
                'success': True,
                'symbol': enhanced_result.get('symbol', ''),
                'timestamp': enhanced_result.get('analysis_timestamp', datetime.now().isoformat()),
                
                # التوصية الأساسية (متوافقة مع النظام الحالي)
                'recommendation': final_rec.get('action', 'hold'),
                'confidence': final_rec.get('confidence_level', 'low'),
                'strength': final_rec.get('recommendation_strength', 0),
                
                # معلومات المخاطر
                'risk_level': final_rec.get('risk_level', 'medium'),
                'risk_score': final_rec.get('risk_score', 50),
                
                # نقاط التداول
                'entry_points': final_rec.get('entry_points', []),
                'stop_loss': final_rec.get('stop_loss'),
                'take_profit': final_rec.get('take_profit'),
                
                # معلومات إضافية
                'trading_style': enhanced_result.get('trading_style', 'day_trading'),
                'timeframes_analyzed': enhanced_result.get('timeframes_analyzed', []),
                'market_condition': enhanced_result.get('market_condition', 'unknown'),
                
                # التحليل المفصل (للمستخدمين المتقدمين)
                'detailed_analysis': {
                    'hierarchical_analysis': enhanced_result.get('hierarchical_analysis', {}),
                    'signal_confirmation': enhanced_result.get('signal_confirmation', {}),
                    'conflict_analysis': enhanced_result.get('conflict_analysis', {}),
                    'risk_assessment': enhanced_result.get('risk_assessment', {}),
                    'data_quality': enhanced_result.get('data_quality', {}),
                    'performance_stats': enhanced_result.get('performance_stats', {})
                },
                
                # ملخص نصي
                'summary': self.enhanced_analyzer.get_analysis_summary(enhanced_result, lang),
                
                # معلومات التوافق
                'analysis_version': 'enhanced_v1.0',
                'enhanced_features': True
            }
            
            return compatible_result
            
        except Exception as e:
            logger.error(f"خطأ في تحويل التنسيق: {str(e)}")
            return self._create_fallback_response("", str(e), lang)

    def _create_fallback_response(self, symbol: str, error_message: str, lang: str = 'ar') -> Dict[str, Any]:
        """إنشاء استجابة احتياطية في حالة الخطأ"""
        return {
            'success': False,
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'error': error_message,
            'recommendation': 'hold',
            'confidence': 'very_low',
            'strength': 0,
            'risk_level': 'high',
            'risk_score': 100,
            'entry_points': [],
            'stop_loss': None,
            'take_profit': None,
            'summary': f"❌ فشل التحليل: {error_message}" if lang == 'ar' else f"❌ Analysis failed: {error_message}",
            'analysis_version': 'enhanced_v1.0',
            'enhanced_features': False
        }
    
    async def get_quick_analysis(self, symbol: str, user_id: str = None) -> str:
        """
        تحليل سريع يعيد ملخص نصي فقط
        مفيد للاستخدام في الردود السريعة
        """
        try:
            # تشغيل التحليل المحسن
            analysis_result = await self.analyze_crypto_enhanced(symbol, user_id)
            
            if analysis_result.get('success', False):
                return analysis_result.get('summary', 'تحليل غير متاح')
            else:
                return f"❌ فشل التحليل: {analysis_result.get('error', 'خطأ غير معروف')}"
                
        except Exception as e:
            logger.error(f"خطأ في التحليل السريع لـ {symbol}: {str(e)}")
            return f"❌ خطأ في التحليل: {str(e)}"
    
    async def compare_trading_styles(self, symbol: str, user_id: str = None) -> Dict[str, Any]:
        """
        عرض تفصيلي للتحليل الموحد مع تفكيك مكونات كل نمط
        """
        try:
            logger.info(f"🔍 إنشاء تقرير تفصيلي للتحليل الموحد لـ {symbol}")

            # الحصول على التحليل الموحد الشامل
            unified_result = await self.analyze_crypto_enhanced(symbol, user_id)

            if not unified_result.get('success', True):
                return {
                    'symbol': symbol,
                    'error': unified_result.get('error', 'فشل التحليل'),
                    'comparison_results': {},
                    'unified_analysis': False
                }

            # استخراج مكونات التحليل من النتيجة المفصلة
            detailed_analysis = unified_result.get('detailed_analysis', {})
            analysis_components = detailed_analysis.get('hierarchical_analysis', {}).get('analysis_components', {})

            # تنظيم النتائج لكل نمط
            results = {}
            style_names = {
                'scalping': 'المضاربة السريعة',
                'day_trading': 'التداول اليومي',
                'swing_trading': 'التداول المتأرجح',
                'position_trading': 'الاستثمار طويل المدى'
            }

            for style_key, style_name in style_names.items():
                component = analysis_components.get(style_key, {})
                signals = component.get('signals', [])

                # تحليل الإشارات لكل نمط
                buy_signals = [s for s in signals if s.get('action') == 'buy']
                sell_signals = [s for s in signals if s.get('action') == 'sell']

                buy_strength = sum(s.get('strength', 0) for s in buy_signals)
                sell_strength = sum(s.get('strength', 0) for s in sell_signals)

                # تحديد التوصية لكل نمط
                if buy_strength > sell_strength and buy_strength > 3:
                    recommendation = 'buy'
                    confidence = min(buy_strength * 15, 85)
                elif sell_strength > buy_strength and sell_strength > 3:
                    recommendation = 'sell'
                    confidence = min(sell_strength * 15, 85)
                else:
                    recommendation = 'hold'
                    confidence = 30

                # تحديد مستوى الثقة
                if confidence > 70:
                    confidence_level = 'high'
                elif confidence > 50:
                    confidence_level = 'medium'
                else:
                    confidence_level = 'low'

                results[style_key] = {
                    'name': style_name,
                    'recommendation': recommendation,
                    'confidence': confidence_level,
                    'strength': confidence,
                    'signals_count': len(signals),
                    'buy_signals': len(buy_signals),
                    'sell_signals': len(sell_signals),
                    'focus': component.get('focus', 'تحليل شامل'),
                    'timeframes': component.get('timeframes', []),
                    'summary': f"تحليل {style_name}: {len(signals)} إشارة، التوصية: {recommendation}"
                }

            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'comparison_results': results,
                'unified_analysis': True,
                'overall_recommendation': unified_result.get('recommendation', 'hold'),
                'overall_confidence': unified_result.get('confidence', 'medium'),
                'total_signals': sum(r['signals_count'] for r in results.values()),
                'analysis_type': 'unified_comprehensive_breakdown'
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير التفصيلي لـ {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'error': str(e),
                'comparison_results': {},
                'unified_analysis': False
            }
    
    def _find_best_trading_style(self, results: Dict[str, Dict[str, Any]]) -> str:
        """تحديد النمط الأقوى في التحليل الموحد"""
        try:
            if not results:
                return "unified_comprehensive"

            best_style = "unified_comprehensive"
            best_score = 0

            for style, result in results.items():
                # حساب نقاط لكل مكون
                score = 0

                # نقاط القوة الإشارات
                strength = result.get('strength', 0)
                score += strength * 0.4

                # عدد الإشارات
                signals_count = result.get('signals_count', 0)
                score += min(signals_count * 10, 30)  # حد أقصى 30 نقطة

                # نقاط الثقة
                confidence_mapping = {
                    'very_high': 100,
                    'high': 80,
                    'medium': 60,
                    'low': 40,
                    'very_low': 20
                }
                confidence_score = confidence_mapping.get(result.get('confidence', 'low'), 40)
                score += confidence_score * 0.3

                if score > best_score:
                    best_score = score
                    best_style = style

            # في النظام الموحد، نعيد النمط الأقوى مع ملاحظة أنه جزء من تحليل شامل
            return f"{best_style}_dominant_in_unified"

        except Exception as e:
            logger.error(f"خطأ في تحديد النمط الأقوى: {str(e)}")
            return "unified_comprehensive"
    
    def get_supported_features(self) -> Dict[str, Any]:
        """الحصول على قائمة بالميزات المدعومة في النظام الموحد"""
        return {
            'enhanced_analysis': True,
            'unified_comprehensive_analysis': True,
            'multi_timeframe': True,
            'signal_confirmation': True,
            'risk_assessment': True,
            'pattern_integration': True,
            'analysis_patterns': {
                'scalping': 'مدمج في التحليل الموحد',
                'day_trading': 'مدمج في التحليل الموحد',
                'swing_trading': 'مدمج في التحليل الموحد',
                'position_trading': 'مدمج في التحليل الموحد'
            },
            'supported_indicators': [
                'RSI', 'MACD', 'Bollinger Bands', 'Stochastic', 'ADX',
                'Williams %R', 'CCI', 'ATR', 'Parabolic SAR', 'Ichimoku Cloud',
                'EMA', 'SMA', 'Volume Analysis', 'Support/Resistance'
            ],
            'timeframes': ['1m', '5m', '15m', '1h', '4h', '1d', '1w', '1M'],
            'unified_features': {
                'pattern_agreement_analysis': True,
                'comprehensive_risk_assessment': True,
                'multi_pattern_signal_synthesis': True,
                'timeframe_diversity_analysis': True
            },
            'cache_enabled': True,
            'performance_optimized': True,
            'analysis_version': 'unified_v4.0'
        }
    
    async def clear_cache(self):
        """مسح التخزين المؤقت"""
        try:
            self.enhanced_analyzer.data_collector.clear_cache()
            logger.info("تم مسح التخزين المؤقت للنظام المحسن")
        except Exception as e:
            logger.error(f"خطأ في مسح التخزين المؤقت: {str(e)}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين المؤقت"""
        try:
            return self.enhanced_analyzer.data_collector.get_cache_stats()
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات التخزين المؤقت: {str(e)}")
            return {}
