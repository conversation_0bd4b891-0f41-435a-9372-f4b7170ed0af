#!/usr/bin/env python3
"""
إعداد الفهارس المطلوبة لـ Firebase Firestore
يحل مشكلة الاستعلامات المعقدة في نظام الإشعارات
"""

import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

# تعريف الفهارس المطلوبة
REQUIRED_INDEXES = {
    "indexes": [
        {
            "collectionGroup": "notifications",
            "queryScope": "COLLECTION",
            "fields": [
                {
                    "fieldPath": "user_id",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "notification_type",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "created_at",
                    "order": "ASCENDING"
                }
            ]
        },
        {
            "collectionGroup": "notifications",
            "queryScope": "COLLECTION",
            "fields": [
                {
                    "fieldPath": "user_id",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "notification_type",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "created_at",
                    "order": "DESCENDING"
                }
            ]
        },
        {
            "collectionGroup": "notifications",
            "queryScope": "COLLECTION",
            "fields": [
                {
                    "fieldPath": "notification_type",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "created_at",
                    "order": "ASCENDING"
                }
            ]
        },
        {
            "collectionGroup": "notifications",
            "queryScope": "COLLECTION",
            "fields": [
                {
                    "fieldPath": "notification_type",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "created_at",
                    "order": "DESCENDING"
                }
            ]
        },
        {
            "collectionGroup": "user_notification_stats",
            "queryScope": "COLLECTION",
            "fields": [
                {
                    "fieldPath": "user_id",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "last_notification",
                    "order": "DESCENDING"
                }
            ]
        },
        {
            "collectionGroup": "notification_rules",
            "queryScope": "COLLECTION",
            "fields": [
                {
                    "fieldPath": "user_id",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "notification_type",
                    "order": "ASCENDING"
                },
                {
                    "fieldPath": "enabled",
                    "order": "ASCENDING"
                }
            ]
        }
    ]
}

def generate_firestore_indexes_file():
    """إنشاء ملف firestore.indexes.json"""
    try:
        # إنشاء ملف الفهارس
        with open('firestore.indexes.json', 'w', encoding='utf-8') as f:
            json.dump(REQUIRED_INDEXES, f, indent=2, ensure_ascii=False)
        
        logger.info("✅ تم إنشاء ملف firestore.indexes.json بنجاح")
        
        # طباعة التعليمات
        print("\n" + "="*60)
        print("📋 تعليمات إعداد الفهارس في Firebase Firestore")
        print("="*60)
        print("\n1️⃣ تم إنشاء ملف firestore.indexes.json")
        print("\n2️⃣ لتطبيق الفهارس، استخدم Firebase CLI:")
        print("   firebase deploy --only firestore:indexes")
        print("\n3️⃣ أو يمكنك إنشاء الفهارس يدوياً من Firebase Console:")
        
        for i, index in enumerate(REQUIRED_INDEXES["indexes"], 1):
            print(f"\n   📌 فهرس {i}: مجموعة {index['collectionGroup']}")
            for field in index["fields"]:
                print(f"      - {field['fieldPath']} ({field['order']})")
        
        print("\n4️⃣ روابط إنشاء الفهارس المباشرة:")
        
        # إنشاء روابط Firebase Console للفهارس
        project_id = "tradingtelegram-da632"  # من رسالة الخطأ
        
        base_url = f"https://console.firebase.google.com/v1/r/project/{project_id}/firestore/indexes"
        
        # فهرس notifications الأساسي
        notifications_index_url = f"{base_url}?create_composite=Cltwcm9qZWN0cy90cmFkaW5ndGVsZWdyYW0tZGE2MzIvZGF0YWJhc2VzLyhkZWZhdWx0KS9jb2xsZWN0aW9uR3JvdXBzL25vdGlmaWNhdGlvbnMvaW5kZXhlcy9fEAEaFQoRbm90aWZpY2F0aW9uX3R5cGUQARoLCgd1c2VyX2lkEAEaDgoKY3JlYXRlZF9hdBABGgwKCF9fbmFtZV9fEAE"
        
        print(f"\n   🔗 فهرس notifications: {notifications_index_url}")
        
        print("\n5️⃣ بعد إنشاء الفهارس، أعد تشغيل البوت")
        print("\n" + "="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف الفهارس: {str(e)}")
        return False

def create_firestore_rules():
    """إنشاء قواعد Firestore المحدثة"""
    
    rules_content = '''rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد إعدادات المستخدمين
    match /user_settings/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد تفضيلات المستخدمين
    match /user_preferences/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // اشعارات القواعد
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // قواعد إحصائيات الإشعارات
    match /user_notification_stats/{userId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // قواعد قواعد الإشعارات
    match /notification_rules/{ruleId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // قواعد تفضيلات الإشعارات
    match /notification_preferences/{userId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // قواعد المستخدمين المحظورين (للإدارة فقط)
    match /banned_users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // قواعد عامة للمجموعات الأخرى
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}'''
    
    try:
        with open('firestore.rules', 'w', encoding='utf-8') as f:
            f.write(rules_content)
        
        logger.info("✅ تم إنشاء ملف firestore.rules بنجاح")
        print("\n📋 تم إنشاء ملف firestore.rules")
        print("   لتطبيق القواعد: firebase deploy --only firestore:rules")
        
        return True
        
    except Exception as e:
        logger.error(f"خطأ في إنشاء ملف القواعد: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إعداد فهارس وقواعد Firebase Firestore...")
    
    # إنشاء ملف الفهارس
    if generate_firestore_indexes_file():
        print("✅ تم إنشاء ملف الفهارس بنجاح")
    else:
        print("❌ فشل في إنشاء ملف الفهارس")
    
    # إنشاء ملف القواعد
    if create_firestore_rules():
        print("✅ تم إنشاء ملف القواعد بنجاح")
    else:
        print("❌ فشل في إنشاء ملف القواعد")
    
    print("\n🎯 الخطوات التالية:")
    print("1. تشغيل: firebase deploy --only firestore:indexes")
    print("2. تشغيل: firebase deploy --only firestore:rules")
    print("3. إعادة تشغيل البوت")

if __name__ == "__main__":
    main()