"""
خدمة التنبيهات والإشعارات
Alert and Notification Service

هذا الملف يحتوي على جميع دوال التنبيهات والإشعارات المنقولة من main.py
"""

import logging
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from telegram.constants import ParseMode

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

# المتغيرات العامة المطلوبة (سيتم تمريرها من main.py)
db = None
subscription_system = None
ca = None
user_states = None
get_text = None
show_main_menu = None
generate_stats_report = None

def initialize_alert_service(firestore_db, subscription_sys, crypto_analysis, user_states_dict, text_func, menu_func, stats_func):
    """تهيئة خدمة التنبيهات مع المتغيرات المطلوبة"""
    global db, subscription_system, ca, user_states, get_text, show_main_menu, generate_stats_report
    db = firestore_db
    subscription_system = subscription_sys
    ca = crypto_analysis
    user_states = user_states_dict
    get_text = text_func
    show_main_menu = menu_func
    generate_stats_report = stats_func
    logger.info("✅ تم تهيئة خدمة التنبيهات بنجاح")

async def alert_command(update: Update, context: CallbackContext):
    """إعداد تنبيه سعر"""
    user_id = str(update.message.from_user.id)
    lang = context.bot_data.get('user_settings', {}).get(user_id, {}).get('language', 'ar')

    keyboard = [[InlineKeyboardButton(get_text('back', lang), callback_data='back_to_main')]]
    reply_markup = InlineKeyboardMarkup(keyboard)

    user_states[update.message.from_user.id] = 'waiting_for_alert_symbol'

    messages = {
        'ar': 'الرجاء إدخال رمز العملة للتنبيه (مثال: BTC/USDT)',
        'en': 'Please enter the currency pair for alert (example: BTC/USDT)'
    }

    await update.message.reply_text(
        messages[lang],
        reply_markup=reply_markup
    )

async def setup_price_alert(update: Update, context: CallbackContext, symbol: str):
    """إعداد تنبيه سعري"""
    try:
        user_id = str(update.effective_user.id)
        lang = subscription_system.get_user_settings(user_id).get('lang', 'ar')

        # التحقق من حدود التنبيهات المجانية
        if not subscription_system.is_subscribed_sync(user_id):
            free_usage = subscription_system.get_free_usage(user_id)
            if free_usage['alerts'] <= 0:
                await update.callback_query.answer(
                    "عذراً، لقد استنفدت عدد التنبيهات المجانية. قم بالترقية للحصول على تنبيهات غير محدودة!" if lang == 'ar' else
                    "Sorry, you've used all free alerts. Upgrade to get unlimited alerts!",
                    show_alert=True
                )
                return

        # الحصول على السعر الحالي
        market_data = await ca.get_market_data(symbol)
        if not market_data:
            await update.callback_query.answer(
                "عذراً، لم نتمكن من الحصول على سعر العملة" if lang == 'ar' else
                "Sorry, couldn't get current price",
                show_alert=True
            )
            return

        current_price = market_data['price']

        # إنشاء أزرار التنبيه
        keyboard = [
            [
                InlineKeyboardButton(
                    f"↗️ +1% ({current_price * 1.01:.4f})",
                    callback_data=f'alert_above_{symbol}_{current_price * 1.01}'
                ),
                InlineKeyboardButton(
                    f"↗️ +2% ({current_price * 1.02:.4f})",
                    callback_data=f'alert_above_{symbol}_{current_price * 1.02}'
                )
            ],
            [
                InlineKeyboardButton(
                    f"↘️ -1% ({current_price * 0.99:.4f})",
                    callback_data=f'alert_below_{symbol}_{current_price * 0.99}'
                ),
                InlineKeyboardButton(
                    f"↘️ -2% ({current_price * 0.98:.4f})",
                    callback_data=f'alert_below_{symbol}_{current_price * 0.98}'
                )
            ],
            [
                InlineKeyboardButton(
                    "🔙 Back" if lang == 'en' else "🔙 رجوع",
                    callback_data='back_to_main'
                )
            ]
        ]

        # إعداد نص الرسالة
        header_text = "Choose alert price or enter custom value:" if lang == 'en' else "اختر نسبة التنبيه أو ادخل سعراً مخصصاً:"
        price_text = "Current price" if lang == 'en' else "السعر الحالي"
        alert_text = f"💱 {symbol}\n💰 {price_text}: {current_price:.4f}\n\n{header_text}"

        # إضافة ملاحظة للمستخدمين غير المشتركين
        if not subscription_system.is_subscribed(user_id):
            note_text = "⭐️ Note: Custom alert is available for subscribers only" if lang == 'en' else "⭐️ ملاحظة: التنبيه المخصص متاح فقط للمشتركين"
            alert_text += f"\n\n{note_text}"

        try:
            await update.callback_query.edit_message_text(
                text=alert_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        except Exception as edit_error:
            await update.callback_query.message.reply_text(
                text=alert_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    except Exception as e:
        logger.error(f"Error setting up price alert: {str(e)}")
        error_message = "Sorry, an error occurred. Please try again" if lang == 'en' else "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى"
        await update.callback_query.answer(error_message, show_alert=True)
        await show_main_menu(update, context, new_message=True)

async def handle_custom_alert(update: Update, context: CallbackContext, symbol: str):
    """معالجة التنبيه المخصص"""
    try:
        user_id = str(update.effective_user.id)
        lang = subscription_system.get_user_settings(user_id).get('lang', 'ar')

        # التحقق من الاشتراك
        if not subscription_system.is_subscribed_sync(user_id):
            await update.callback_query.answer(
                "عذراً، التنبيه المخصص متاح فقط للمشتركين" if lang == 'ar' else
                "Sorry, custom alerts are only available for subscribers",
                show_alert=True
            )
            return

        # الحصول على السعر الحالي
        market_data = await ca.get_market_data(symbol)
        if not market_data:
            await update.callback_query.answer(
                "عذراً، لم نتمكن من الحصول على سعر العملة" if lang == 'ar' else
                "Sorry, couldn't get current price",
                show_alert=True
            )
            return

        current_price = market_data['price']

        # تخزين حالة المستخدم
        user_states[user_id] = {
            'state': 'waiting_for_custom_alert',
            'symbol': symbol,
            'current_price': current_price
        }

        # إرسال رسالة للمستخدم
        keyboard = [[
            InlineKeyboardButton(
                "🔙 إلغاء" if lang == 'ar' else "🔙 Cancel",
                callback_data='back_to_main'
            )
        ]]

        hint_text = get_text('alert_price_hint', lang)
        message_text = (
            f"💱 {symbol}\n"
            f"💰 {get_text('current_price', lang)}: {current_price:.4f}\n\n"
            f"{hint_text}"
        )

        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"Error handling custom alert: {str(e)}")
        await update.callback_query.answer(
            "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
            "Sorry, an error occurred. Please try again",
            show_alert=True
        )

async def process_custom_alert(update: Update, context: CallbackContext):
    """معالجة إدخال السعر المخصص"""
    try:
        user_id = str(update.effective_user.id)
        lang = subscription_system.get_user_settings(user_id).get('lang', 'ar')

        if user_id not in user_states or user_states[user_id].get('state') != 'waiting_for_custom_alert':
            return

        state_data = user_states[user_id]
        symbol = state_data['symbol']
        current_price = state_data['current_price']
        price_input = update.message.text.strip()

        # معالجة النسبة المئوية
        if price_input.endswith('%'):
            try:
                percentage = float(price_input.rstrip('%').strip())
                target_price = current_price * (1 + percentage/100)
                condition = 'above' if percentage > 0 else 'below'
            except ValueError:
                await update.message.reply_text(
                    "عذراً، الرجاء إدخال نسبة صحيحة" if lang == 'ar' else
                    "Sorry, please enter a valid percentage"
                )
                return
        else:
            try:
                target_price = float(price_input)
                condition = 'above' if target_price > current_price else 'below'
            except ValueError:
                await update.message.reply_text(
                    "عذراً، الرجاء إدخال سعر صحيح" if lang == 'ar' else
                    "Sorry, please enter a valid price"
                )
                return

        # إضافة التنبيه
        alerts_ref = db.collection('alerts').document(user_id)
        alert_data = alerts_ref.get()

        if alert_data.exists:
            user_alerts = alert_data.to_dict()
        else:
            user_alerts = {}

        if symbol not in user_alerts:
            user_alerts[symbol] = []

        user_alerts[symbol].append({
            'price': target_price,
            'condition': condition,
            'created_at': datetime.now().isoformat()
        })

        alerts_ref.set(user_alerts)

        # إرسال تأكيد
        condition_text = get_text('alert_condition_above', lang) if condition == 'above' else get_text('alert_condition_below', lang)
        await update.message.reply_text(
            get_text('alert_added', lang).format(
                symbol=symbol,
                condition=condition_text,
                price=target_price
            )
        )

        # مسح حالة المستخدم
        del user_states[user_id]

        # العودة للقائمة الرئيسية
        await show_main_menu(update, context, new_message=True)

    except Exception as e:
        logger.error(f"Error processing custom alert: {str(e)}")
        await update.message.reply_text(
            "عذراً، حدث خطأ. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
            "Sorry, an error occurred. Please try again"
        )

async def check_alerts(context: CallbackContext):
    """التحقق من التنبيهات وإرسال الإشعارات"""
    try:
        alerts_ref = db.collection('alerts')
        alerts = alerts_ref.get()
        for alert_doc in alerts:
            # تجاهل وثائق البيانات الوصفية
            if alert_doc.id == '_metadata' or alert_doc.id == '_init':
                continue

            user_id = alert_doc.id
            user_alerts = alert_doc.to_dict()

            # التعديل هنا: استخدام list() لتكرار نسخة من العناصر لتجنب خطأ تغيير حجم القاموس أثناء التكرار
            for symbol, symbol_alerts in list(user_alerts.items()):
                # التحقق من صحة رمز العملة
                if not isinstance(symbol, str) or symbol.startswith('_'):
                    continue

                try:
                    market_data = await ca.get_market_data(symbol, user_id=user_id)
                    if market_data:
                        current_price = market_data['price']
                        triggered_alerts = []

                        for alert in symbol_alerts:
                            price = alert['price']
                            condition = alert['condition']

                            if (condition == 'above' and current_price >= price) or \
                               (condition == 'below' and current_price <= price):
                                triggered_alerts.append(alert)

                        for alert in triggered_alerts:
                            condition_text = "أعلى من" if alert['condition'] == 'above' else "أقل من"
                            alert_text = (
                                f"🔔 تنبيه!\n\n"
                                f"💱 {symbol}\n"
                                f"💰 السعر الحالي: {current_price:.2f}\n"
                                f"⚠️ {condition_text} {alert['price']:.2f}"
                            )

                            try:
                                await context.bot.send_message(
                                    chat_id=user_id,
                                    text=alert_text
                                )
                                # حذف التنبيه المنفذ
                                symbol_alerts.remove(alert)

                            except Exception as e:
                                logger.error(f"Error sending alert to user {user_id}: {str(e)}")

                        # تحديث التنبيهات في Firestore
                        if triggered_alerts:
                            if symbol_alerts:
                                user_alerts[symbol] = symbol_alerts
                            else:
                                del user_alerts[symbol]

                            if user_alerts:
                                alerts_ref.document(user_id).set(user_alerts)
                            else:
                                alerts_ref.document(user_id).delete()

                except Exception as e:
                    logger.error(f"Error checking alerts for symbol {symbol}: {str(e)}")
                    continue

    except Exception as e:
        logger.error(f"Error in check_alerts: {str(e)}")

async def send_daily_report(context: CallbackContext):
    """إرسال تقرير إحصائي للمستخدم المحدد عند حدوث تغييرات"""
    try:
        # تحديث الإحصائيات وفحص التغييرات
        changes = subscription_system.update_stats()

        # إرسال التقرير فقط إذا كان هناك تغييرات
        if changes:
            report = await generate_stats_report()
            changes_text = "\n".join(changes)
            full_report = f"{report}\n\n📝 *التغييرات:*\n{changes_text}"

            await context.bot.send_message(
                chat_id=7839527436,  # معرف المالك
                text=full_report,
                parse_mode=ParseMode.MARKDOWN
            )
            logger.info("تم إرسال تقرير التغييرات بنجاح إلى المالك")

    except Exception as e:
        logger.error(f"خطأ في إرسال التقرير: {str(e)}")
