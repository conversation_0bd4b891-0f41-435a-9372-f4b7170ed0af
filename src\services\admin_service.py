"""
خدمات الإدارة - Admin Services
تحتوي على جميع الدوال الإدارية المنقولة من main.py
"""

import logging
import asyncio
from datetime import datetime, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from telegram.constants import ParseMode
import telegram

# إعداد السجل
logger = logging.getLogger(__name__)

# متغيرات عامة (سيتم تعيينها من main.py)
db = None
SystemConfig = None

def initialize_admin_service(database, system_config):
    """تهيئة خدمة الإدارة"""
    global db, SystemConfig
    db = database
    SystemConfig = system_config
    logger.info("✅ تم تهيئة خدمة الإدارة بنجاح")

async def cast(update: Update, context: CallbackContext):
    """أمر إرسال رسالة جماعية لجميع المستخدمين"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لإرسال رسالة جماعية من قبل المستخدم {update.effective_user.id}")
        return

    if not context.args:
        await update.message.reply_text("الرجاء إدخال نص الرسالة بعد الأمر.")
        return

    # تجميع الرسالة من الوسائط
    message = ' '.join(context.args)

    # تنظيف الرسالة من أي علامات خاصة غير مرغوبة
    message = message.replace('<|im_start|>', '').replace('<|im_end|>', '')
    message = message.strip()
    
    success_count = 0
    fail_count = 0
    format_error_count = 0
    inactive_users = []

    # إرسال رسالة تأكيد للمطور
    confirm_msg = await update.message.reply_text("جاري إرسال الرسالة الجماعية... يرجى الانتظار.")

    # قراءة قائمة المستخدمين من Firestore
    users_ref = db.collection('users')
    users = users_ref.stream()

    # تحويل المستخدمين إلى قائمة لمعرفة العدد الإجمالي
    user_list = []
    for user in users:
        user_id = user.id
        user_data = user.to_dict()

        # تخطي المستخدمين غير الصالحين
        if not user_id or not user_id.isdigit() or user_id.startswith('_'):
            logger.info(f"تخطي معرف مستخدم غير صالح: {user_id}")
            continue

        # تخطي المستخدمين المحظورين والغير نشطين
        status = user_data.get('status', '')
        if status == 'banned' or status == 'inactive':
            logger.info(f"تخطي المستخدم {user_id} بسبب الحالة: {status}")
            continue

        user_list.append(user)

    total_users = len(user_list)

    if total_users == 0:
        await confirm_msg.edit_text("لا يوجد مستخدمين نشطين لإرسال الرسالة إليهم.")
        return

    await confirm_msg.edit_text(f"جاري إرسال الرسالة الجماعية إلى {total_users} مستخدم... يرجى الانتظار.")

    for user in user_list:
        user_data = user.to_dict()
        user_id = user.id

        try:
            # محاولة إرسال الرسالة مع تنسيق Markdown أولاً
            try:
                clean_message = message.strip()
                await context.bot.send_message(
                    chat_id=int(user_id),
                    text=clean_message,
                    parse_mode=ParseMode.MARKDOWN
                )
                success_count += 1
            except telegram.error.BadRequest as md_error:
                try:
                    # إذا فشل Markdown، نحاول HTML
                    logger.info(f"محاولة استخدام HTML للمستخدم {user_id}")
                    await context.bot.send_message(
                        chat_id=int(user_id),
                        text=clean_message,
                        parse_mode=ParseMode.HTML
                    )
                    success_count += 1
                except telegram.error.BadRequest as html_error:
                    # إذا فشلت جميع التنسيقات، نرسل بدون تنسيق
                    logger.warning(f"فشلت جميع التنسيقات للمستخدم {user_id}، إرسال بدون تنسيق")
                    plain_message = clean_message
                    # إزالة علامات التنسيق المحتملة
                    for char in ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']:
                        plain_message = plain_message.replace(char, ' ' + char + ' ')
                    plain_message = ' '.join(plain_message.split())

                    await context.bot.send_message(
                        chat_id=int(user_id),
                        text=plain_message,
                        parse_mode=None
                    )
                    success_count += 1
                    format_error_count += 1

            await asyncio.sleep(0.1)  # تأخير صغير لتجنب تجاوز حدود API

        except telegram.error.Forbidden:
            logger.info(f"المستخدم {user_id} قام بحظر البوت")
            inactive_users.append(user_id)

        except telegram.error.BadRequest as e:
            if "chat not found" in str(e).lower():
                logger.info(f"لم يتم العثور على الدردشة للمستخدم {user_id}")
                inactive_users.append(user_id)
            else:
                logger.error(f"خطأ في إرسال الرسالة للمستخدم {user_id}: {str(e)}")
                fail_count += 1

        except Exception as e:
            logger.error(f"خطأ غير متوقع في إرسال الرسالة للمستخدم {user_id}: {str(e)}")
            fail_count += 1

        # تحديث رسالة التقدم كل 10 مستخدمين
        if (success_count + fail_count + len(inactive_users)) % 10 == 0:
            progress = (success_count + fail_count + len(inactive_users)) / total_users * 100
            await confirm_msg.edit_text(
                f"جاري إرسال الرسالة الجماعية... {progress:.1f}%\n"
                f"✅ نجح: {success_count}\n"
                f"❌ فشل: {fail_count}\n"
                f"⚠️ غير نشط: {len(inactive_users)}"
            )

    # تحديث حالة المستخدمين غير النشطين في قاعدة البيانات
    for inactive_user_id in inactive_users:
        try:
            user_ref = db.collection('users').document(inactive_user_id)
            user_doc = user_ref.get()

            if user_doc.exists:
                user_ref.update({
                    'status': 'inactive',
                    'lastUpdated': datetime.now().isoformat()
                })
                logger.info(f"تم تحديث حالة المستخدم {inactive_user_id} إلى غير نشط")
            else:
                user_ref.set({
                    'userId': inactive_user_id,
                    'status': 'inactive',
                    'lastUpdated': datetime.now().isoformat(),
                    'createdAt': datetime.now().isoformat()
                })
                logger.info(f"تم إنشاء وثيقة جديدة للمستخدم {inactive_user_id} بحالة غير نشط")
        except Exception as e:
            logger.error(f"خطأ في تحديث حالة المستخدم {inactive_user_id}: {str(e)}")

    # إنشاء رسالة الحالة النهائية
    active_users = total_users - len(inactive_users)
    status_message = f"""
تم إرسال الرسالة الجماعية:
✅ نجح: {success_count} ({format_error_count} منها تم إرسالها بدون تنسيق)
❌ فشل: {fail_count}
⚠️ غير نشط: {len(inactive_users)}
📊 إجمالي المستخدمين النشطين: {active_users}
"""
    await confirm_msg.edit_text(status_message)

    # إرسال رسالة تقرير إضافية للمطور فقط
    if len(inactive_users) > 0:
        inactive_report = f"تم تحديث حالة {len(inactive_users)} مستخدم إلى 'غير نشط' لأنهم قاموا بحظر البوت أو حذف المحادثة."
        await update.message.reply_text(inactive_report)

async def ban_user(update: Update, context: CallbackContext):
    """أمر حظر مستخدم من استخدام البوت"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لحظر مستخدم من قبل المستخدم {update.effective_user.id}")
        return

    if not context.args:
        await update.message.reply_text("الرجاء إدخال معرف المستخدم المراد حظره.")
        return

    user_id = context.args[0]

    try:
        # إضافة المستخدم إلى قائمة المحظورين
        banned_users_ref = db.collection('banned_users').document(user_id)
        banned_users_ref.set({
            'banned_at': datetime.now().isoformat(),
            'banned_by': str(update.effective_user.id),
            'status': 'banned'
        })

        # تحديث حالة المستخدم في جدول users إن وجد
        users_ref = db.collection('users').document(user_id)
        user_data = users_ref.get()
        if user_data.exists:
            users_ref.update({
                'status': 'banned',
                'lastUpdated': datetime.now().isoformat()
            })

        logger.info(f"تم حظر المستخدم {user_id} بنجاح")
        await update.message.reply_text(f"تم حظر المستخدم {user_id} بنجاح.")
    except Exception as e:
        logger.error(f"خطأ في حظر المستخدم {user_id}: {str(e)}")
        await update.message.reply_text(f"حدث خطأ أثناء حظر المستخدم: {str(e)}")

async def unban_user(update: Update, context: CallbackContext):
    """أمر إلغاء حظر مستخدم"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لإلغاء حظر مستخدم من قبل المستخدم {update.effective_user.id}")
        return

    if not context.args:
        await update.message.reply_text("الرجاء إدخال معرف المستخدم المراد إلغاء حظره.")
        return

    user_id = context.args[0]

    try:
        # حذف المستخدم من قائمة المحظورين
        banned_users_ref = db.collection('banned_users').document(user_id)
        banned_user_data = banned_users_ref.get()

        if not banned_user_data.exists:
            await update.message.reply_text(f"المستخدم {user_id} غير موجود في قائمة المحظورين.")
            return

        banned_users_ref.delete()

        # تحديث حالة المستخدم في جدول users إن وجد
        users_ref = db.collection('users').document(user_id)
        user_data = users_ref.get()
        if user_data.exists:
            users_ref.update({
                'status': 'active',
                'lastUpdated': datetime.now().isoformat()
            })

        logger.info(f"تم إلغاء حظر المستخدم {user_id} بنجاح")
        await update.message.reply_text(f"تم إلغاء حظر المستخدم {user_id} بنجاح.")
    except Exception as e:
        logger.error(f"خطأ في إلغاء حظر المستخدم {user_id}: {str(e)}")
        await update.message.reply_text(f"حدث خطأ أثناء إلغاء حظر المستخدم: {str(e)}")

async def system_info(update: Update, context: CallbackContext):
    """أمر عرض معلومات النظام"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لعرض معلومات النظام من قبل المستخدم {update.effective_user.id}")
        return

    try:
        # جمع إحصائيات النظام
        users_ref = db.collection('users')
        total_users = len(list(users_ref.get()))

        subscriptions_ref = db.collection('subscriptions')
        active_subscriptions = 0
        expired_subscriptions = 0

        for sub_doc in subscriptions_ref.get():
            sub_data = sub_doc.to_dict()
            expiry_date = datetime.fromisoformat(sub_data.get('expiry', '1970-01-01T00:00:00'))
            if expiry_date > datetime.now():
                active_subscriptions += 1
            else:
                expired_subscriptions += 1

        banned_users_ref = db.collection('banned_users')
        banned_users_count = len(list(banned_users_ref.get()))

        # إحصائيات إضافية
        transactions_ref = db.collection('transactions')
        total_transactions = len(list(transactions_ref.get()))

        # إنشاء رسالة معلومات النظام
        system_info_text = f"""
📊 **معلومات النظام**

👥 **إحصائيات المستخدمين:**
• إجمالي المستخدمين: {total_users}
• المشتركين النشطين: {active_subscriptions}
• الاشتراكات المنتهية: {expired_subscriptions}
• المستخدمين المحظورين: {banned_users_count}

💰 **إحصائيات المعاملات:**
• إجمالي المعاملات: {total_transactions}

⏰ **وقت التحديث:**
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        await update.message.reply_text(system_info_text, parse_mode=ParseMode.MARKDOWN)
        logger.info("تم عرض معلومات النظام للمطور")

    except Exception as e:
        logger.error(f"خطأ في عرض معلومات النظام: {str(e)}")
        await update.message.reply_text(f"❌ حدث خطأ أثناء جمع معلومات النظام: {str(e)}")

async def cleanup_system(update: Update, context: CallbackContext):
    """أمر تنظيف النظام من البيانات القديمة"""
    # التحقق من أن المستخدم هو المطور
    if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
        await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
        logger.warning(f"محاولة غير مصرح بها لتنظيف النظام من قبل المستخدم {update.effective_user.id}")
        return

    try:
        cleanup_count = 0

        # تنظيف المعاملات القديمة (أكثر من 30 يوم)
        from google.cloud.firestore import FieldFilter
        transactions_ref = db.collection('transactions')
        old_transactions = transactions_ref.where(filter=FieldFilter('created_at', '<',
                                                 (datetime.now() - timedelta(days=30)).isoformat())).get()

        for transaction in old_transactions:
            transaction.reference.delete()
            cleanup_count += 1

        # تنظيف الأخطاء القديمة (أكثر من 7 أيام)
        errors_ref = db.collection('errors')
        old_errors = errors_ref.where(filter=FieldFilter('timestamp', '<',
                                     (datetime.now() - timedelta(days=7)).isoformat())).get()

        for error in old_errors:
            error.reference.delete()
            cleanup_count += 1

        await update.message.reply_text(f"✅ تم تنظيف النظام بنجاح. تم حذف {cleanup_count} عنصر قديم.")
        logger.info(f"تم تنظيف النظام: حذف {cleanup_count} عنصر")

    except Exception as e:
        logger.error(f"خطأ في تنظيف النظام: {str(e)}")
        await update.message.reply_text(f"❌ حدث خطأ أثناء تنظيف النظام: {str(e)}")

async def backup_data(context: CallbackContext):
    """نسخ احتياطي للبيانات"""
    try:
        # إنشاء مجموعة البيانات
        backup_data = {
            'timestamp': datetime.now().isoformat(),
            'users': [],
            'subscriptions': [],
            'transactions': []
        }

        # نسخ بيانات المستخدمين
        users_ref = db.collection('users')
        for user_doc in users_ref.get():
            backup_data['users'].append({
                'id': user_doc.id,
                'data': user_doc.to_dict()
            })

        # نسخ بيانات الاشتراكات
        subscriptions_ref = db.collection('subscriptions')
        for sub_doc in subscriptions_ref.get():
            backup_data['subscriptions'].append({
                'id': sub_doc.id,
                'data': sub_doc.to_dict()
            })

        # نسخ بيانات المعاملات
        transactions_ref = db.collection('transactions')
        for trans_doc in transactions_ref.get():
            backup_data['transactions'].append({
                'id': trans_doc.id,
                'data': trans_doc.to_dict()
            })

        # حفظ النسخة الاحتياطية في Firestore
        backup_ref = db.collection('backups').document(f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        backup_ref.set(backup_data)

        logger.info(f"تم إنشاء نسخة احتياطية بنجاح: {len(backup_data['users'])} مستخدم، {len(backup_data['subscriptions'])} اشتراك، {len(backup_data['transactions'])} معاملة")
        return True

    except Exception as e:
        logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {str(e)}")
        return False

async def grant_free_day_command(update: Update, context: CallbackContext):
    """منح يوم مجاني للمستخدمين (للمطور فقط)"""
    try:
        # التحقق من أن المستخدم هو المطور
        if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            return

        if not context.args:
            await update.message.reply_text(
                "❌ يرجى تحديد معرف المستخدم أو 'all'.\n"
                "أمثلة:\n"
                "• /free_day 123456789\n"
                "• /free_day 123456789 48\n"
                "• /free_day all\n"
                "• /free_day all 12"
            )
            return

        target_user_id = context.args[0]

        # تحديد مدة اليوم المجاني (افتراضياً 24 ساعة)
        duration_hours = 24
        if len(context.args) > 1:
            try:
                duration_hours = int(context.args[1])
                if duration_hours <= 0 or duration_hours > 168:  # أقصى أسبوع
                    await update.message.reply_text("❌ مدة اليوم المجاني يجب أن تكون بين 1 و 168 ساعة (أسبوع)")
                    return
            except ValueError:
                await update.message.reply_text("❌ مدة اليوم المجاني يجب أن تكون رقماً صحيحاً")
                return

        from services.free_day_system import free_day_system

        # التحقق من منح يوم مجاني لجميع المستخدمين
        if target_user_id.lower() == 'all':
            await update.message.reply_text("🔄 جاري منح يوم مجاني لجميع المستخدمين غير المشتركين...")

            stats = free_day_system.grant_free_day_to_all(duration_hours)

            result_text = f"""
✅ تم منح يوم مجاني لجميع المستخدمين!

📊 الإحصائيات:
• إجمالي المستخدمين: {stats['total']}
• تم منحهم بنجاح: {stats['success']}
• فشل في المنح: {stats['failed']}
• تم تخطيهم (مشتركين): {stats['skipped_subscribers']}
• مدة اليوم المجاني: {duration_hours} ساعة
"""
            await update.message.reply_text(result_text)
            logger.info(f"تم منح يوم مجاني لجميع المستخدمين بواسطة المطور - النجح: {stats['success']}, فشل: {stats['failed']}")
            return

        # منح يوم مجاني لمستخدم محدد
        # التحقق من وجود المستخدم
        user_ref = db.collection('users').document(target_user_id)
        user_doc = user_ref.get()

        if not user_doc.exists:
            await update.message.reply_text(f"❌ المستخدم {target_user_id} غير موجود في النظام")
            return

        # منح يوم مجاني
        success = free_day_system.grant_free_day(target_user_id, duration_hours)

        if success:
            await update.message.reply_text(f"✅ تم منح يوم مجاني للمستخدم {target_user_id} لمدة {duration_hours} ساعة بنجاح")
            logger.info(f"تم منح يوم مجاني للمستخدم {target_user_id} لمدة {duration_hours} ساعة بواسطة المطور")

            # إرسال إشعار للمستخدم
            try:
                notification_text = f"🎉 تهانينا! تم منحك يوم مجاني لمدة {duration_hours} ساعة للاستمتاع بجميع ميزات البوت المتقدمة!"
                await context.bot.send_message(
                    chat_id=target_user_id,
                    text=notification_text
                )
            except Exception as e:
                logger.warning(f"فشل في إرسال إشعار اليوم المجاني للمستخدم {target_user_id}: {str(e)}")
        else:
            await update.message.reply_text(f"❌ فشل في منح يوم مجاني للمستخدم {target_user_id}")

    except Exception as e:
        logger.error(f"خطأ في منح يوم مجاني: {str(e)}")
        await update.message.reply_text(f"❌ حدث خطأ أثناء منح اليوم المجاني: {str(e)}")

async def remove_free_day_command(update: Update, context: CallbackContext):
    """إزالة يوم مجاني من المستخدمين (للمطور فقط)"""
    try:
        # التحقق من أن المستخدم هو المطور
        if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            return

        if not context.args:
            await update.message.reply_text(
                "❌ يرجى تحديد معرف المستخدم.\n"
                "مثال:\n"
                "• /remove_free_day 123456789"
            )
            return

        target_user_id = context.args[0]

        from services.free_day_system import free_day_system

        # التحقق من وجود المستخدم
        user_ref = db.collection('users').document(target_user_id)
        user_doc = user_ref.get()

        if not user_doc.exists:
            await update.message.reply_text(f"❌ المستخدم {target_user_id} غير موجود في النظام")
            return

        # إزالة اليوم المجاني
        success = free_day_system.remove_free_day(target_user_id)

        if success:
            await update.message.reply_text(f"✅ تم إزالة اليوم المجاني من المستخدم {target_user_id} بنجاح")
            logger.info(f"تم إزالة اليوم المجاني من المستخدم {target_user_id} بواسطة المطور")

            # إرسال إشعار للمستخدم
            try:
                notification_text = "⚠️ تم إنهاء اليوم المجاني الخاص بك. شكراً لاستخدام البوت!"
                await context.bot.send_message(
                    chat_id=target_user_id,
                    text=notification_text
                )
            except Exception as e:
                logger.warning(f"فشل في إرسال إشعار إزالة اليوم المجاني للمستخدم {target_user_id}: {str(e)}")
        else:
            await update.message.reply_text(f"❌ فشل في إزالة اليوم المجاني من المستخدم {target_user_id}")

    except Exception as e:
        logger.error(f"خطأ في إزالة يوم مجاني: {str(e)}")
        await update.message.reply_text(f"❌ حدث خطأ أثناء إزالة اليوم المجاني: {str(e)}")

async def stop_all_scheduled_tasks(update: Update, context: CallbackContext):
    """إيقاف وإزالة جميع المهام المجدولة (للمطور فقط)"""
    try:
        # التحقق من أن المستخدم هو المطور
        if str(update.effective_user.id) != SystemConfig.DEVELOPER_ID:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            return

        # الحصول على المجدول من السياق
        scheduler = context.job_queue

        if scheduler:
            # إيقاف جميع المهام
            jobs = scheduler.jobs()
            job_count = len(jobs)

            for job in jobs:
                job.schedule_removal()

            await update.message.reply_text(f"✅ تم إيقاف {job_count} مهمة مجدولة بنجاح")
            logger.info(f"تم إيقاف {job_count} مهمة مجدولة بواسطة المطور")
        else:
            await update.message.reply_text("❌ لا يوجد مجدول مهام نشط")

    except Exception as e:
        logger.error(f"خطأ في إيقاف المهام المجدولة: {str(e)}")
        await update.message.reply_text(f"❌ حدث خطأ أثناء إيقاف المهام المجدولة: {str(e)}")
