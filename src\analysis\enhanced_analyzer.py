"""
المحلل المحسن متعدد الإطارات الزمنية - الملف الرئيسي
Enhanced Multi-Timeframe Analyzer - Main Integration File

هذا الملف يجمع جميع مكونات النظام المحسن:
1. التحليل الهرمي المتكامل
2. نظام تأكيد الإشارات متعدد المستويات
3. جمع البيانات المحسن
4. تحليل التناقضات والتوافقات
5. إنتاج التوصيات النهائية
"""

import logging
import asyncio
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional, List

# استيراد المكونات المطورة
from .enhanced_multi_timeframe import (
    HierarchicalAnalysis, TradingStyle, MarketCondition, 
    SignalStrength, TimeframeConfig
)
from .signal_confirmation import SignalConfirmation, TimeframeConflictAnalysis
from .enhanced_data_collector import EnhancedDataCollector

# إعداد السجل
logger = logging.getLogger(__name__)

class EnhancedMultiTimeframeAnalyzer:
    """المحلل المحسن متعدد الإطارات الزمنية"""
    
    def __init__(self, binance_manager=None, api_manager=None):
        # تهيئة المكونات
        self.data_collector = EnhancedDataCollector(binance_manager, api_manager)
        self.signal_confirmation = SignalConfirmation()
        self.conflict_analyzer = TimeframeConflictAnalysis()
        
        # إعدادات التحليل
        self.default_trading_style = TradingStyle.DAY_TRADING
        self.analysis_cache = {}
        self.cache_timeout = 300  # 5 دقائق
    
    async def analyze_symbol(self, symbol: str, trading_style: TradingStyle = None,
                           user_id: str = None) -> Dict[str, Any]:
        """تحليل شامل موحد لرمز العملة - يجمع جميع الأنماط الأربعة"""
        try:
            logger.info(f"بدء التحليل المحسن الموحد لـ {symbol}")

            # النظام الموحد الجديد: تحليل جميع الأنماط الأربعة
            all_styles = [
                TradingStyle.SCALPING,
                TradingStyle.DAY_TRADING,
                TradingStyle.SWING_TRADING,
                TradingStyle.POSITION
            ]

            unified_analysis = {}
            all_timeframes_data = {}

            # 1. جمع البيانات لجميع الأنماط
            for style in all_styles:
                logger.info(f"جمع البيانات لنمط {style.value}")
                style_data = await self.data_collector.collect_multi_timeframe_data(
                    symbol, style, user_id
                )
                if style_data:
                    unified_analysis[style.value] = {
                        'timeframes_data': style_data,
                        'style': style
                    }
                    # دمج جميع الإطارات الزمنية
                    all_timeframes_data.update(style_data)

            if not unified_analysis:
                return self._create_error_response("فشل في جمع البيانات لجميع الأنماط")

            # 2. التحليل الهرمي لكل نمط
            for style_key, style_data in unified_analysis.items():
                hierarchical_analyzer = HierarchicalAnalysis(style_data['style'])
                hierarchical_analysis = hierarchical_analyzer.analyze_top_down(style_data['timeframes_data'])
                unified_analysis[style_key]['hierarchical_analysis'] = hierarchical_analysis

            # 3. تحليل التناقضات والتوافقات الشامل
            conflict_analysis = self.conflict_analyzer.analyze_conflicts(all_timeframes_data)

            # 4. دمج الإشارات من جميع الأنماط
            unified_signals = self._merge_all_style_signals(unified_analysis)
            signal_confirmation = await self._confirm_unified_signals(unified_signals, all_timeframes_data)

            # 5. تقييم المخاطر الشامل
            risk_assessment = await self._assess_unified_risk(all_timeframes_data, unified_analysis)

            # 6. إنتاج التوصية الموحدة النهائية
            final_recommendation = self._generate_unified_recommendation(
                unified_analysis, conflict_analysis, signal_confirmation, risk_assessment
            )
            
            # 7. تجميع النتائج الموحدة النهائية
            analysis_result = {
                'symbol': symbol,
                'analysis_type': 'unified_enhanced',
                'analysis_timestamp': datetime.now().isoformat(),
                'timeframes_analyzed': list(all_timeframes_data.keys()),
                'styles_analyzed': list(unified_analysis.keys()),

                # التحليلات الموحدة
                'unified_analysis': unified_analysis,
                'conflict_analysis': conflict_analysis,
                'signal_confirmation': signal_confirmation,
                'risk_assessment': risk_assessment,

                # التوصية الموحدة النهائية
                'final_recommendation': final_recommendation,

                # معلومات إضافية
                'data_quality': self._assess_data_quality(all_timeframes_data),
                'analysis_confidence': final_recommendation.get('overall_confidence', 0),
                'market_condition': self._determine_market_condition(all_timeframes_data),

                # إحصائيات الأداء الموحدة
                'performance_stats': {
                    'styles_count': len(unified_analysis),
                    'timeframes_count': len(all_timeframes_data),
                    'indicators_calculated': self._count_indicators(all_timeframes_data),
                    'cache_hit_rate': self._calculate_cache_hit_rate()
                }
            }

            logger.info(f"تم إكمال التحليل المحسن الموحد لـ {symbol} بنجاح")
            return analysis_result
            
        except Exception as e:
            logger.error(f"خطأ في التحليل المحسن الموحد لـ {symbol}: {str(e)}")
            return self._create_error_response(f"خطأ في التحليل الموحد: {str(e)}")

    def _merge_all_style_signals(self, unified_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """دمج الإشارات من جميع أنماط التداول"""
        try:
            merged_signals = {
                'buy_signals': 0,
                'sell_signals': 0,
                'hold_signals': 0,
                'total_signals': 0,
                'style_recommendations': {},
                'consensus_strength': 0
            }

            for style_key, style_data in unified_analysis.items():
                hierarchical = style_data.get('hierarchical_analysis', {})
                recommendation = hierarchical.get('recommendation', {})
                action = recommendation.get('action', 'hold')
                confidence = recommendation.get('confidence_level', 0)

                # تسجيل توصية كل نمط
                merged_signals['style_recommendations'][style_key] = {
                    'action': action,
                    'confidence': confidence,
                    'recommendation': recommendation
                }

                # عد الإشارات
                if action in ['buy', 'strong_buy']:
                    merged_signals['buy_signals'] += 1
                elif action in ['sell', 'strong_sell']:
                    merged_signals['sell_signals'] += 1
                else:
                    merged_signals['hold_signals'] += 1

                merged_signals['total_signals'] += 1

            # حساب قوة الإجماع
            if merged_signals['total_signals'] > 0:
                max_signals = max(
                    merged_signals['buy_signals'],
                    merged_signals['sell_signals'],
                    merged_signals['hold_signals']
                )
                merged_signals['consensus_strength'] = (max_signals / merged_signals['total_signals']) * 100

            return merged_signals

        except Exception as e:
            logger.error(f"خطأ في دمج إشارات الأنماط: {str(e)}")
            return {
                'buy_signals': 0,
                'sell_signals': 0,
                'hold_signals': 0,
                'total_signals': 0,
                'style_recommendations': {},
                'consensus_strength': 0
            }

    async def _confirm_unified_signals(self, unified_signals: Dict[str, Any],
                                     all_timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تأكيد الإشارات الموحدة"""
        try:
            # تحديد الإشارة المهيمنة
            if unified_signals['buy_signals'] > unified_signals['sell_signals'] and unified_signals['buy_signals'] > unified_signals['hold_signals']:
                dominant_signal = 'buy'
            elif unified_signals['sell_signals'] > unified_signals['buy_signals'] and unified_signals['sell_signals'] > unified_signals['hold_signals']:
                dominant_signal = 'sell'
            else:
                dominant_signal = 'hold'

            # تأكيد الإشارة المهيمنة
            confirmation_result = self.signal_confirmation.confirm_signal(
                {'action': dominant_signal}, all_timeframes_data
            )

            # إضافة معلومات الإجماع
            confirmation_result['unified_consensus'] = {
                'dominant_signal': dominant_signal,
                'consensus_strength': unified_signals['consensus_strength'],
                'buy_votes': unified_signals['buy_signals'],
                'sell_votes': unified_signals['sell_signals'],
                'hold_votes': unified_signals['hold_signals'],
                'total_votes': unified_signals['total_signals']
            }

            return confirmation_result

        except Exception as e:
            logger.error(f"خطأ في تأكيد الإشارات الموحدة: {str(e)}")
            return {
                'signal_confirmed': False,
                'confidence_score': 0,
                'unified_consensus': {
                    'dominant_signal': 'hold',
                    'consensus_strength': 0
                }
            }

    async def _assess_unified_risk(self, all_timeframes_data: Dict[str, Dict[str, Any]],
                                 unified_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم المخاطر الموحدة لجميع الأنماط"""
        try:
            # تقييم المخاطر الأساسية
            base_risk = await self._assess_multi_dimensional_risk(all_timeframes_data, {})

            # تقييم مخاطر التناقض بين الأنماط
            style_conflict_risk = self._assess_style_conflict_risk(unified_analysis)

            # دمج تقييمات المخاطر
            unified_risk = {
                'base_risk': base_risk,
                'style_conflicts': style_conflict_risk,
                'overall_risk_score': (base_risk.get('overall_risk_score', 50) + style_conflict_risk.get('score', 50)) / 2,
                'risk_level': 'medium',
                'unified_warnings': []
            }

            # تحديد مستوى المخاطر الموحد
            overall_score = unified_risk['overall_risk_score']
            if overall_score >= 80:
                unified_risk['risk_level'] = 'very_high'
                unified_risk['unified_warnings'].append('مخاطر عالية جداً - تجنب التداول')
            elif overall_score >= 60:
                unified_risk['risk_level'] = 'high'
                unified_risk['unified_warnings'].append('مخاطر عالية - توخي الحذر الشديد')
            elif overall_score >= 40:
                unified_risk['risk_level'] = 'medium'
                unified_risk['unified_warnings'].append('مخاطر متوسطة - إدارة المخاطر مطلوبة')
            elif overall_score >= 20:
                unified_risk['risk_level'] = 'low'
            else:
                unified_risk['risk_level'] = 'very_low'

            return unified_risk

        except Exception as e:
            logger.error(f"خطأ في تقييم المخاطر الموحدة: {str(e)}")
            return {
                'overall_risk_score': 50,
                'risk_level': 'medium',
                'unified_warnings': ['خطأ في تقييم المخاطر - توخي الحذر']
            }

    def _assess_style_conflict_risk(self, unified_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم مخاطر التناقض بين الأنماط"""
        try:
            actions = []
            for style_data in unified_analysis.values():
                hierarchical = style_data.get('hierarchical_analysis', {})
                recommendation = hierarchical.get('recommendation', {})
                action = recommendation.get('action', 'hold')
                actions.append(action)

            # حساب التناقضات
            unique_actions = set(actions)
            conflict_score = 0

            if len(unique_actions) == 1:
                # إجماع كامل
                conflict_score = 0
            elif len(unique_actions) == 2:
                # تناقض متوسط
                conflict_score = 30
            elif len(unique_actions) >= 3:
                # تناقض عالي
                conflict_score = 60

            # إضافة مخاطر إضافية للتناقضات المتضادة
            if 'buy' in actions and 'sell' in actions:
                conflict_score += 40

            return {
                'score': min(conflict_score, 100),
                'unique_actions': len(unique_actions),
                'actions_distribution': {action: actions.count(action) for action in unique_actions},
                'description': f'تناقض بين {len(unique_actions)} أنماط مختلفة'
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم تناقض الأنماط: {str(e)}")
            return {'score': 50, 'description': 'خطأ في التقييم'}

    def _generate_unified_recommendation(self, unified_analysis: Dict[str, Any],
                                       conflict_analysis: Dict[str, Any],
                                       signal_confirmation: Dict[str, Any],
                                       risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """إنتاج التوصية الموحدة النهائية من جميع الأنماط"""
        try:
            # استخراج معلومات الإجماع
            consensus = signal_confirmation.get('unified_consensus', {})
            dominant_signal = consensus.get('dominant_signal', 'hold')
            consensus_strength = consensus.get('consensus_strength', 0)

            # استخراج معلومات المخاطر
            overall_risk = risk_assessment.get('overall_risk_score', 50)
            risk_level = risk_assessment.get('risk_level', 'medium')

            # تحديد قوة التوصية الموحدة
            unified_strength = self._calculate_unified_strength(
                consensus_strength, signal_confirmation.get('confidence_score', 0), overall_risk
            )

            # تحديد التوصية النهائية
            if unified_strength >= 80 and consensus_strength >= 75 and overall_risk < 30:
                final_action = dominant_signal
                recommendation_type = f"strong_{final_action}" if final_action != 'hold' else 'strong_hold'
                confidence_level = 'very_high'
            elif unified_strength >= 60 and consensus_strength >= 50 and overall_risk < 50:
                final_action = dominant_signal
                recommendation_type = final_action
                confidence_level = 'high'
            elif unified_strength >= 40 and overall_risk < 70:
                final_action = dominant_signal
                recommendation_type = f"weak_{final_action}" if final_action != 'hold' else 'hold'
                confidence_level = 'medium'
            else:
                recommendation_type = 'hold'
                final_action = 'hold'
                confidence_level = 'low'

            # إنشاء التوصية الموحدة النهائية
            unified_recommendation = {
                'action': final_action,
                'recommendation_type': recommendation_type,
                'confidence_level': confidence_level,
                'unified_strength': unified_strength,
                'overall_confidence': min(consensus_strength, 100 - overall_risk),

                # معلومات الإجماع
                'consensus_info': {
                    'dominant_signal': dominant_signal,
                    'consensus_strength': consensus_strength,
                    'buy_votes': consensus.get('buy_votes', 0),
                    'sell_votes': consensus.get('sell_votes', 0),
                    'hold_votes': consensus.get('hold_votes', 0),
                    'total_votes': consensus.get('total_votes', 0)
                },

                # معلومات المخاطر الموحدة
                'risk_level': risk_level,
                'risk_score': overall_risk,
                'unified_warnings': risk_assessment.get('unified_warnings', []),

                # معلومات التأكيد
                'signal_confirmed': signal_confirmation.get('signal_confirmed', False),
                'confirmation_sources': signal_confirmation.get('total_confirmations', 0),
                'timeframe_alignment': conflict_analysis.get('overall_consensus', 'unknown'),

                # تفاصيل الأنماط
                'style_breakdown': self._create_style_breakdown(unified_analysis),

                # توصيات إضافية
                'additional_notes': self._generate_unified_notes(
                    unified_analysis, consensus_strength, overall_risk
                ),
                'recommended_approach': self._suggest_unified_approach(
                    final_action, consensus_strength, risk_level
                ),
                'market_timing': self._assess_unified_market_timing(unified_analysis)
            }

            return unified_recommendation

        except Exception as e:
            logger.error(f"خطأ في إنتاج التوصية الموحدة: {str(e)}")
            return {
                'action': 'hold',
                'recommendation_type': 'hold',
                'confidence_level': 'very_low',
                'unified_strength': 0,
                'overall_confidence': 0,
                'risk_level': 'high',
                'additional_notes': ['حدث خطأ في التحليل الموحد - تجنب التداول']
            }

    def _calculate_unified_strength(self, consensus_strength: float,
                                  confirmation_score: float, risk_score: float) -> float:
        """حساب قوة التوصية الموحدة"""
        try:
            # وزن كل عامل
            consensus_weight = 0.4
            confirmation_weight = 0.3
            risk_weight = 0.3

            # حساب النتيجة الموزونة
            weighted_score = (
                consensus_strength * consensus_weight +
                confirmation_score * confirmation_weight +
                (100 - risk_score) * risk_weight
            )

            return min(max(weighted_score, 0), 100)

        except Exception as e:
            logger.error(f"خطأ في حساب قوة التوصية الموحدة: {str(e)}")
            return 0

    def _create_style_breakdown(self, unified_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """إنشاء تفصيل لتوصيات كل نمط"""
        try:
            breakdown = {}

            style_names = {
                'scalping': 'المضاربة السريعة',
                'day_trading': 'التداول اليومي',
                'swing_trading': 'التداول المتأرجح',
                'position': 'الاستثمار طويل المدى'
            }

            for style_key, style_data in unified_analysis.items():
                hierarchical = style_data.get('hierarchical_analysis', {})
                recommendation = hierarchical.get('recommendation', {})

                breakdown[style_key] = {
                    'name': style_names.get(style_key, style_key),
                    'action': recommendation.get('action', 'hold'),
                    'confidence': recommendation.get('confidence_level', 'low'),
                    'entry_quality': recommendation.get('entry_quality', 0),
                    'timeframes': len(style_data.get('timeframes_data', {}))
                }

            return breakdown

        except Exception as e:
            logger.error(f"خطأ في إنشاء تفصيل الأنماط: {str(e)}")
            return {}

    def _generate_unified_notes(self, unified_analysis: Dict[str, Any],
                              consensus_strength: float, risk_score: float) -> list:
        """إنشاء ملاحظات للتحليل الموحد"""
        try:
            notes = []

            # ملاحظات الإجماع
            if consensus_strength >= 75:
                notes.append('إجماع قوي بين جميع أنماط التحليل')
            elif consensus_strength >= 50:
                notes.append('إجماع متوسط بين أنماط التحليل')
            else:
                notes.append('تناقض في توصيات أنماط التحليل المختلفة')

            # ملاحظات المخاطر
            if risk_score >= 70:
                notes.append('مستوى مخاطر عالي - يُنصح بتجنب التداول')
            elif risk_score >= 40:
                notes.append('مستوى مخاطر متوسط - إدارة المخاطر ضرورية')
            else:
                notes.append('مستوى مخاطر منخفض - فرصة جيدة للتداول')

            # ملاحظات الأنماط
            styles_count = len(unified_analysis)
            notes.append(f'تم تحليل {styles_count} أنماط تداول مختلفة')

            return notes

        except Exception as e:
            logger.error(f"خطأ في إنشاء الملاحظات الموحدة: {str(e)}")
            return ['خطأ في إنشاء الملاحظات']

    def _suggest_unified_approach(self, action: str, consensus_strength: float,
                                risk_level: str) -> str:
        """اقتراح النهج الموحد للتداول"""
        try:
            if action == 'hold' or risk_level in ['high', 'very_high']:
                return 'تجنب التداول حالياً وانتظار إشارات أوضح'
            elif consensus_strength >= 75:
                return f'نهج متدرج: ابدأ بمركز صغير واتبع إدارة المخاطر'
            elif consensus_strength >= 50:
                return f'نهج حذر: مركز صغير مع وقف خسارة ضيق'
            else:
                return 'انتظار توافق أكبر بين الأنماط قبل الدخول'

        except Exception as e:
            logger.error(f"خطأ في اقتراح النهج الموحد: {str(e)}")
            return 'نهج حذر مع إدارة مخاطر صارمة'

    def _assess_unified_market_timing(self, unified_analysis: Dict[str, Any]) -> str:
        """تقييم توقيت السوق الموحد"""
        try:
            timing_scores = []

            for style_data in unified_analysis.values():
                hierarchical = style_data.get('hierarchical_analysis', {})
                recommendation = hierarchical.get('recommendation', {})
                action = recommendation.get('action', 'hold')

                if action in ['buy', 'strong_buy']:
                    timing_scores.append(1)
                elif action in ['sell', 'strong_sell']:
                    timing_scores.append(-1)
                else:
                    timing_scores.append(0)

            if timing_scores:
                avg_timing = sum(timing_scores) / len(timing_scores)

                if avg_timing > 0.5:
                    return 'ممتاز للشراء'
                elif avg_timing < -0.5:
                    return 'ممتاز للبيع'
                elif abs(avg_timing) <= 0.25:
                    return 'محايد - انتظار'
                else:
                    return 'متوسط'

            return 'غير محدد'

        except Exception as e:
            logger.error(f"خطأ في تقييم توقيت السوق الموحد: {str(e)}")
            return 'غير محدد'
    
    async def _confirm_primary_signal(self, primary_signal: Dict[str, Any], 
                                    timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تأكيد الإشارة الأساسية"""
        try:
            if not primary_signal:
                return {'confirmed': False, 'reason': 'no_primary_signal'}
            
            # تأكيد الإشارة من مصادر متعددة
            confirmation_result = self.signal_confirmation.confirm_signal(primary_signal, timeframes_data)
            
            return confirmation_result
            
        except Exception as e:
            logger.error(f"خطأ في تأكيد الإشارة الأساسية: {str(e)}")
            return {'confirmed': False, 'reason': 'confirmation_error'}
    
    async def _assess_multi_dimensional_risk(self, timeframes_data: Dict[str, Dict[str, Any]], 
                                           hierarchical_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم المخاطر متعدد الأبعاد"""
        try:
            risk_factors = {}
            
            # 1. مخاطر التقلبات
            volatility_risk = self._calculate_volatility_risk(timeframes_data)
            risk_factors['volatility'] = volatility_risk
            
            # 2. مخاطر انعكاس الاتجاه
            reversal_risk = self._assess_reversal_risk(timeframes_data, hierarchical_analysis)
            risk_factors['trend_reversal'] = reversal_risk
            
            # 3. مخاطر الدعم والمقاومة
            support_resistance_risk = self._evaluate_support_resistance_risk(timeframes_data)
            risk_factors['support_resistance'] = support_resistance_risk
            
            # 4. مخاطر الحجم
            volume_risk = self._analyze_volume_risk(timeframes_data)
            risk_factors['volume'] = volume_risk
            
            # 5. مخاطر التناقضات بين الإطارات
            timeframe_conflict_risk = self._assess_timeframe_conflict_risk(timeframes_data)
            risk_factors['timeframe_conflicts'] = timeframe_conflict_risk
            
            # حساب المخاطر الإجمالية
            overall_risk = self._calculate_overall_risk(risk_factors)
            
            return {
                'risk_factors': risk_factors,
                'overall_risk_score': overall_risk,
                'risk_level': self._categorize_risk_level(overall_risk),
                'risk_recommendations': self._generate_risk_recommendations(risk_factors)
            }
            
        except Exception as e:
            logger.error(f"خطأ في تقييم المخاطر: {str(e)}")
            return {
                'risk_factors': {},
                'overall_risk_score': 50,
                'risk_level': 'medium',
                'risk_recommendations': ['تحليل غير مكتمل - توخي الحذر']
            }
    
    def _generate_final_recommendation(self, hierarchical_analysis: Dict[str, Any], 
                                     conflict_analysis: Dict[str, Any],
                                     signal_confirmation: Dict[str, Any], 
                                     risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """إنتاج التوصية النهائية"""
        try:
            # استخراج المعلومات الأساسية
            primary_recommendation = hierarchical_analysis.get('recommendation', {})
            signal_confirmed = signal_confirmation.get('signal_confirmed', False)
            confidence_score = signal_confirmation.get('confidence_score', 0)
            overall_risk = risk_assessment.get('overall_risk_score', 50)
            
            # تحديد قوة التوصية
            recommendation_strength = self._calculate_recommendation_strength(
                primary_recommendation, signal_confirmed, confidence_score, overall_risk
            )
            
            # تحديد نوع التوصية النهائية
            if recommendation_strength >= 80 and signal_confirmed and overall_risk < 30:
                final_action = primary_recommendation.get('action', 'hold')
                recommendation_type = f"strong_{final_action}" if final_action != 'hold' else 'strong_hold'
                confidence_level = 'very_high'
            elif recommendation_strength >= 60 and signal_confirmed and overall_risk < 50:
                final_action = primary_recommendation.get('action', 'hold')
                recommendation_type = final_action
                confidence_level = 'high'
            elif recommendation_strength >= 40 and overall_risk < 70:
                final_action = primary_recommendation.get('action', 'hold')
                recommendation_type = f"weak_{final_action}" if final_action != 'hold' else 'hold'
                confidence_level = 'medium'
            else:
                recommendation_type = 'hold'
                final_action = 'hold'
                confidence_level = 'low'
            
            # تحديد نقاط الدخول والخروج
            entry_exit_points = self._calculate_entry_exit_points(
                hierarchical_analysis, risk_assessment
            )
            
            # إنشاء التوصية النهائية
            final_recommendation = {
                'action': final_action,
                'recommendation_type': recommendation_type,
                'confidence_level': confidence_level,
                'recommendation_strength': recommendation_strength,
                'overall_confidence': min(confidence_score, 100 - overall_risk),
                
                # نقاط التداول
                'entry_points': entry_exit_points.get('entry_points', []),
                'exit_points': entry_exit_points.get('exit_points', []),
                'stop_loss': entry_exit_points.get('stop_loss'),
                'take_profit': entry_exit_points.get('take_profit'),
                
                # معلومات المخاطر
                'risk_level': risk_assessment.get('risk_level', 'medium'),
                'risk_score': overall_risk,
                'risk_warnings': risk_assessment.get('risk_recommendations', []),
                
                # معلومات التأكيد
                'signal_confirmed': signal_confirmed,
                'confirmation_sources': signal_confirmation.get('total_confirmations', 0),
                'timeframe_alignment': conflict_analysis.get('overall_consensus', 'unknown'),
                
                # توصيات إضافية
                'additional_notes': self._generate_additional_notes(
                    hierarchical_analysis, conflict_analysis, risk_assessment
                ),
                'recommended_timeframe': self._suggest_optimal_timeframe(hierarchical_analysis),
                'market_timing': self._assess_market_timing(hierarchical_analysis)
            }
            
            return final_recommendation
            
        except Exception as e:
            logger.error(f"خطأ في إنتاج التوصية النهائية: {str(e)}")
            return {
                'action': 'hold',
                'recommendation_type': 'hold',
                'confidence_level': 'very_low',
                'recommendation_strength': 0,
                'overall_confidence': 0,
                'risk_level': 'high',
                'additional_notes': ['حدث خطأ في التحليل - تجنب التداول']
            }
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """إنشاء استجابة خطأ"""
        return {
            'success': False,
            'error': error_message,
            'timestamp': datetime.now().isoformat(),
            'recommendation': {
                'action': 'hold',
                'confidence_level': 'very_low',
                'risk_level': 'high',
                'additional_notes': [error_message]
            }
        }
    
    def get_analysis_summary(self, analysis_result: Dict[str, Any], lang: str = 'ar') -> str:
        """الحصول على ملخص التحليل بالتنسيق المطلوب مع دعم اللغات"""
        try:
            if not analysis_result.get('success', True):
                if lang == 'ar':
                    return f"❌ فشل التحليل: {analysis_result.get('error', 'خطأ غير معروف')}"
                else:
                    return f"❌ Analysis failed: {analysis_result.get('error', 'Unknown error')}"

            final_rec = analysis_result.get('final_recommendation', {})
            action = final_rec.get('action', 'hold')
            confidence = final_rec.get('confidence_level', 'low')
            risk = final_rec.get('risk_level', 'medium')

            # ترجمة الإجراءات
            if lang == 'ar':
                action_translations = {
                    'buy': '🟢 شراء',
                    'strong_buy': '🟢 شراء قوي',
                    'sell': '🔴 بيع',
                    'strong_sell': '🔴 بيع قوي',
                    'hold': '🟡 انتظار'
                }

                # ترجمة مستويات الثقة
                confidence_translations = {
                    'very_high': 'عالية',
                    'high': 'عالية',
                    'medium': 'متوسطة',
                    'low': 'منخفضة',
                    'very_low': 'منخفضة'
                }

                # ترجمة مستويات المخاطر
                risk_translations = {
                    'very_low': 'منخفضة',
                    'low': 'منخفضة',
                    'medium': 'متوسطة',
                    'high': 'عالية',
                    'very_high': 'عالية'
                }

                # ترجمة نمط التداول
                trading_style_translations = {
                    'scalping': 'سكالبينغ',
                    'day_trading': 'تداول يومي',
                    'swing_trading': 'تداول متأرجح',
                    'position': 'تداول طويل المدى'
                }

                # ترجمة توافق الإطارات
                alignment_translations = {
                    'strong_consensus': 'متوافق',
                    'moderate_consensus': 'متوافق جزئياً',
                    'weak_consensus': 'ضعيف التوافق',
                    'conflicted': 'متناقض',
                    'unknown': 'غير محدد'
                }
            else:
                action_translations = {
                    'buy': '🟢 Buy',
                    'strong_buy': '🟢 Strong Buy',
                    'sell': '🔴 Sell',
                    'strong_sell': '🔴 Strong Sell',
                    'hold': '🟡 Hold'
                }

                # ترجمة مستويات الثقة
                confidence_translations = {
                    'very_high': 'Very High',
                    'high': 'High',
                    'medium': 'Medium',
                    'low': 'Low',
                    'very_low': 'Very Low'
                }

                # ترجمة مستويات المخاطر
                risk_translations = {
                    'very_low': 'Very Low',
                    'low': 'Low',
                    'medium': 'Medium',
                    'high': 'High',
                    'very_high': 'Very High'
                }

                # ترجمة نمط التداول
                trading_style_translations = {
                    'scalping': 'Scalping',
                    'day_trading': 'Day Trading',
                    'swing_trading': 'Swing Trading',
                    'position': 'Position Trading'
                }

                # ترجمة توافق الإطارات
                alignment_translations = {
                    'strong_consensus': 'Strong Consensus',
                    'moderate_consensus': 'Moderate Consensus',
                    'weak_consensus': 'Weak Consensus',
                    'conflicted': 'Conflicted',
                    'unknown': 'Unknown'
                }

            action_text = action_translations.get(action, action)
            confidence_text = confidence_translations.get(confidence, confidence)
            risk_text = risk_translations.get(risk, risk)

            # للنظام الموحد الجديد
            if analysis_result.get('analysis_type') == 'unified_enhanced':
                trading_style_text = 'تحليل موحد (جميع الأنماط)' if lang == 'ar' else 'Unified Analysis (All Styles)'
            else:
                # للنظام القديم
                trading_style_text = trading_style_translations.get(
                    analysis_result.get('trading_style', 'day_trading'),
                    'Day Trading' if lang == 'en' else 'تداول يومي'
                )

            # الحصول على توافق الإطارات
            alignment = final_rec.get('timeframe_alignment', 'unknown')
            alignment_text = alignment_translations.get(alignment, 'Unknown' if lang == 'en' else 'غير محدد')

            # الحصول على عدد الإطارات المحللة
            timeframes_count = len(analysis_result.get('timeframes_analyzed', []))

            # الحصول على توقيت السوق
            timing = final_rec.get('market_timing', 'غير محدد' if lang == 'ar' else 'Unknown')
            if timing == 'غير محدد' or timing == 'Unknown':
                # تحديد توقيت السوق بناءً على التحليل
                if action in ['buy', 'strong_buy']:
                    timing = 'جيد - فرصة للشراء' if lang == 'ar' else 'Good - Buy Opportunity'
                elif action in ['sell', 'strong_sell']:
                    timing = 'جيد - فرصة للبيع' if lang == 'ar' else 'Good - Sell Opportunity'
                else:
                    timing = 'سيء - تجنب التداول حالياً' if lang == 'ar' else 'Poor - Avoid Trading Now'

            # إنشاء الروابط المباشرة
            from .traditional_analysis import generate_direct_links
            symbol = analysis_result.get('symbol', '')
            direct_links = generate_direct_links(symbol)

            # تنسيق الرسالة بالشكل المطلوب
            if lang == 'ar':
                summary = f"""🚀 تحليل محسن متعدد الإطارات الزمنية

🎯 التوصية: {action_text}
📊 مستوى الثقة: {confidence_text}
⚠️ مستوى المخاطر: {risk_text}
📈 نمط التداول: {trading_style_text}
⏰ الإطارات المحللة: {timeframes_count}
🔄 توافق الإطارات: {alignment_text}
⏰ توقيت السوق: {timing}"""

                # إضافة التحذيرات إذا كانت موجودة
                warnings = final_rec.get('risk_warnings', [])
                if warnings:
                    summary += f"\n\n⚠️ تحذيرات مهمة:"
                    for warning in warnings[:2]:  # أول تحذيرين فقط
                        summary += f"\n• {warning}"

                # إضافة الروابط المباشرة
                if symbol:
                    summary += f"\n\n🔗 [عرض الرسم البياني على TradingView]({direct_links['tradingview']})"
                    summary += f"\n🔗 [رابط مباشر للعملة على Binance]({direct_links['binance']})"
            else:
                summary = f"""🚀 Enhanced Multi-Timeframe Analysis

🎯 Recommendation: {action_text}
📊 Confidence Level: {confidence_text}
⚠️ Risk Level: {risk_text}
📈 Trading Style: {trading_style_text}
⏰ Timeframes Analyzed: {timeframes_count}
🔄 Timeframe Alignment: {alignment_text}
⏰ Market Timing: {timing}"""

                # إضافة التحذيرات إذا كانت موجودة
                warnings = final_rec.get('risk_warnings', [])
                if warnings:
                    summary += f"\n\n⚠️ Important Warnings:"
                    for warning in warnings[:2]:  # أول تحذيرين فقط
                        summary += f"\n• {warning}"

                # إضافة الروابط المباشرة
                if symbol:
                    summary += f"\n\n🔗 [View Chart on TradingView]({direct_links['tradingview']})"
                    summary += f"\n🔗 [Direct Link to Coin on Binance]({direct_links['binance']})"

            # تطبيق الحل المبهر لتنسيق النص
            try:
                from utils.utils import fix_bold_formatting
                summary = fix_bold_formatting(summary.strip(), lang)
            except ImportError:
                logger.warning("لم يتم العثور على دالة fix_bold_formatting")
                summary = summary.strip()

            return summary

        except Exception as e:
            logger.error(f"خطأ في إنشاء ملخص التحليل: {str(e)}")
            if lang == 'ar':
                return "❌ خطأ في إنشاء ملخص التحليل"
            else:
                return "❌ Error creating analysis summary"

    # دوال مساعدة لتقييم المخاطر
    def _calculate_volatility_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """حساب مخاطر التقلبات"""
        try:
            volatility_scores = []

            for timeframe, data in timeframes_data.items():
                atr = data.get('atr', 0)
                price = data.get('price', 1)

                if atr and price:
                    volatility_percentage = (atr / price) * 100
                    volatility_scores.append(volatility_percentage)

            if volatility_scores:
                avg_volatility = sum(volatility_scores) / len(volatility_scores)
                risk_score = min(avg_volatility * 10, 100)  # تطبيع النتيجة

                if risk_score > 80:
                    risk_level = 'very_high'
                elif risk_score > 60:
                    risk_level = 'high'
                elif risk_score > 40:
                    risk_level = 'medium'
                elif risk_score > 20:
                    risk_level = 'low'
                else:
                    risk_level = 'very_low'

                return {
                    'score': risk_score,
                    'level': risk_level,
                    'average_volatility': avg_volatility,
                    'description': f'متوسط التقلبات: {avg_volatility:.2f}%'
                }

            return {'score': 50, 'level': 'medium', 'description': 'بيانات غير كافية'}

        except Exception as e:
            logger.error(f"خطأ في حساب مخاطر التقلبات: {str(e)}")
            return {'score': 50, 'level': 'medium', 'description': 'خطأ في الحساب'}

    def _assess_reversal_risk(self, timeframes_data: Dict[str, Dict[str, Any]],
                            hierarchical_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم مخاطر انعكاس الاتجاه"""
        try:
            reversal_indicators = 0
            total_indicators = 0

            for timeframe, data in timeframes_data.items():
                # فحص مؤشرات الانعكاس
                rsi = data.get('rsi', 50)
                stoch_k = data.get('stoch_k', 50)

                total_indicators += 2

                # RSI في مناطق التشبع
                if rsi >= 70 or rsi <= 30:
                    reversal_indicators += 1

                # Stochastic في مناطق التشبع
                if stoch_k >= 80 or stoch_k <= 20:
                    reversal_indicators += 1

            if total_indicators > 0:
                reversal_probability = (reversal_indicators / total_indicators) * 100

                if reversal_probability > 70:
                    risk_level = 'very_high'
                elif reversal_probability > 50:
                    risk_level = 'high'
                elif reversal_probability > 30:
                    risk_level = 'medium'
                else:
                    risk_level = 'low'

                return {
                    'score': reversal_probability,
                    'level': risk_level,
                    'probability': reversal_probability,
                    'description': f'احتمالية الانعكاس: {reversal_probability:.1f}%'
                }

            return {'score': 30, 'level': 'medium', 'description': 'بيانات غير كافية'}

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر الانعكاس: {str(e)}")
            return {'score': 30, 'level': 'medium', 'description': 'خطأ في التقييم'}

    def _evaluate_support_resistance_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم مخاطر الدعم والمقاومة"""
        try:
            risk_factors = []

            for timeframe, data in timeframes_data.items():
                price = data.get('price', 0)
                bb_upper = data.get('bb_upper', 0)
                bb_lower = data.get('bb_lower', 0)

                if all([price, bb_upper, bb_lower]):
                    # حساب موقع السعر في النطاق
                    range_size = bb_upper - bb_lower
                    if range_size > 0:
                        position = (price - bb_lower) / range_size

                        # كلما اقترب السعر من الحدود، زادت المخاطر
                        if position > 0.9 or position < 0.1:
                            risk_factors.append(80)
                        elif position > 0.8 or position < 0.2:
                            risk_factors.append(60)
                        elif position > 0.7 or position < 0.3:
                            risk_factors.append(40)
                        else:
                            risk_factors.append(20)

            if risk_factors:
                avg_risk = sum(risk_factors) / len(risk_factors)

                if avg_risk > 70:
                    risk_level = 'very_high'
                elif avg_risk > 50:
                    risk_level = 'high'
                elif avg_risk > 30:
                    risk_level = 'medium'
                else:
                    risk_level = 'low'

                return {
                    'score': avg_risk,
                    'level': risk_level,
                    'description': f'مخاطر الدعم/المقاومة: {avg_risk:.1f}%'
                }

            return {'score': 40, 'level': 'medium', 'description': 'بيانات غير كافية'}

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر الدعم والمقاومة: {str(e)}")
            return {'score': 40, 'level': 'medium', 'description': 'خطأ في التقييم'}

    def _analyze_volume_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل مخاطر الحجم"""
        try:
            volume_ratios = []

            for timeframe, data in timeframes_data.items():
                volume_ratio = data.get('volume_ratio', 1)
                if volume_ratio:
                    volume_ratios.append(volume_ratio)

            if volume_ratios:
                avg_volume_ratio = sum(volume_ratios) / len(volume_ratios)

                # حجم منخفض = مخاطر عالية
                if avg_volume_ratio < 0.5:
                    risk_score = 80
                    risk_level = 'very_high'
                    description = 'حجم تداول منخفض جداً'
                elif avg_volume_ratio < 0.8:
                    risk_score = 60
                    risk_level = 'high'
                    description = 'حجم تداول منخفض'
                elif avg_volume_ratio > 3:
                    risk_score = 70
                    risk_level = 'high'
                    description = 'حجم تداول مرتفع بشكل غير طبيعي'
                elif avg_volume_ratio > 2:
                    risk_score = 40
                    risk_level = 'medium'
                    description = 'حجم تداول مرتفع'
                else:
                    risk_score = 20
                    risk_level = 'low'
                    description = 'حجم تداول طبيعي'

                return {
                    'score': risk_score,
                    'level': risk_level,
                    'volume_ratio': avg_volume_ratio,
                    'description': description
                }

            return {'score': 50, 'level': 'medium', 'description': 'بيانات حجم غير متاحة'}

        except Exception as e:
            logger.error(f"خطأ في تحليل مخاطر الحجم: {str(e)}")
            return {'score': 50, 'level': 'medium', 'description': 'خطأ في التحليل'}

    def _assess_timeframe_conflict_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم مخاطر التناقضات بين الإطارات"""
        try:
            conflicts = self.conflict_analyzer.analyze_conflicts(timeframes_data)

            total_comparisons = len(conflicts.get('conflicts', [])) + len(conflicts.get('confirmations', []))
            conflict_count = len(conflicts.get('conflicts', []))

            if total_comparisons > 0:
                conflict_ratio = (conflict_count / total_comparisons) * 100

                if conflict_ratio > 70:
                    risk_level = 'very_high'
                elif conflict_ratio > 50:
                    risk_level = 'high'
                elif conflict_ratio > 30:
                    risk_level = 'medium'
                else:
                    risk_level = 'low'

                return {
                    'score': conflict_ratio,
                    'level': risk_level,
                    'conflicts_count': conflict_count,
                    'total_comparisons': total_comparisons,
                    'description': f'تناقضات بين الإطارات: {conflict_ratio:.1f}%'
                }

            return {'score': 30, 'level': 'medium', 'description': 'بيانات غير كافية للمقارنة'}

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر التناقضات: {str(e)}")
            return {'score': 30, 'level': 'medium', 'description': 'خطأ في التقييم'}

    def _calculate_overall_risk(self, risk_factors: Dict[str, Dict[str, Any]]) -> float:
        """حساب المخاطر الإجمالية"""
        try:
            # أوزان عوامل المخاطر
            weights = {
                'volatility': 0.25,
                'trend_reversal': 0.25,
                'support_resistance': 0.20,
                'volume': 0.15,
                'timeframe_conflicts': 0.15
            }

            total_risk = 0
            total_weight = 0

            for factor_name, factor_data in risk_factors.items():
                weight = weights.get(factor_name, 0.1)
                score = factor_data.get('score', 50)

                total_risk += score * weight
                total_weight += weight

            if total_weight > 0:
                return total_risk / total_weight
            else:
                return 50

        except Exception as e:
            logger.error(f"خطأ في حساب المخاطر الإجمالية: {str(e)}")
            return 50

    def _categorize_risk_level(self, risk_score: float) -> str:
        """تصنيف مستوى المخاطر"""
        if risk_score >= 80:
            return 'very_high'
        elif risk_score >= 60:
            return 'high'
        elif risk_score >= 40:
            return 'medium'
        elif risk_score >= 20:
            return 'low'
        else:
            return 'very_low'

    def _generate_risk_recommendations(self, risk_factors: Dict[str, Dict[str, Any]]) -> List[str]:
        """إنتاج توصيات المخاطر"""
        try:
            recommendations = []

            for factor_name, factor_data in risk_factors.items():
                risk_level = factor_data.get('level', 'medium')

                if factor_name == 'volatility' and risk_level in ['high', 'very_high']:
                    recommendations.append('تقلبات عالية - استخدم أحجام صفقات أصغر')

                elif factor_name == 'trend_reversal' and risk_level in ['high', 'very_high']:
                    recommendations.append('احتمالية انعكاس عالية - راقب إشارات الخروج')

                elif factor_name == 'volume' and risk_level in ['high', 'very_high']:
                    recommendations.append('مشاكل في الحجم - تأكد من السيولة قبل التداول')

                elif factor_name == 'timeframe_conflicts' and risk_level in ['high', 'very_high']:
                    recommendations.append('تناقضات بين الإطارات - انتظر إشارات أوضح')

            if not recommendations:
                recommendations.append('مستوى مخاطر مقبول - تابع خطة التداول')

            return recommendations

        except Exception as e:
            logger.error(f"خطأ في إنتاج توصيات المخاطر: {str(e)}")
            return ['خطأ في تقييم المخاطر - توخي الحذر']

    def _calculate_recommendation_strength(self, primary_recommendation: Dict[str, Any],
                                         signal_confirmed: bool, confidence_score: float,
                                         overall_risk: float) -> float:
        """حساب قوة التوصية"""
        try:
            base_strength = primary_recommendation.get('entry_quality', 0)

            # تعديل القوة بناءً على التأكيد
            if signal_confirmed:
                base_strength *= 1.2
            else:
                base_strength *= 0.8

            # تعديل القوة بناءً على مستوى الثقة
            confidence_factor = confidence_score / 100
            base_strength *= (0.5 + confidence_factor * 0.5)

            # تعديل القوة بناءً على المخاطر
            risk_factor = (100 - overall_risk) / 100
            base_strength *= risk_factor

            return min(base_strength, 100)

        except Exception as e:
            logger.error(f"خطأ في حساب قوة التوصية: {str(e)}")
            return 0

    def _calculate_entry_exit_points(self, hierarchical_analysis: Dict[str, Any],
                                   risk_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """حساب نقاط الدخول والخروج"""
        try:
            # استخراج بيانات السعر الحالي
            current_price = 0
            atr = 0

            # البحث عن السعر الحالي في التحليل
            short_term = hierarchical_analysis.get('short_term_signals', {})
            if short_term:
                for signal in short_term.get('signals', []):
                    if 'price' in signal.get('details', {}):
                        current_price = signal['details']['price']
                        break

            # البحث عن ATR لحساب المسافات
            medium_term = hierarchical_analysis.get('medium_term_trend', {})
            if medium_term and 'details' in medium_term:
                for signal in medium_term.get('details', {}).get('individual_signals', []):
                    if 'atr' in signal.get('details', {}):
                        atr = signal['details']['atr']
                        break

            if not current_price:
                return {}

            # حساب نقاط الدخول والخروج بناءً على ATR
            if not atr:
                atr = current_price * 0.02  # افتراض 2% كـ ATR

            recommendation = hierarchical_analysis.get('recommendation', {})
            action = recommendation.get('action', 'hold')

            entry_exit_points = {}

            if action in ['buy', 'strong_buy']:
                # نقاط دخول للشراء
                entry_exit_points['entry_points'] = [
                    round(current_price - (atr * 0.5), 6),  # دخول عند تراجع طفيف
                    round(current_price, 6),  # دخول فوري
                    round(current_price + (atr * 0.3), 6)   # دخول عند كسر المقاومة
                ]

                # وقف الخسارة
                entry_exit_points['stop_loss'] = round(current_price - (atr * 2), 6)

                # جني الأرباح
                entry_exit_points['take_profit'] = round(current_price + (atr * 3), 6)

            elif action in ['sell', 'strong_sell']:
                # نقاط دخول للبيع
                entry_exit_points['entry_points'] = [
                    round(current_price + (atr * 0.5), 6),  # دخول عند ارتفاع طفيف
                    round(current_price, 6),  # دخول فوري
                    round(current_price - (atr * 0.3), 6)   # دخول عند كسر الدعم
                ]

                # وقف الخسارة
                entry_exit_points['stop_loss'] = round(current_price + (atr * 2), 6)

                # جني الأرباح
                entry_exit_points['take_profit'] = round(current_price - (atr * 3), 6)

            return entry_exit_points

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط الدخول والخروج: {str(e)}")
            return {}

    def _generate_additional_notes(self, hierarchical_analysis: Dict[str, Any],
                                 conflict_analysis: Dict[str, Any],
                                 risk_assessment: Dict[str, Any]) -> List[str]:
        """إنتاج ملاحظات إضافية"""
        try:
            notes = []

            # ملاحظات حول التوافق
            consensus = conflict_analysis.get('overall_consensus', 'unknown')
            if consensus == 'strong_agreement':
                notes.append('توافق قوي بين جميع الإطارات الزمنية')
            elif consensus == 'partial_agreement':
                notes.append('توافق جزئي بين الإطارات الزمنية')
            elif consensus == 'conflicting':
                notes.append('تناقضات بين الإطارات الزمنية - توخي الحذر')

            # ملاحظات حول المخاطر
            overall_risk = risk_assessment.get('overall_risk_score', 50)
            if overall_risk > 70:
                notes.append('مستوى مخاطر عالي - فكر في تقليل حجم الصفقة')
            elif overall_risk < 30:
                notes.append('مستوى مخاطر منخفض - فرصة جيدة للتداول')

            # ملاحظات حول قوة الإشارة
            long_term = hierarchical_analysis.get('long_term_trend', {})
            if long_term.get('strength', 0) >= 4:
                notes.append('اتجاه طويل المدى قوي')

            return notes[:5]  # أقصى 5 ملاحظات

        except Exception as e:
            logger.error(f"خطأ في إنتاج الملاحظات الإضافية: {str(e)}")
            return ['خطأ في إنتاج الملاحظات']

    def _suggest_optimal_timeframe(self, hierarchical_analysis: Dict[str, Any]) -> str:
        """اقتراح الإطار الزمني الأمثل"""
        try:
            # تحليل قوة الإشارات في كل إطار
            long_term = hierarchical_analysis.get('long_term_trend', {})
            medium_term = hierarchical_analysis.get('medium_term_trend', {})
            short_term = hierarchical_analysis.get('short_term_signals', {})

            long_strength = long_term.get('strength', 0)
            medium_strength = medium_term.get('strength', 0)
            short_strength = short_term.get('entry_quality', 0) / 20  # تطبيع إلى 5

            if long_strength >= 4:
                return '1d أو أعلى - للاستثمار طويل المدى'
            elif medium_strength >= 4:
                return '4h إلى 1d - للتداول المتأرجح'
            elif short_strength >= 4:
                return '15m إلى 1h - للتداول اليومي'
            else:
                return '1h - إطار زمني متوازن'

        except Exception as e:
            logger.error(f"خطأ في اقتراح الإطار الزمني: {str(e)}")
            return 'غير محدد'

    def _assess_market_timing(self, hierarchical_analysis: Dict[str, Any]) -> str:
        """تقييم توقيت السوق"""
        try:
            short_term = hierarchical_analysis.get('short_term_signals', {})
            timing_score = short_term.get('timing_score', 0)

            if timing_score >= 80:
                return 'ممتاز - توقيت مثالي للدخول'
            elif timing_score >= 60:
                return 'جيد - توقيت مناسب للدخول'
            elif timing_score >= 40:
                return 'متوسط - يمكن الانتظار لتوقيت أفضل'
            elif timing_score >= 20:
                return 'ضعيف - انتظر إشارات أوضح'
            else:
                return 'سيء - تجنب التداول حالياً'

        except Exception as e:
            logger.error(f"خطأ في تقييم توقيت السوق: {str(e)}")
            return 'غير محدد'

    # دوال مساعدة إضافية
    def _assess_data_quality(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم جودة البيانات"""
        try:
            total_indicators = 0
            valid_indicators = 0

            for data in timeframes_data.values():
                for key, value in data.items():
                    if key != 'price' and value is not None:
                        total_indicators += 1
                        if isinstance(value, (int, float)) and not np.isnan(value):
                            valid_indicators += 1

            quality_percentage = (valid_indicators / total_indicators * 100) if total_indicators > 0 else 0

            return {
                'quality_percentage': quality_percentage,
                'total_indicators': total_indicators,
                'valid_indicators': valid_indicators,
                'quality_level': 'high' if quality_percentage > 80 else 'medium' if quality_percentage > 60 else 'low'
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة البيانات: {str(e)}")
            return {'quality_level': 'unknown', 'quality_percentage': 0}

    def _determine_market_condition(self, timeframes_data: Dict[str, Dict[str, Any]]) -> str:
        """تحديد حالة السوق"""
        try:
            volatility_scores = []
            trend_scores = []

            for data in timeframes_data.values():
                # حساب التقلبات
                atr = data.get('atr', 0)
                price = data.get('price', 1)
                if atr and price:
                    volatility = (atr / price) * 100
                    volatility_scores.append(volatility)

                # حساب قوة الاتجاه
                adx = data.get('adx', 25)
                if adx:
                    trend_scores.append(adx)

            avg_volatility = sum(volatility_scores) / len(volatility_scores) if volatility_scores else 2
            avg_trend_strength = sum(trend_scores) / len(trend_scores) if trend_scores else 25

            if avg_volatility > 5:
                return 'volatile'
            elif avg_volatility < 1:
                return 'low_volatility'
            elif avg_trend_strength > 30:
                return 'trending'
            else:
                return 'sideways'

        except Exception as e:
            logger.error(f"خطأ في تحديد حالة السوق: {str(e)}")
            return 'unknown'

    def _count_indicators(self, timeframes_data: Dict[str, Dict[str, Any]]) -> int:
        """عد المؤشرات المحسوبة"""
        try:
            total_indicators = 0
            for data in timeframes_data.values():
                total_indicators += len([v for v in data.values() if v is not None])
            return total_indicators
        except Exception:
            return 0

    def _calculate_cache_hit_rate(self) -> float:
        """حساب معدل نجاح التخزين المؤقت"""
        try:
            cache_stats = self.data_collector.get_cache_stats()
            total = cache_stats.get('total_entries', 0)
            valid = cache_stats.get('valid_entries', 0)
            return (valid / total * 100) if total > 0 else 0
        except Exception:
            return 0
