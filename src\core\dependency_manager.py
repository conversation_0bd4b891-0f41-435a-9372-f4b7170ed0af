"""
🧠 مدير التبعيات الذكي - Trading Telegram Bot
===============================================

نظام متقدم لإدارة التبعيات والاستيرادات بطريقة ذكية ومحسنة.
يوفر تحميل ذكي (Lazy Loading) ومراقبة الأداء وإدارة التبعيات الدائرية.

الميزات:
- تحميل ذكي للوحدات حسب الحاجة
- إدارة التبعيات الدائرية
- مراقبة استهلاك الذاكرة
- تحسين وقت بدء التشغيل
- نظام تخزين مؤقت للوحدات
- مراقبة الأداء في الوقت الفعلي

المؤلف: Augment Agent
الإصدار: 1.0.0
التاريخ: ديسمبر 2024
"""

import logging
import time
import gc
import sys
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import weakref

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class LoadingPriority(Enum):
    """أولويات تحميل الوحدات"""
    CRITICAL = 1      # حرجة - يجب تحميلها فوراً
    HIGH = 2          # عالية - تحميل مبكر
    MEDIUM = 3        # متوسطة - تحميل حسب الحاجة
    LOW = 4           # منخفضة - تحميل متأخر
    OPTIONAL = 5      # اختيارية - تحميل عند الطلب فقط

@dataclass
class ModuleInfo:
    """معلومات الوحدة"""
    name: str
    priority: LoadingPriority
    dependencies: List[str] = field(default_factory=list)
    loaded: bool = False
    load_time: Optional[float] = None
    memory_usage: Optional[int] = None
    error: Optional[str] = None
    module_object: Optional[Any] = None

class DependencyManager:
    """
    مدير التبعيات الذكي
    
    يدير تحميل الوحدات بطريقة ذكية ومحسنة مع مراقبة الأداء
    """
    
    def __init__(self):
        self.modules: Dict[str, ModuleInfo] = {}
        self.loading_order: List[str] = []
        self.circular_dependencies: List[tuple] = []
        self.cache: Dict[str, Any] = {}
        self.performance_stats: Dict[str, Any] = {}
        self.lock = threading.RLock()
        self._initialize_module_registry()
    
    def _initialize_module_registry(self):
        """تهيئة سجل الوحدات مع أولوياتها"""
        
        # الوحدات الحرجة - يجب تحميلها فوراً
        critical_modules = {
            'logging': [],
            'os': [],
            'sys': [],
            'json': [],
            'time': [],
            'asyncio': []
        }
        
        # الوحدات عالية الأولوية
        high_priority_modules = {
            'telegram': ['logging'],
            'firebase_admin': ['logging'],
            'requests': [],
            'numpy': [],
            'pandas': ['numpy']
        }
        
        # الوحدات متوسطة الأولوية
        medium_priority_modules = {
            'services': ['firebase_admin', 'logging'],
            'analysis': ['numpy', 'pandas', 'requests'],
            'handlers': ['telegram', 'services']
        }
        
        # الوحدات منخفضة الأولوية
        low_priority_modules = {
            'education': ['analysis', 'services'],
            'monitoring': ['services'],
            'utils': []
        }
        
        # الوحدات الاختيارية
        optional_modules = {
            'web3': [],
            'cryptography': []
        }
        
        # تسجيل جميع الوحدات
        for modules_dict, priority in [
            (critical_modules, LoadingPriority.CRITICAL),
            (high_priority_modules, LoadingPriority.HIGH),
            (medium_priority_modules, LoadingPriority.MEDIUM),
            (low_priority_modules, LoadingPriority.LOW),
            (optional_modules, LoadingPriority.OPTIONAL)
        ]:
            for module_name, deps in modules_dict.items():
                self.register_module(module_name, priority, deps)
    
    def register_module(self, name: str, priority: LoadingPriority, 
                       dependencies: List[str] = None):
        """
        تسجيل وحدة جديدة
        
        Args:
            name: اسم الوحدة
            priority: أولوية التحميل
            dependencies: قائمة التبعيات
        """
        with self.lock:
            self.modules[name] = ModuleInfo(
                name=name,
                priority=priority,
                dependencies=dependencies or []
            )
            logger.debug(f"تم تسجيل الوحدة: {name} (أولوية: {priority.name})")
    
    def load_essential(self) -> Dict[str, Any]:
        """
        تحميل الوحدات الأساسية فقط
        
        Returns:
            dict: الوحدات المحملة
        """
        start_time = time.time()
        loaded_modules = {}
        
        logger.info("🔄 بدء تحميل الوحدات الأساسية...")
        
        # تحميل الوحدات الحرجة فقط
        critical_modules = [
            name for name, info in self.modules.items() 
            if info.priority == LoadingPriority.CRITICAL
        ]
        
        for module_name in critical_modules:
            try:
                module = self._load_module(module_name)
                if module:
                    loaded_modules[module_name] = module
                    logger.debug(f"✅ تم تحميل الوحدة الأساسية: {module_name}")
            except Exception as e:
                logger.error(f"❌ فشل في تحميل الوحدة الأساسية {module_name}: {e}")
        
        load_time = time.time() - start_time
        logger.info(f"✅ تم تحميل {len(loaded_modules)} وحدة أساسية في {load_time:.2f} ثانية")
        
        # تحديث إحصائيات الأداء
        self.performance_stats['essential_load_time'] = load_time
        self.performance_stats['essential_modules_count'] = len(loaded_modules)
        
        return loaded_modules
    
    def load_by_priority(self, max_priority: LoadingPriority = LoadingPriority.HIGH) -> Dict[str, Any]:
        """
        تحميل الوحدات حسب الأولوية
        
        Args:
            max_priority: أقصى أولوية للتحميل
            
        Returns:
            dict: الوحدات المحملة
        """
        start_time = time.time()
        loaded_modules = {}
        
        logger.info(f"🔄 بدء تحميل الوحدات حتى أولوية {max_priority.name}...")
        
        # ترتيب الوحدات حسب الأولوية
        sorted_modules = sorted(
            self.modules.items(),
            key=lambda x: x[1].priority.value
        )
        
        for module_name, module_info in sorted_modules:
            if module_info.priority.value <= max_priority.value:
                try:
                    module = self._load_module(module_name)
                    if module:
                        loaded_modules[module_name] = module
                        logger.debug(f"✅ تم تحميل الوحدة: {module_name}")
                except Exception as e:
                    logger.warning(f"⚠️ فشل في تحميل الوحدة {module_name}: {e}")
        
        load_time = time.time() - start_time
        logger.info(f"✅ تم تحميل {len(loaded_modules)} وحدة في {load_time:.2f} ثانية")
        
        return loaded_modules
    
    def load_on_demand(self, module_name: str) -> Optional[Any]:
        """
        تحميل وحدة عند الطلب
        
        Args:
            module_name: اسم الوحدة
            
        Returns:
            الوحدة المحملة أو None
        """
        with self.lock:
            # التحقق من التخزين المؤقت
            if module_name in self.cache:
                logger.debug(f"📦 تم استرجاع الوحدة من التخزين المؤقت: {module_name}")
                return self.cache[module_name]
            
            # تحميل الوحدة
            try:
                module = self._load_module(module_name)
                if module:
                    self.cache[module_name] = module
                    logger.debug(f"✅ تم تحميل الوحدة عند الطلب: {module_name}")
                    return module
            except Exception as e:
                logger.error(f"❌ فشل في تحميل الوحدة عند الطلب {module_name}: {e}")
                return None
    
    def _load_module(self, module_name: str) -> Optional[Any]:
        """
        تحميل وحدة واحدة مع مراقبة الأداء
        
        Args:
            module_name: اسم الوحدة
            
        Returns:
            الوحدة المحملة أو None
        """
        if module_name not in self.modules:
            logger.warning(f"⚠️ الوحدة غير مسجلة: {module_name}")
            return None
        
        module_info = self.modules[module_name]
        
        # التحقق من التحميل المسبق
        if module_info.loaded and module_info.module_object:
            return module_info.module_object
        
        start_time = time.time()
        memory_before = self._get_memory_usage()
        
        try:
            # تحميل التبعيات أولاً
            for dep in module_info.dependencies:
                if dep not in self.cache:
                    self._load_module(dep)
            
            # تحميل الوحدة
            if module_name in sys.modules:
                module = sys.modules[module_name]
            else:
                module = __import__(module_name)
            
            # تحديث معلومات الوحدة
            load_time = time.time() - start_time
            memory_after = self._get_memory_usage()
            
            module_info.loaded = True
            module_info.load_time = load_time
            module_info.memory_usage = memory_after - memory_before
            module_info.module_object = weakref.ref(module)
            module_info.error = None
            
            logger.debug(f"📊 {module_name}: {load_time:.3f}s, {module_info.memory_usage}KB")
            
            return module
            
        except Exception as e:
            module_info.error = str(e)
            logger.error(f"❌ خطأ في تحميل {module_name}: {e}")
            return None
    
    def _get_memory_usage(self) -> int:
        """
        الحصول على استهلاك الذاكرة الحالي
        
        Returns:
            استهلاك الذاكرة بالكيلوبايت
        """
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss // 1024  # KB
        except ImportError:
            # استخدام gc كبديل
            return len(gc.get_objects())
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        تقرير الأداء الشامل
        
        Returns:
            dict: تقرير مفصل عن الأداء
        """
        total_modules = len(self.modules)
        loaded_modules = sum(1 for info in self.modules.values() if info.loaded)
        failed_modules = sum(1 for info in self.modules.values() if info.error)
        
        total_load_time = sum(
            info.load_time for info in self.modules.values() 
            if info.load_time is not None
        )
        
        total_memory = sum(
            info.memory_usage for info in self.modules.values() 
            if info.memory_usage is not None
        )
        
        return {
            'modules_summary': {
                'total': total_modules,
                'loaded': loaded_modules,
                'failed': failed_modules,
                'success_rate': (loaded_modules / total_modules * 100) if total_modules > 0 else 0
            },
            'performance': {
                'total_load_time': total_load_time,
                'average_load_time': total_load_time / loaded_modules if loaded_modules > 0 else 0,
                'total_memory_usage': total_memory,
                'cache_size': len(self.cache)
            },
            'by_priority': {
                priority.name: {
                    'total': sum(1 for info in self.modules.values() if info.priority == priority),
                    'loaded': sum(1 for info in self.modules.values() if info.priority == priority and info.loaded)
                }
                for priority in LoadingPriority
            }
        }
    
    def cleanup_cache(self):
        """تنظيف التخزين المؤقت"""
        with self.lock:
            self.cache.clear()
            gc.collect()
            logger.info("🧹 تم تنظيف التخزين المؤقت")
    
    def get_module_status(self, module_name: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على حالة وحدة معينة
        
        Args:
            module_name: اسم الوحدة
            
        Returns:
            dict: معلومات الوحدة
        """
        if module_name not in self.modules:
            return None
        
        info = self.modules[module_name]
        return {
            'name': info.name,
            'priority': info.priority.name,
            'loaded': info.loaded,
            'load_time': info.load_time,
            'memory_usage': info.memory_usage,
            'dependencies': info.dependencies,
            'error': info.error,
            'in_cache': module_name in self.cache
        }

# إنشاء مثيل عام لمدير التبعيات
dependency_manager = DependencyManager()

def get_dependency_manager() -> DependencyManager:
    """الحصول على مدير التبعيات العام"""
    return dependency_manager
