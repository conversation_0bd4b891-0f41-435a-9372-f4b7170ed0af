"""
خدمة إدارة المعاملات المالية
Transaction Service - إدارة المعاملات المالية والدفع

هذا الملف يحتوي على جميع الدوال المتعلقة بإدارة المعاملات المالية:
- إنشاء المعاملات
- التحقق من المعاملات
- إلغاء المعاملات
- التحقق من PayPal
- تفعيل الاشتراكات
- إدارة دورة حياة المعاملات
- تنظيف المعاملات المنتهية
- إشعارات المعاملات

تم نقل هذه الدوال من main.py في إطار المرحلة 6 من خطة التحسين التدريجي.
"""

import logging
import asyncio
import aiohttp
import pytz
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

# استيراد Firebase
from firebase_admin import firestore
from firebase_admin.firestore import FieldFilter

# استيراد Telegram
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, CallbackContext
from telegram.constants import ParseMode

# استيراد APScheduler
from apscheduler.schedulers.asyncio import AsyncIOScheduler

# استيراد الخدمات المحلية
from services.system_settings import system_settings

logger = logging.getLogger(__name__)


class TransactionManager:
    """مدير المعاملات - إدارة دورة حياة المعاملات والتنظيف التلقائي"""

    def __init__(self):
        self.cleanup_scheduler = AsyncIOScheduler(timezone=pytz.UTC)
        self.cleanup_scheduler.add_job(
            self.cleanup_failed_transactions,
            'interval',
            minutes=30
        )

    async def initialize(self):
        """تهيئة مدير المعاملات وبدء جدولة التنظيف"""
        if not self.cleanup_scheduler.running:
            self.cleanup_scheduler.start()

    async def mark_transaction_failed(self, transaction_id: str, reason: str = None):
        """تحديد المعاملة كفاشلة وحذفها مباشرةً"""
        try:
            # الحصول على قاعدة البيانات من الخدمة
            service = get_transaction_service()
            if not service or not service.db:
                logger.error("خدمة المعاملات غير متاحة")
                return

            transaction_ref = service.db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get()

            if not transaction_data.exists:
                logger.error(f"المعاملة {transaction_id} غير موجودة")
                return

            # تحديث حالة المعاملة
            update_data = {
                'status': 'failed',
                'failed_at': datetime.now().isoformat(),
                'failure_reason': reason
            }

            if transaction_data.to_dict():
                update_data['retry_count'] = transaction_data.to_dict().get('retry_count', 0) + 1

            # تحديث الحالة ثم الحذف
            transaction_ref.update(update_data)
            transaction_ref.delete()

            logger.info(f"❌ تم تحديد وحذف المعاملة {transaction_id} كفاشلة")

        except Exception as e:
            logger.error(f"Error marking and deleting failed transaction: {str(e)}")

    async def cleanup_failed_transactions(self):
        """تنظيف المعاملات الفاشلة بشكل دوري"""
        try:
            logger.info(f"⏰ بدء عملية التنظيف الدوري في {datetime.now().isoformat()}")

            # الحصول على قاعدة البيانات من الخدمة
            service = get_transaction_service()
            if not service or not service.db:
                logger.error("خدمة المعاملات غير متاحة للتنظيف")
                return

            transactions_ref = service.db.collection('transactions')
            # استخدام FieldFilter لتجنب التحذير
            failed_transactions = transactions_ref.where(
                filter=FieldFilter('status', '==', 'failed')
            ).get()

            deleted_count = 0
            for transaction in failed_transactions:
                try:
                    if transaction.id == '_metadata':
                        continue

                    # التحقق من حالة المعاملة قبل الحذف
                    transaction_data = transaction.to_dict()
                    if transaction_data.get('status') != 'failed':
                        logger.warning(f"⚠️ المعاملة {transaction.id} ليست فاشلة. الحالة الحالية: {transaction_data.get('status')}")
                        continue

                    # حذف المعاملة
                    transaction.reference.delete()
                    deleted_count += 1
                    logger.info(f"✅ تم حذف المعاملة الفاشلة: {transaction.id}")

                except Exception as tx_error:
                    logger.error(f"❌ خطأ في حذف المعاملة {transaction.id}: {str(tx_error)}")
                    continue

            if deleted_count > 0:
                logger.info(f"🗑️ تم حذف {deleted_count} معاملة فاشلة")
            else:
                logger.info("⚠️ لا توجد معاملات فاشلة للحذف")

            logger.info(f"⏰ انتهاء عملية التنظيف الدوري في {datetime.now().isoformat()}")

        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف المعاملات الفاشلة: {str(e)}")


class TransactionService:
    """خدمة إدارة المعاملات المالية"""
    
    def __init__(self, db_client=None, subscription_system=None):
        """تهيئة خدمة المعاملات"""
        self.db = db_client or firestore.client()
        self.subscription_system = subscription_system
        
    async def create_payment_transaction(self, user_id: str, binance_txid: str = None) -> str:
        """إنشاء معاملة دفع جديدة"""
        try:
            # حذف المعاملات المعلقة القديمة للمستخدم
            transactions_ref = self.db.collection('transactions')
            old_transactions = transactions_ref.where(filter=FieldFilter('user_id', '==', user_id)).where(filter=FieldFilter('status', '==', 'pending')).get()

            for old_tx in old_transactions:
                # حذف المعاملات المعلقة التي مر عليها أكثر من ساعة
                created_at = datetime.fromisoformat(old_tx.to_dict().get('created_at', '2020-01-01'))
                if datetime.now() - created_at > timedelta(hours=1):
                    old_tx.reference.delete()
                    logger.info(f"تم حذف معاملة معلقة قديمة: {old_tx.id}")

            transaction_data = {
                'user_id': user_id,
                'amount': 5.0,  # USDT
                'created_at': datetime.now().isoformat(),
                'status': 'pending',
                'used': False,
                'expires_at': (datetime.now() + timedelta(hours=1)).isoformat()  # تنتهي بعد ساعة
            }

            if binance_txid:
                # استخدام معرف المعاملة من Binance
                transaction_ref = transactions_ref.document(binance_txid)
                transaction_data['binance_txid'] = binance_txid
            else:
                # إنشاء معرف مؤقت
                transaction_ref = transactions_ref.document()

            transaction_ref.set(transaction_data)
            return transaction_ref.id

        except Exception as e:
            logger.error(f"خطأ في إنشاء معاملة دفع: {str(e)}")
            return None

    async def update_transaction_with_binance_id(self, temp_txid: str, binance_txid: str) -> bool:
        """تحديث معرف المعاملة المؤقت بمعرف Binance الفعلي"""
        try:
            # نسخ بيانات المعاملة المؤقتة
            temp_ref = self.db.collection('transactions').document(temp_txid)
            temp_data = temp_ref.get()

            if temp_data.exists:
                transaction_data = temp_data.to_dict()

                # إنشاء وثيقة جديدة بمعرف Binance
                new_ref = self.db.collection('transactions').document(binance_txid)
                transaction_data['binance_txid'] = binance_txid
                new_ref.set(transaction_data)

                # حذف المعاملة المؤقتة
                temp_ref.delete()

                logger.info(f"Transaction ID updated from {temp_txid} to {binance_txid}")
                return True

            return False
        except Exception as e:
            logger.error(f"Error updating transaction ID: {str(e)}")
            return False

    async def cancel_transaction(self, transaction_id: str, user_id: str) -> bool:
        """إلغاء المعاملة وتحديث حالتها في Firestore"""
        try:
            transaction_ref = self.db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get()

            if transaction_data.exists:
                transaction = transaction_data.to_dict()
                if transaction['user_id'] == user_id:
                    # تحديث حالة المعاملة
                    transaction_ref.update({
                        'status': 'cancelled',
                        'cancelled_at': datetime.now().isoformat(),
                        'used': False
                    })

                    # حذف المعاملة بعد التحديث
                    transaction_ref.delete()

                    logger.info(f"تم إلغاء وحذف المعاملة {transaction_id} بنجاح")
                    return True
            return False
        except Exception as e:
            logger.error(f"خطأ في إلغاء المعاملة {transaction_id}: {str(e)}")
            return False

    async def verify_payment_transaction(self, user_id: str, transaction_id: str, lang: str = 'ar') -> bool:
        """التحقق من عملية الدفع وإضافة الاشتراك"""
        try:
            # التحقق من وجود المعاملة في Firestore
            transaction_ref = self.db.collection('transactions').document(transaction_id)
            transaction_data = transaction_ref.get()

            if not transaction_data.exists:
                logger.error(f"Transaction {transaction_id} not found")
                return False

            transaction = transaction_data.to_dict()

            # التحقق من أن المعاملة لم يتم استخدامها من قبل
            if transaction.get('used', False):
                logger.error(f"Transaction {transaction_id} already used")
                return False

            # التحقق من معرف المستخدم
            if transaction.get('user_id') != user_id:
                logger.error(f"Transaction {transaction_id} belongs to different user")
                return False

            # التحقق من المبلغ
            if transaction.get('amount') != 5:
                logger.error(f"Invalid amount for transaction {transaction_id}")
                return False

            # تحديث حالة المعاملة
            transaction_ref.update({
                'used': True,
                'activation_date': datetime.now().isoformat()
            })

            # إضافة الاشتراك
            if self.subscription_system:
                return self.subscription_system.add_subscription(user_id, lang, transaction_id)
            else:
                logger.warning("Subscription system not available")
                return False

        except Exception as e:
            logger.error(f"Error verifying payment: {str(e)}")
            return False

    async def verify_paypal_transaction(self, user_id: str, amount: float = 5.0, transaction_id: str = None) -> bool:
        """التحقق من معاملة PayPal"""
        try:
            # التحقق من وضع التطوير من قاعدة البيانات
            is_dev_mode = system_settings.get("DEV_MODE", False)

            # إذا لم تكن الإعدادات موجودة في قاعدة البيانات، نستخدم المتغيرات البيئية
            if is_dev_mode is None:
                from services.system_settings import SystemConfig
                is_dev_mode = SystemConfig.get_env_var("DEV_MODE", "false").lower() == "true"
                system_settings.set("DEV_MODE", is_dev_mode)

            # في وضع التطوير، نقوم بمحاكاة نجاح العملية
            if is_dev_mode:
                logger.info(f"🔧 وضع التطوير: محاكاة نجاح معاملة PayPal للمستخدم {user_id}")
                return True

            # إذا تم تمرير معرف المعاملة، نتحقق من وجودها في قاعدة البيانات أولاً
            if transaction_id:
                transaction_ref = self.db.collection('transactions').document(transaction_id)
                transaction_data = transaction_ref.get()

                if transaction_data.exists:
                    transaction = transaction_data.to_dict()

                    # التحقق من أن المعاملة تخص المستخدم المحدد
                    if transaction.get('user_id') == user_id and transaction.get('status') == 'completed':
                        logger.info(f"✅ تم التحقق من معاملة PayPal {transaction_id} للمستخدم {user_id} من قاعدة البيانات")
                        return True

            # الحصول على بيانات الاعتماد من قاعدة البيانات
            paypal_client_id = system_settings.get("PAYPAL_CLIENT_ID", sensitive=True)
            paypal_secret = system_settings.get("PAYPAL_CLIENT_SECRET", sensitive=True)
            is_sandbox = system_settings.get("PAYPAL_SANDBOX_MODE", False)  # تغيير القيمة الافتراضية إلى False (وضع الإنتاج)

            # إذا لم تكن الإعدادات موجودة في قاعدة البيانات، نستخدم المتغيرات البيئية
            if not paypal_client_id:
                from services.system_settings import SystemConfig
                paypal_client_id = SystemConfig.get_env_var("PAYPAL_CLIENT_ID")
                system_settings.set("PAYPAL_CLIENT_ID", paypal_client_id, sensitive=True)

            if not paypal_secret:
                from services.system_settings import SystemConfig
                paypal_secret = SystemConfig.get_env_var("PAYPAL_CLIENT_SECRET")
                system_settings.set("PAYPAL_CLIENT_SECRET", paypal_secret, sensitive=True)

            if is_sandbox is None:
                from services.system_settings import SystemConfig
                is_sandbox = SystemConfig.get_env_var("PAYPAL_SANDBOX_MODE", "false").lower() == "true"  # تغيير القيمة الافتراضية إلى false
                system_settings.set("PAYPAL_SANDBOX_MODE", is_sandbox)

            # إنشاء مدير PayPal
            from integrations.paypal_manager import PayPalManager
            paypal_manager = PayPalManager(
                paypal_client_id,
                paypal_secret,
                is_sandbox
            )

            # الحصول على رمز الوصول
            auth = aiohttp.BasicAuth(paypal_client_id, paypal_secret)
            base_url = "https://api-m.sandbox.paypal.com" if is_sandbox else "https://api-m.paypal.com"

            # الحصول على رمز الوصول
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{base_url}/v1/oauth2/token',
                    auth=auth,
                    data={'grant_type': 'client_credentials'},
                    timeout=30
                ) as response:
                    if response.status != 200:
                        logger.error(f"خطأ في الحصول على رمز الوصول: {await response.text()}")
                        return False

                    token_data = await response.json()
                    access_token = token_data['access_token']

            # تعديل نطاق البحث عن المعاملات (آخر 24 ساعة)
            end_time = datetime.now(pytz.UTC)
            start_time = end_time - timedelta(days=1)

            # تنسيق التواريخ بشكل صحيح
            start_date = start_time.strftime('%Y-%m-%dT%H:%M:%S-0000')
            end_date = end_time.strftime('%Y-%m-%dT%H:%M:%S-0000')

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            # البحث عن المعاملات
            async with aiohttp.ClientSession() as session:
                params = {
                    'start_date': start_date,
                    'end_date': end_date,
                    'fields': 'all',
                    'page_size': 100,
                    'page': 1
                }

                logger.info(f"جاري البحث عن المعاملات من {start_date} إلى {end_date}")

                async with session.get(
                    f'{base_url}/v1/reporting/transactions',
                    headers=headers,
                    params=params,
                    timeout=30
                ) as response:
                    if response.status != 200:
                        logger.error(f"خطأ في البحث عن المعاملات: {await response.text()}")
                        return False

                    transactions = await response.json()

                    # البحث عن المعاملة المطلوبة
                    for transaction in transactions.get('transaction_details', []):
                        transaction_info = transaction.get('transaction_info', {})
                        payer_info = transaction.get('payer_info', {})

                        # محاولة العثور على معرف المستخدم في أماكن مختلفة
                        note_user_id = payer_info.get('note', '').strip()
                        custom_field = transaction_info.get('custom_field', '').strip()
                        transaction_note = transaction_info.get('transaction_note', '').strip()

                        found_user_id = note_user_id or custom_field or transaction_note

                        # التحقق من المعاملة
                        if (
                            transaction_info.get('transaction_status') == 'S' and  # ناجحة
                            abs(float(transaction_info.get('transaction_amount', {}).get('value', 0)) - amount) < 0.01 and  # المبلغ صحيح
                            found_user_id == user_id
                        ):
                            # حفظ المعاملة في قاعدة البيانات إذا لم يكن لدينا معرف معاملة
                            if not transaction_id:
                                new_transaction_id = transaction_info.get('transaction_id', '')
                                if new_transaction_id:
                                    transaction_ref = self.db.collection('transactions').document(new_transaction_id)
                                    transaction_data = {
                                        'user_id': user_id,
                                        'amount': amount,
                                        'created_at': datetime.now().isoformat(),
                                        'status': 'completed',
                                        'completed_at': datetime.now().isoformat(),
                                        'used': False,
                                        'payment_method': 'paypal',
                                        'paypal_transaction_id': new_transaction_id,
                                        'verification_method': 'manual'
                                    }
                                    transaction_ref.set(transaction_data)
                                    transaction_id = new_transaction_id
                            elif transaction_id:
                                # تحديث المعاملة الموجودة
                                transaction_ref = self.db.collection('transactions').document(transaction_id)
                                transaction_ref.update({
                                    'status': 'completed',
                                    'completed_at': datetime.now().isoformat(),
                                    'paypal_transaction_id': transaction_info.get('transaction_id', ''),
                                    'verification_method': 'manual'
                                })

                            logger.info(f"✅ تم العثور على معاملة PayPal صالحة للمستخدم {user_id}")
                            return True

            # إذا لم يتم العثور على معاملة، نتحقق من وجود معاملات معلقة للمستخدم
            if transaction_id:
                # تحديث حالة المعاملة للتحقق منها مرة أخرى لاحقًا
                transaction_ref = self.db.collection('transactions').document(transaction_id)
                transaction_data = transaction_ref.get()

                if transaction_data.exists:
                    transaction = transaction_data.to_dict()
                    verification_attempts = transaction.get('verification_attempts', 0) + 1

                    transaction_ref.update({
                        'verification_attempts': verification_attempts,
                        'last_verification': datetime.now().isoformat()
                    })

                    # إذا كان عدد المحاولات أقل من الحد الأقصى، نضيف المعاملة للتحقق التلقائي
                    if verification_attempts < 12:  # 12 محاولة (ساعة واحدة)
                        logger.info(f"⏳ سيتم التحقق من المعاملة {transaction_id} للمستخدم {user_id} تلقائيًا لاحقًا")
                        # سيتم التحقق منها تلقائيًا لاحقًا
                        return False
                    else:
                        # تحديث حالة المعاملة إلى فاشلة
                        transaction_ref.update({
                            'status': 'failed',
                            'failure_reason': 'تجاوز الحد الأقصى لمحاولات التحقق'
                        })
                        logger.warning(f"❌ تم تجاوز الحد الأقصى لمحاولات التحقق للمعاملة {transaction_id}")
                        return False

            logger.warning(f"❌ لم يتم العثور على معاملة PayPal صالحة للمستخدم {user_id}")
            return False

        except aiohttp.ClientError as e:
            logger.error(f"خطأ في الاتصال مع PayPal: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"خطأ غير متوقع في التحقق من معاملة PayPal: {str(e)}")
            return False

    async def extend_transaction(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تمديد صلاحية معاملة معلقة"""
        try:
            # الحصول على معرف المعاملة من بيانات الاستدعاء
            callback_data = update.callback_query.data
            transaction_id = callback_data.replace("extend_transaction_", "")

            # الحصول على معرف المستخدم
            user_id = str(update.effective_user.id)

            # الحصول على المعاملة من قاعدة البيانات
            transaction_ref = self.db.collection('transactions').document(transaction_id)
            transaction_doc = transaction_ref.get()

            if not transaction_doc.exists:
                await update.callback_query.answer("❌ المعاملة غير موجودة", show_alert=True)
                return

            transaction_data = transaction_doc.to_dict()

            # التحقق من أن المعاملة تخص المستخدم الحالي
            if transaction_data.get('user_id') != user_id:
                await update.callback_query.answer("❌ ليس لديك صلاحية لتمديد هذه المعاملة", show_alert=True)
                return

            # التحقق من حالة المعاملة
            if transaction_data.get('status') != 'pending':
                await update.callback_query.answer("❌ لا يمكن تمديد المعاملة لأنها ليست معلقة", show_alert=True)
                return

            # تمديد صلاحية المعاملة لمدة ساعة إضافية
            new_expiry = datetime.now() + timedelta(hours=1)
            transaction_ref.update({
                'expires_at': new_expiry.isoformat(),
                'notification_sent': False  # إعادة تعيين حالة الإشعار
            })

            # إرسال تأكيد للمستخدم
            await update.callback_query.answer("✅ تم تمديد صلاحية المعاملة لمدة ساعة إضافية", show_alert=True)

            # تحديث الرسالة
            message = f"✅ تم تمديد صلاحية المعاملة بنجاح حتى {new_expiry.strftime('%H:%M:%S')}.\n\n"
            message += "يمكنك الآن إكمال عملية الدفع من خلال الضغط على الزر أدناه."

            keyboard = [
                [
                    InlineKeyboardButton("💳 إكمال الدفع", callback_data=f"complete_payment_{transaction_id}")
                ]
            ]

            await update.callback_query.message.edit_text(
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

            logger.info(f"🔄 تم تمديد صلاحية المعاملة {transaction_id} للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في تمديد صلاحية المعاملة: {str(e)}")
            await update.callback_query.answer("❌ حدث خطأ أثناء تمديد صلاحية المعاملة", show_alert=True)

    async def complete_payment(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إكمال عملية الدفع لمعاملة معلقة"""
        try:
            # الحصول على معرف المعاملة من بيانات الاستدعاء
            callback_data = update.callback_query.data
            transaction_id = callback_data.replace("complete_payment_", "")

            # الحصول على معرف المستخدم
            user_id = str(update.effective_user.id)

            # الحصول على المعاملة من قاعدة البيانات
            transaction_ref = self.db.collection('transactions').document(transaction_id)
            transaction_doc = transaction_ref.get()

            if not transaction_doc.exists:
                await update.callback_query.answer("❌ المعاملة غير موجودة", show_alert=True)
                return

            transaction_data = transaction_doc.to_dict()

            # التحقق من أن المعاملة تخص المستخدم الحالي
            if transaction_data.get('user_id') != user_id:
                await update.callback_query.answer("❌ ليس لديك صلاحية لإكمال هذه المعاملة", show_alert=True)
                return

            # التحقق من حالة المعاملة
            if transaction_data.get('status') != 'pending':
                await update.callback_query.answer("❌ لا يمكن إكمال المعاملة لأنها ليست معلقة", show_alert=True)
                return

            # الحصول على رابط الدفع من المعاملة
            payment_url = transaction_data.get('payment_url')

            if not payment_url:
                # إنشاء رابط دفع جديد
                from services.handle_paypal_payment import create_paypal_payment_link

                # إرسال رسالة انتظار
                wait_message = await update.callback_query.message.edit_text(
                    "جاري إنشاء رابط دفع جديد... يرجى الانتظار."
                )

                # إنشاء رابط دفع جديد
                payment_url, order_id = await create_paypal_payment_link(user_id)

                if not payment_url:
                    await wait_message.edit_text(
                        "❌ فشل في إنشاء رابط دفع جديد. يرجى المحاولة مرة أخرى لاحقًا."
                    )
                    return

                # تحديث المعاملة برابط الدفع الجديد
                transaction_ref.update({
                    'payment_url': payment_url,
                    'order_id': order_id,
                    'updated_at': datetime.now().isoformat()
                })

            # إنشاء أزرار التحكم
            keyboard = [
                [
                    InlineKeyboardButton("💳 الدفع عبر PayPal", url=payment_url)
                ],
                [
                    InlineKeyboardButton("✅ تأكيد الدفع", callback_data=f"verify_payment_{transaction_id}")
                ]
            ]

            # إرسال رابط الدفع للمستخدم
            await update.callback_query.message.edit_text(
                text="يرجى النقر على الزر أدناه لإكمال عملية الدفع عبر PayPal.\n\n"
                     "بعد إكمال عملية الدفع، انقر على زر 'تأكيد الدفع' للتحقق من حالة الدفع وتفعيل اشتراكك.",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

            logger.info(f"💳 تم إرسال رابط الدفع للمستخدم {user_id} للمعاملة {transaction_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في إكمال عملية الدفع: {str(e)}")
            await update.callback_query.answer("❌ حدث خطأ أثناء إكمال عملية الدفع", show_alert=True)

    async def verify_payment_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """التحقق من حالة الدفع لمعاملة معلقة"""
        try:
            # الحصول على معرف المعاملة من بيانات الاستدعاء
            callback_data = update.callback_query.data
            transaction_id = callback_data.replace("verify_payment_", "")

            # الحصول على معرف المستخدم
            user_id = str(update.effective_user.id)

            # إرسال رسالة انتظار
            await update.callback_query.answer("جاري التحقق من حالة الدفع... يرجى الانتظار.")

            # تحديث الرسالة
            wait_message = await update.callback_query.message.edit_text(
                "⏳ جاري التحقق من حالة الدفع... يرجى الانتظار."
            )

            # التحقق من حالة الدفع
            is_verified = await self.verify_paypal_transaction(user_id, 5.0, transaction_id)

            if is_verified:
                # تم التحقق من الدفع بنجاح
                success_keyboard = [[
                    InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back_to_main")
                ]]

                await wait_message.edit_text(
                    "✅ تم التحقق من الدفع بنجاح وتفعيل اشتراكك!\n\n"
                    "يمكنك الآن الاستمتاع بجميع ميزات الاشتراك المدفوع.",
                    reply_markup=InlineKeyboardMarkup(success_keyboard)
                )

                logger.info(f"✅ تم التحقق من الدفع بنجاح للمستخدم {user_id} للمعاملة {transaction_id}")
            else:
                # فشل في التحقق من الدفع
                # الحصول على المعاملة من قاعدة البيانات
                transaction_ref = self.db.collection('transactions').document(transaction_id)
                transaction_doc = transaction_ref.get()

                if not transaction_doc.exists:
                    await wait_message.edit_text(
                        "❌ المعاملة غير موجودة. يرجى المحاولة مرة أخرى لاحقًا."
                    )
                    return

                transaction_data = transaction_doc.to_dict()
                payment_url = transaction_data.get('payment_url')

                # إنشاء أزرار التحكم
                keyboard = []

                # إضافة زر الدفع فقط إذا كان رابط الدفع متوفر
                if payment_url:
                    keyboard.append([
                        InlineKeyboardButton("💳 الدفع عبر PayPal", url=payment_url)
                    ])

                # إضافة زر التحقق مرة أخرى
                keyboard.append([
                    InlineKeyboardButton("🔄 التحقق مرة أخرى", callback_data=f"verify_payment_{transaction_id}")
                ])

                # إضافة زر العودة للقائمة الرئيسية
                keyboard.append([
                    InlineKeyboardButton("🔙 القائمة الرئيسية", callback_data="back_to_main")
                ])

                await wait_message.edit_text(
                    "❌ لم يتم التحقق من الدفع بعد. يرجى التأكد من إكمال عملية الدفع عبر PayPal ثم المحاولة مرة أخرى.\n\n"
                    "إذا كنت قد أكملت عملية الدفع بالفعل، يرجى الانتظار بضع دقائق ثم المحاولة مرة أخرى.",
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

                logger.warning(f"❌ فشل في التحقق من الدفع للمستخدم {user_id} للمعاملة {transaction_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من حالة الدفع: {str(e)}")
            await update.callback_query.message.edit_text(
                f"❌ حدث خطأ أثناء التحقق من حالة الدفع: {str(e)}"
            )

    async def cleanup_pending_transactions(self, context: CallbackContext):
        """تنظيف المعاملات المعلقة القديمة"""
        try:
            logger.info(f"⏰ بدء عملية التنظيف الدوري في {datetime.now().isoformat()}")
            transactions_ref = self.db.collection('transactions')
            pending_transactions = transactions_ref.where(filter=FieldFilter('status', '==', 'pending')).get()

            count = 0
            for tx in pending_transactions:
                tx_data = tx.to_dict()
                created_at = datetime.fromisoformat(tx_data.get('created_at', '2020-01-01'))

                # حذف المعاملات المعلقة التي مر عليها أكثر من ساعة
                if datetime.now() - created_at > timedelta(hours=1):
                    tx.reference.delete()
                    count += 1

            if count > 0:
                logger.info(f"🗑️ تم حذف {count} من المعاملات المعلقة")
            else:
                logger.info(f"⚠️ لا توجد معاملات معلقة للحذف")

            logger.info(f"⏰ انتهاء عملية التنظيف الدوري في {datetime.now().isoformat()}")
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف المعاملات المعلقة: {str(e)}")

    async def notify_expiring_transactions(self, context: CallbackContext):
        """إرسال إشعارات للمستخدمين قبل انتهاء صلاحية معاملاتهم"""
        try:
            logger.info(f"⏰ بدء عملية إرسال إشعارات المعاملات المنتهية في {datetime.now().isoformat()}")
            transactions_ref = self.db.collection('transactions')

            # الحصول على المعاملات المعلقة التي ستنتهي خلال الساعة القادمة
            now = datetime.now()
            one_hour_later = now + timedelta(minutes=30)  # إشعار قبل 30 دقيقة من انتهاء الصلاحية

            # البحث عن المعاملات المعلقة التي ستنتهي قريباً
            pending_transactions = transactions_ref.where(
                filter=FieldFilter('status', '==', 'pending')
            ).get()

            notification_count = 0
            for tx in pending_transactions:
                tx_data = tx.to_dict()
                tx_id = tx.id
                user_id = tx_data.get('user_id')

                # التحقق من وقت انتهاء الصلاحية
                if 'expires_at' in tx_data:
                    expires_at = datetime.fromisoformat(tx_data.get('expires_at'))

                    # إذا كانت المعاملة ستنتهي خلال الـ 30 دقيقة القادمة
                    if now < expires_at <= one_hour_later:
                        # التحقق من أن المستخدم لم يتلق إشعاراً بالفعل
                        if not tx_data.get('notification_sent', False):
                            # إرسال إشعار للمستخدم
                            await self.send_transaction_expiry_notification(context, user_id, tx_id, expires_at)

                            # تحديث حالة الإشعار
                            tx.reference.update({
                                'notification_sent': True,
                                'notification_time': now.isoformat()
                            })

                            notification_count += 1

            if notification_count > 0:
                logger.info(f"📩 تم إرسال {notification_count} إشعار للمستخدمين حول المعاملات المنتهية قريباً")
            else:
                logger.info("ℹ️ لا توجد معاملات ستنتهي قريباً لإرسال إشعارات")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعارات المعاملات المنتهية: {str(e)}")

    async def send_transaction_expiry_notification(self, context: ContextTypes.DEFAULT_TYPE, user_id: str, transaction_id: str, expires_at: datetime):
        """إرسال إشعار للمستخدم قبل انتهاء صلاحية المعاملة"""
        try:
            # حساب الوقت المتبقي
            now = datetime.now()
            time_left = expires_at - now
            minutes_left = int(time_left.total_seconds() / 60)

            # إنشاء رسالة الإشعار
            message = f"⚠️ *تنبيه*: لديك معاملة دفع معلقة ستنتهي خلال {minutes_left} دقيقة.\n\n"
            message += "يمكنك تمديد صلاحية المعاملة أو إكمال عملية الدفع من خلال الضغط على الزر أدناه."

            # إنشاء أزرار التحكم
            keyboard = [
                [
                    InlineKeyboardButton("🔄 تمديد الصلاحية", callback_data=f"extend_transaction_{transaction_id}")
                ],
                [
                    InlineKeyboardButton("💳 إكمال الدفع", callback_data=f"complete_payment_{transaction_id}")
                ]
            ]

            # إرسال الإشعار
            await context.bot.send_message(
                chat_id=user_id,
                text=message,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode=ParseMode.MARKDOWN
            )

            logger.info(f"📩 تم إرسال إشعار انتهاء صلاحية المعاملة للمستخدم {user_id} للمعاملة {transaction_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار انتهاء صلاحية المعاملة للمستخدم {user_id}: {str(e)}")

    async def handle_payment_verification(self, update: Update, context: CallbackContext):
        """معالجة التحقق من الدفع"""
        try:
            user_id = str(update.effective_user.id)
            query = update.callback_query
            callback_data = query.data

            # الحصول على إعدادات المستخدم
            if self.subscription_system:
                settings = self.subscription_system.get_user_settings(user_id)
                lang = settings.get('lang', 'ar')
            else:
                lang = 'ar'

            # التحقق من وجود معرف معاملة محدد في callback_data
            order_id = None
            if callback_data.startswith('verify_payment_'):
                order_id = callback_data.split('_')[2]
                logger.info(f"التحقق من معاملة محددة: {order_id} للمستخدم {user_id}")

            # إرسال رسالة انتظار
            await query.answer(
                "جاري التحقق من الدفع..." if lang == 'ar' else
                "Verifying payment...",
                show_alert=False
            )

            # إرسال رسالة انتظار
            wait_message = await query.edit_message_text(
                "⏳ جاري التحقق من الدفع... يرجى الانتظار" if lang == 'ar' else
                "⏳ Verifying payment... Please wait"
            )

            # إذا كان هناك معرف معاملة محدد، نتحقق منه مباشرة
            if order_id:
                # التحقق من المعاملة في قاعدة البيانات
                transaction_ref = self.db.collection('transactions').document(order_id)
                transaction_data = transaction_ref.get()

                if not transaction_data.exists:
                    logger.warning(f"المعاملة {order_id} غير موجودة في قاعدة البيانات")
                    # إنشاء رسالة خطأ
                    error_message = (
                        "⚠️ لم يتم العثور على المعاملة المحددة. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                        "⚠️ The specified transaction was not found. Please try again."
                    )

                    await wait_message.edit_text(
                        error_message,
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton(
                                "🔍 التحقق مرة أخرى" if lang == 'ar' else "🔍 Verify Again",
                                callback_data='verify_payment'
                            )],
                            [InlineKeyboardButton(
                                "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                                callback_data='back_to_main'
                            )]
                        ])
                    )
                    return

                # التحقق من أن المعاملة تخص المستخدم الحالي
                transaction = transaction_data.to_dict()
                if transaction.get('user_id') != user_id:
                    logger.warning(f"المعاملة {order_id} تخص مستخدم آخر")
                    # إنشاء رسالة خطأ
                    error_message = (
                        "⚠️ هذه المعاملة تخص مستخدم آخر. يرجى التحقق من المعاملة الصحيحة." if lang == 'ar' else
                        "⚠️ This transaction belongs to another user. Please check the correct transaction."
                    )

                    await wait_message.edit_text(
                        error_message,
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton(
                                "🔍 التحقق مرة أخرى" if lang == 'ar' else "🔍 Verify Again",
                                callback_data='verify_payment'
                            )],
                            [InlineKeyboardButton(
                                "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                                callback_data='back_to_main'
                            )]
                        ])
                    )
                    return

                # التحقق من حالة المعاملة
                if transaction.get('status') == 'completed' and transaction.get('used', False):
                    logger.info(f"المعاملة {order_id} تم استخدامها بالفعل")
                    # إنشاء رسالة نجاح
                    success_message = (
                        "✅ تم تأكيد الدفع بنجاح!\n\n"
                        "تم تفعيل اشتراكك وبإمكانك الآن الاستمتاع بجميع الميزات المتقدمة."
                    ) if lang == 'ar' else (
                        "✅ Payment confirmed successfully!\n\n"
                        "Your subscription has been activated and you can now enjoy all advanced features."
                    )

                    await wait_message.edit_text(
                        success_message,
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton(
                                "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                                callback_data='back_to_main'
                            )]
                        ])
                    )
                    return

            # التحقق من المعاملة في PayPal
            verification_result = await self.verify_paypal_transaction(user_id, amount=5.0, transaction_id=order_id)

            if verification_result:
                # تفعيل الاشتراك
                if self.subscription_system and await self.subscription_system.activate_subscription(user_id, order_id, lang):
                    # إرسال رسالة نجاح
                    success_message = (
                        "✅ تم تأكيد الدفع بنجاح!\n\n"
                        "تم تفعيل اشتراكك وبإمكانك الآن الاستمتاع بجميع الميزات المتقدمة."
                    ) if lang == 'ar' else (
                        "✅ Payment confirmed successfully!\n\n"
                        "Your subscription has been activated and you can now enjoy all advanced features."
                    )

                    await wait_message.edit_text(
                        success_message,
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton(
                                "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                                callback_data='back_to_main'
                            )]
                        ])
                    )
                    return
            else:
                # تحديث رسالة الانتظار - المعاملة معلقة وسيتم التحقق منها تلقائيًا
                error_message = (
                    "⚠️ لم يتم العثور على دفع مؤكد. يرجى التأكد من إتمام عملية الدفع والمحاولة مرة أخرى.\n\n"
                    "ملاحظات:\n"
                    "• قد يستغرق تأكيد الدفع بضع دقائق\n"
                    "• تأكد من إكمال عملية الدفع بالكامل\n"
                    "• إذا كنت قد أكملت الدفع، يرجى الانتظار قليلاً ثم المحاولة مرة أخرى"
                ) if lang == 'ar' else (
                    "⚠️ No confirmed payment found. Please make sure you've completed the payment process and try again.\n\n"
                    "Notes:\n"
                    "• Payment confirmation may take a few minutes\n"
                    "• Make sure you've completed the entire payment process\n"
                    "• If you've completed payment, please wait a moment and try again"
                )

                keyboard = [
                    [InlineKeyboardButton(
                        "🔍 التحقق مرة أخرى" if lang == 'ar' else "🔍 Verify Again",
                        callback_data='verify_payment' if not order_id else f'verify_payment_{order_id}'
                    )],
                    [InlineKeyboardButton(
                        "💳 الدفع عبر PayPal" if lang == 'ar' else "💳 Pay with PayPal",
                        callback_data='payment_paypal'
                    )],
                    [InlineKeyboardButton(
                        "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                        callback_data='back_to_main'
                    )]
                ]

                await wait_message.edit_text(
                    error_message,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

        except Exception as e:
            logger.error(f"خطأ في التحقق من الدفع: {str(e)}")
            error_message = (
                "❌ حدث خطأ أثناء التحقق من الدفع. الرجاء المحاولة مرة أخرى" if lang == 'ar' else
                "❌ An error occurred while verifying payment. Please try again"
            )
            if 'wait_message' in locals():
                await wait_message.edit_text(
                    error_message,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton(
                            "🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                            callback_data='back_to_main'
                        )]
                    ])
                )
            else:
                await query.answer(error_message, show_alert=True)


# إنشاء مثيل عام من خدمة المعاملات
_transaction_service_instance = None

def get_transaction_service(db_client=None, subscription_system=None):
    """الحصول على مثيل خدمة المعاملات (Singleton pattern)"""
    global _transaction_service_instance
    if _transaction_service_instance is None:
        _transaction_service_instance = TransactionService(db_client, subscription_system)
    return _transaction_service_instance

def initialize_transaction_service(db_client, subscription_system):
    """تهيئة خدمة المعاملات مع قاعدة البيانات ونظام الاشتراكات"""
    global _transaction_service_instance
    _transaction_service_instance = TransactionService(db_client, subscription_system)
    logger.info("✅ تم تهيئة خدمة المعاملات بنجاح")
    return _transaction_service_instance

# دوال مساعدة للتوافق مع الكود الحالي
async def create_payment_transaction(user_id: str, binance_txid: str = None) -> str:
    """دالة مساعدة لإنشاء معاملة دفع جديدة"""
    service = get_transaction_service()
    return await service.create_payment_transaction(user_id, binance_txid)

async def update_transaction_with_binance_id(temp_txid: str, binance_txid: str) -> bool:
    """دالة مساعدة لتحديث معرف المعاملة المؤقت بمعرف Binance الفعلي"""
    service = get_transaction_service()
    return await service.update_transaction_with_binance_id(temp_txid, binance_txid)

async def cancel_transaction(transaction_id: str, user_id: str) -> bool:
    """دالة مساعدة لإلغاء المعاملة وتحديث حالتها في Firestore"""
    service = get_transaction_service()
    return await service.cancel_transaction(transaction_id, user_id)

async def verify_payment_transaction(user_id: str, transaction_id: str, lang: str = 'ar') -> bool:
    """دالة مساعدة للتحقق من عملية الدفع وإضافة الاشتراك"""
    service = get_transaction_service()
    return await service.verify_payment_transaction(user_id, transaction_id, lang)

async def verify_paypal_transaction(user_id: str, amount: float = 5.0, transaction_id: str = None) -> bool:
    """دالة مساعدة للتحقق من معاملة PayPal"""
    service = get_transaction_service()
    return await service.verify_paypal_transaction(user_id, amount, transaction_id)

# دوال مساعدة جديدة للمعاملات المتقدمة
async def extend_transaction(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """دالة مساعدة لتمديد صلاحية معاملة معلقة"""
    service = get_transaction_service()
    return await service.extend_transaction(update, context)

async def complete_payment(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """دالة مساعدة لإكمال عملية الدفع لمعاملة معلقة"""
    service = get_transaction_service()
    return await service.complete_payment(update, context)

async def verify_payment(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """دالة مساعدة للتحقق من حالة الدفع لمعاملة معلقة"""
    service = get_transaction_service()
    return await service.verify_payment_handler(update, context)

async def cleanup_pending_transactions(context: CallbackContext):
    """دالة مساعدة لتنظيف المعاملات المعلقة القديمة"""
    service = get_transaction_service()
    return await service.cleanup_pending_transactions(context)

async def notify_expiring_transactions(context: CallbackContext):
    """دالة مساعدة لإرسال إشعارات للمستخدمين قبل انتهاء صلاحية معاملاتهم"""
    service = get_transaction_service()
    return await service.notify_expiring_transactions(context)

async def send_transaction_expiry_notification(context: ContextTypes.DEFAULT_TYPE, user_id: str, transaction_id: str, expires_at: datetime):
    """دالة مساعدة لإرسال إشعار للمستخدم قبل انتهاء صلاحية المعاملة"""
    service = get_transaction_service()
    return await service.send_transaction_expiry_notification(context, user_id, transaction_id, expires_at)

async def handle_payment_verification(update: Update, context: CallbackContext):
    """دالة مساعدة لمعالجة التحقق من الدفع"""
    service = get_transaction_service()
    return await service.handle_payment_verification(update, context)

# إنشاء مثيل عام من مدير المعاملات
_transaction_manager_instance = None

def get_transaction_manager():
    """الحصول على مثيل مدير المعاملات (Singleton pattern)"""
    global _transaction_manager_instance
    if _transaction_manager_instance is None:
        _transaction_manager_instance = TransactionManager()
    return _transaction_manager_instance

async def initialize_transaction_manager():
    """تهيئة مدير المعاملات وبدء جدولة التنظيف"""
    manager = get_transaction_manager()
    await manager.initialize()
    logger.info("✅ تم تهيئة مدير المعاملات بنجاح")
    return manager
