"""
وظائف للحصول على بيانات السوق باستخدام API المستخدم
"""

import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from binance.client import Client

# إعداد السجل
logger = logging.getLogger(__name__)

async def get_market_data_with_user_api(client, symbol: str, timeframe: str = '1d', limit: int = 100, target_currency: str = 'USD') -> Optional[Dict[str, Any]]:
    """
    الحصول على بيانات السوق باستخدام API المستخدم

    Args:
        client: عميل Binance
        symbol: رمز العملة
        timeframe: الإطار الزمني
        limit: عدد الشموع المطلوبة
        target_currency: العملة المستهدفة للتحويل (الافتراضية: USD)

    Returns:
        قاموس يحتوي على بيانات السوق أو None إذا فشل الحصول على البيانات
    """
    try:
        # تنظيف وتحقق من رمز العملة
        original_symbol = symbol
        symbol = symbol.upper().strip()

        # التأكد من أن الرمز يحتوي على USDT
        if not symbol.endswith('USDT'):
            symbol = f"{symbol}USDT"

        logger.info(f"معالجة رمز العملة: {original_symbol} -> {symbol}")

        # التحقق من صحة الرمز قبل استدعاء API
        try:
            # محاولة الحصول على معلومات الرمز للتأكد من صحته
            exchange_info = client.get_exchange_info()
            valid_symbols = [s['symbol'] for s in exchange_info['symbols'] if s['status'] == 'TRADING']

            if symbol not in valid_symbols:
                logger.error(f"الرمز {symbol} غير متاح للتداول في Binance")
                return None

            logger.info(f"تم التحقق من صحة الرمز {symbol} في Binance")

        except Exception as validation_error:
            logger.error(f"خطأ في التحقق من صحة الرمز {symbol}: {str(validation_error)}")
            return None

        # تحويل الإطار الزمني إلى تنسيق Binance
        interval_map = {
            '1m': Client.KLINE_INTERVAL_1MINUTE,
            '5m': Client.KLINE_INTERVAL_5MINUTE,
            '15m': Client.KLINE_INTERVAL_15MINUTE,
            '30m': Client.KLINE_INTERVAL_30MINUTE,
            '1h': Client.KLINE_INTERVAL_1HOUR,
            '4h': Client.KLINE_INTERVAL_4HOUR,
            '1d': Client.KLINE_INTERVAL_1DAY,
            '1w': Client.KLINE_INTERVAL_1WEEK,
            '1M': Client.KLINE_INTERVAL_1MONTH
        }

        interval = interval_map.get(timeframe, Client.KLINE_INTERVAL_1DAY)
        logger.info(f"استخدام الإطار الزمني: {timeframe} -> {interval}")

        # الحصول على بيانات الشموع
        logger.info(f"جاري الحصول على بيانات الشموع للرمز {symbol}")
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            limit=limit
        )

        if not klines:
            logger.warning(f"لم يتم العثور على بيانات لـ {symbol}")
            return None

        # تحويل البيانات إلى DataFrame
        df = pd.DataFrame(klines, columns=[
            'timestamp', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
        ])

        # تحويل أنواع البيانات
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        for col in ['open', 'high', 'low', 'close', 'volume']:
            df[col] = df[col].astype(float)

        # تعيين الفهرس
        df.set_index('timestamp', inplace=True)

        # حساب المؤشرات الفنية
        indicators = calculate_indicators(df)

        # الحصول على معلومات العملة
        try:
            logger.info(f"جاري الحصول على معلومات التيكر للرمز {symbol}")
            ticker = client.get_ticker(symbol=symbol)
            logger.info(f"تم الحصول على معلومات التيكر بنجاح للرمز {symbol}")
        except Exception as ticker_error:
            logger.error(f"خطأ في الحصول على معلومات التيكر للرمز {symbol}: {str(ticker_error)}")
            # محاولة استخدام آخر سعر من بيانات الشموع
            if len(df) > 0:
                current_price = float(df['close'].iloc[-1])
                previous_price = float(df['close'].iloc[-2]) if len(df) > 1 else current_price
                price_change = ((current_price - previous_price) / previous_price) * 100 if previous_price != 0 else 0
                logger.info(f"استخدام السعر من بيانات الشموع: {current_price}, التغير: {price_change}%")
            else:
                logger.error(f"لا توجد بيانات متاحة للرمز {symbol}")
                return None

            # إنشاء ticker مؤقت من بيانات الشموع
            ticker = {
                'lastPrice': str(current_price),
                'priceChangePercent': str(price_change),
                'volume': str(df['volume'].iloc[-1]) if len(df) > 0 else '0',
                'highPrice': str(df['high'].max()) if len(df) > 0 else str(current_price),
                'lowPrice': str(df['low'].min()) if len(df) > 0 else str(current_price)
            }

        # استخراج السعر الحالي والتغير
        try:
            current_price = float(ticker['lastPrice'])
            logger.info(f"السعر الحالي للرمز {symbol}: {current_price}")
        except (ValueError, KeyError) as e:
            logger.error(f"خطأ في استخراج السعر الحالي: {str(e)}")
            current_price = 0

        try:
            price_change = float(ticker['priceChangePercent'])
            logger.info(f"نسبة التغيير للرمز {symbol}: {price_change}%")
        except (ValueError, KeyError) as e:
            logger.error(f"خطأ في استخراج نسبة التغيير: {str(e)}")
            price_change = 0

        # إنشاء قاموس النتائج بنفس هيكل البيانات المتوقع في main.py
        market_data = {
            'symbol': symbol,
            'price': current_price,
            'price_change': price_change,
            'currency': target_currency,  # استخدام العملة المستهدفة المحددة
            'rsi': indicators['rsi'],
            'ema20': indicators['ema20'],
            'ema50': indicators['ema50'],
            'macd': indicators['macd'],
            'macd_signal': indicators['macd_signal'],
            'macd_histogram': indicators['macd_hist'],
            'bb_upper': indicators['bb_upper'],
            'bb_middle': indicators['bb_middle'],
            'bb_lower': indicators['bb_lower'],
            'stoch_k': indicators['stoch_k'],
            'stoch_d': indicators['stoch_d'],
            'adx': indicators['adx'],
            'plus_di': indicators['plus_di'],
            'minus_di': indicators['minus_di'],
            # إضافة مؤشرات Ichimoku Cloud
            'ichimoku_tenkan': indicators.get('ichimoku_tenkan', float('nan')),
            'ichimoku_kijun': indicators.get('ichimoku_kijun', float('nan')),
            'ichimoku_senkou_a': indicators.get('ichimoku_senkou_a', float('nan')),
            'ichimoku_senkou_b': indicators.get('ichimoku_senkou_b', float('nan')),
            'ichimoku_chikou': indicators.get('ichimoku_chikou', float('nan')),
            'df': df,  # إضافة DataFrame للاستخدام في إنشاء المخطط
            'chart_path': None  # سيتم إنشاء المخطط في main.py
        }

        # إضافة البيانات الإضافية التي قد تكون مفيدة
        try:
            market_data['volume'] = float(ticker['volume'])
        except (ValueError, KeyError) as e:
            logger.warning(f"خطأ في استخراج حجم التداول: {str(e)}")
            market_data['volume'] = 0

        try:
            market_data['high_24h'] = float(ticker['highPrice'])
        except (ValueError, KeyError) as e:
            logger.warning(f"خطأ في استخراج أعلى سعر: {str(e)}")
            market_data['high_24h'] = 0

        try:
            market_data['low_24h'] = float(ticker['lowPrice'])
        except (ValueError, KeyError) as e:
            logger.warning(f"خطأ في استخراج أدنى سعر: {str(e)}")
            market_data['low_24h'] = 0

        market_data['timeframe'] = timeframe

        logger.info(f"تم استخراج بيانات السوق بنجاح: السعر={current_price}, التغير={price_change}%")

        return market_data

    except Exception as e:
        logger.error(f"خطأ في الحصول على بيانات السوق باستخدام API المستخدم: {str(e)}")
        return None

def calculate_indicators(df: pd.DataFrame) -> Dict[str, Any]:
    """
    حساب المؤشرات الفنية

    Args:
        df: DataFrame يحتوي على بيانات الأسعار

    Returns:
        قاموس يحتوي على المؤشرات الفنية
    """
    try:
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # EMA
        ema20 = df['close'].ewm(span=20, adjust=False).mean()
        ema50 = df['close'].ewm(span=50, adjust=False).mean()

        # MACD
        ema12 = df['close'].ewm(span=12, adjust=False).mean()
        ema26 = df['close'].ewm(span=26, adjust=False).mean()
        macd = ema12 - ema26
        signal = macd.ewm(span=9, adjust=False).mean()
        histogram = macd - signal

        # Bollinger Bands
        sma20 = df['close'].rolling(window=20).mean()
        std20 = df['close'].rolling(window=20).std()
        upper_band = sma20 + (std20 * 2)
        lower_band = sma20 - (std20 * 2)

        # Stochastic RSI
        stoch_k = 100 * ((df['close'] - df['low'].rolling(14).min()) /
                         (df['high'].rolling(14).max() - df['low'].rolling(14).min()))
        stoch_d = stoch_k.rolling(3).mean()

        # ADX
        plus_dm = df['high'].diff()
        minus_dm = df['low'].diff()
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm > 0] = 0
        minus_dm = abs(minus_dm)

        tr1 = df['high'] - df['low']
        tr2 = abs(df['high'] - df['close'].shift(1))
        tr3 = abs(df['low'] - df['close'].shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        atr = tr.rolling(14).mean()

        plus_di = 100 * (plus_dm.rolling(14).mean() / atr)
        minus_di = 100 * (minus_dm.rolling(14).mean() / atr)

        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(14).mean()

        # Ichimoku Cloud
        # حساب خط التحويل (Tenkan-sen)
        conversion_period = 9
        tenkan_sen = (df['high'].rolling(window=conversion_period).max() + df['low'].rolling(window=conversion_period).min()) / 2

        # حساب خط الأساس (Kijun-sen)
        base_period = 26
        kijun_sen = (df['high'].rolling(window=base_period).max() + df['low'].rolling(window=base_period).min()) / 2

        # حساب خط السبان A (Senkou Span A)
        displacement = 26
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(displacement)

        # حساب خط السبان B (Senkou Span B)
        span_period = 52
        senkou_span_b = ((df['high'].rolling(window=span_period).max() + df['low'].rolling(window=span_period).min()) / 2).shift(displacement)

        # حساب خط التشيكو (Chikou Span)
        chikou_span = df['close'].shift(-displacement)

        # إنشاء قاموس المؤشرات
        indicators = {
            'rsi': rsi.iloc[-1],
            'ema20': ema20.iloc[-1],
            'ema50': ema50.iloc[-1],
            'macd': macd.iloc[-1],
            'macd_signal': signal.iloc[-1],
            'macd_hist': histogram.iloc[-1],
            'bb_upper': upper_band.iloc[-1],
            'bb_middle': sma20.iloc[-1],
            'bb_lower': lower_band.iloc[-1],
            'stoch_k': stoch_k.iloc[-1],
            'stoch_d': stoch_d.iloc[-1],
            'adx': adx.iloc[-1],
            'plus_di': plus_di.iloc[-1],
            'minus_di': minus_di.iloc[-1],
            # إضافة مؤشرات Ichimoku Cloud
            'ichimoku_tenkan': tenkan_sen.iloc[-1] if not tenkan_sen.empty else float('nan'),
            'ichimoku_kijun': kijun_sen.iloc[-1] if not kijun_sen.empty else float('nan'),
            'ichimoku_senkou_a': senkou_span_a.iloc[-displacement] if len(senkou_span_a) > displacement else float('nan'),
            'ichimoku_senkou_b': senkou_span_b.iloc[-displacement] if len(senkou_span_b) > displacement else float('nan'),
            'ichimoku_chikou': chikou_span.iloc[-1] if not chikou_span.empty else float('nan')
        }

        # إضافة إشارات المؤشرات - تخزين القيم الرقمية بدلاً من النصية
        # تحويل الإشارات النصية إلى قيم رقمية لتجنب مشاكل التنسيق
        # 1 = إيجابي (شراء/صعودي/تشبع بيع)، -1 = سلبي (بيع/هبوطي/تشبع شراء)، 0 = محايد
        indicators['rsi_signal'] = 1 if rsi.iloc[-1] < 30 else -1 if rsi.iloc[-1] > 70 else 0
        indicators['ema_signal'] = 1 if ema20.iloc[-1] > ema50.iloc[-1] else -1
        indicators['macd_signal'] = 1 if macd.iloc[-1] > signal.iloc[-1] else -1
        indicators['bb_signal'] = 1 if df['close'].iloc[-1] < lower_band.iloc[-1] else -1 if df['close'].iloc[-1] > upper_band.iloc[-1] else 0
        indicators['stoch_signal'] = 1 if stoch_k.iloc[-1] < 20 else -1 if stoch_k.iloc[-1] > 80 else 0
        indicators['adx_signal'] = 1 if adx.iloc[-1] > 25 else 0

        # إضافة إشارة Ichimoku Cloud
        if not tenkan_sen.empty and not kijun_sen.empty:
            indicators['ichimoku_signal'] = 1 if tenkan_sen.iloc[-1] > kijun_sen.iloc[-1] else -1 if tenkan_sen.iloc[-1] < kijun_sen.iloc[-1] else 0
        else:
            indicators['ichimoku_signal'] = 0

        # إضافة الإشارات النصية كحقول منفصلة للاستخدام في العرض
        indicators['rsi_signal_text'] = 'oversold' if rsi.iloc[-1] < 30 else 'overbought' if rsi.iloc[-1] > 70 else 'neutral'
        indicators['ema_signal_text'] = 'bullish' if ema20.iloc[-1] > ema50.iloc[-1] else 'bearish'
        indicators['macd_signal_text'] = 'buy' if macd.iloc[-1] > signal.iloc[-1] else 'sell'
        indicators['bb_signal_text'] = 'oversold' if df['close'].iloc[-1] < lower_band.iloc[-1] else 'overbought' if df['close'].iloc[-1] > upper_band.iloc[-1] else 'neutral'
        indicators['stoch_signal_text'] = 'oversold' if stoch_k.iloc[-1] < 20 else 'overbought' if stoch_k.iloc[-1] > 80 else 'neutral'
        indicators['adx_signal_text'] = 'strong_trend' if adx.iloc[-1] > 25 else 'weak_trend'

        # إضافة إشارة Ichimoku Cloud النصية
        if not tenkan_sen.empty and not kijun_sen.empty:
            indicators['ichimoku_signal_text'] = 'bullish' if tenkan_sen.iloc[-1] > kijun_sen.iloc[-1] else 'bearish' if tenkan_sen.iloc[-1] < kijun_sen.iloc[-1] else 'neutral'
        else:
            indicators['ichimoku_signal_text'] = 'neutral'

        # إضافة توصية عامة - استخدام القيم الرقمية
        # إضافة مؤشر Ichimoku Cloud إلى قائمة المؤشرات المستخدمة في التوصية
        signal_indicators = ['rsi_signal', 'ema_signal', 'macd_signal', 'bb_signal', 'stoch_signal', 'ichimoku_signal']
        buy_signals = sum(1 for signal in signal_indicators
                         if indicators.get(signal, 0) == 1)  # القيم الإيجابية تشير إلى إشارات شراء
        sell_signals = sum(1 for signal in signal_indicators
                          if indicators.get(signal, 0) == -1)  # القيم السلبية تشير إلى إشارات بيع

        # تخزين عدد إشارات الشراء والبيع
        indicators['buy_signals_count'] = buy_signals
        indicators['sell_signals_count'] = sell_signals

        # تخزين التوصية كقيمة رقمية: 1 = شراء، -1 = بيع، 0 = انتظار
        if buy_signals > sell_signals:
            indicators['recommendation'] = 1
        elif sell_signals > buy_signals:
            indicators['recommendation'] = -1
        else:
            indicators['recommendation'] = 0

        # تخزين التوصية كنص للعرض
        if buy_signals > sell_signals:
            indicators['recommendation_text'] = 'buy'
        elif sell_signals > buy_signals:
            indicators['recommendation_text'] = 'sell'
        else:
            indicators['recommendation_text'] = 'hold'

        return indicators

    except Exception as e:
        logger.error(f"خطأ في حساب المؤشرات الفنية: {str(e)}")
        return {}
