"""
نظام تأكيد الإشارات متعدد المستويات
Multi-Level Signal Confirmation System

هذا الملف يحتوي على نظام متطور لتأكيد الإشارات من خلال:
1. تأكيد من الإطار الزمني الأعلى
2. تأكيد من الإطار الزمني المتوسط
3. تأكيد من المؤشرات المتعددة
4. تحليل التناقضات والتوافقات
5. حساب مستوى الثقة النهائي
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional
from enum import Enum
from datetime import datetime

# إعداد السجل
logger = logging.getLogger(__name__)

class ConfirmationLevel(Enum):
    """مستويات التأكيد"""
    VERY_HIGH = 5
    HIGH = 4
    MEDIUM = 3
    LOW = 2
    VERY_LOW = 1

class SignalType(Enum):
    """أنواع الإشارات"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    STRONG_BUY = "strong_buy"
    STRONG_SELL = "strong_sell"

class SignalConfirmation:
    """نظام تأكيد الإشارات متعدد المستويات"""
    
    def __init__(self):
        # أوزان التأكيد حسب المصدر
        self.confirmation_weights = {
            'higher_timeframe': 0.4,
            'medium_timeframe': 0.3,
            'multiple_indicators': 0.2,
            'volume_confirmation': 0.1
        }
        
        # حدود الثقة
        self.confidence_thresholds = {
            'very_high': 85,
            'high': 70,
            'medium': 55,
            'low': 40,
            'very_low': 0
        }
    
    def confirm_signal(self, signal: Dict[str, Any], timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تأكيد الإشارة من مصادر متعددة"""
        try:
            confirmations = 0
            confirmation_details = {}
            
            # 1. تأكيد من الإطار الأعلى
            higher_tf_confirmation = self._confirm_from_higher_timeframe(signal, timeframes_data)
            if higher_tf_confirmation['confirmed']:
                confirmations += higher_tf_confirmation['strength'] * self.confirmation_weights['higher_timeframe']
                confirmation_details['higher_timeframe'] = higher_tf_confirmation
            
            # 2. تأكيد من الإطار المتوسط
            medium_tf_confirmation = self._confirm_from_medium_timeframe(signal, timeframes_data)
            if medium_tf_confirmation['confirmed']:
                confirmations += medium_tf_confirmation['strength'] * self.confirmation_weights['medium_timeframe']
                confirmation_details['medium_timeframe'] = medium_tf_confirmation
            
            # 3. تأكيد من المؤشرات المتعددة
            indicators_confirmation = self._confirm_from_multiple_indicators(signal, timeframes_data)
            if indicators_confirmation['confirmed']:
                confirmations += indicators_confirmation['strength'] * self.confirmation_weights['multiple_indicators']
                confirmation_details['multiple_indicators'] = indicators_confirmation
            
            # 4. تأكيد من الحجم
            volume_confirmation = self._confirm_from_volume(signal, timeframes_data)
            if volume_confirmation['confirmed']:
                confirmations += volume_confirmation['strength'] * self.confirmation_weights['volume_confirmation']
                confirmation_details['volume'] = volume_confirmation
            
            # حساب مستوى الثقة النهائي
            final_confidence = self._calculate_confidence(confirmations)
            
            return {
                'signal_confirmed': confirmations > 0.5,
                'confidence_score': final_confidence,
                'confidence_level': self._get_confidence_level(final_confidence),
                'confirmation_details': confirmation_details,
                'total_confirmations': len(confirmation_details),
                'weighted_score': confirmations * 100
            }
            
        except Exception as e:
            logger.error(f"خطأ في تأكيد الإشارة: {str(e)}")
            return {
                'signal_confirmed': False,
                'confidence_score': 0,
                'confidence_level': 'very_low',
                'confirmation_details': {},
                'total_confirmations': 0,
                'weighted_score': 0
            }
    
    def _confirm_from_higher_timeframe(self, signal: Dict[str, Any], timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تأكيد من الإطار الزمني الأعلى"""
        try:
            signal_direction = signal.get('action', 'hold')
            
            # البحث عن إطار زمني أعلى
            available_timeframes = list(timeframes_data.keys())
            timeframe_hierarchy = ['1M', '1w', '1d', '4h', '1h', '15m', '5m', '1m']
            
            higher_timeframes = []
            for tf in timeframe_hierarchy:
                if tf in available_timeframes:
                    higher_timeframes.append(tf)
            
            if len(higher_timeframes) < 2:
                return {'confirmed': False, 'strength': 0, 'reason': 'insufficient_timeframes'}
            
            # استخدام الإطار الأعلى المتاح
            higher_tf = higher_timeframes[0]
            higher_data = timeframes_data.get(higher_tf, {})
            
            if not higher_data:
                return {'confirmed': False, 'strength': 0, 'reason': 'no_higher_timeframe_data'}
            
            # تحليل الاتجاه في الإطار الأعلى
            higher_trend = self._analyze_trend_direction(higher_data)
            
            # التحقق من التوافق
            if signal_direction in ['buy', 'strong_buy'] and higher_trend == 'bullish':
                return {'confirmed': True, 'strength': 4, 'reason': 'bullish_alignment', 'timeframe': higher_tf}
            elif signal_direction in ['sell', 'strong_sell'] and higher_trend == 'bearish':
                return {'confirmed': True, 'strength': 4, 'reason': 'bearish_alignment', 'timeframe': higher_tf}
            elif higher_trend == 'neutral':
                return {'confirmed': True, 'strength': 2, 'reason': 'neutral_higher_timeframe', 'timeframe': higher_tf}
            else:
                return {'confirmed': False, 'strength': 0, 'reason': 'conflicting_trends', 'timeframe': higher_tf}
            
        except Exception as e:
            logger.error(f"خطأ في تأكيد الإطار الأعلى: {str(e)}")
            return {'confirmed': False, 'strength': 0, 'reason': 'error'}
    
    def _confirm_from_medium_timeframe(self, signal: Dict[str, Any], timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تأكيد من الإطار الزمني المتوسط"""
        try:
            signal_direction = signal.get('action', 'hold')
            
            # البحث عن إطار زمني متوسط
            available_timeframes = list(timeframes_data.keys())
            medium_timeframes = ['4h', '1h']
            
            medium_tf = None
            for tf in medium_timeframes:
                if tf in available_timeframes:
                    medium_tf = tf
                    break
            
            if not medium_tf:
                return {'confirmed': False, 'strength': 0, 'reason': 'no_medium_timeframe'}
            
            medium_data = timeframes_data.get(medium_tf, {})
            if not medium_data:
                return {'confirmed': False, 'strength': 0, 'reason': 'no_medium_timeframe_data'}
            
            # تحليل المؤشرات في الإطار المتوسط
            macd_signal = self._analyze_macd_confirmation(medium_data, signal_direction)
            adx_signal = self._analyze_adx_confirmation(medium_data, signal_direction)
            
            confirmations = 0
            if macd_signal:
                confirmations += 1
            if adx_signal:
                confirmations += 1
            
            if confirmations >= 1:
                return {
                    'confirmed': True, 
                    'strength': min(confirmations + 1, 4), 
                    'reason': 'medium_timeframe_indicators',
                    'timeframe': medium_tf,
                    'macd_confirmed': macd_signal,
                    'adx_confirmed': adx_signal
                }
            else:
                return {'confirmed': False, 'strength': 0, 'reason': 'no_medium_confirmation'}
            
        except Exception as e:
            logger.error(f"خطأ في تأكيد الإطار المتوسط: {str(e)}")
            return {'confirmed': False, 'strength': 0, 'reason': 'error'}
    
    def _confirm_from_multiple_indicators(self, signal: Dict[str, Any], timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تأكيد من المؤشرات المتعددة"""
        try:
            signal_direction = signal.get('action', 'hold')
            
            # استخدام الإطار الزمني الأقصر للمؤشرات السريعة
            short_timeframes = ['15m', '5m', '1m']
            short_tf = None
            
            available_timeframes = list(timeframes_data.keys())
            for tf in short_timeframes:
                if tf in available_timeframes:
                    short_tf = tf
                    break
            
            if not short_tf:
                return {'confirmed': False, 'strength': 0, 'reason': 'no_short_timeframe'}
            
            short_data = timeframes_data.get(short_tf, {})
            if not short_data:
                return {'confirmed': False, 'strength': 0, 'reason': 'no_short_timeframe_data'}
            
            # تحليل المؤشرات المتعددة
            confirmations = []
            
            # RSI
            rsi_confirmed = self._analyze_rsi_confirmation(short_data, signal_direction)
            if rsi_confirmed:
                confirmations.append('rsi')
            
            # Stochastic
            stoch_confirmed = self._analyze_stochastic_confirmation(short_data, signal_direction)
            if stoch_confirmed:
                confirmations.append('stochastic')
            
            # Bollinger Bands
            bb_confirmed = self._analyze_bollinger_confirmation(short_data, signal_direction)
            if bb_confirmed:
                confirmations.append('bollinger_bands')
            
            # CCI
            cci_confirmed = self._analyze_cci_confirmation(short_data, signal_direction)
            if cci_confirmed:
                confirmations.append('cci')
            
            if len(confirmations) >= 2:
                return {
                    'confirmed': True,
                    'strength': min(len(confirmations), 4),
                    'reason': 'multiple_indicators_agreement',
                    'timeframe': short_tf,
                    'confirmed_indicators': confirmations,
                    'total_confirmations': len(confirmations)
                }
            else:
                return {'confirmed': False, 'strength': 0, 'reason': 'insufficient_indicator_agreement'}
            
        except Exception as e:
            logger.error(f"خطأ في تأكيد المؤشرات المتعددة: {str(e)}")
            return {'confirmed': False, 'strength': 0, 'reason': 'error'}
    
    def _confirm_from_volume(self, signal: Dict[str, Any], timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تأكيد من الحجم"""
        try:
            signal_direction = signal.get('action', 'hold')
            
            # استخدام أي إطار زمني متاح للحجم
            for timeframe, data in timeframes_data.items():
                volume_ratio = data.get('volume_ratio')
                obv = data.get('obv')
                
                if volume_ratio and volume_ratio > 1.5:  # حجم أعلى من المتوسط
                    if signal_direction in ['buy', 'strong_buy']:
                        return {
                            'confirmed': True,
                            'strength': 3,
                            'reason': 'high_volume_support',
                            'timeframe': timeframe,
                            'volume_ratio': volume_ratio
                        }
                    elif signal_direction in ['sell', 'strong_sell']:
                        return {
                            'confirmed': True,
                            'strength': 3,
                            'reason': 'high_volume_distribution',
                            'timeframe': timeframe,
                            'volume_ratio': volume_ratio
                        }
            
            return {'confirmed': False, 'strength': 0, 'reason': 'no_volume_confirmation'}
            
        except Exception as e:
            logger.error(f"خطأ في تأكيد الحجم: {str(e)}")
            return {'confirmed': False, 'strength': 0, 'reason': 'error'}

    # دوال مساعدة لتحليل المؤشرات
    def _analyze_trend_direction(self, data: Dict[str, Any]) -> str:
        """تحليل اتجاه الترند العام"""
        try:
            ema20 = data.get('ema20')
            ema50 = data.get('ema50')
            price = data.get('price', 0)

            bullish_signals = 0
            bearish_signals = 0

            # تحليل موقع السعر بالنسبة للمتوسطات
            if ema20 and price > ema20:
                bullish_signals += 1
            elif ema20 and price < ema20:
                bearish_signals += 1

            # تحليل ترتيب المتوسطات
            if ema20 and ema50:
                if ema20 > ema50:
                    bullish_signals += 1
                else:
                    bearish_signals += 1

            # تحليل Ichimoku إذا كان متاحاً
            ichimoku_tenkan = data.get('ichimoku_tenkan')
            ichimoku_kijun = data.get('ichimoku_kijun')
            if ichimoku_tenkan and ichimoku_kijun:
                if ichimoku_tenkan > ichimoku_kijun:
                    bullish_signals += 1
                else:
                    bearish_signals += 1

            if bullish_signals > bearish_signals:
                return 'bullish'
            elif bearish_signals > bullish_signals:
                return 'bearish'
            else:
                return 'neutral'

        except Exception as e:
            logger.error(f"خطأ في تحليل اتجاه الترند: {str(e)}")
            return 'neutral'

    def _analyze_macd_confirmation(self, data: Dict[str, Any], signal_direction: str) -> bool:
        """تحليل تأكيد MACD"""
        try:
            macd = data.get('macd')
            macd_signal = data.get('macd_signal')
            macd_histogram = data.get('macd_histogram')

            if not all([macd, macd_signal, macd_histogram]):
                return False

            if signal_direction in ['buy', 'strong_buy']:
                # إشارة شراء: MACD فوق الإشارة والهيستوجرام موجب
                return macd > macd_signal and macd_histogram > 0
            elif signal_direction in ['sell', 'strong_sell']:
                # إشارة بيع: MACD تحت الإشارة والهيستوجرام سالب
                return macd < macd_signal and macd_histogram < 0

            return False

        except Exception as e:
            logger.error(f"خطأ في تحليل تأكيد MACD: {str(e)}")
            return False

    def _analyze_adx_confirmation(self, data: Dict[str, Any], signal_direction: str) -> bool:
        """تحليل تأكيد ADX"""
        try:
            adx = data.get('adx')
            plus_di = data.get('plus_di')
            minus_di = data.get('minus_di')

            if not all([adx, plus_di, minus_di]):
                return False

            # ADX يجب أن يكون أعلى من 20 لتأكيد وجود اتجاه
            if adx < 20:
                return False

            if signal_direction in ['buy', 'strong_buy']:
                return plus_di > minus_di
            elif signal_direction in ['sell', 'strong_sell']:
                return minus_di > plus_di

            return False

        except Exception as e:
            logger.error(f"خطأ في تحليل تأكيد ADX: {str(e)}")
            return False

    def _analyze_rsi_confirmation(self, data: Dict[str, Any], signal_direction: str) -> bool:
        """تحليل تأكيد RSI"""
        try:
            rsi = data.get('rsi')
            if not rsi:
                return False

            if signal_direction in ['buy', 'strong_buy']:
                # إشارة شراء: RSI في منطقة ذروة البيع أو يخرج منها
                return rsi <= 35 or (rsi > 30 and rsi <= 50)
            elif signal_direction in ['sell', 'strong_sell']:
                # إشارة بيع: RSI في منطقة ذروة الشراء أو يخرج منها
                return rsi >= 65 or (rsi < 70 and rsi >= 50)

            return False

        except Exception as e:
            logger.error(f"خطأ في تحليل تأكيد RSI: {str(e)}")
            return False

    def _analyze_stochastic_confirmation(self, data: Dict[str, Any], signal_direction: str) -> bool:
        """تحليل تأكيد Stochastic"""
        try:
            stoch_k = data.get('stoch_k')
            stoch_d = data.get('stoch_d')

            if not all([stoch_k, stoch_d]):
                return False

            if signal_direction in ['buy', 'strong_buy']:
                # إشارة شراء: %K فوق %D في منطقة ذروة البيع
                return stoch_k > stoch_d and (stoch_k <= 25 or stoch_d <= 25)
            elif signal_direction in ['sell', 'strong_sell']:
                # إشارة بيع: %K تحت %D في منطقة ذروة الشراء
                return stoch_k < stoch_d and (stoch_k >= 75 or stoch_d >= 75)

            return False

        except Exception as e:
            logger.error(f"خطأ في تحليل تأكيد Stochastic: {str(e)}")
            return False

    def _analyze_bollinger_confirmation(self, data: Dict[str, Any], signal_direction: str) -> bool:
        """تحليل تأكيد Bollinger Bands"""
        try:
            bb_upper = data.get('bb_upper')
            bb_lower = data.get('bb_lower')
            price = data.get('price', 0)

            if not all([bb_upper, bb_lower, price]):
                return False

            if signal_direction in ['buy', 'strong_buy']:
                # إشارة شراء: السعر يلامس أو يقترب من النطاق السفلي
                return price <= bb_lower * 1.02  # 2% tolerance
            elif signal_direction in ['sell', 'strong_sell']:
                # إشارة بيع: السعر يلامس أو يقترب من النطاق العلوي
                return price >= bb_upper * 0.98  # 2% tolerance

            return False

        except Exception as e:
            logger.error(f"خطأ في تحليل تأكيد Bollinger Bands: {str(e)}")
            return False

    def _analyze_cci_confirmation(self, data: Dict[str, Any], signal_direction: str) -> bool:
        """تحليل تأكيد CCI"""
        try:
            cci = data.get('cci')
            if not cci:
                return False

            if signal_direction in ['buy', 'strong_buy']:
                # إشارة شراء: CCI في منطقة ذروة البيع أو يخرج منها
                return cci <= -100 or (cci > -100 and cci <= 0)
            elif signal_direction in ['sell', 'strong_sell']:
                # إشارة بيع: CCI في منطقة ذروة الشراء أو يخرج منها
                return cci >= 100 or (cci < 100 and cci >= 0)

            return False

        except Exception as e:
            logger.error(f"خطأ في تحليل تأكيد CCI: {str(e)}")
            return False

    def _calculate_confidence(self, confirmations: float) -> float:
        """حساب مستوى الثقة النهائي"""
        try:
            # تحويل النقاط إلى نسبة مئوية
            confidence_percentage = min(confirmations * 100, 100)
            return confidence_percentage

        except Exception as e:
            logger.error(f"خطأ في حساب مستوى الثقة: {str(e)}")
            return 0

    def _get_confidence_level(self, confidence_score: float) -> str:
        """تحديد مستوى الثقة النصي"""
        try:
            if confidence_score >= self.confidence_thresholds['very_high']:
                return 'very_high'
            elif confidence_score >= self.confidence_thresholds['high']:
                return 'high'
            elif confidence_score >= self.confidence_thresholds['medium']:
                return 'medium'
            elif confidence_score >= self.confidence_thresholds['low']:
                return 'low'
            else:
                return 'very_low'

        except Exception as e:
            logger.error(f"خطأ في تحديد مستوى الثقة: {str(e)}")
            return 'very_low'

class TimeframeConflictAnalysis:
    """تحليل التناقضات والتوافقات بين الإطارات الزمنية"""

    def __init__(self):
        # أوزان الإطارات الزمنية
        self.timeframe_weights = {
            '1M': 1.0,
            '1w': 0.9,
            '1d': 0.8,
            '4h': 0.7,
            '1h': 0.6,
            '15m': 0.5,
            '5m': 0.4,
            '1m': 0.3
        }

    def analyze_conflicts(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل التناقضات بين الإطارات الزمنية"""
        try:
            conflicts = []
            confirmations = []

            # الحصول على الإطارات الزمنية المتاحة
            available_timeframes = list(timeframes_data.keys())

            # مقارنة كل زوج من الإطارات الزمنية
            for i, tf1 in enumerate(available_timeframes):
                for tf2 in available_timeframes[i+1:]:
                    conflict_result = self._compare_timeframes(tf1, tf2, timeframes_data)

                    if conflict_result['type'] == 'conflict':
                        conflicts.append(conflict_result)
                    elif conflict_result['type'] == 'confirmation':
                        confirmations.append(conflict_result)

            # ترتيب الإشارات حسب الأولوية
            prioritized_signals = self._prioritize_signals(conflicts, confirmations)

            return {
                'conflicts': conflicts,
                'confirmations': confirmations,
                'prioritized_signals': prioritized_signals,
                'overall_consensus': self._calculate_overall_consensus(confirmations, conflicts),
                'recommendation': self._generate_conflict_recommendation(prioritized_signals)
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل التناقضات: {str(e)}")
            return {
                'conflicts': [],
                'confirmations': [],
                'prioritized_signals': [],
                'overall_consensus': 'unknown',
                'recommendation': 'hold'
            }

    def _compare_timeframes(self, tf1: str, tf2: str, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """مقارنة إطارين زمنيين"""
        try:
            data1 = timeframes_data.get(tf1, {})
            data2 = timeframes_data.get(tf2, {})

            # تحليل الاتجاه في كل إطار
            trend1 = self._analyze_timeframe_trend(data1)
            trend2 = self._analyze_timeframe_trend(data2)

            # تحديد نوع العلاقة
            if trend1 == trend2 and trend1 != 'neutral':
                return {
                    'type': 'confirmation',
                    'timeframes': [tf1, tf2],
                    'trend': trend1,
                    'strength': self._calculate_confirmation_strength(data1, data2),
                    'weight': (self.timeframe_weights.get(tf1, 0.5) + self.timeframe_weights.get(tf2, 0.5)) / 2
                }
            elif trend1 != trend2 and 'neutral' not in [trend1, trend2]:
                return {
                    'type': 'conflict',
                    'timeframes': [tf1, tf2],
                    'trends': [trend1, trend2],
                    'conflict_severity': self._calculate_conflict_severity(data1, data2),
                    'weight': (self.timeframe_weights.get(tf1, 0.5) + self.timeframe_weights.get(tf2, 0.5)) / 2
                }
            else:
                return {
                    'type': 'neutral',
                    'timeframes': [tf1, tf2],
                    'trends': [trend1, trend2],
                    'weight': 0.1
                }

        except Exception as e:
            logger.error(f"خطأ في مقارنة الإطارات الزمنية: {str(e)}")
            return {'type': 'error', 'timeframes': [tf1, tf2]}

    def _analyze_timeframe_trend(self, data: Dict[str, Any]) -> str:
        """تحليل اتجاه إطار زمني واحد"""
        try:
            # استخدام عدة مؤشرات لتحديد الاتجاه
            signals = []

            # تحليل المتوسطات المتحركة
            ema20 = data.get('ema20')
            ema50 = data.get('ema50')
            price = data.get('price', 0)

            if ema20 and ema50 and price:
                if price > ema20 > ema50:
                    signals.append('bullish')
                elif price < ema20 < ema50:
                    signals.append('bearish')

            # تحليل MACD
            macd = data.get('macd')
            macd_signal = data.get('macd_signal')
            if macd and macd_signal:
                if macd > macd_signal:
                    signals.append('bullish')
                else:
                    signals.append('bearish')

            # تحليل RSI
            rsi = data.get('rsi')
            if rsi:
                if rsi > 50:
                    signals.append('bullish')
                else:
                    signals.append('bearish')

            # تحديد الاتجاه النهائي
            bullish_count = signals.count('bullish')
            bearish_count = signals.count('bearish')

            if bullish_count > bearish_count:
                return 'bullish'
            elif bearish_count > bullish_count:
                return 'bearish'
            else:
                return 'neutral'

        except Exception as e:
            logger.error(f"خطأ في تحليل اتجاه الإطار الزمني: {str(e)}")
            return 'neutral'

    def _calculate_confirmation_strength(self, data1: Dict[str, Any], data2: Dict[str, Any]) -> float:
        """حساب قوة التأكيد بين إطارين"""
        try:
            # حساب قوة الإشارات في كل إطار
            strength1 = self._calculate_signal_strength(data1)
            strength2 = self._calculate_signal_strength(data2)

            # متوسط القوة
            return (strength1 + strength2) / 2

        except Exception as e:
            logger.error(f"خطأ في حساب قوة التأكيد: {str(e)}")
            return 0.5

    def _calculate_conflict_severity(self, data1: Dict[str, Any], data2: Dict[str, Any]) -> float:
        """حساب شدة التناقض بين إطارين"""
        try:
            # حساب قوة الإشارات المتناقضة
            strength1 = self._calculate_signal_strength(data1)
            strength2 = self._calculate_signal_strength(data2)

            # شدة التناقض تعتمد على قوة الإشارات المتناقضة
            return min(strength1, strength2)

        except Exception as e:
            logger.error(f"خطأ في حساب شدة التناقض: {str(e)}")
            return 0.5

    def _calculate_signal_strength(self, data: Dict[str, Any]) -> float:
        """حساب قوة الإشارة في إطار زمني"""
        try:
            strength_factors = []

            # قوة RSI
            rsi = data.get('rsi')
            if rsi:
                rsi_strength = abs(rsi - 50) / 50  # كلما ابتعد عن 50 كلما زادت القوة
                strength_factors.append(rsi_strength)

            # قوة MACD
            macd = data.get('macd')
            macd_signal = data.get('macd_signal')
            if macd and macd_signal:
                macd_strength = abs(macd - macd_signal) / max(abs(macd), abs(macd_signal), 0.001)
                strength_factors.append(min(macd_strength, 1.0))

            # قوة ADX
            adx = data.get('adx')
            if adx:
                adx_strength = min(adx / 50, 1.0)  # تطبيع ADX
                strength_factors.append(adx_strength)

            if strength_factors:
                return sum(strength_factors) / len(strength_factors)
            else:
                return 0.5

        except Exception as e:
            logger.error(f"خطأ في حساب قوة الإشارة: {str(e)}")
            return 0.5

    def _prioritize_signals(self, conflicts: list, confirmations: list) -> list:
        """ترتيب الإشارات حسب الأولوية والوزن"""
        try:
            all_signals = []

            # إضافة التأكيدات مع أولوية عالية
            for confirmation in confirmations:
                signal = {
                    'type': 'confirmation',
                    'data': confirmation,
                    'priority': confirmation.get('weight', 0.5) * 2,  # أولوية مضاعفة للتأكيدات
                    'strength': confirmation.get('strength', 1),
                    'timeframes': confirmation.get('timeframes', []),
                    'trend': confirmation.get('trend', 'neutral')
                }
                all_signals.append(signal)

            # إضافة التناقضات مع أولوية منخفضة
            for conflict in conflicts:
                signal = {
                    'type': 'conflict',
                    'data': conflict,
                    'priority': conflict.get('weight', 0.5) * 0.5,  # أولوية منخفضة للتناقضات
                    'severity': conflict.get('conflict_severity', 1),
                    'timeframes': conflict.get('timeframes', []),
                    'trends': conflict.get('trends', [])
                }
                all_signals.append(signal)

            # ترتيب حسب الأولوية (من الأعلى للأقل)
            prioritized_signals = sorted(all_signals, key=lambda x: x['priority'], reverse=True)

            return prioritized_signals

        except Exception as e:
            logger.error(f"خطأ في ترتيب الإشارات: {str(e)}")
            return []

    def _calculate_overall_consensus(self, confirmations: list, conflicts: list) -> str:
        """حساب الإجماع العام بين الإطارات الزمنية"""
        try:
            if not confirmations and not conflicts:
                return 'unknown'

            # حساب وزن التأكيدات
            confirmation_weight = sum(conf.get('weight', 0.5) for conf in confirmations)

            # حساب وزن التناقضات
            conflict_weight = sum(conf.get('weight', 0.5) for conf in conflicts)

            total_weight = confirmation_weight + conflict_weight

            if total_weight == 0:
                return 'unknown'

            # حساب نسبة التأكيد
            consensus_ratio = confirmation_weight / total_weight

            if consensus_ratio >= 0.8:
                return 'strong_consensus'
            elif consensus_ratio >= 0.6:
                return 'moderate_consensus'
            elif consensus_ratio >= 0.4:
                return 'weak_consensus'
            else:
                return 'conflicted'

        except Exception as e:
            logger.error(f"خطأ في حساب الإجماع العام: {str(e)}")
            return 'unknown'

    def _generate_conflict_recommendation(self, prioritized_signals: list) -> str:
        """إنتاج توصية بناءً على تحليل التناقضات"""
        try:
            if not prioritized_signals:
                return 'hold'

            # أخذ أعلى 3 إشارات أولوية
            top_signals = prioritized_signals[:3]

            buy_weight = 0
            sell_weight = 0
            hold_weight = 0

            for signal in top_signals:
                weight = signal.get('priority', 0.5)

                if signal['type'] == 'confirmation':
                    trend = signal.get('trend', 'neutral')
                    if trend in ['bullish', 'buy']:
                        buy_weight += weight
                    elif trend in ['bearish', 'sell']:
                        sell_weight += weight
                    else:
                        hold_weight += weight
                else:  # conflict
                    # التناقضات تزيد من وزن الانتظار
                    hold_weight += weight * 1.5

            # تحديد التوصية بناءً على الأوزان
            if buy_weight > sell_weight and buy_weight > hold_weight:
                return 'buy'
            elif sell_weight > buy_weight and sell_weight > hold_weight:
                return 'sell'
            else:
                return 'hold'

        except Exception as e:
            logger.error(f"خطأ في إنتاج التوصية: {str(e)}")
            return 'hold'
