# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
TelegramTrading/
telegram_trading/
TelegramTrading
TradingTelegram
venv/
ENV/
env/
.env
.venv
env.bak/
venv.bak/
TradingTelegram-env 
/TradingTelegram-env
TradingTelegram_venv
# IDEs
.idea/
.vscode/
*.swp
*.swo

# Logs
*.log
logs/
log/

# Local configuration
config.ini
.env
.env.local
.env.*.local

# Temporary files
*.tmp
temp/
.DS_Store

# Charts and images
*.png
*.jpg
*.jpeg
*.gif

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/

# Project specific
alerts.json
settings/

# ملفات البيئة
.env
*.env.local

# ملفات Firebase
*-firebase-adminsdk-*.json

# ملفات التكوين الحساسة
config_secret.ini

# أضف هذه السطور
*.json
tradingtelegram-da632-firebase-adminsdk-fbsvc-a67cf6e086.json