# handlers/settings_handlers.py
"""
معالجات الإعدادات - TradingTelegram
تحتوي على جميع دوال تعديل الإعدادات المنقولة من main.py
"""

import logging
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from telegram.constants import ParseMode

# إعداد السجل
logger = logging.getLogger(__name__)

# متغيرات عامة (سيتم تعيينها من main.py)
subscription_system = None
db = None
firestore_cache = None

def set_dependencies(sub_system, database, cache):
    """تعيين التبعيات المطلوبة من main.py"""
    global subscription_system, db, firestore_cache
    subscription_system = sub_system
    db = database
    firestore_cache = cache

async def set_trading_style(update: Update, context: CallbackContext, style: str):
    """تعيين نمط التداول"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # التحقق من حالة الاشتراك
        if not subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # تحديث نمط التداول باستخدام keyword arguments
        success = subscription_system.update_user_settings(user_id, **{'trading_style': style})

        if success:
            styles_ar = {
                'scalping': 'المضاربة السريعة',
                'day_trading': 'التداول اليومي',
                'swing_trading': 'التداول المتأرجح',
                'position': 'الاستثمار طويل المدى'
            }

            style_name = styles_ar.get(style, style) if lang == 'ar' else style.replace('_', ' ').title()

            await update.callback_query.answer(
                f"✅ تم تعيين نمط التحليل إلى {style_name}" if lang == 'ar' else
                f"✅ Analysis style set to {style_name}",
                show_alert=True
            )

            # العودة إلى إعدادات النظام المحسن
            from .menu_handlers import show_enhanced_settings
            await show_enhanced_settings(update, context)
        else:
            await update.callback_query.answer(
                "❌ حدث خطأ في تعيين نمط التحليل" if lang == 'ar' else "❌ Error setting analysis style",
                show_alert=True
            )

    except Exception as e:
        logger.error(f"خطأ في تعيين نمط التداول: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ في تعيين نمط التحليل" if lang == 'ar' else "❌ Error setting analysis style",
            show_alert=True
        )

async def set_analysis_type(update: Update, context: CallbackContext, analysis_type: str):
    """تعيين نوع التحليل"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # التحقق من حالة الاشتراك
        if not await subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # تحديث نوع التحليل باستخدام keyword arguments
        success = subscription_system.update_user_settings(user_id, **{'analysis_type': analysis_type})

        if success:
            analysis_types_ar = {
                'traditional': 'التحليل التقليدي',
                'ai': 'الذكاء الاصطناعي',
                'enhanced': 'النظام المحسن'
            }

            type_name = analysis_types_ar.get(analysis_type, analysis_type) if lang == 'ar' else analysis_type.replace('_', ' ').title()

            await update.callback_query.answer(
                f"✅ تم تعيين نوع التحليل إلى {type_name}" if lang == 'ar' else
                f"✅ Analysis type set to {type_name}",
                show_alert=True
            )

            # العودة إلى إعدادات النظام المحسن
            from .menu_handlers import show_enhanced_settings
            await show_enhanced_settings(update, context)
        else:
            await update.callback_query.answer(
                "❌ حدث خطأ في تعيين نوع التحليل" if lang == 'ar' else "❌ Error setting analysis type",
                show_alert=True
            )

    except Exception as e:
        logger.error(f"خطأ في تعيين نوع التحليل: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ" if lang == 'ar' else "❌ An error occurred",
            show_alert=True
        )

async def reset_enhanced_settings(update: Update, context: CallbackContext):
    """إعادة تعيين إعدادات النظام المحسن"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        if subscription_system is None:
            logger.error("subscription_system غير متوفر")
            await update.callback_query.answer(
                "❌ حدث خطأ في النظام. الرجاء المحاولة لاحقاً",
                show_alert=True
            )
            return

        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        # التحقق من حالة الاشتراك
        if not await subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else
                "This feature is available for subscribers only",
                show_alert=True
            )
            return

        # إعادة تعيين الإعدادات إلى القيم الافتراضية باستخدام keyword arguments
        success = subscription_system.update_user_settings(
            user_id,
            **{
                'trading_style': 'day_trading',
                'analysis_type': 'enhanced'
            }
        )

        if success:
            await update.callback_query.answer(
                "✅ تم إعادة تعيين الإعدادات إلى القيم الافتراضية" if lang == 'ar' else
                "✅ Settings reset to default values",
                show_alert=True
            )

            # العودة إلى إعدادات النظام المحسن
            from .menu_handlers import show_enhanced_settings
            await show_enhanced_settings(update, context)
        else:
            await update.callback_query.answer(
                "❌ حدث خطأ في إعادة تعيين الإعدادات" if lang == 'ar' else "❌ Error resetting settings",
                show_alert=True
            )

    except Exception as e:
        logger.error(f"خطأ في إعادة تعيين إعدادات النظام المحسن: {str(e)}")
        await update.callback_query.answer(
            "❌ حدث خطأ" if lang == 'ar' else "❌ An error occurred",
            show_alert=True
        )

async def set_language(update: Update, context: CallbackContext, lang: str):
    """تغيير لغة البوت"""
    if not update.callback_query:
        return

    user_id = str(update.callback_query.from_user.id)

    # استخدام نظام الاشتراكات الموحد لحفظ اللغة
    success = subscription_system.update_user_settings(
        user_id,
        lang=lang,
        language=lang,  # للتوافق
        lang_selected=True,  # المستخدم اختار اللغة بوضوح
        updated_at=datetime.now().isoformat()
    )

    if not success:
        logger.error(f"فشل في تحديث لغة المستخدم {user_id} إلى {lang}")
        await update.callback_query.answer("❌ حدث خطأ في تغيير اللغة", show_alert=True)
        return

    # تزامن اللغة عبر جميع المجموعات
    try:
        # حفظ إضافي في user_settings للتأكد
        settings_ref = db.collection('user_settings').document(user_id)
        settings = {
            'lang': lang,
            'language': lang,
            'lang_selected': True,
            'updated_at': datetime.now().isoformat()
        }
        settings_ref.set(settings, merge=True)

        # تحديث notification_preferences أيضاً
        notif_ref = db.collection('notification_preferences').document(user_id)
        notif_ref.set({
            'language': lang,
            'lang': lang,
            'updated_at': datetime.now().isoformat()
        }, merge=True)

        logger.info(f"✅ تم تزامن لغة المستخدم {user_id} إلى {lang} عبر جميع المجموعات")

    except Exception as e:
        logger.error(f"خطأ في تزامن اللغة للمستخدم {user_id}: {str(e)}")

    # تحديث الذاكرة المؤقتة
    firestore_cache.set(f'settings_{user_id}', settings, ex=subscription_system.cache_timeout, cache_type="user_data")

    # إرسال رسالة تأكيد باللغة المناسبة
    success_message = "✅ Language changed to English!" if lang == 'en' else "✅ تم تغيير اللغة إلى العربية!"
    await update.callback_query.answer(success_message)

    # تحديث القائمة الرئيسية باللغة الجديدة
    try:
        # استيراد الدوال المطلوبة من utils.text_helpers
        from utils.text_helpers import get_main_menu_text, get_main_menu_keyboard

        menu_text = await get_main_menu_text(user_id, lang)
        keyboard = get_main_menu_keyboard(user_id, lang)

        await update.callback_query.edit_message_text(
            text=menu_text,
            reply_markup=keyboard,
            parse_mode=ParseMode.HTML
        )
    except Exception as e:
        logger.error(f"خطأ في تحديث القائمة الرئيسية بعد تغيير اللغة: {str(e)}")
        # في حالة فشل تحديث القائمة، نرسل رسالة جديدة
        try:
            from handlers.main_handlers import show_main_menu
            await show_main_menu(update, context)
        except Exception as inner_e:
            logger.error(f"خطأ في عرض القائمة الرئيسية: {str(inner_e)}")
