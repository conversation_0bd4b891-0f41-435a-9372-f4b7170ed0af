"""
وظائف مساعدة لتنسيق المؤشرات الفنية
"""

import logging
import re
from typing import Dict, Any
from utils import fix_bold_formatting

# إعداد السجل
logger = logging.getLogger(__name__)

def format_technical_indicators_rtl(analysis: str, market_data: Dict[str, Any]) -> str:
    """
    تنسيق المؤشرات الفنية من اليمين إلى اليسار

    Args:
        analysis: نص التحليل
        market_data: بيانات السوق والمؤشرات الفنية

    Returns:
        نص التحليل بعد التنسيق
    """
    try:
        # تحديد قسم المؤشرات الفنية
        indicators_section_start = analysis.find("المؤشرات الفنية:")
        if indicators_section_start == -1:
            return analysis

        # تقسيم التحليل إلى أجزاء
        before_indicators = analysis[:indicators_section_start + len("المؤشرات الفنية:")]
        indicators_section = analysis[indicators_section_start + len("المؤشرات الفنية:"):]

        # تحديد نهاية قسم المؤشرات الفنية
        next_section_start = indicators_section.find("\n\n")
        if next_section_start == -1:
            indicators_text = indicators_section
            after_indicators = ""
        else:
            indicators_text = indicators_section[:next_section_start]
            after_indicators = indicators_section[next_section_start:]

        # تنسيق المؤشرات الفنية
        indicators_lines = indicators_text.strip().split("\n")
        formatted_indicators = []

        for line in indicators_lines:
            if line.strip():
                # إزالة النقطة والمسافة في بداية السطر إذا وجدت
                if line.strip().startswith("•"):
                    line = line.strip()[1:].strip()

                # تقسيم السطر إلى اسم المؤشر والقيمة
                parts = line.split(":")
                if len(parts) == 2:
                    indicator_name = parts[0].strip()
                    indicator_value = parts[1].strip()
                    # إعادة تنسيق السطر من اليمين إلى اليسار
                    formatted_line = f"• {indicator_value} :{indicator_name}"
                    formatted_indicators.append(formatted_line)
                else:
                    formatted_indicators.append(f"• {line.strip()}")

        # إعادة بناء التحليل
        formatted_analysis = before_indicators + "\n" + "\n".join(formatted_indicators) + after_indicators

        return formatted_analysis

    except Exception as e:
        logger.error(f"خطأ في تنسيق المؤشرات الفنية: {str(e)}")
        return analysis


def format_gemini_analysis(analysis: str, symbol: str, price: float, price_change: float, lang: str = 'ar') -> str:
    """
    تنسيق تحليل Gemini بالكامل مع إصلاح مشاكل التباعد العربي

    Args:
        analysis: نص التحليل
        symbol: رمز العملة
        price: السعر الحالي
        price_change: نسبة التغير
        lang: لغة التحليل

    Returns:
        نص التحليل المنسق والمحسن
    """
    try:
        # تنظيف الاستجابة
        analysis = analysis.strip()

        # إزالة النجوم الزائدة وتحسين التنسيق أولاً
        import re
        # استبدال النجوم المتعددة بتنسيق bold بسيط
        analysis = re.sub(r'\*{3,}([^*]+)\*{3,}', r'**\1**', analysis)  # *** أو أكثر إلى **
        analysis = re.sub(r'\*{5,}', '**', analysis)  # إزالة النجوم الزائدة

        # إصلاح النص العريض المكسور
        analysis = re.sub(r'\*\*([^*\n]+)(?!\*\*)', r'**\1**', analysis)

        # استخدام التنظيف البسيط للمحتوى المُولد بالذكاء الاصطناعي
        from utils.utils import simple_ai_text_cleanup
        analysis = simple_ai_text_cleanup(analysis, lang)

        # إضافة عنوان محسن إذا لم يكن موجوداً
        if not analysis.startswith("📊") and not analysis.startswith("🤖"):
            if lang == 'ar':
                analysis = f"🤖 **تحليل محسن بالذكاء الاصطناعي** 📊\n\n💎 **عملة {symbol}**\n\n" + analysis
            else:
                analysis = f"🤖 **AI-Enhanced Analysis** 📊\n\n💎 **{symbol} Cryptocurrency**\n\n" + analysis

        # إضافة معلومات السعر إذا لم تكن موجودة - تم تحديثه لإصلاح مشكلة النقطتين
        # التحقق من صحة بيانات السعر
        try:
            # التأكد من أن السعر رقم
            if isinstance(price, str):
                try:
                    price = float(price)
                except ValueError:
                    logger.warning(f"لا يمكن تحويل السعر إلى رقم: {price}")
                    price = 0

            if price is None or price == 0:
                logger.warning(f"السعر غير صالح: {price}")
                price_str = "غير متوفر" if lang == 'ar' else "N/A"
                price_info_ar = f"💰 **السعر الحالي** {price_str}\n"
                price_info_en = f"💰 **Current Price** {price_str}\n"
            else:
                price_info_ar = f"💰 **السعر الحالي** {price:.4f} USDT\n"
                price_info_en = f"💰 **Current Price** {price:.4f} USDT\n"

            # التأكد من أن نسبة التغيير رقم
            if isinstance(price_change, str):
                try:
                    price_change = float(price_change)
                except ValueError:
                    logger.warning(f"لا يمكن تحويل نسبة التغيير إلى رقم: {price_change}")
                    price_change = 0

            if price_change is None or price_change == 0:
                logger.warning(f"نسبة التغيير غير صالحة: {price_change}")
                change_str = "غير متوفرة" if lang == 'ar' else "N/A"
                price_info_ar += f"📊 **نسبة التغير** {change_str}\n\n"
                price_info_en += f"📊 **Change** {change_str}\n\n"
            else:
                price_info_ar += f"📊 **نسبة التغير** {price_change:.2f}%\n\n"
                price_info_en += f"📊 **Change** {price_change:.2f}%\n\n"
        except Exception as e:
            logger.error(f"خطأ في تنسيق معلومات السعر: {str(e)}")
            # استخدام قيم افتراضية في حالة حدوث خطأ
            price_info_ar = "💰 **السعر الحالي** غير متوفر\n📊 **نسبة التغير** غير متوفرة\n\n"
            price_info_en = "💰 **Current Price** N/A\n📊 **Change** N/A\n\n"

        if lang == 'ar' and "السعر الحالي" not in analysis[:200]:
            # إضافة معلومات السعر بعد العنوان
            title_end = analysis.find("\n\n")
            if title_end != -1:
                analysis = analysis[:title_end+2] + price_info_ar + analysis[title_end+2:]
        elif lang == 'en' and "Current Price" not in analysis[:200]:
            # إضافة معلومات السعر بعد العنوان
            title_end = analysis.find("\n\n")
            if title_end != -1:
                analysis = analysis[:title_end+2] + price_info_en + analysis[title_end+2:]

        # تنسيق المؤشرات الفنية من اليمين إلى اليسار للغة العربية
        if lang == 'ar':
            # تحديد قسم المؤشرات الفنية
            indicators_section_start = analysis.find("المؤشرات الفنية:")
            if indicators_section_start != -1:
                # تقسيم التحليل إلى أجزاء
                before_indicators = analysis[:indicators_section_start + len("المؤشرات الفنية:")]
                indicators_section = analysis[indicators_section_start + len("المؤشرات الفنية:"):]

                # تحديد نهاية قسم المؤشرات الفنية
                next_section_start = indicators_section.find("\n\n")
                if next_section_start == -1:
                    indicators_text = indicators_section
                    after_indicators = ""
                else:
                    indicators_text = indicators_section[:next_section_start]
                    after_indicators = indicators_section[next_section_start:]

                # تنسيق المؤشرات الفنية
                indicators_lines = indicators_text.strip().split("\n")
                formatted_indicators = []

                for line in indicators_lines:
                    if line.strip():
                        # إزالة النقطة والمسافة في بداية السطر إذا وجدت
                        if line.strip().startswith("•"):
                            line = line.strip()[1:].strip()

                        # تقسيم السطر إلى اسم المؤشر والقيمة - تم تحديثه لإصلاح مشكلة النقطتين
                        parts = line.split(":")
                        if len(parts) == 2:
                            indicator_name = parts[0].strip()
                            indicator_value = parts[1].strip()
                            # إعادة تنسيق السطر من اليمين إلى اليسار بدون نقطتين
                            # إزالة النقطتين من اسم المؤشر إذا كانت موجودة
                            if indicator_name.endswith(':'):
                                indicator_name = indicator_name[:-1]
                            formatted_line = f"• {indicator_value} **{indicator_name}**"
                            formatted_indicators.append(formatted_line)
                        else:
                            formatted_indicators.append(f"• {line.strip()}")

                # إعادة بناء التحليل
                analysis = before_indicators + "\n" + "\n".join(formatted_indicators) + after_indicators

        # إزالة التنبيهات المكررة
        disclaimer_patterns = [
            r"\n\n⚠️.*?تعليمية فقط.*?استثمارية\.?$",
            r"\n\n⚠️.*?educational purposes.*?advice\.?$",
            r"\n\nملاحظة:.*?تعليمية فقط.*?استثمارية\.?$",
            r"\n\nNote:.*?educational purposes.*?advice\.?$",
            r"\n\nتنبيه:.*?تعليمية فقط.*?استثمارية\.?$",
            r"\n\nWarning:.*?educational purposes.*?advice\.?$"
        ]

        for pattern in disclaimer_patterns:
            analysis = re.sub(pattern, "", analysis)

        # إزالة الأسطر الفارغة المتكررة
        while "\n\n\n" in analysis:
            analysis = analysis.replace("\n\n\n", "\n\n")

        # استخدام دالة fix_bold_formatting لإصلاح تنسيق النص العريض
        analysis = fix_bold_formatting(analysis, lang)

        return analysis

    except Exception as e:
        logger.error(f"خطأ في تنسيق تحليل Gemini: {str(e)}")
        return analysis
