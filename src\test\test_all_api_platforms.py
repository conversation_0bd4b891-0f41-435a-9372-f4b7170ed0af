#!/usr/bin/env python3
"""
اختبار شامل لجميع منصات API المدعومة
"""

import asyncio
import logging
from unittest.mock import Mock, AsyncMock

# إعداد السجل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestAllAPIPlatforms:
    """فئة اختبار جميع منصات API"""
    
    def __init__(self):
        self.platforms = [
            'binance',
            'gemini', 
            'kucoin',
            'coinbase',
            'bybit',
            'okx',
            'kraken'
        ]
        
        # منصات تتطلب مفتاح وسر
        self.platforms_with_secret = [
            'binance', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken'
        ]
        
        # منصات تتطلب مفتاح فقط
        self.platforms_key_only = ['gemini']
    
    def test_callback_data_generation(self):
        """اختبار توليد callback_data لجميع المنصات"""
        logger.info("🔄 اختبار توليد callback_data...")
        
        failed_tests = []
        
        for platform in self.platforms:
            # اختبار setup callbacks
            setup_callback = f"setup_{platform}_api"
            delete_callback = f"delete_{platform}_api"
            
            # التحقق من صحة التنسيق
            if not setup_callback.startswith('setup_') or not setup_callback.endswith('_api'):
                failed_tests.append(f"setup callback format error: {setup_callback}")
            
            if not delete_callback.startswith('delete_') or not delete_callback.endswith('_api'):
                failed_tests.append(f"delete callback format error: {delete_callback}")
            
            logger.info(f"✅ {platform}: setup={setup_callback}, delete={delete_callback}")
        
        if failed_tests:
            logger.error(f"❌ فشل في اختبار callback_data: {failed_tests}")
            return False
        else:
            logger.info("✅ جميع callback_data صحيحة")
            return True
    
    def test_api_setup_states(self):
        """اختبار حالات إعداد API لجميع المنصات"""
        logger.info("🔄 اختبار حالات إعداد API...")
        
        failed_tests = []
        
        for platform in self.platforms:
            # حالة إدخال المفتاح
            key_state = f"{platform}_key"
            
            # حالة إدخال السر (للمنصات التي تتطلب ذلك)
            if platform in self.platforms_with_secret:
                secret_state = f"{platform}_secret"
                logger.info(f"✅ {platform}: key_state={key_state}, secret_state={secret_state}")
            else:
                logger.info(f"✅ {platform}: key_state={key_state} (key only)")
        
        logger.info("✅ جميع حالات إعداد API صحيحة")
        return True
    
    def test_platform_requirements(self):
        """اختبار متطلبات كل منصة"""
        logger.info("🔄 اختبار متطلبات المنصات...")
        
        # اختبار المنصات التي تتطلب مفتاح وسر
        for platform in self.platforms_with_secret:
            if platform not in self.platforms:
                logger.error(f"❌ منصة مفقودة: {platform}")
                return False
            logger.info(f"✅ {platform}: يتطلب مفتاح + سر")
        
        # اختبار المنصات التي تتطلب مفتاح فقط
        for platform in self.platforms_key_only:
            if platform not in self.platforms:
                logger.error(f"❌ منصة مفقودة: {platform}")
                return False
            logger.info(f"✅ {platform}: يتطلب مفتاح فقط")
        
        logger.info("✅ جميع متطلبات المنصات صحيحة")
        return True
    
    def test_api_manager_methods(self):
        """اختبار طرق مدير API"""
        logger.info("🔄 اختبار طرق مدير API...")
        
        # قائمة الطرق المطلوبة
        required_methods = [
            'save_api_key',
            'get_api_keys', 
            'delete_api_keys',
            'has_api_keys',
            'get_api_info'
        ]
        
        try:
            # محاولة استيراد APIManager
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            
            from api_manager import APIManager
            
            # التحقق من وجود الطرق المطلوبة
            for method in required_methods:
                if hasattr(APIManager, method):
                    logger.info(f"✅ طريقة موجودة: {method}")
                else:
                    logger.error(f"❌ طريقة مفقودة: {method}")
                    return False
            
            logger.info("✅ جميع طرق مدير API موجودة")
            return True
            
        except ImportError as e:
            logger.error(f"❌ فشل في استيراد APIManager: {str(e)}")
            return False
    
    def test_api_validators(self):
        """اختبار مدققات API"""
        logger.info("🔄 اختبار مدققات API...")
        
        try:
            from api_validators import verify_binance_api, verify_gemini_api
            
            logger.info("✅ verify_binance_api موجود")
            logger.info("✅ verify_gemini_api موجود")
            
            # التحقق من أن الدوال async
            if asyncio.iscoroutinefunction(verify_binance_api):
                logger.info("✅ verify_binance_api هو async function")
            else:
                logger.error("❌ verify_binance_api ليس async function")
                return False
            
            if asyncio.iscoroutinefunction(verify_gemini_api):
                logger.info("✅ verify_gemini_api هو async function")
            else:
                logger.error("❌ verify_gemini_api ليس async function")
                return False
            
            logger.info("✅ جميع مدققات API صحيحة")
            return True
            
        except ImportError as e:
            logger.error(f"❌ فشل في استيراد مدققات API: {str(e)}")
            return False
    
    def test_ui_functions(self):
        """اختبار دوال واجهة المستخدم"""
        logger.info("🔄 اختبار دوال واجهة المستخدم...")
        
        try:
            from api_ui import (
                setup_api_keys,
                show_platform_selection, 
                show_api_instructions,
                delete_api_keys_ui
            )
            
            # التحقق من أن الدوال async
            ui_functions = [
                ('setup_api_keys', setup_api_keys),
                ('show_platform_selection', show_platform_selection),
                ('show_api_instructions', show_api_instructions),
                ('delete_api_keys_ui', delete_api_keys_ui)
            ]
            
            for name, func in ui_functions:
                if asyncio.iscoroutinefunction(func):
                    logger.info(f"✅ {name} هو async function")
                else:
                    logger.error(f"❌ {name} ليس async function")
                    return False
            
            logger.info("✅ جميع دوال واجهة المستخدم صحيحة")
            return True
            
        except ImportError as e:
            logger.error(f"❌ فشل في استيراد دوال واجهة المستخدم: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🚀 بدء تشغيل اختبارات شاملة لجميع منصات API...")
        
        tests = [
            ("اختبار توليد callback_data", self.test_callback_data_generation),
            ("اختبار حالات إعداد API", self.test_api_setup_states),
            ("اختبار متطلبات المنصات", self.test_platform_requirements),
            ("اختبار طرق مدير API", self.test_api_manager_methods),
            ("اختبار مدققات API", self.test_api_validators),
            ("اختبار دوال واجهة المستخدم", self.test_ui_functions)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"🔄 تشغيل {test_name}...")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                if result:
                    logger.info(f"✅ {test_name}: نجح")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: فشل")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ {test_name}: خطأ - {str(e)}")
                failed += 1
        
        logger.info(f"📊 نتائج الاختبار الشامل: {passed} نجح، {failed} فشل")
        
        if failed == 0:
            logger.info("🎉 جميع اختبارات منصات API نجحت!")
            return True
        else:
            logger.error("❌ بعض اختبارات منصات API فشلت")
            return False

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = TestAllAPIPlatforms()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 جميع اختبارات منصات API نجحت!")
        print("✅ النظام جاهز للاستخدام مع جميع المنصات المدعومة")
        print(f"📋 المنصات المدعومة: {', '.join(tester.platforms)}")
    else:
        print("\n❌ بعض اختبارات منصات API فشلت")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    asyncio.run(main())
