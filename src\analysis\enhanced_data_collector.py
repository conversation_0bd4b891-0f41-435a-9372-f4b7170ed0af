"""
نظام جمع البيانات المحسن للتحليل متعدد الإطارات الزمنية
Enhanced Data Collector for Multi-Timeframe Analysis

هذا الملف يحتوي على نظام متطور لجمع البيانات من مصادر متعددة:
1. جمع بيانات متعددة الإطارات الزمنية
2. حساب المؤشرات المتخصصة لكل إطار
3. تحسين الأداء والتخزين المؤقت
4. معالجة الأخطاء والبيانات المفقودة
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

# استيراد المؤشرات المتقدمة
from .enhanced_multi_timeframe import AdvancedIndicators, TradingStyle, TimeframeConfig

# إعداد السجل
logger = logging.getLogger(__name__)

class EnhancedDataCollector:
    """نظام جمع البيانات المحسن"""
    
    def __init__(self, binance_manager=None, api_manager=None):
        self.binance_manager = binance_manager
        self.api_manager = api_manager
        self.advanced_indicators = AdvancedIndicators()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # إعدادات التخزين المؤقت
        self.cache_timeout = 300  # 5 دقائق
        self.data_cache = {}
        self.cache_timestamps = {}
    
    async def collect_multi_timeframe_data(self, symbol: str, trading_style: TradingStyle, 
                                         user_id: str = None) -> Dict[str, Dict[str, Any]]:
        """جمع البيانات من إطارات زمنية متعددة"""
        try:
            # الحصول على الإطارات الزمنية المطلوبة
            timeframes = TimeframeConfig.SMART_TIMEFRAMES[trading_style]
            
            # جمع البيانات بشكل متوازي
            tasks = []
            for timeframe in timeframes:
                task = self._collect_timeframe_data(symbol, timeframe, user_id)
                tasks.append(task)
            
            # انتظار جميع المهام
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # تجميع النتائج
            timeframes_data = {}
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"خطأ في جمع بيانات {timeframes[i]}: {str(result)}")
                    continue
                
                if result:
                    timeframes_data[timeframes[i]] = result
            
            return timeframes_data
            
        except Exception as e:
            logger.error(f"خطأ في جمع البيانات متعددة الإطارات: {str(e)}")
            return {}
    
    async def _collect_timeframe_data(self, symbol: str, timeframe: str, user_id: str = None) -> Optional[Dict[str, Any]]:
        """جمع بيانات إطار زمني واحد"""
        try:
            # التحقق من التخزين المؤقت
            cache_key = f"{symbol}_{timeframe}_{user_id}"
            if self._is_cache_valid(cache_key):
                return self.data_cache[cache_key]
            
            # جمع البيانات الخام
            raw_data = await self._fetch_raw_data(symbol, timeframe, user_id)
            if not raw_data:
                return None
            
            # حساب المؤشرات المتخصصة
            enhanced_data = await self._calculate_enhanced_indicators(raw_data, timeframe)
            
            # تخزين في الذاكرة المؤقتة
            self.data_cache[cache_key] = enhanced_data
            self.cache_timestamps[cache_key] = datetime.now()
            
            return enhanced_data
            
        except Exception as e:
            logger.error(f"خطأ في جمع بيانات {timeframe}: {str(e)}")
            return None
    
    async def _fetch_raw_data(self, symbol: str, timeframe: str, user_id: str = None) -> Optional[List]:
        """جلب البيانات الخام من Binance"""
        try:
            if not self.binance_manager:
                return None
            
            # تحديد عدد الشموع المطلوبة حسب الإطار الزمني
            limit = self._get_optimal_limit(timeframe)
            
            # جلب البيانات
            klines = await self.binance_manager.get_klines(
                symbol=symbol,
                interval=timeframe,
                limit=limit,
                user_id=user_id
            )
            
            return klines
            
        except Exception as e:
            logger.error(f"خطأ في جلب البيانات الخام: {str(e)}")
            return None
    
    def _get_optimal_limit(self, timeframe: str) -> int:
        """تحديد العدد الأمثل للشموع حسب الإطار الزمني"""
        limits = {
            '1m': 200,
            '5m': 200,
            '15m': 200,
            '1h': 168,   # أسبوع
            '4h': 180,   # شهر
            '1d': 100,   # 3 أشهر
            '1w': 52,    # سنة
            '1M': 24     # سنتان
        }
        return limits.get(timeframe, 100)
    
    async def _calculate_enhanced_indicators(self, klines: List, timeframe: str) -> Dict[str, Any]:
        """حساب المؤشرات المحسنة"""
        try:
            if not klines or len(klines) < 50:
                return {}
            
            # تحويل البيانات إلى DataFrame
            df = self._klines_to_dataframe(klines)
            
            # حساب المؤشرات الأساسية
            basic_indicators = await self._calculate_basic_indicators(df)
            
            # حساب المؤشرات المتقدمة
            advanced_indicators = await self._calculate_timeframe_specific_indicators(df, timeframe)
            
            # حساب مؤشرات الحجم
            volume_indicators = self._calculate_volume_indicators(df)
            
            # دمج جميع المؤشرات
            all_indicators = {
                **basic_indicators,
                **advanced_indicators,
                **volume_indicators
            }
            
            # إضافة معلومات السعر الحالي
            current_data = self._extract_current_price_data(df)
            all_indicators.update(current_data)
            
            return all_indicators
            
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات المحسنة: {str(e)}")
            return {}
    
    def _klines_to_dataframe(self, klines: List) -> pd.DataFrame:
        """تحويل بيانات الشموع إلى DataFrame"""
        try:
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # تحويل أنواع البيانات
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # تحويل الوقت
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"خطأ في تحويل البيانات: {str(e)}")
            return pd.DataFrame()
    
    async def _calculate_basic_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """حساب المؤشرات الأساسية"""
        try:
            indicators = {}
            
            # RSI
            indicators['rsi'] = self._calculate_rsi(df['close'])
            
            # المتوسطات المتحركة
            indicators['ema20'] = df['close'].ewm(span=20).mean().iloc[-1]
            indicators['ema50'] = df['close'].ewm(span=50).mean().iloc[-1]
            indicators['sma50'] = df['close'].rolling(window=50).mean().iloc[-1]
            indicators['sma200'] = df['close'].rolling(window=200).mean().iloc[-1] if len(df) >= 200 else None
            
            # MACD
            macd_data = self._calculate_macd(df['close'])
            indicators.update(macd_data)
            
            # Bollinger Bands
            bb_data = self._calculate_bollinger_bands(df['close'])
            indicators.update(bb_data)
            
            # Stochastic
            stoch_data = self._calculate_stochastic(df['high'], df['low'], df['close'])
            indicators.update(stoch_data)
            
            # ADX
            adx_data = self._calculate_adx(df['high'], df['low'], df['close'])
            indicators.update(adx_data)
            
            return indicators
            
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات الأساسية: {str(e)}")
            return {}
    
    async def _calculate_timeframe_specific_indicators(self, df: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """حساب المؤشرات المتخصصة حسب الإطار الزمني"""
        try:
            indicators = {}
            
            # تحديد نوع الإطار الزمني
            if timeframe in ['1m', '5m', '15m']:
                # مؤشرات قصيرة المدى
                indicators['williams_r'] = self.advanced_indicators.calculate_williams_r(
                    df['high'], df['low'], df['close']
                ).iloc[-1]
                
                indicators['cci'] = self.advanced_indicators.calculate_cci(
                    df['high'], df['low'], df['close']
                ).iloc[-1]
                
            elif timeframe in ['1h', '4h']:
                # مؤشرات متوسطة المدى
                indicators['atr'] = self.advanced_indicators.calculate_atr(
                    df['high'], df['low'], df['close']
                ).iloc[-1]
                
                indicators['parabolic_sar'] = self.advanced_indicators.calculate_parabolic_sar(
                    df['high'], df['low'], df['close']
                ).iloc[-1]
                
                indicators['trend_strength'] = self.advanced_indicators.calculate_trend_strength(
                    df['close']
                ).iloc[-1]
                
            else:
                # مؤشرات طويلة المدى (1d, 1w, 1M)
                ichimoku_data = self._calculate_ichimoku(df)
                indicators.update(ichimoku_data)
            
            return indicators
            
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات المتخصصة: {str(e)}")
            return {}
    
    def _calculate_volume_indicators(self, df: pd.DataFrame) -> Dict[str, Any]:
        """حساب مؤشرات الحجم"""
        try:
            volume_data = self.advanced_indicators.calculate_volume_indicators(
                df['volume'], df['close']
            )
            
            return {
                'volume_sma': volume_data['volume_sma'].iloc[-1],
                'volume_ratio': volume_data['volume_ratio'].iloc[-1],
                'obv': volume_data['obv'].iloc[-1]
            }
            
        except Exception as e:
            logger.error(f"خطأ في حساب مؤشرات الحجم: {str(e)}")
            return {}
    
    def _extract_current_price_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """استخراج بيانات السعر الحالي"""
        try:
            current_price = float(df['close'].iloc[-1])
            previous_price = float(df['close'].iloc[-2]) if len(df) > 1 else current_price
            
            price_change = ((current_price - previous_price) / previous_price) * 100
            
            return {
                'price': current_price,
                'price_change': price_change,
                'high_24h': float(df['high'].tail(24).max()) if len(df) >= 24 else float(df['high'].max()),
                'low_24h': float(df['low'].tail(24).min()) if len(df) >= 24 else float(df['low'].min()),
                'volume_24h': float(df['volume'].tail(24).sum()) if len(df) >= 24 else float(df['volume'].sum())
            }
            
        except Exception as e:
            logger.error(f"خطأ في استخراج بيانات السعر: {str(e)}")
            return {'price': 0, 'price_change': 0}
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """التحقق من صحة التخزين المؤقت"""
        try:
            if cache_key not in self.cache_timestamps:
                return False
            
            cache_time = self.cache_timestamps[cache_key]
            current_time = datetime.now()
            
            return (current_time - cache_time).total_seconds() < self.cache_timeout
            
        except Exception:
            return False

    # دوال حساب المؤشرات المساعدة
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """حساب مؤشر RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return float(rsi.iloc[-1])
        except Exception as e:
            logger.error(f"خطأ في حساب RSI: {str(e)}")
            return 50.0

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, float]:
        """حساب مؤشر MACD"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            macd_signal = macd.ewm(span=signal).mean()
            macd_histogram = macd - macd_signal

            return {
                'macd': float(macd.iloc[-1]),
                'macd_signal': float(macd_signal.iloc[-1]),
                'macd_histogram': float(macd_histogram.iloc[-1])
            }
        except Exception as e:
            logger.error(f"خطأ في حساب MACD: {str(e)}")
            return {'macd': 0, 'macd_signal': 0, 'macd_histogram': 0}

    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: int = 2) -> Dict[str, float]:
        """حساب نطاقات بولينجر"""
        try:
            sma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()

            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)

            return {
                'bb_upper': float(upper_band.iloc[-1]),
                'bb_middle': float(sma.iloc[-1]),
                'bb_lower': float(lower_band.iloc[-1])
            }
        except Exception as e:
            logger.error(f"خطأ في حساب Bollinger Bands: {str(e)}")
            return {'bb_upper': 0, 'bb_middle': 0, 'bb_lower': 0}

    def _calculate_stochastic(self, high: pd.Series, low: pd.Series, close: pd.Series,
                            k_period: int = 14, d_period: int = 3) -> Dict[str, float]:
        """حساب مؤشر Stochastic"""
        try:
            lowest_low = low.rolling(window=k_period).min()
            highest_high = high.rolling(window=k_period).max()

            k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
            d_percent = k_percent.rolling(window=d_period).mean()

            return {
                'stoch_k': float(k_percent.iloc[-1]),
                'stoch_d': float(d_percent.iloc[-1])
            }
        except Exception as e:
            logger.error(f"خطأ في حساب Stochastic: {str(e)}")
            return {'stoch_k': 50, 'stoch_d': 50}

    def _calculate_adx(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> Dict[str, float]:
        """حساب مؤشر ADX"""
        try:
            # حساب True Range
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

            # حساب Directional Movement
            plus_dm = high.diff()
            minus_dm = low.diff()

            plus_dm = plus_dm.where((plus_dm > minus_dm) & (plus_dm > 0), 0)
            minus_dm = minus_dm.where((minus_dm > plus_dm) & (minus_dm > 0), 0)
            minus_dm = abs(minus_dm)

            # حساب Smoothed values
            atr = true_range.rolling(window=period).mean()
            plus_di = 100 * (plus_dm.rolling(window=period).mean() / atr)
            minus_di = 100 * (minus_dm.rolling(window=period).mean() / atr)

            # حساب ADX
            dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
            adx = dx.rolling(window=period).mean()

            return {
                'adx': float(adx.iloc[-1]) if not pd.isna(adx.iloc[-1]) else 25,
                'plus_di': float(plus_di.iloc[-1]) if not pd.isna(plus_di.iloc[-1]) else 25,
                'minus_di': float(minus_di.iloc[-1]) if not pd.isna(minus_di.iloc[-1]) else 25
            }
        except Exception as e:
            logger.error(f"خطأ في حساب ADX: {str(e)}")
            return {'adx': 25, 'plus_di': 25, 'minus_di': 25}

    def _calculate_ichimoku(self, df: pd.DataFrame) -> Dict[str, float]:
        """حساب مؤشر Ichimoku Cloud"""
        try:
            # Tenkan-sen (Conversion Line): (9-period high + 9-period low)/2
            tenkan_sen = (df['high'].rolling(window=9).max() + df['low'].rolling(window=9).min()) / 2

            # Kijun-sen (Base Line): (26-period high + 26-period low)/2
            kijun_sen = (df['high'].rolling(window=26).max() + df['low'].rolling(window=26).min()) / 2

            # Senkou Span A (Leading Span A): (Conversion Line + Base Line)/2
            senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(26)

            # Senkou Span B (Leading Span B): (52-period high + 52-period low)/2
            senkou_span_b = ((df['high'].rolling(window=52).max() + df['low'].rolling(window=52).min()) / 2).shift(26)

            return {
                'ichimoku_tenkan': float(tenkan_sen.iloc[-1]) if not pd.isna(tenkan_sen.iloc[-1]) else 0,
                'ichimoku_kijun': float(kijun_sen.iloc[-1]) if not pd.isna(kijun_sen.iloc[-1]) else 0,
                'ichimoku_senkou_a': float(senkou_span_a.iloc[-1]) if not pd.isna(senkou_span_a.iloc[-1]) else 0,
                'ichimoku_senkou_b': float(senkou_span_b.iloc[-1]) if not pd.isna(senkou_span_b.iloc[-1]) else 0
            }
        except Exception as e:
            logger.error(f"خطأ في حساب Ichimoku: {str(e)}")
            return {
                'ichimoku_tenkan': 0,
                'ichimoku_kijun': 0,
                'ichimoku_senkou_a': 0,
                'ichimoku_senkou_b': 0
            }

    def clear_cache(self):
        """مسح التخزين المؤقت"""
        try:
            self.data_cache.clear()
            self.cache_timestamps.clear()
            logger.info("تم مسح التخزين المؤقت بنجاح")
        except Exception as e:
            logger.error(f"خطأ في مسح التخزين المؤقت: {str(e)}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين المؤقت"""
        try:
            current_time = datetime.now()
            valid_entries = 0
            expired_entries = 0

            for key, timestamp in self.cache_timestamps.items():
                if (current_time - timestamp).total_seconds() < self.cache_timeout:
                    valid_entries += 1
                else:
                    expired_entries += 1

            return {
                'total_entries': len(self.data_cache),
                'valid_entries': valid_entries,
                'expired_entries': expired_entries,
                'cache_timeout': self.cache_timeout,
                'memory_usage_mb': len(str(self.data_cache)) / (1024 * 1024)
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات التخزين المؤقت: {str(e)}")
            return {}
