#!/usr/bin/env python3
"""
سكريبت إصلاح مشاكل اللغة في نظام الأخبار الذكي
يقوم بفحص وإصلاح جميع مشاكل اللغة في قاعدة البيانات
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# تهيئة Firebase أولاً
try:
    from integrations.firebase_init import initialize_firebase
    db = initialize_firebase()
    if not db:
        print("❌ فشل في تهيئة Firebase")
        sys.exit(1)
    print("✅ تم تهيئة Firebase بنجاح")
except Exception as e:
    print(f"❌ خطأ في تهيئة Firebase: {str(e)}")
    sys.exit(1)

# استيراد الوحدات المطلوبة
try:
    from services.automatic_news_notifications import AutomaticNewsNotifications
    from services.subscription_system import get_subscription_system
    print("✅ تم استيراد الوحدات بنجاح")
except Exception as e:
    print(f"❌ خطأ في استيراد الوحدات: {str(e)}")
    sys.exit(1)

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('language_fix.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LanguageFixer:
    """فئة إصلاح مشاكل اللغة"""

    def __init__(self):
        self.db = db
        # إنشاء نظام الأخبار بدون bot للاختبار
        self.news_system = AutomaticNewsNotifications(db, None)
        self.subscription_system = get_subscription_system(db)
        self.stats = {
            'users_processed': 0,
            'users_fixed': 0,
            'errors': 0,
            'inconsistencies_found': 0
        }
    
    async def run_full_fix(self):
        """تشغيل إصلاح شامل لجميع مشاكل اللغة"""
        logger.info("🚀 بدء إصلاح مشاكل اللغة الشامل...")
        
        try:
            # 1. فحص وإصلاح التناقضات الموجودة
            await self._fix_existing_inconsistencies()
            
            # 2. توحيد أسماء الحقول
            await self._standardize_field_names()
            
            # 3. إنشاء إعدادات افتراضية للمستخدمين الجدد
            await self._create_missing_settings()
            
            # 4. التحقق من النتائج
            await self._verify_fixes()
            
            # 5. طباعة التقرير النهائي
            self._print_final_report()
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل الإصلاح الشامل: {str(e)}")
    
    async def _fix_existing_inconsistencies(self):
        """إصلاح التناقضات الموجودة في بيانات اللغة"""
        logger.info("🔧 إصلاح التناقضات الموجودة...")
        
        try:
            # الحصول على جميع المستخدمين من user_settings
            settings_docs = self.db.collection('user_settings').get()
            
            for doc in settings_docs:
                user_id = doc.id
                if user_id.startswith('_'):  # تجاهل الوثائق الوصفية
                    continue
                
                self.stats['users_processed'] += 1
                
                try:
                    # فحص تناسق اللغة للمستخدم
                    original_lang = await self.news_system._get_user_language(user_id)
                    fixed_lang = await self.news_system.verify_and_fix_user_language_consistency(user_id)
                    
                    if original_lang != fixed_lang:
                        self.stats['users_fixed'] += 1
                        self.stats['inconsistencies_found'] += 1
                        logger.info(f"✅ إصلاح تناقض للمستخدم {user_id}: {original_lang} → {fixed_lang}")
                    
                    # تجنب الحمل الزائد
                    if self.stats['users_processed'] % 10 == 0:
                        await asyncio.sleep(0.1)
                        logger.info(f"📊 تم معالجة {self.stats['users_processed']} مستخدم...")
                        
                except Exception as e:
                    self.stats['errors'] += 1
                    logger.error(f"❌ خطأ في معالجة المستخدم {user_id}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح التناقضات: {str(e)}")
    
    async def _standardize_field_names(self):
        """توحيد أسماء حقول اللغة"""
        logger.info("📝 توحيد أسماء حقول اللغة...")
        
        try:
            # توحيد user_settings
            await self._standardize_collection('user_settings', primary_field='lang', secondary_field='language')
            
            # توحيد notification_preferences
            await self._standardize_collection('notification_preferences', primary_field='language', secondary_field='lang')
            
        except Exception as e:
            logger.error(f"❌ خطأ في توحيد أسماء الحقول: {str(e)}")
    
    async def _standardize_collection(self, collection_name, primary_field, secondary_field):
        """توحيد حقول اللغة في مجموعة معينة"""
        logger.info(f"🔄 توحيد {collection_name}...")
        
        try:
            docs = self.db.collection(collection_name).get()
            updated_count = 0
            
            for doc in docs:
                if doc.id.startswith('_'):
                    continue
                
                data = doc.to_dict()
                if not data:
                    continue
                
                # فحص وتوحيد حقول اللغة
                primary_value = data.get(primary_field)
                secondary_value = data.get(secondary_field)
                
                # تحديد القيمة الصحيحة
                correct_lang = None
                if primary_value and primary_value in ['ar', 'en']:
                    correct_lang = primary_value
                elif secondary_value and secondary_value in ['ar', 'en']:
                    correct_lang = secondary_value
                else:
                    correct_lang = 'ar'  # افتراضي
                
                # تحديث الوثيقة إذا لزم الأمر
                update_needed = False
                update_data = {}
                
                if data.get(primary_field) != correct_lang:
                    update_data[primary_field] = correct_lang
                    update_needed = True
                
                if data.get(secondary_field) != correct_lang:
                    update_data[secondary_field] = correct_lang
                    update_needed = True
                
                if update_needed:
                    update_data['updated_at'] = datetime.now().isoformat()
                    doc.reference.update(update_data)
                    updated_count += 1
                    logger.debug(f"✅ تحديث {collection_name}/{doc.id}: {update_data}")
            
            logger.info(f"✅ تم توحيد {updated_count} وثيقة في {collection_name}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في توحيد {collection_name}: {str(e)}")
    
    async def _create_missing_settings(self):
        """إنشاء إعدادات مفقودة للمستخدمين"""
        logger.info("🆕 إنشاء إعدادات مفقودة...")
        
        try:
            # البحث عن مستخدمين بدون إعدادات
            users_docs = self.db.collection('users').get()
            missing_settings_count = 0
            
            for doc in users_docs:
                user_id = doc.id
                if user_id.startswith('_'):
                    continue
                
                # فحص وجود إعدادات
                settings_doc = self.db.collection('user_settings').document(user_id).get()
                if not settings_doc.exists:
                    # إنشاء إعدادات افتراضية
                    detected_lang = await self.news_system._detect_user_language_from_telegram(user_id)
                    await self.news_system._create_default_user_settings(user_id, detected_lang)
                    missing_settings_count += 1
                    logger.info(f"✅ إنشاء إعدادات للمستخدم {user_id} باللغة {detected_lang}")
            
            logger.info(f"✅ تم إنشاء إعدادات لـ {missing_settings_count} مستخدم")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الإعدادات المفقودة: {str(e)}")
    
    async def _verify_fixes(self):
        """التحقق من نجاح الإصلاحات"""
        logger.info("🔍 التحقق من نجاح الإصلاحات...")
        
        try:
            # فحص عينة من المستخدمين
            settings_docs = self.db.collection('user_settings').limit(20).get()
            verification_passed = 0
            verification_failed = 0
            
            for doc in settings_docs:
                user_id = doc.id
                if user_id.startswith('_'):
                    continue
                
                try:
                    # فحص تناسق اللغة
                    lang = await self.news_system.verify_and_fix_user_language_consistency(user_id)
                    if lang in ['ar', 'en']:
                        verification_passed += 1
                    else:
                        verification_failed += 1
                        logger.warning(f"⚠️ فشل التحقق للمستخدم {user_id}: {lang}")
                        
                except Exception as e:
                    verification_failed += 1
                    logger.error(f"❌ خطأ في التحقق للمستخدم {user_id}: {str(e)}")
            
            logger.info(f"📊 نتائج التحقق: نجح {verification_passed}, فشل {verification_failed}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق: {str(e)}")
    
    def _print_final_report(self):
        """طباعة التقرير النهائي"""
        logger.info("📋 التقرير النهائي لإصلاح مشاكل اللغة:")
        logger.info(f"   👥 المستخدمين المعالجين: {self.stats['users_processed']}")
        logger.info(f"   🔧 المستخدمين المُصلحين: {self.stats['users_fixed']}")
        logger.info(f"   ⚠️ التناقضات المكتشفة: {self.stats['inconsistencies_found']}")
        logger.info(f"   ❌ الأخطاء: {self.stats['errors']}")
        
        if self.stats['errors'] == 0:
            logger.info("✅ تم إكمال الإصلاح بنجاح بدون أخطاء!")
        else:
            logger.warning(f"⚠️ تم إكمال الإصلاح مع {self.stats['errors']} أخطاء")

async def main():
    """الدالة الرئيسية"""
    logger.info("🚀 بدء سكريبت إصلاح مشاكل اللغة...")
    
    try:
        fixer = LanguageFixer()
        await fixer.run_full_fix()
        logger.info("✅ تم إكمال سكريبت الإصلاح بنجاح!")
        
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل السكريبت: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
