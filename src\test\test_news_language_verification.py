#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحقق من إرسال الأخبار حسب اللغة المختارة
"""

import os
import sys

def test_language_support_in_notifications():
    """اختبار دعم اللغات في نظام الإشعارات"""
    print("🧪 اختبار دعم اللغات في نظام الإشعارات...")
    
    # قراءة ملف الإشعارات للتحقق من دعم اللغات
    notifications_file = "services/automatic_news_notifications.py"
    
    if not os.path.exists(notifications_file):
        print("❌ ملف الإشعارات غير موجود")
        return False
    
    with open(notifications_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من وجود دعم اللغة الإنجليزية
    english_support_checks = [
        "lang == 'en'",
        "Breaking News",
        "New Coin", 
        "Market Analysis",
        "English language"
    ]
    
    # التحقق من وجود دعم اللغة العربية
    arabic_support_checks = [
        "lang == 'ar'",
        "خبر عاجل",
        "عملة جديدة",
        "تحليل السوق",
        "اللغة العربية"
    ]
    
    print("🔍 التحقق من دعم اللغة الإنجليزية:")
    english_score = 0
    for check in english_support_checks:
        if check in content:
            print(f"  ✅ {check}")
            english_score += 1
        else:
            print(f"  ❌ {check}")
    
    print(f"\n🔍 التحقق من دعم اللغة العربية:")
    arabic_score = 0
    for check in arabic_support_checks:
        if check in content:
            print(f"  ✅ {check}")
            arabic_score += 1
        else:
            print(f"  ❌ {check}")
    
    # التحقق من وجود دالة تحديد اللغة
    language_detection_checks = [
        "_get_user_language",
        "user_settings",
        "lang",
        "language"
    ]
    
    print(f"\n🔍 التحقق من آلية تحديد اللغة:")
    lang_detection_score = 0
    for check in language_detection_checks:
        if check in content:
            print(f"  ✅ {check}")
            lang_detection_score += 1
        else:
            print(f"  ❌ {check}")
    
    # التحقق من آلية الحذف الفوري
    cleanup_checks = [
        "_cleanup_successfully_sent_news",
        "_mark_news_sent_to_user", 
        "sent_to_users",
        "interested_users",
        "delete()"
    ]
    
    print(f"\n🔍 التحقق من آلية الحذف الفوري:")
    cleanup_score = 0
    for check in cleanup_checks:
        if check in content:
            print(f"  ✅ {check}")
            cleanup_score += 1
        else:
            print(f"  ❌ {check}")
    
    # النتائج النهائية
    total_score = english_score + arabic_score + lang_detection_score + cleanup_score
    max_score = len(english_support_checks) + len(arabic_support_checks) + len(language_detection_checks) + len(cleanup_checks)
    
    print(f"\n📊 النتائج النهائية:")
    print(f"  🇬🇧 دعم الإنجليزية: {english_score}/{len(english_support_checks)}")
    print(f"  🇸🇦 دعم العربية: {arabic_score}/{len(arabic_support_checks)}")
    print(f"  🔍 تحديد اللغة: {lang_detection_score}/{len(language_detection_checks)}")
    print(f"  🗑️ الحذف الفوري: {cleanup_score}/{len(cleanup_checks)}")
    print(f"  📈 النتيجة الإجمالية: {total_score}/{max_score} ({total_score/max_score*100:.1f}%)")
    
    if total_score >= max_score * 0.8:
        print("✅ النظام يدعم اللغات والحذف الفوري بشكل جيد!")
        return True
    else:
        print("⚠️ النظام يحتاج إلى تحسينات في دعم اللغات أو الحذف الفوري")
        return False

def test_news_scheduler_cleanup():
    """اختبار آلية التنظيف في جدولة الأخبار"""
    print("\n🧪 اختبار آلية التنظيف في جدولة الأخبار...")
    
    scheduler_file = "services/automatic_news_scheduler.py"
    
    if not os.path.exists(scheduler_file):
        print("❌ ملف جدولة الأخبار غير موجود")
        return False
    
    with open(scheduler_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # التحقق من آلية التنظيف القديمة
    old_cleanup_checks = [
        "_cleanup_old_data",
        "timedelta(days=3)",
        "CronTrigger(hour=2",
        "delete()"
    ]
    
    # التحقق من الحقول الجديدة في حفظ الأخبار
    new_fields_checks = [
        "interested_users",
        "sent_to_users", 
        "total_interested_users",
        "is_sent_to_all"
    ]
    
    print("🔍 التحقق من آلية التنظيف القديمة:")
    old_cleanup_score = 0
    for check in old_cleanup_checks:
        if check in content:
            print(f"  ✅ {check}")
            old_cleanup_score += 1
        else:
            print(f"  ❌ {check}")
    
    print(f"\n🔍 التحقق من الحقول الجديدة:")
    new_fields_score = 0
    for check in new_fields_checks:
        if check in content:
            print(f"  ✅ {check}")
            new_fields_score += 1
        else:
            print(f"  ❌ {check}")
    
    total_score = old_cleanup_score + new_fields_score
    max_score = len(old_cleanup_checks) + len(new_fields_checks)
    
    print(f"\n📊 النتائج:")
    print(f"  🧹 التنظيف القديم: {old_cleanup_score}/{len(old_cleanup_checks)}")
    print(f"  🆕 الحقول الجديدة: {new_fields_score}/{len(new_fields_checks)}")
    print(f"  📈 النتيجة الإجمالية: {total_score}/{max_score} ({total_score/max_score*100:.1f}%)")
    
    return total_score >= max_score * 0.75

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبار نظام الأخبار المحسن...")
    print("=" * 60)
    
    # اختبار دعم اللغات والحذف الفوري
    notifications_ok = test_language_support_in_notifications()
    
    # اختبار جدولة الأخبار
    scheduler_ok = test_news_scheduler_cleanup()
    
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    
    if notifications_ok:
        print("✅ نظام الإشعارات: يدعم اللغات والحذف الفوري")
    else:
        print("❌ نظام الإشعارات: يحتاج تحسينات")
    
    if scheduler_ok:
        print("✅ جدولة الأخبار: تم تحديثها بالحقول الجديدة")
    else:
        print("❌ جدولة الأخبار: تحتاج تحديثات")
    
    if notifications_ok and scheduler_ok:
        print("\n🎉 النظام جاهز! يدعم:")
        print("  📱 إرسال الأخبار حسب لغة المستخدم")
        print("  🗑️ حذف الأخبار فوراً بعد الإرسال الناجح")
        print("  🔄 تتبع حالة الإرسال لكل مستخدم")
    else:
        print("\n⚠️ النظام يحتاج إلى مراجعة وتحسينات إضافية")

if __name__ == "__main__":
    main()
