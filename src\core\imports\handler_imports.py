"""
🎮 استيرادات معالجات الواجهة
============================

جميع معالجات واجهة المستخدم والتفاعل مع Telegram.
هذه المعالجات تدير التفاعل مع المستخدمين والقوائم.

الفئات:
- معالجات القوائم الرئيسية
- معالجات الإعدادات
- معالجات القوائم الفرعية
- معالجات الواجهة الرئيسية
"""

# ===== معالجات القوائم والإعدادات =====
try:
    from handlers import (
        # معالجات القوائم
        show_language_selection,
        show_terms_and_conditions,
        show_enhanced_analysis_explanation,
        show_analysis_comparison,
        show_upgrade_info,
        
        # معالجات الإعدادات
        set_trading_style,
        set_analysis_type,
        reset_enhanced_settings,
        set_language
    )
    MAIN_HANDLERS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد المعالجات الرئيسية: {e}")
    MAIN_HANDLERS_AVAILABLE = False

# ===== معالجات القوائم الفرعية =====
try:
    from handlers.menu_handlers import (
        show_enhanced_settings,
        show_trading_style_options,
        show_analysis_type_options
    )
    MENU_HANDLERS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد معالجات القوائم: {e}")
    MENU_HANDLERS_AVAILABLE = False

# ===== معالجات الواجهة الرئيسية =====
try:
    from handlers.main_handlers import (
        initialize_main_handlers,
        show_main_menu,
        help_command,
        button_click,
        handle_message,
        handle_trading_education_callback,
        handle_ai_tutor_message_wrapper
    )
    CORE_HANDLERS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد معالجات الواجهة الرئيسية: {e}")
    CORE_HANDLERS_AVAILABLE = False

# تجميع جميع معالجات الواجهة
all_handler_functions = {
    'main_handlers': {
        'available': MAIN_HANDLERS_AVAILABLE,
        'functions': [
            'show_language_selection', 'show_terms_and_conditions',
            'show_enhanced_analysis_explanation', 'show_analysis_comparison',
            'show_upgrade_info', 'set_trading_style', 'set_analysis_type',
            'reset_enhanced_settings', 'set_language'
        ],
        'critical': True
    },
    'menu_handlers': {
        'available': MENU_HANDLERS_AVAILABLE,
        'functions': [
            'show_enhanced_settings', 'show_trading_style_options',
            'show_analysis_type_options'
        ],
        'critical': True
    },
    'core_handlers': {
        'available': CORE_HANDLERS_AVAILABLE,
        'functions': [
            'initialize_main_handlers', 'show_main_menu', 'help_command',
            'button_click', 'handle_message', 'handle_trading_education_callback',
            'handle_ai_tutor_message_wrapper'
        ],
        'critical': True
    }
}

__all__ = ['all_handler_functions'] + [
    'MAIN_HANDLERS_AVAILABLE', 'MENU_HANDLERS_AVAILABLE', 'CORE_HANDLERS_AVAILABLE'
]

def get_handler_status():
    """
    حالة توفر جميع معالجات الواجهة
    
    Returns:
        dict: حالة كل مجموعة معالجات
    """
    return {
        category: {
            'available': info['available'],
            'critical': info['critical'],
            'functions_count': len(info['functions'])
        }
        for category, info in all_handler_functions.items()
    }

def validate_critical_handlers():
    """
    التحقق من توفر معالجات الواجهة الحرجة
    
    Returns:
        tuple: (success: bool, missing_critical: list)
    """
    missing_critical = []
    
    for category, info in all_handler_functions.items():
        if info['critical'] and not info['available']:
            missing_critical.append(category)
    
    return len(missing_critical) == 0, missing_critical

# اختبار فوري لمعالجات الواجهة
if __name__ == "__main__":
    success, missing = validate_critical_handlers()
    if success:
        print("✅ جميع معالجات الواجهة الحرجة متوفرة")
    else:
        print(f"❌ معالجات واجهة حرجة مفقودة: {missing}")
        
    # عرض حالة كل مجموعة
    status = get_handler_status()
    print("\n🎮 حالة معالجات الواجهة:")
    for category, info in status.items():
        status_icon = "✅" if info['available'] else "❌"
        critical_icon = "🔴" if info['critical'] else "🟡"
        print(f"{status_icon} {critical_icon} {category}: {info['functions_count']} دالة")
