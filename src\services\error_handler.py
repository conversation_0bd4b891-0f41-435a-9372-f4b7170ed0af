"""
خدمة معالجة الأخطاء والتسجيل المتقدمة
تحتوي على جميع دوال معالجة الأخطاء والاستثناءات والتسجيل
"""

import logging
import traceback
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any, Union
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ContextTypes
from telegram.constants import ParseMode

# إعداد نظام التسجيل المتقدم
class ErrorHandler:
    """فئة شاملة لمعالجة الأخطاء والتسجيل"""
    
    def __init__(self, db=None):
        self.db = db
        self.logger = logging.getLogger(__name__)
        self.error_counts = {}  # عداد الأخطاء لكل نوع
        self.last_errors = {}   # آخر الأخطاء المسجلة
        
    def setup_logging(self, level=logging.INFO):
        """إعداد نظام التسجيل"""
        logging.basicConfig(
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            level=level
        )
        
        # تعطيل سجلات غير ضرورية
        logging.getLogger("httpx").setLevel(logging.WARNING)
        logging.getLogger("apscheduler").setLevel(logging.WARNING)
        
    def log_error(self, error_type: str, error_message: str, user_id: str = None, 
                  context: str = None, exception: Exception = None):
        """تسجيل الأخطاء مع تفاصيل إضافية"""
        try:
            # تسجيل الخطأ في السجلات
            if exception:
                self.logger.error(f"[{error_type}] {error_message}", exc_info=True)
            else:
                self.logger.error(f"[{error_type}] {error_message}")
            
            # تحديث عداد الأخطاء
            if error_type not in self.error_counts:
                self.error_counts[error_type] = 0
            self.error_counts[error_type] += 1
            
            # حفظ آخر خطأ
            self.last_errors[error_type] = {
                'message': error_message,
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'context': context
            }
            
            # حفظ في قاعدة البيانات إذا كانت متاحة
            if self.db and user_id:
                self._save_error_to_db(error_type, error_message, user_id, context)
                
        except Exception as e:
            # تجنب حلقة لا نهائية من الأخطاء
            print(f"خطأ في تسجيل الخطأ: {str(e)}")
    
    def _save_error_to_db(self, error_type: str, error_message: str, 
                         user_id: str, context: str = None):
        """حفظ الخطأ في قاعدة البيانات"""
        try:
            error_data = {
                'type': error_type,
                'message': error_message,
                'user_id': user_id,
                'context': context,
                'timestamp': datetime.now().isoformat()
            }
            
            # حفظ في مجموعة الأخطاء
            self.db.collection('errors').add(error_data)
            
        except Exception as e:
            self.logger.error(f"فشل في حفظ الخطأ في قاعدة البيانات: {str(e)}")
    
    def log_info(self, message: str, context: str = None):
        """تسجيل معلومات عامة"""
        if context:
            self.logger.info(f"[{context}] {message}")
        else:
            self.logger.info(message)
    
    def log_warning(self, message: str, context: str = None):
        """تسجيل تحذيرات"""
        if context:
            self.logger.warning(f"[{context}] {message}")
        else:
            self.logger.warning(message)
    
    def log_debug(self, message: str, context: str = None):
        """تسجيل معلومات التشخيص"""
        if context:
            self.logger.debug(f"[{context}] {message}")
        else:
            self.logger.debug(message)
    
    async def handle_telegram_error(self, update: Update, context: CallbackContext, 
                                  error_message: str = None, show_to_user: bool = True):
        """معالجة أخطاء تيليجرام وإرسال رسائل للمستخدم"""
        try:
            # تسجيل الخطأ
            if context.error:
                self.log_error(
                    "TELEGRAM_ERROR", 
                    f"Exception while handling an update: {context.error}",
                    user_id=str(update.effective_user.id) if update.effective_user else None,
                    context="telegram_handler",
                    exception=context.error
                )
            
            # إرسال رسالة خطأ للمستخدم إذا طُلب ذلك
            if show_to_user and update and update.effective_message:
                await self._send_error_message_to_user(update, context, error_message)
                
        except Exception as e:
            self.log_error("ERROR_HANDLER_FAILURE", f"فشل في معالجة خطأ تيليجرام: {str(e)}")
    
    async def _send_error_message_to_user(self, update: Update, context: CallbackContext, 
                                        custom_message: str = None):
        """إرسال رسالة خطأ للمستخدم"""
        try:
            user_id = str(update.effective_user.id) if update.effective_user else None
            
            # تحديد اللغة
            lang = 'ar'  # افتراضي
            if context.bot_data.get('user_settings', {}).get(user_id, {}).get('language'):
                lang = context.bot_data['user_settings'][user_id]['language']
            
            # رسالة الخطأ
            if custom_message:
                error_text = custom_message
            else:
                error_text = (
                    "❌ حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else
                    "❌ An unexpected error occurred. Please try again later."
                )
            
            # زر العودة للقائمة الرئيسية
            back_text = "🏠 العودة للقائمة الرئيسية" if lang == 'ar' else "🏠 Back to Main Menu"
            keyboard = [[InlineKeyboardButton(back_text, callback_data='back_to_main')]]
            
            await update.effective_message.reply_text(
                error_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            
        except Exception as e:
            # في حالة فشل إرسال رسالة الخطأ، لا نفعل شيئاً لتجنب حلقة لا نهائية
            self.log_error("SEND_ERROR_MESSAGE_FAILURE", f"فشل في إرسال رسالة خطأ للمستخدم: {str(e)}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأخطاء"""
        return {
            'error_counts': self.error_counts.copy(),
            'last_errors': self.last_errors.copy(),
            'total_errors': sum(self.error_counts.values())
        }
    
    def clear_error_statistics(self):
        """مسح إحصائيات الأخطاء"""
        self.error_counts.clear()
        self.last_errors.clear()
        self.log_info("تم مسح إحصائيات الأخطاء", "error_handler")

# إنشاء مثيل عام لمعالج الأخطاء
error_handler = None

def initialize_error_handler(db=None):
    """تهيئة معالج الأخطاء"""
    global error_handler
    error_handler = ErrorHandler(db)
    error_handler.setup_logging()
    return error_handler

def get_error_handler():
    """الحصول على معالج الأخطاء"""
    global error_handler
    if error_handler is None:
        error_handler = ErrorHandler()
        error_handler.setup_logging()
    return error_handler

# دوال مساعدة للاستخدام السريع
def log_error(error_type: str, message: str, user_id: str = None, 
              context: str = None, exception: Exception = None):
    """دالة مساعدة لتسجيل الأخطاء"""
    handler = get_error_handler()
    handler.log_error(error_type, message, user_id, context, exception)

def log_info(message: str, context: str = None):
    """دالة مساعدة لتسجيل المعلومات"""
    handler = get_error_handler()
    handler.log_info(message, context)

def log_warning(message: str, context: str = None):
    """دالة مساعدة لتسجيل التحذيرات"""
    handler = get_error_handler()
    handler.log_warning(message, context)

def log_debug(message: str, context: str = None):
    """دالة مساعدة لتسجيل معلومات التشخيص"""
    handler = get_error_handler()
    handler.log_debug(message, context)

async def handle_telegram_error(update: Update, context: CallbackContext,
                              error_message: str = None, show_to_user: bool = True):
    """دالة مساعدة لمعالجة أخطاء تيليجرام"""
    handler = get_error_handler()
    await handler.handle_telegram_error(update, context, error_message, show_to_user)

# دوال معالجة أخطاء متخصصة
class SpecializedErrorHandlers:
    """معالجات أخطاء متخصصة لحالات مختلفة"""

    @staticmethod
    async def handle_analysis_error(update: Update, context: CallbackContext,
                                  symbol: str, error: Exception, lang: str = 'ar'):
        """معالجة أخطاء التحليل"""
        try:
            user_id = str(update.effective_user.id) if update.effective_user else None

            # تسجيل الخطأ
            log_error(
                "ANALYSIS_ERROR",
                f"خطأ في تحليل العملة {symbol}: {str(error)}",
                user_id=user_id,
                context="analysis",
                exception=error
            )

            # رسالة خطأ للمستخدم
            error_text = (
                f"❌ حدث خطأ أثناء تحليل العملة {symbol}. يرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else
                f"❌ An error occurred while analyzing {symbol}. Please try again later."
            )

            # إرسال الرسالة
            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.message.reply_text(error_text)
            elif hasattr(update, 'message') and update.message:
                await update.message.reply_text(error_text)

        except Exception as e:
            log_error("ANALYSIS_ERROR_HANDLER_FAILURE", f"فشل في معالجة خطأ التحليل: {str(e)}")

    @staticmethod
    async def handle_payment_error(update: Update, context: CallbackContext,
                                 transaction_id: str, error: Exception, lang: str = 'ar'):
        """معالجة أخطاء المدفوعات"""
        try:
            user_id = str(update.effective_user.id) if update.effective_user else None

            # تسجيل الخطأ
            log_error(
                "PAYMENT_ERROR",
                f"خطأ في معالجة المعاملة {transaction_id}: {str(error)}",
                user_id=user_id,
                context="payment",
                exception=error
            )

            # رسالة خطأ للمستخدم
            error_text = (
                "❌ حدث خطأ في معالجة عملية الدفع. يرجى المحاولة مرة أخرى أو التواصل مع الدعم." if lang == 'ar' else
                "❌ An error occurred while processing payment. Please try again or contact support."
            )

            await update.message.reply_text(error_text)

        except Exception as e:
            log_error("PAYMENT_ERROR_HANDLER_FAILURE", f"فشل في معالجة خطأ الدفع: {str(e)}")

    @staticmethod
    async def handle_api_error(update: Update, context: CallbackContext,
                             api_name: str, error: Exception, lang: str = 'ar'):
        """معالجة أخطاء API"""
        try:
            user_id = str(update.effective_user.id) if update.effective_user else None

            # تسجيل الخطأ
            log_error(
                "API_ERROR",
                f"خطأ في API {api_name}: {str(error)}",
                user_id=user_id,
                context="api",
                exception=error
            )

            # رسالة خطأ للمستخدم
            error_text = (
                f"❌ حدث خطأ في الاتصال بخدمة {api_name}. يرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else
                f"❌ An error occurred connecting to {api_name} service. Please try again later."
            )

            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.answer(error_text, show_alert=True)
            elif hasattr(update, 'message') and update.message:
                await update.message.reply_text(error_text)

        except Exception as e:
            log_error("API_ERROR_HANDLER_FAILURE", f"فشل في معالجة خطأ API: {str(e)}")

    @staticmethod
    async def handle_database_error(update: Update, context: CallbackContext,
                                  operation: str, error: Exception, lang: str = 'ar'):
        """معالجة أخطاء قاعدة البيانات"""
        try:
            user_id = str(update.effective_user.id) if update.effective_user else None

            # تسجيل الخطأ
            log_error(
                "DATABASE_ERROR",
                f"خطأ في قاعدة البيانات أثناء {operation}: {str(error)}",
                user_id=user_id,
                context="database",
                exception=error
            )

            # رسالة خطأ للمستخدم
            error_text = (
                "❌ حدث خطأ في حفظ البيانات. يرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else
                "❌ An error occurred while saving data. Please try again later."
            )

            if update and update.effective_message:
                await update.effective_message.reply_text(error_text)

        except Exception as e:
            log_error("DATABASE_ERROR_HANDLER_FAILURE", f"فشل في معالجة خطأ قاعدة البيانات: {str(e)}")

    @staticmethod
    async def handle_validation_error(update: Update, context: CallbackContext,
                                    field_name: str, error_message: str, lang: str = 'ar'):
        """معالجة أخطاء التحقق من صحة البيانات"""
        try:
            user_id = str(update.effective_user.id) if update.effective_user else None

            # تسجيل الخطأ
            log_error(
                "VALIDATION_ERROR",
                f"خطأ في التحقق من صحة {field_name}: {error_message}",
                user_id=user_id,
                context="validation"
            )

            # رسالة خطأ للمستخدم
            error_text = (
                f"❌ خطأ في البيانات المدخلة: {error_message}" if lang == 'ar' else
                f"❌ Input validation error: {error_message}"
            )

            if update and update.effective_message:
                await update.effective_message.reply_text(error_text)

        except Exception as e:
            log_error("VALIDATION_ERROR_HANDLER_FAILURE", f"فشل في معالجة خطأ التحقق: {str(e)}")

# إنشاء مثيل للمعالجات المتخصصة
specialized_handlers = SpecializedErrorHandlers()

# دوال إضافية لمعالجة الأخطاء
async def safe_send_message(bot, chat_id: str, text: str, **kwargs):
    """إرسال رسالة بشكل آمن مع معالجة الأخطاء"""
    try:
        return await bot.send_message(chat_id=chat_id, text=text, **kwargs)
    except Exception as e:
        log_error(
            "SEND_MESSAGE_ERROR",
            f"فشل في إرسال رسالة للمستخدم {chat_id}: {str(e)}",
            user_id=chat_id,
            context="messaging",
            exception=e
        )
        return None

async def safe_edit_message(message, text: str, **kwargs):
    """تعديل رسالة بشكل آمن مع معالجة الأخطاء"""
    try:
        return await message.edit_text(text=text, **kwargs)
    except Exception as e:
        log_error(
            "EDIT_MESSAGE_ERROR",
            f"فشل في تعديل الرسالة: {str(e)}",
            context="messaging",
            exception=e
        )
        return None

async def safe_delete_message(bot, chat_id: str, message_id: int):
    """حذف رسالة بشكل آمن مع معالجة الأخطاء"""
    try:
        await bot.delete_message(chat_id=chat_id, message_id=message_id)
        return True
    except Exception as e:
        log_error(
            "DELETE_MESSAGE_ERROR",
            f"فشل في حذف الرسالة {message_id} للمستخدم {chat_id}: {str(e)}",
            user_id=chat_id,
            context="messaging",
            exception=e
        )
        return False

def safe_operation(operation_name: str, operation_func, *args, **kwargs):
    """تنفيذ عملية بشكل آمن مع معالجة الأخطاء"""
    try:
        return operation_func(*args, **kwargs)
    except Exception as e:
        log_error(
            "SAFE_OPERATION_ERROR",
            f"فشل في تنفيذ العملية {operation_name}: {str(e)}",
            context="safe_operation",
            exception=e
        )
        return None

async def safe_async_operation(operation_name: str, operation_func, *args, **kwargs):
    """تنفيذ عملية غير متزامنة بشكل آمن مع معالجة الأخطاء"""
    try:
        return await operation_func(*args, **kwargs)
    except Exception as e:
        log_error(
            "SAFE_ASYNC_OPERATION_ERROR",
            f"فشل في تنفيذ العملية غير المتزامنة {operation_name}: {str(e)}",
            context="safe_async_operation",
            exception=e
        )
        return None

def generate_error_report() -> str:
    """إنشاء تقرير شامل عن الأخطاء"""
    try:
        handler = get_error_handler()
        stats = handler.get_error_statistics()

        report = "📊 **تقرير الأخطاء**\n\n"

        if stats['total_errors'] == 0:
            report += "✅ لا توجد أخطاء مسجلة\n"
        else:
            report += f"📈 **إجمالي الأخطاء**: {stats['total_errors']}\n\n"

            # عرض أنواع الأخطاء
            report += "🔍 **أنواع الأخطاء**:\n"
            for error_type, count in stats['error_counts'].items():
                report += f"• {error_type}: {count}\n"

            report += "\n"

            # عرض آخر الأخطاء
            if stats['last_errors']:
                report += "🕐 **آخر الأخطاء**:\n"
                for error_type, error_info in stats['last_errors'].items():
                    timestamp = error_info['timestamp'][:19]  # إزالة الميكروثواني
                    report += f"• {error_type}: {timestamp}\n"
                    if error_info.get('context'):
                        report += f"  السياق: {error_info['context']}\n"

        return report

    except Exception as e:
        log_error("ERROR_REPORT_GENERATION", f"فشل في إنشاء تقرير الأخطاء: {str(e)}")
        return "❌ فشل في إنشاء تقرير الأخطاء"

# دالة معالج الأخطاء الرئيسي لتيليجرام
async def telegram_error_handler(update: Update, context: CallbackContext):
    """معالج الأخطاء الرئيسي لتيليجرام"""
    try:
        # تسجيل الخطأ
        log_error(
            "TELEGRAM_MAIN_ERROR",
            f"Exception while handling an update: {context.error}",
            user_id=str(update.effective_user.id) if update.effective_user else None,
            context="telegram_main_handler",
            exception=context.error
        )

        # إرسال رسالة خطأ للمستخدم
        if update and update.effective_message:
            user_id = str(update.effective_user.id)

            # تحديد اللغة (افتراضي عربي)
            lang = 'ar'
            try:
                if context.bot_data.get('user_settings', {}).get(user_id, {}).get('language'):
                    lang = context.bot_data['user_settings'][user_id]['language']
            except:
                pass

            # رسالة الخطأ
            error_text = (
                "❌ حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else
                "❌ An unexpected error occurred. Please try again later."
            )

            # زر العودة للقائمة الرئيسية
            back_text = "🏠 العودة للقائمة الرئيسية" if lang == 'ar' else "🏠 Back to Main Menu"
            keyboard = [[InlineKeyboardButton(back_text, callback_data='back_to_main')]]

            await safe_send_message(
                context.bot,
                chat_id=user_id,
                text=error_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
    except:
        # في حالة فشل معالج الأخطاء نفسه، لا نفعل شيئاً لتجنب حلقة لا نهائية
        pass
