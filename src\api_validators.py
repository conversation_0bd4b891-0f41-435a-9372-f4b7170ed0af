"""
وظائف للتحقق من صحة مفاتيح API
"""

import logging
import aiohttp
import asyncio
from typing import Tuple, Optional, Dict, Any

# إعداد السجل
logger = logging.getLogger(__name__)

async def verify_binance_api(api_key: str, api_secret: str) -> Tuple[bool, Optional[str]]:
    """
    التحقق من صحة مفاتيح Binance API

    Args:
        api_key: مفتاح API
        api_secret: سر API

    Returns:
        زوج من (is_valid, error_message)
    """
    try:
        # استخدام مكتبة python-binance للتحقق
        from binance.client import Client

        # إنشاء عميل Binance
        client = Client(api_key, api_secret)

        # محاولة الحصول على معلومات الحساب
        # استخدام طريقة آمنة للتحقق (لا تتطلب صلاحيات كاملة)
        info = client.get_account_api_permissions()

        # التحقق من الصلاحيات
        if not info.get('enableReading', False):
            return False, "مفاتيح API لا تملك صلاحيات القراءة. يرجى تمكين صلاحيات القراءة."

        # إذا وصلنا إلى هنا، فالمفاتيح صحيحة
        return True, None

    except Exception as e:
        error_message = str(e)
        logger.error(f"خطأ في التحقق من مفاتيح Binance API: {error_message}")

        # تحسين رسالة الخطأ
        if "API-key format invalid" in error_message:
            return False, "تنسيق مفتاح API غير صالح"
        elif "Signature for this request is not valid" in error_message:
            return False, "سر API غير صالح"
        elif "Invalid API-key, IP, or permissions for action" in error_message:
            return False, "مفتاح API غير صالح، أو عنوان IP غير مسموح به، أو صلاحيات غير كافية"

        return False, f"خطأ في التحقق من مفاتيح API: {error_message}"

async def verify_gemini_api(api_key: str) -> Tuple[bool, Optional[str]]:
    """
    التحقق من صحة مفتاح Gemini API

    Args:
        api_key: مفتاح API

    Returns:
        زوج من (is_valid, error_message)
    """
    try:
        # تهيئة Gemini API
        import google.generativeai as genai

        genai.configure(api_key=api_key)

        # محاولة إنشاء نموذج واستدعائه - استخدام نموذج Gemini 1.5 Flash
        logger.info("محاولة التحقق من مفتاح Gemini API باستخدام نموذج gemini-2.0-flash")
        model = genai.GenerativeModel('gemini-2.0-flash')

        # استخدام to_thread لتجنب مشاكل التزامن
        try:
            response = await asyncio.to_thread(
                lambda: model.generate_content("Hello").text
            )
        except Exception as thread_error:
            logger.warning(f"فشل استدعاء to_thread: {str(thread_error)}")
            # محاولة بديلة باستخدام generate_content_async
            response = await model.generate_content_async("Hello")
            response = response.text if hasattr(response, 'text') else str(response)

        # التحقق من الاستجابة
        if response:
            logger.info("تم التحقق من مفتاح Gemini API بنجاح")
            return True, None

        return False, "لم يتم الحصول على استجابة صالحة من Gemini API"

    except Exception as e:
        error_message = str(e)
        logger.error(f"خطأ في التحقق من مفتاح Gemini API: {error_message}")

        # تحسين رسالة الخطأ
        if "API key not valid" in error_message:
            return False, "مفتاح API غير صالح"
        elif "quota exceeded" in error_message.lower():
            return False, "تم تجاوز حصة API. يرجى المحاولة لاحقًا."
        elif "404" in error_message:
            return False, "خطأ 404: لم يتم العثور على النموذج. تأكد من استخدام 'gemini-2.0-flash'"
        # تم إزالة مرجع gemini-pro (نموذج مكلف)
        elif "models/gemini-2.0-flash is not found" in error_message:
            return False, "نموذج gemini-2.0-flash غير متاح. يرجى التأكد من أن مفتاح API يدعم نماذج Gemini 1.5"
        elif "401" in error_message or "403" in error_message:
            return False, "خطأ في المصادقة: مفتاح API غير صالح أو منتهي الصلاحية"

        return False, f"خطأ في التحقق من مفتاح API: {error_message}"

async def get_binance_client(api_key: str, api_secret: str):
    """
    إنشاء عميل Binance باستخدام مفاتيح API

    Args:
        api_key: مفتاح API
        api_secret: سر API

    Returns:
        عميل Binance أو None إذا فشل الإنشاء
    """
    try:
        from binance.client import Client

        # إنشاء عميل Binance
        client = Client(api_key, api_secret)

        # التحقق من الاتصال
        client.ping()

        return client

    except Exception as e:
        logger.error(f"خطأ في إنشاء عميل Binance: {str(e)}")
        return None



async def get_gemini_model(api_key: str):
    """
    إنشاء نموذج Gemini باستخدام مفتاح API

    Args:
        api_key: مفتاح API

    Returns:
        نموذج Gemini أو None إذا فشل الإنشاء
    """
    try:
        import google.generativeai as genai

        # تهيئة Gemini API
        genai.configure(api_key=api_key)

        # إنشاء نموذج - استخدام نموذج Gemini 2.0 Flash (المجاني)
        try:
            # محاولة استخدام نموذج gemini-2.0-flash-exp أولاً
            logger.info("محاولة إنشاء نموذج gemini-2.0-flash-exp")
            model = genai.GenerativeModel('gemini-2.0-flash-exp')

            # اختبار النموذج للتأكد من أنه يعمل
            test_response = await asyncio.to_thread(
                lambda: model.generate_content("Test").text
            )

            if test_response:
                logger.info("تم إنشاء واختبار نموذج Gemini 2.0 Flash Exp بنجاح")
                return model
            else:
                logger.warning("تم إنشاء نموذج Gemini 2.0 Flash Exp ولكن الاختبار فشل")
                raise Exception("فشل اختبار النموذج")

        except Exception as model_error:
            error_message = str(model_error)
            logger.warning(f"فشل في إنشاء أو اختبار نموذج gemini-2.0-flash-exp: {error_message}")

            # محاولة النموذج البديل gemini-2.0-flash
            try:
                logger.info("محاولة استخدام النموذج البديل gemini-2.0-flash")
                model = genai.GenerativeModel('gemini-2.0-flash')
                test_response = await asyncio.to_thread(
                    lambda: model.generate_content("Test").text
                )
                if test_response:
                    logger.info("تم إنشاء واختبار نموذج Gemini 2.0 Flash بنجاح")
                    return model
                else:
                    raise Exception("فشل اختبار النموذج البديل")
            except Exception as fallback_error:
                logger.error(f"فشل في استخدام النموذج البديل: {str(fallback_error)}")
                return None

    except Exception as e:
        logger.error(f"خطأ في إنشاء نموذج Gemini: {str(e)}")
        return None
