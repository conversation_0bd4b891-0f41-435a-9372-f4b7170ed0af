"""
🛠️ استيرادات الأدوات المساعدة
=============================

جميع الأدوات المساعدة والمرافق في النظام.
هذه الأدوات تدعم الوظائف الأساسية وتحسن الأداء.

الفئات:
- إدارة API
- واجهة API
- التحقق من صحة API
- الأنظمة المحسنة
- أدوات الأمان والمراقبة
"""

# ===== إدارة API =====
try:
    from api_manager import APIManager
    API_MANAGER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد مدير API: {e}")
    API_MANAGER_AVAILABLE = False

# ===== واجهة API =====
try:
    from api_ui import (
        setup_api_keys, 
        show_api_instructions, 
        delete_api_keys_ui, 
        show_platform_selection
    )
    API_UI_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد واجهة API: {e}")
    API_UI_AVAILABLE = False

# ===== التحقق من صحة API =====
try:
    from api_validators import verify_binance_api, get_binance_client
    API_VALIDATORS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد مدققات API: {e}")
    API_VALIDATORS_AVAILABLE = False

# ===== الأنظمة المحسنة - الإصدار 4.4.0 =====
# تم حذف مراقب الأداء ومدير الذاكرة لأن الاستضافة تتولى هذه المهام
# الملفات البديلة الفارغة متوفرة لتجنب أخطاء الاستيراد
try:
    from monitoring.real_time_performance import RealTimePerformanceMonitor
    PERFORMANCE_MONITOR_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد مراقب الأداء: {e}")
    PERFORMANCE_MONITOR_AVAILABLE = False

try:
    from utils.memory_manager import AdvancedMemoryManager
    MEMORY_MANAGER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد مدير الذاكرة: {e}")
    MEMORY_MANAGER_AVAILABLE = False

try:
    from security.api_security import AdvancedAPISecurityManager
    API_SECURITY_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد مدير أمان API: {e}")
    API_SECURITY_AVAILABLE = False

try:
    from database.optimized_queries import OptimizedFirestoreManager
    OPTIMIZED_DB_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد مدير قاعدة البيانات المحسن: {e}")
    OPTIMIZED_DB_AVAILABLE = False

# تجميع جميع الأدوات المساعدة
all_utility_functions = {
    'api_management': {
        'available': API_MANAGER_AVAILABLE,
        'functions': ['APIManager'],
        'critical': True
    },
    'api_ui': {
        'available': API_UI_AVAILABLE,
        'functions': [
            'setup_api_keys', 'show_api_instructions', 
            'delete_api_keys_ui', 'show_platform_selection'
        ],
        'critical': True
    },
    'api_validators': {
        'available': API_VALIDATORS_AVAILABLE,
        'functions': ['verify_binance_api', 'get_binance_client'],
        'critical': True
    },
    'performance_monitoring': {
        'available': PERFORMANCE_MONITOR_AVAILABLE,
        'functions': ['RealTimePerformanceMonitor'],
        'critical': False,
        'note': 'ملفات بديلة فارغة - الاستضافة تتولى هذه المهام'
    },
    'memory_management': {
        'available': MEMORY_MANAGER_AVAILABLE,
        'functions': ['AdvancedMemoryManager'],
        'critical': False,
        'note': 'ملفات بديلة فارغة - الاستضافة تتولى هذه المهام'
    },
    'api_security': {
        'available': API_SECURITY_AVAILABLE,
        'functions': ['AdvancedAPISecurityManager'],
        'critical': False
    },
    'optimized_database': {
        'available': OPTIMIZED_DB_AVAILABLE,
        'functions': ['OptimizedFirestoreManager'],
        'critical': False
    }
}

__all__ = ['all_utility_functions'] + [
    'API_MANAGER_AVAILABLE', 'API_UI_AVAILABLE', 'API_VALIDATORS_AVAILABLE',
    'PERFORMANCE_MONITOR_AVAILABLE', 'MEMORY_MANAGER_AVAILABLE', 
    'API_SECURITY_AVAILABLE', 'OPTIMIZED_DB_AVAILABLE'
]

def get_utility_status():
    """
    حالة توفر جميع الأدوات المساعدة
    
    Returns:
        dict: حالة كل مجموعة أدوات
    """
    return {
        category: {
            'available': info['available'],
            'critical': info['critical'],
            'functions_count': len(info['functions'])
        }
        for category, info in all_utility_functions.items()
    }

def validate_critical_utilities():
    """
    التحقق من توفر الأدوات المساعدة الحرجة
    
    Returns:
        tuple: (success: bool, missing_critical: list)
    """
    missing_critical = []
    
    for category, info in all_utility_functions.items():
        if info['critical'] and not info['available']:
            missing_critical.append(category)
    
    return len(missing_critical) == 0, missing_critical

def get_enhancement_level():
    """
    حساب مستوى التحسينات المتاحة
    
    Returns:
        dict: معلومات عن مستوى التحسينات
    """
    enhancement_modules = [
        'performance_monitoring', 'memory_management', 
        'api_security', 'optimized_database'
    ]
    
    available_enhancements = sum(
        1 for module in enhancement_modules 
        if all_utility_functions[module]['available']
    )
    
    enhancement_percentage = (available_enhancements / len(enhancement_modules)) * 100
    
    return {
        'available_enhancements': available_enhancements,
        'total_enhancements': len(enhancement_modules),
        'enhancement_percentage': enhancement_percentage,
        'enhancement_level': (
            'متقدم' if enhancement_percentage >= 75 else
            'متوسط' if enhancement_percentage >= 50 else
            'أساسي' if enhancement_percentage >= 25 else
            'محدود'
        )
    }

# اختبار فوري للأدوات المساعدة
if __name__ == "__main__":
    success, missing = validate_critical_utilities()
    if success:
        print("✅ جميع الأدوات المساعدة الحرجة متوفرة")
    else:
        print(f"❌ أدوات مساعدة حرجة مفقودة: {missing}")
        
    # عرض مستوى التحسينات
    enhancement_info = get_enhancement_level()
    print(f"\n🚀 مستوى التحسينات: {enhancement_info['enhancement_level']}")
    print(f"({enhancement_info['available_enhancements']}/{enhancement_info['total_enhancements']} تحسينات متاحة - {enhancement_info['enhancement_percentage']:.1f}%)")
    
    # عرض حالة كل مجموعة
    status = get_utility_status()
    print("\n🛠️ حالة الأدوات المساعدة:")
    for category, info in status.items():
        status_icon = "✅" if info['available'] else "❌"
        critical_icon = "🔴" if info['critical'] else "🟡"
        print(f"{status_icon} {critical_icon} {category}: {info['functions_count']} دالة")
