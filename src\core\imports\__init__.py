"""
🚀 نظام الاستيراد الهرمي الذكي - Trading Telegram Bot
=======================================================

نقطة دخول موحدة لجميع الاستيرادات المنظمة والمحسنة.
يوفر واجهة بسيطة للوصول لجميع المكونات مع تحسين الأداء.

المؤلف: Augment Agent
الإصدار: 1.0.0
التاريخ: ديسمبر 2024
"""

from .standard_imports import *
from .external_imports import *
from .service_imports import *
from .analysis_imports import *
from .handler_imports import *
from .utils_imports import *
from .integration_imports import *

__version__ = "1.0.0"
__author__ = "Augment Agent"

# تصدير جميع الاستيرادات للوصول المباشر
__all__ = [
    # من standard_imports
    'logging', 'os', 'sys', 'json', 'time', 'asyncio', 'threading',
    'datetime', 'timedelta', 'datetime_time', 'pytz', 'hmac', 'hashlib',
    'typing_imports', 'urllib_parse', 'tempfile', 'traceback', 'shutil',
    'base64', 'uuid', 'gc', 'concurrent_futures', 're',
    
    # من external_imports
    'requests', 'numpy', 'pandas', 'aiohttp', 'telegram_imports',
    'firebase_imports', 'web3', 'cryptography_imports', 'memory_profiler',
    'apscheduler', 'dotenv',
    
    # من service_imports
    'all_service_functions',
    
    # من analysis_imports
    'all_analysis_functions',
    
    # من handler_imports
    'all_handler_functions',
    
    # من utils_imports
    'all_utility_functions',
    
    # من integration_imports
    'all_integration_functions'
]

def get_essential_imports():
    """
    إرجاع الاستيرادات الأساسية فقط للتحميل السريع
    
    Returns:
        dict: قاموس بالاستيرادات الأساسية
    """
    return {
        'logging': logging,
        'os': os,
        'sys': sys,
        'asyncio': asyncio,
        'json': json,
        'time': time
    }

def get_all_imports():
    """
    إرجاع جميع الاستيرادات المتاحة
    
    Returns:
        dict: قاموس بجميع الاستيرادات
    """
    import inspect
    current_module = inspect.currentframe().f_globals
    
    # فلترة الاستيرادات فقط (تجاهل المتغيرات الداخلية)
    imports = {
        name: obj for name, obj in current_module.items()
        if not name.startswith('_') and not inspect.ismodule(obj)
    }
    
    return imports

def validate_imports():
    """
    التحقق من صحة جميع الاستيرادات
    
    Returns:
        tuple: (success: bool, errors: list)
    """
    errors = []
    
    try:
        # اختبار الاستيرادات الأساسية
        essential = get_essential_imports()
        for name, module in essential.items():
            if module is None:
                errors.append(f"فشل في استيراد الوحدة الأساسية: {name}")
        
        # اختبار الاستيرادات الخارجية
        try:
            import requests
            import numpy
            import pandas
        except ImportError as e:
            errors.append(f"فشل في استيراد مكتبة خارجية: {str(e)}")
        
        return len(errors) == 0, errors
        
    except Exception as e:
        errors.append(f"خطأ عام في التحقق من الاستيرادات: {str(e)}")
        return False, errors
