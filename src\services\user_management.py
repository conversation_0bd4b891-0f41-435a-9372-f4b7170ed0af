"""
خدمات إدارة المستخدمين - User Management Services

هذا الملف يحتوي على جميع الدوال المتعلقة بإدارة المستخدمين:
- بداية استخدام البوت
- إضافة المستخدمين الجدد
- عرض إحصائيات المستخدمين
- إدارة الاشتراكات المنتهية
- إشعارات انتهاء الاشتراك
- تفعيل الاشتراكات
- اختبار حالة الاشتراك

تم نقل هذه الدوال من main.py كجزء من خطة إعادة الهيكلة - المرحلة التاسعة
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext, ContextTypes
from telegram.constants import ParseMode
from firebase_admin import firestore
from google.cloud.firestore_v1.base_query import FieldFilter

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

# متغيرات عامة (سيتم تهيئتها من main.py)
db = None
subscription_system = None
api_manager = None
encryption_key = None
free_day_system = None
user_states = None
show_main_menu = None
show_language_selection = None
show_terms_and_conditions = None
initialize_system = None
add_user_to_users_collection = None
SystemConfig = None
get_or_create_encryption_key = None
APIManager = None
get_text = None

def initialize_user_management(
    firestore_db,
    subscription_sys,
    api_mgr,
    encrypt_key,
    free_day_sys,
    user_st,
    show_main_menu_func,
    show_language_selection_func,
    show_terms_and_conditions_func,
    initialize_system_func,
    system_config,
    get_or_create_encryption_key_func,
    api_manager_class,
    get_text_func
):
    """تهيئة خدمة إدارة المستخدمين مع جميع التبعيات المطلوبة"""
    global db, subscription_system, api_manager, encryption_key, free_day_system
    global user_states, show_main_menu, show_language_selection, show_terms_and_conditions
    global initialize_system, SystemConfig, get_or_create_encryption_key, APIManager, get_text

    db = firestore_db
    subscription_system = subscription_sys
    api_manager = api_mgr
    encryption_key = encrypt_key
    free_day_system = free_day_sys
    user_states = user_st
    show_main_menu = show_main_menu_func
    show_language_selection = show_language_selection_func
    show_terms_and_conditions = show_terms_and_conditions_func
    initialize_system = initialize_system_func
    SystemConfig = system_config
    get_or_create_encryption_key = get_or_create_encryption_key_func
    APIManager = api_manager_class
    get_text = get_text_func

    logger.info("✅ تم تهيئة خدمة إدارة المستخدمين بنجاح")

async def start(update: Update, context: CallbackContext):
    """بداية استخدام البوت"""
    try:
        user_id = str(update.effective_user.id)
        username = update.effective_user.username or "غير معروف"

        # تفعيل اليوم المجاني فورًا إذا كان اليوم المجاني ولم يتم تفعيله
        if free_day_system is not None:
            try:
                is_today = free_day_system.is_today_free_day(user_id)
                is_active = free_day_system.is_free_day_active(user_id)
                if is_today and not is_active:
                    await free_day_system.activate_free_day(user_id)
                    logger.info(f"✅ تم تفعيل اليوم المجاني الفوري للمستخدم {user_id} عند الدخول")
            except Exception as e:
                logger.warning(f"تعذر تفعيل اليوم المجاني الفوري للمستخدم {user_id}: {str(e)}")

        logger.info(f"🔄 معالجة أمر /start للمستخدم {user_id} (المرة #{context.user_data.get('start_count', 0) + 1})")

        # تتبع عدد مرات استدعاء /start لهذا المستخدم
        context.user_data['start_count'] = context.user_data.get('start_count', 0) + 1

        # التحقق من تهيئة قاعدة البيانات
        if db is None:
            logger.error("قاعدة البيانات غير مُهيأة - محاولة التهيئة...")

            # محاولة الحصول على قاعدة البيانات من النظام المُهيأ
            from core.system_initialization_extended import get_system_components, is_system_initialized

            if is_system_initialized():
                logger.info("النظام مُهيأ، محاولة الحصول على قاعدة البيانات...")
                # لا نحتاج لتحديث المتغير العام db هنا، سنستخدم قاعدة البيانات مباشرة
                from integrations.firebase_init import initialize_firebase
                temp_db = initialize_firebase()
                if temp_db is None:
                    logger.error("فشل في الحصول على قاعدة البيانات")
                    await update.message.reply_text("❌ حدث خطأ في تهيئة النظام. الرجاء المحاولة مرة أخرى لاحقًا.")
                    return

                # استخدام قاعدة البيانات المؤقتة للتحقق من الحظر
                banned_ref = temp_db.collection('banned_users').document(user_id).get()
            else:
                logger.error("النظام غير مُهيأ")
                await update.message.reply_text("❌ النظام قيد التهيئة. الرجاء المحاولة مرة أخرى خلال دقيقة.")
                return
        else:
            # التحقق من حظر المستخدم
            banned_ref = db.collection('banned_users').document(user_id).get()
        if banned_ref.exists and banned_ref.to_dict().get('status') == 'banned':
            logger.info(f"محاولة وصول من مستخدم محظور: {user_id}")
            await update.message.reply_text("⛔️ عذراً، تم حظر حسابك من استخدام البوت. يرجى التواصل مع المطور إذا كنت تعتقد أن هذا خطأ.")
            return

        # إضافة المستخدم إلى قاعدة البيانات
        await add_user_to_users_collection(user_id, username)

        # التأكد من تهيئة مدير API
        global api_manager, encryption_key
        if api_manager is None:
            logger.info(f"مدير API غير مُهيأ للمستخدم {user_id}، التحقق من حالة النظام...")

            # استخدام الدوال المساعدة للحصول على المكونات
            from core.system_initialization_extended import get_system_components, is_system_initialized

            if is_system_initialized():
                # استخدام المكونات المحفوظة
                logger.info("استخدام المكونات المحفوظة من النظام...")
                system_components = get_system_components()
                if system_components:
                    api_manager = system_components.get('api_manager')
                    encryption_key = system_components.get('encryption_key')

                    if api_manager:
                        logger.info("✅ تم الحصول على مدير API من المكونات المحفوظة")
                    else:
                        logger.warning("⚠️ مدير API غير موجود في المكونات المحفوظة")

            # إذا لم نحصل على مدير API، نحاول التهيئة البسيطة
            if api_manager is None:
                logger.warning("فشل في الحصول على مدير API من المكونات المحفوظة، جاري إنشاء مدير API مؤقت...")
                if encryption_key is None:
                    from cryptography.fernet import Fernet
                    encryption_key = Fernet.generate_key().decode()
                api_manager = APIManager(db, encryption_key)
                logger.info("✅ تم إنشاء مدير API مؤقت")

        # التأكد من وجود مدير API قبل عرض القائمة
        if api_manager is None:
            logger.error("لم يتم تهيئة مدير API بشكل صحيح")
            await update.message.reply_text("❌ حدث خطأ في تهيئة النظام. الرجاء المحاولة مرة أخرى لاحقًا.")
            return

        # التحقق من إعدادات المستخدم
        user_settings = subscription_system.get_user_settings(user_id)
        lang_selected = user_settings.get('lang_selected', False)  # هل اختار المستخدم اللغة من قبل
        terms_accepted = user_settings.get('terms_accepted', False)  # هل وافق المستخدم على الشروط والأحكام

        logger.info(f"إعدادات المستخدم {user_id}: lang_selected={lang_selected}, terms_accepted={terms_accepted}")

        # إذا لم يختر المستخدم اللغة بعد، عرض اختيار اللغة
        if not lang_selected:
            logger.info(f"المستخدم {user_id} لم يختر اللغة بعد - عرض اختيار اللغة")
            await show_language_selection(update, context)
            return

        # إذا لم يوافق المستخدم على الشروط والأحكام بعد، عرضها
        if not terms_accepted:
            lang = user_settings.get('lang', 'ar')
            logger.info(f"المستخدم {user_id} لم يوافق على الشروط بعد - عرض الشروط والأحكام باللغة {lang}")
            await show_terms_and_conditions(update, context, lang)
            return

        # عرض القائمة الرئيسية
        logger.info(f"عرض القائمة الرئيسية للمستخدم {user_id}")
        await show_main_menu(update, context, new_message=True)
        logger.info(f"تم عرض القائمة الرئيسية للمستخدم {user_id} بنجاح")

    except Exception as e:
        logger.error(f"خطأ في معالجة أمر /start: {str(e)}")
        # محاولة إرسال رسالة بسيطة على الأقل
        try:
            await update.message.reply_text("❌ حدث خطأ أثناء بدء البوت. الرجاء المحاولة مرة أخرى لاحقًا.")
        except Exception as inner_e:
            logger.error(f"خطأ في إرسال رسالة الخطأ: {str(inner_e)}")

async def add_user_to_users_collection(user_id: str, username: str):
    """إضافة المستخدم إلى جدول users في Firestore"""
    try:
        # التحقق من تهيئة قاعدة البيانات
        current_db = db
        if current_db is None:
            from integrations.firebase_init import initialize_firebase
            current_db = initialize_firebase()
            if current_db is None:
                logger.error("فشل في الحصول على قاعدة البيانات لإضافة المستخدم")
                return

        users_ref = current_db.collection('users')
        user_doc = users_ref.document(user_id)

        if not user_doc.get().exists:
            user_data = {
                'userId': user_id,
                'username': username,
                'chatId': user_id,
                'subscriptionStatus': 'غير مشترك',
                'subscriptionExpiry': None,
                'language': 'ar',
                'createdAt': datetime.now().isoformat()
            }
            user_doc.set(user_data)
            logger.info(f"تم إضافة المستخدم {user_id} إلى جدول users بنجاح")

            # منح يوم مجاني للمستخدم الجديد - تفعيل فوري إذا كان اليوم المناسب
            try:
                # أولاً: منح يوم مجاني مؤقت (24 ساعة) كهدية ترحيب
                if free_day_system.grant_free_day(user_id, 24):
                    logger.info(f"تم منح يوم مجاني مؤقت (24 ساعة) للمستخدم الجديد {user_id}")
                    
                    # ثانياً: التحقق من إمكانية تفعيل اليوم المجاني الأسبوعي فوراً
                    if free_day_system.is_today_free_day(user_id):
                        # إذا كان اليوم الحالي هو اليوم المجاني الأسبوعي، تفعيله فوراً
                        await free_day_system.activate_free_day(user_id)
                        logger.info(f"تم تفعيل اليوم المجاني الأسبوعي فوراً للمستخدم الجديد {user_id}")
                else:
                    logger.warning(f"فشل في منح يوم مجاني للمستخدم الجديد {user_id}")
            except Exception as e:
                logger.error(f"خطأ في منح يوم مجاني للمستخدم الجديد {user_id}: {str(e)}")

            # تفعيل الإشعارات التلقائية للمستخدم الجديد
            try:
                from services.automatic_news_notifications import AutomaticNewsNotifications
                notifications_system = AutomaticNewsNotifications(db=current_db)
                await notifications_system.enable_automatic_notifications_for_user(user_id)
                logger.info(f"تم تفعيل الإشعارات التلقائية للمستخدم الجديد {user_id}")
            except Exception as e:
                logger.warning(f"فشل في تفعيل الإشعارات التلقائية للمستخدم {user_id}: {str(e)}")
        else:
            logger.info(f"المستخدم {user_id} موجود بالفعل في جدول users")

    except Exception as e:
        logger.error(f"خطأ في إضافة المستخدم {user_id} إلى جدول users: {str(e)}")

async def show_user_stats(update: Update, context: CallbackContext):
    """عرض إحصائيات المستخدمين للبوت"""
    try:
        # سجلات تصحيح للتحقق من قيم المتغيرات
        user_id = str(update.effective_user.id)
        developer_id = SystemConfig.DEVELOPER_ID
        logger.info(f"معرف المستخدم: '{user_id}' (النوع: {type(user_id).__name__})")
        logger.info(f"معرف المطور: '{developer_id}' (النوع: {type(developer_id).__name__})")
        logger.info(f"هل المعرفان متساويان؟ {user_id == developer_id}")
        
        # التحقق من أن المستخدم هو المطور
        if user_id != developer_id:
            await update.message.reply_text("⛔️ عذراً، هذا الأمر متاح فقط للمطور")
            logger.warning(f"محاولة غير مصرح بها لعرض إحصائيات المستخدمين من قبل المستخدم {update.effective_user.id}")
            return

        # جلب إحصائيات المستخدمين من جدول users
        # التحقق من تهيئة قاعدة البيانات
        current_db = db
        if current_db is None:
            from integrations.firebase_init import initialize_firebase
            current_db = initialize_firebase()
            if current_db is None:
                await update.message.reply_text("❌ حدث خطأ في الاتصال بقاعدة البيانات")
                return

        users_ref = current_db.collection('users')
        total_users = 0
        active_subs = 0

        for doc in users_ref.get():
            if doc.id.startswith('_'):  # تجاهل الوثائق الوصفية
                continue
            total_users += 1
            user_data = doc.to_dict()
            if user_data.get('subscriptionStatus') == 'مشترك':
                try:
                    expiry = datetime.fromisoformat(user_data.get('subscriptionExpiry'))
                    if expiry > datetime.now():
                        active_subs += 1
                except (KeyError, ValueError):
                    logger.warning(f"تحذير: مفتاح 'subscriptionExpiry' غير موجود أو تنسيقه غير صحيح للمستخدم {doc.id}")

        inactive_subs = total_users - active_subs

        # إنشاء النص للإحصائيات
        stats_text = (
            f"📊 *إحصائيات المستخدمين*\n\n"
            f"• إجمالي المستخدمين: {total_users}\n"
            f"• المشتركين: {active_subs}\n"
            f"• غير المشتركين: {inactive_subs}"
        )

        await update.message.reply_text(stats_text, parse_mode=ParseMode.MARKDOWN)

    except Exception as e:
        logger.error(f"خطأ في عرض إحصائيات المستخدمين: {str(e)}")
        await update.message.reply_text("❌ حدث خطأ أثناء عرض إحصائيات المستخدمين")

async def test_subscription(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """اختبار سريع لحالة الاشتراك"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من حالة الاشتراك
        subscription_status = subscription_system.is_subscribed_sync(user_id)
        free_usage = subscription_system.get_free_usage(user_id)
        subscription_info = subscription_system.get_subscription_info(user_id)

        # إنشاء رسالة التقرير
        report = "📊 *تقرير حالة الاشتراك*\n\n"

        if subscription_status:
            report += "✅ *حالة الاشتراك:* مشترك\n"
            report += f"📅 *تاريخ الانتهاء:* {subscription_info['expiry_date']}\n"
            report += "🌟 *المزايا المتاحة:*\n"
            report += "  • تحليلات غير محدودة\n"
            report += "  • تنبيهات غير محدودة\n"
            report += "  • جميع المؤشرات الفنية\n"
            report += "  • جميع أزواج العملات\n"
        else:
            report += "❌ *حالة الاشتراك:* غير مشترك\n"
            report += "*الاستخدام المجاني المتبقي اليوم:*\n"
            report += f"📊 تحليلات متبقية: {free_usage['analyses']}/3\n"
            report += f"🔔 تنبيهات متبقية: {free_usage['alerts']}/1\n"
            report += "\n💡 *للحصول على مزايا غير محدودة، يرجى الاشتراك*"

        await update.message.reply_text(report, parse_mode=ParseMode.MARKDOWN)
    except Exception as e:
        logger.error(f"Error in test_subscription: {str(e)}")
        await update.message.reply_text("❌ حدث خطأ أثناء فحص حالة الاشتراك. الرجاء المحاولة مرة أخرى.")

async def activate_subscription(user_id: str, transaction_id: str) -> bool:
    """تفعيل الاشتراك بعد التحقق من الدفع (واجهة متوافقة مع الكود القديم)"""
    try:
        # استخدام الدالة الموحدة في نظام الاشتراك
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')
        return await subscription_system.activate_subscription(user_id, transaction_id, lang)
    except Exception as e:
        logger.error(f"Error activating subscription for user {user_id}: {str(e)}")
        return False

async def check_expired_subscriptions(context: CallbackContext):
    """التحقق من الاشتراكات المنتهية وتحديث حالة المستخدمين"""
    try:
        logger.info(f"⏰ بدء التحقق من الاشتراكات المنتهية في {datetime.now().isoformat()}")
        # الحصول على جميع المستخدمين المشتركين
        # التحقق من تهيئة قاعدة البيانات
        current_db = db
        if current_db is None:
            from integrations.firebase_init import initialize_firebase
            current_db = initialize_firebase()
            if current_db is None:
                logger.error("فشل في الحصول على قاعدة البيانات للتحقق من الاشتراكات")
                return

        users_ref = current_db.collection('users')
        users = users_ref.where(filter=FieldFilter('subscriptionStatus', '==', 'مشترك')).get()

        expired_count = 0
        for user in users:
            user_data = user.to_dict()
            user_id = user.id

            try:
                if 'subscriptionExpiry' not in user_data:
                    logger.warning(f"تاريخ انتهاء الاشتراك غير موجود للمستخدم {user_id}")
                    continue

                expiry = datetime.fromisoformat(user_data.get('subscriptionExpiry'))

                if expiry <= datetime.now():
                    # تحديث حالة المستخدم إلى غير مشترك
                    await _update_expired_subscription(user_id)
                    expired_count += 1

            except (ValueError, TypeError) as e:
                logger.warning(f"تنسيق تاريخ انتهاء الاشتراك غير صالح للمستخدم {user_id}: {str(e)}")

        logger.info(f"تم التحقق من الاشتراكات المنتهية. تم تحديث {expired_count} اشتراك منتهي.")

    except Exception as e:
        logger.error(f"خطأ في التحقق من الاشتراكات المنتهية: {str(e)}")

async def _update_expired_subscription(user_id: str):
    """تحديث حالة الاشتراك المنتهي"""
    try:
        # التحقق من تهيئة قاعدة البيانات
        current_db = db
        if current_db is None:
            from integrations.firebase_init import initialize_firebase
            current_db = initialize_firebase()
            if current_db is None:
                logger.error("فشل في الحصول على قاعدة البيانات لتحديث الاشتراك المنتهي")
                return

        # تحديث في جدول users
        users_ref = current_db.collection('users').document(user_id)
        users_ref.update({
            'subscriptionStatus': 'غير مشترك',
            'subscriptionExpiry': None
        })

        # تحديث في جدول subscriptions
        subscription_ref = current_db.collection('subscriptions').document(user_id)
        subscription_ref.update({
            'status': 'expired',
            'expiry': datetime.now().isoformat()
        })

        # تحديث الذاكرة المحلية
        current_time = datetime.now()
        subscription_system._subscription_cache[user_id] = {
            'is_active': False,
            'expiry': None,
            'features': subscription_system.get_free_features()
        }
        subscription_system._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

        logger.info(f"تم تحديث حالة الاشتراك للمستخدم {user_id} إلى منتهي")

    except Exception as e:
        logger.error(f"خطأ في تحديث حالة الاشتراك المنتهي للمستخدم {user_id}: {str(e)}")

async def notify_expiring_subscriptions(context: CallbackContext):
    """إشعار المستخدمين بقرب انتهاء اشتراكاتهم"""
    try:
        # الحصول على جميع المستخدمين المشتركين
        # التحقق من تهيئة قاعدة البيانات
        current_db = db
        if current_db is None:
            from integrations.firebase_init import initialize_firebase
            current_db = initialize_firebase()
            if current_db is None:
                logger.error("فشل في الحصول على قاعدة البيانات لإشعار انتهاء الاشتراكات")
                return

        users_ref = current_db.collection('users')
        users = users_ref.where(filter=FieldFilter('subscriptionStatus', '==', 'مشترك')).get()

        for user in users:
            user_data = user.to_dict()
            user_id = user.id

            try:
                if 'subscriptionExpiry' not in user_data:
                    continue

                expiry = datetime.fromisoformat(user_data.get('subscriptionExpiry'))
                now = datetime.now()

                # حساب الوقت المتبقي
                time_left = expiry - now
                hours_left = time_left.total_seconds() / 3600

                # إرسال إشعارات مختلفة حسب الوقت المتبقي
                if 0 < hours_left <= 24:  # أقل من 24 ساعة
                    await _send_expiry_notification(context, user_id, 'soon', hours_left)
                elif hours_left <= 0:  # منتهي بالفعل
                    await _update_expired_subscription(user_id)
                    await _send_expiry_notification(context, user_id, 'expired')
                elif hours_left <= 48:  # أقل من 48 ساعة
                    await _send_expiry_notification(context, user_id, 'warning', hours_left)

            except (ValueError, TypeError) as e:
                logger.warning(f"تنسيق تاريخ انتهاء الاشتراك غير صالح للمستخدم {user_id}: {str(e)}")

    except Exception as e:
        logger.error(f"خطأ في إرسال إشعارات الاشتراكات: {str(e)}")

async def _send_expiry_notification(context: ContextTypes.DEFAULT_TYPE, user_id: str, notification_type: str, hours_left: float = 0):
    """إرسال إشعار انتهاء الاشتراك"""
    try:
        # تحديد نوع الإشعار
        if notification_type == 'soon':
            message = f"🔔 تنبيه: اشتراكك سينتهي خلال {int(hours_left)} ساعة. يرجى التجديد للاستمرار في الاستفادة من المزايا."
        elif notification_type == 'warning':
            message = f"⚠️ تحذير: اشتراكك سينتهي خلال {int(hours_left)} ساعة. يرجى التجديد قريباً."
        elif notification_type == 'expired':
            message = "⚠️ انتهى اشتراكك. يرجى التجديد للاستمرار في الاستفادة من المزايا."
        else:
            return

        # إرسال الإشعار
        await context.bot.send_message(
            chat_id=user_id,
            text=message
        )

        logger.info(f"تم إرسال إشعار {notification_type} للمستخدم {user_id}")

    except Exception as e:
        logger.error(f"خطأ في إرسال إشعار انتهاء الاشتراك للمستخدم {user_id}: {str(e)}")

async def manage_free_day_settings(update: Update, context: CallbackContext):
    """إدارة إعدادات اليوم المجاني"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # الحصول على حالة اليوم المجاني
        # نستخدم get_user_free_day_status هنا لأننا نحتاج إلى معلومات إضافية مثل اليوم المحدد
        free_day_status = free_day_system.get_user_free_day_status(user_id)
        current_day = free_day_status['free_day_of_week']
        day_name = free_day_status['day_name']
        next_free_day = free_day_status['next_free_day']
        is_active = free_day_status['is_free_day_active']

        # إنشاء نص الإعدادات
        if lang == 'ar':
            settings_text = "🎁 إعدادات اليوم المجاني\n\n"
            settings_text += f"اليوم المجاني الحالي: {day_name}\n"
            settings_text += f"اليوم المجاني القادم: {next_free_day.strftime('%Y-%m-%d')}\n"
            settings_text += f"الحالة: {'مفعل' if is_active else 'غير مفعل'}\n\n"
            settings_text += "يمكنك الاستمتاع بالميزات المدفوعة مجانًا يومًا واحدًا في الأسبوع. اختر يومك المفضل أدناه."
        else:
            settings_text = "🎁 Free Day Settings\n\n"
            settings_text += f"Current free day: {day_name}\n"
            settings_text += f"Next free day: {next_free_day.strftime('%Y-%m-%d')}\n"
            settings_text += f"Status: {'Active' if is_active else 'Inactive'}\n\n"
            settings_text += "You can enjoy premium features for free one day per week. Choose your preferred day below."

        # إنشاء أزرار اختيار اليوم
        days_ar = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
        days_en = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

        keyboard = []
        for i in range(7):
            day_text = days_en[i] if lang == 'en' else days_ar[i]
            # إضافة علامة ✓ لليوم الحالي
            if i == current_day:
                day_text += " ✓"
            keyboard.append([InlineKeyboardButton(day_text, callback_data=f'set_free_day_{i}')])

        # إضافة زر العودة
        keyboard.append([InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')])

        # إرسال الرسالة
        await update.callback_query.edit_message_text(
            text=settings_text,
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    except Exception as e:
        logger.error(f"خطأ في إدارة إعدادات اليوم المجاني: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ أثناء تحميل إعدادات اليوم المجاني", show_alert=True)
        await show_main_menu(update, context)

async def set_free_day(update: Update, context: CallbackContext):
    """تعيين اليوم المجاني"""
    try:
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar')

        # الحصول على اليوم المحدد
        callback_data = update.callback_query.data
        day_index = int(callback_data.replace("set_free_day_", ""))

        # تعيين اليوم المجاني
        success = await free_day_system.set_free_day(user_id, day_index)

        if success:
            # إرسال تأكيد
            days_ar = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
            days_en = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

            day_name = days_en[day_index] if lang == 'en' else days_ar[day_index]

            await update.callback_query.answer(
                f"✅ {'Free day set to ' + day_name if lang == 'en' else 'تم تعيين اليوم المجاني إلى ' + day_name}",
                show_alert=True
            )

            # إعادة عرض إعدادات اليوم المجاني
            await manage_free_day_settings(update, context)
        else:
            await update.callback_query.answer(
                "❌ " + ("Failed to set free day" if lang == 'en' else "فشل في تعيين اليوم المجاني"),
                show_alert=True
            )

    except Exception as e:
        logger.error(f"خطأ في تعيين اليوم المجاني: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ أثناء تعيين اليوم المجاني", show_alert=True)
        await show_main_menu(update, context)

async def stop(update: Update, context: CallbackContext):
    """إيقاف البوت وإلغاء الاشتراك"""
    user_id = str(update.effective_user.id)
    user_states.pop(user_id, None)  # إزالة حالة المستخدم
    await update.message.reply_text("✅ تم إلغاء الاشتراك بنجاح. شكرًا لاستخدامك البوت!")  # رسالة تأكيد
