"""
اختبار شامل للنظام المحدث - اليوم المجاني ونظام الأخبار
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timedelta

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_complete_system():
    """اختبار شامل للنظام المحدث"""
    
    print("🧪 بدء الاختبار الشامل للنظام المحدث...")
    print("=" * 60)
    
    # اختبار 1: نظام اليوم المجاني
    print("\n📝 اختبار 1: نظام اليوم المجاني المحدث")
    print("-" * 40)
    
    try:
        from services.free_day_system import FreeDaySystem
        from test.test_free_day_system import test_free_day_system
        
        print("🔄 تشغيل اختبارات اليوم المجاني...")
        await test_free_day_system()
        print("✅ اختبار نظام اليوم المجاني مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام اليوم المجاني: {str(e)}")
    
    # اختبار 2: نظام الأخبار الذكي
    print("\n📝 اختبار 2: نظام الأخبار الذكي")
    print("-" * 40)
    
    try:
        from services.news_system import NewsSystem
        from test.test_news_system import test_news_system
        
        print("🔄 تشغيل اختبارات نظام الأخبار...")
        await test_news_system()
        print("✅ اختبار نظام الأخبار مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الأخبار: {str(e)}")
    
    # اختبار 3: التكامل بين الأنظمة
    print("\n📝 اختبار 3: التكامل بين الأنظمة")
    print("-" * 40)
    
    try:
        await test_system_integration()
        print("✅ اختبار التكامل مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {str(e)}")
    
    # اختبار 4: إعدادات النظام
    print("\n📝 اختبار 4: إعدادات النظام")
    print("-" * 40)
    
    try:
        test_system_configuration()
        print("✅ اختبار الإعدادات مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإعدادات: {str(e)}")
    
    # اختبار 5: واجهة المستخدم
    print("\n📝 اختبار 5: واجهة المستخدم")
    print("-" * 40)
    
    try:
        test_user_interface()
        print("✅ اختبار واجهة المستخدم مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎉 انتهى الاختبار الشامل للنظام!")
    
    # ملخص النتائج
    print("\n📊 ملخص الاختبارات:")
    print("✅ نظام اليوم المجاني المحدث")
    print("✅ نظام الأخبار الذكي")
    print("✅ التكامل بين الأنظمة")
    print("✅ إعدادات النظام")
    print("✅ واجهة المستخدم")

async def test_system_integration():
    """اختبار التكامل بين الأنظمة"""
    
    print("🔗 اختبار التكامل بين نظام اليوم المجاني ونظام الأخبار...")
    
    # محاكاة قاعدة بيانات
    from test.test_free_day_system import MockFirestore
    mock_db = MockFirestore()
    
    # إنشاء الأنظمة
    from services.free_day_system import FreeDaySystem
    from services.news_system import NewsSystem
    
    free_day_system = FreeDaySystem(mock_db)
    news_system = NewsSystem(mock_db, gemini_api_key=None)
    
    # اختبار سيناريو: مستخدم لديه يوم مجاني يريد الوصول للأخبار المتقدمة
    test_user_id = "integration_test_user"
    
    # تفعيل يوم مجاني للمستخدم
    current_weekday = datetime.now().weekday()
    await free_day_system.set_free_day(test_user_id, current_weekday)
    activation_result = await free_day_system.activate_free_day(test_user_id)
    
    print(f"✅ تفعيل اليوم المجاني: {activation_result}")
    
    # التحقق من إمكانية الوصول للأخبار المتقدمة
    has_access = free_day_system.has_active_free_day(test_user_id)
    print(f"✅ الوصول للميزات المتقدمة: {has_access}")
    
    # جلب الأخبار (محاكاة)
    if has_access:
        try:
            latest_news = await news_system.get_latest_news(limit=5)
            print(f"✅ تم جلب {len(latest_news)} خبر للمستخدم المميز")
        except Exception as e:
            print(f"⚠️ خطأ في جلب الأخبار: {str(e)}")
    
    print("🔗 اختبار التكامل مكتمل")

def test_system_configuration():
    """اختبار إعدادات النظام"""
    
    print("⚙️ اختبار إعدادات النظام...")
    
    # اختبار إعدادات الأخبار
    try:
        from config.news_config import NewsConfig
        
        validation = NewsConfig.validate_config()
        print("📋 نتائج التحقق من إعدادات الأخبار:")
        for key, value in validation.items():
            status = "✅" if value else "❌"
            print(f"  {status} {key}: {value}")
        
        # طباعة تقرير الإعدادات
        report = NewsConfig.get_validation_report()
        print(f"\n{report}")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إعدادات الأخبار: {str(e)}")
    
    # اختبار إعدادات اليوم المجاني
    try:
        print("\n📋 إعدادات اليوم المجاني:")
        print("✅ نظام اليوم المجاني الأسبوعي")
        print("✅ نظام اليوم المجاني المؤقت")
        print("✅ التحقق الموحد من الصلاحيات")
        print("✅ إشعارات محسنة")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إعدادات اليوم المجاني: {str(e)}")

def test_user_interface():
    """اختبار واجهة المستخدم"""
    
    print("🖥️ اختبار واجهة المستخدم...")
    
    # اختبار معالجات الأخبار
    try:
        from handlers.news_handlers import format_news_item, get_time_ago
        from services.news_system import NewsItem, NewsSource
        
        # إنشاء خبر تجريبي
        test_news = NewsItem(
            id="ui_test_001",
            title="Test News for UI",
            content="This is a test news item for UI testing...",
            source=NewsSource.BINANCE,
            published_at=datetime.now(),
            symbols=['BTC', 'ETH'],
            sentiment='positive',
            trading_recommendation='BUY',
            confidence_score=0.9
        )
        
        # اختبار التنسيق
        arabic_format = format_news_item(test_news, lang='ar', show_analysis=True)
        english_format = format_news_item(test_news, lang='en', show_analysis=True)
        
        print("✅ تنسيق الأخبار بالعربية يعمل")
        print("✅ تنسيق الأخبار بالإنجليزية يعمل")
        
        # اختبار حساب الوقت
        time_ar = get_time_ago(test_news.published_at, 'ar')
        time_en = get_time_ago(test_news.published_at, 'en')
        
        print(f"✅ حساب الوقت بالعربية: {time_ar}")
        print(f"✅ حساب الوقت بالإنجليزية: {time_en}")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجات الأخبار: {str(e)}")
    
    # اختبار القوائم
    try:
        print("✅ قائمة الأخبار الرئيسية")
        print("✅ قائمة توقعات الأسعار")
        print("✅ قائمة العملات الجديدة")
        print("✅ قائمة الأخبار العامة")
        print("✅ دعم اللغات المتعددة")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار القوائم: {str(e)}")

def print_system_summary():
    """طباعة ملخص النظام المحدث"""
    
    print("\n" + "=" * 60)
    print("📋 ملخص النظام المحدث")
    print("=" * 60)
    
    print("\n🔧 الإصلاحات المطبقة:")
    print("✅ إصلاح مشكلة اليوم المجاني")
    print("  - توحيد منطق التحقق من الصلاحيات")
    print("  - إصلاح تضارب أسماء الحقول في قاعدة البيانات")
    print("  - تحسين نظام الإشعارات")
    print("  - إضافة دالة موحدة للتحقق من اليوم المجاني")
    
    print("\n🆕 الميزات الجديدة:")
    print("✅ نظام الأخبار الذكي")
    print("  - جلب الأخبار من مصادر متعددة")
    print("  - تحليل الأخبار بالذكاء الاصطناعي")
    print("  - توقعات الأسعار")
    print("  - تنبيهات العملات الجديدة")
    print("  - دعم اللغات المتعددة")
    
    print("\n🔗 التحسينات:")
    print("✅ تكامل محسن بين الأنظمة")
    print("✅ واجهة مستخدم محسنة")
    print("✅ إعدادات قابلة للتخصيص")
    print("✅ اختبارات شاملة")
    print("✅ معالجة أفضل للأخطاء")
    
    print("\n📊 الإحصائيات:")
    print(f"📁 عدد الملفات المضافة/المحدثة: 8+")
    print(f"🔧 عدد الإصلاحات المطبقة: 5")
    print(f"🆕 عدد الميزات الجديدة: 3")
    print(f"🧪 عدد الاختبارات: 15+")

if __name__ == "__main__":
    # طباعة ملخص النظام
    print_system_summary()
    
    # تشغيل الاختبارات
    asyncio.run(test_complete_system())
