"""
🔗 استيرادات التكاملات الخارجية
===============================

جميع التكاملات مع الخدمات والمنصات الخارجية.
هذه التكاملات تربط النظام بالخدمات الخارجية المطلوبة.

الفئات:
- تكامل Binance
- تكامل Firebase
- تكامل التعليم
- تكاملات أخرى
"""

# ===== تكامل Binance =====
try:
    from integrations.binance_manager import BinanceManager
    BINANCE_INTEGRATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد تكامل Binance: {e}")
    BINANCE_INTEGRATION_AVAILABLE = False

# ===== تكامل Firebase =====
try:
    from integrations.firebase_init import initialize_firebase
    FIREBASE_INTEGRATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد تكامل Firebase: {e}")
    FIREBASE_INTEGRATION_AVAILABLE = False

# ===== تكامل التعليم =====
try:
    from education.trading_education import (
        handle_learn_trading_ai,
        handle_message_for_ai_tutor,
        generate_and_send_chapter,
        start_quiz,
        handle_quiz_answer,
        check_gemini_api_key,
        handle_ask_ai_tutor_button,
        show_quiz_results_or_next_steps,
        show_supplementary_chapters,
        generate_and_send_supplementary_chapter,
        set_firestore_db as set_trading_education_db
    )
    EDUCATION_INTEGRATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد تكامل التعليم: {e}")
    EDUCATION_INTEGRATION_AVAILABLE = False

# تجميع جميع التكاملات الخارجية
all_integration_functions = {
    'binance_integration': {
        'available': BINANCE_INTEGRATION_AVAILABLE,
        'functions': ['BinanceManager'],
        'critical': True,
        'description': 'تكامل مع منصة Binance للتداول والبيانات'
    },
    'firebase_integration': {
        'available': FIREBASE_INTEGRATION_AVAILABLE,
        'functions': ['initialize_firebase'],
        'critical': True,
        'description': 'تكامل مع Firebase لقاعدة البيانات'
    },
    'education_integration': {
        'available': EDUCATION_INTEGRATION_AVAILABLE,
        'functions': [
            'handle_learn_trading_ai', 'handle_message_for_ai_tutor',
            'generate_and_send_chapter', 'start_quiz', 'handle_quiz_answer',
            'check_gemini_api_key', 'handle_ask_ai_tutor_button',
            'show_quiz_results_or_next_steps', 'show_supplementary_chapters',
            'generate_and_send_supplementary_chapter', 'set_trading_education_db'
        ],
        'critical': False,
        'description': 'نظام التعليم التفاعلي بالذكاء الاصطناعي'
    }
}

__all__ = ['all_integration_functions'] + [
    'BINANCE_INTEGRATION_AVAILABLE', 'FIREBASE_INTEGRATION_AVAILABLE', 
    'EDUCATION_INTEGRATION_AVAILABLE'
]

def get_integration_status():
    """
    حالة توفر جميع التكاملات الخارجية
    
    Returns:
        dict: حالة كل تكامل
    """
    return {
        category: {
            'available': info['available'],
            'critical': info['critical'],
            'functions_count': len(info['functions']),
            'description': info['description']
        }
        for category, info in all_integration_functions.items()
    }

def validate_critical_integrations():
    """
    التحقق من توفر التكاملات الحرجة
    
    Returns:
        tuple: (success: bool, missing_critical: list)
    """
    missing_critical = []
    
    for category, info in all_integration_functions.items():
        if info['critical'] and not info['available']:
            missing_critical.append(category)
    
    return len(missing_critical) == 0, missing_critical

def get_integration_capabilities():
    """
    الحصول على قدرات التكامل المتاحة
    
    Returns:
        dict: قدرات التكامل
    """
    capabilities = {
        'trading_platform_access': BINANCE_INTEGRATION_AVAILABLE,
        'cloud_database': FIREBASE_INTEGRATION_AVAILABLE,
        'ai_education_system': EDUCATION_INTEGRATION_AVAILABLE
    }
    
    # حساب النسبة المئوية للقدرات المتاحة
    available_count = sum(1 for available in capabilities.values() if available)
    total_count = len(capabilities)
    availability_percentage = (available_count / total_count) * 100
    
    return {
        'capabilities': capabilities,
        'availability_percentage': availability_percentage,
        'available_count': available_count,
        'total_count': total_count,
        'integration_level': (
            'كامل' if availability_percentage == 100 else
            'متقدم' if availability_percentage >= 75 else
            'أساسي' if availability_percentage >= 50 else
            'محدود'
        )
    }

def get_available_integrations():
    """
    الحصول على قائمة بالتكاملات المتاحة
    
    Returns:
        dict: التكاملات المتاحة مع تفاصيلها
    """
    available_integrations = {}
    
    for category, info in all_integration_functions.items():
        if info['available']:
            available_integrations[category] = {
                'functions': info['functions'],
                'description': info['description'],
                'critical': info['critical']
            }
    
    return available_integrations

# اختبار فوري للتكاملات
if __name__ == "__main__":
    success, missing = validate_critical_integrations()
    if success:
        print("✅ جميع التكاملات الحرجة متوفرة")
    else:
        print(f"❌ تكاملات حرجة مفقودة: {missing}")
        
    # عرض قدرات التكامل
    capabilities = get_integration_capabilities()
    print(f"\n🔗 مستوى التكامل: {capabilities['integration_level']}")
    print(f"({capabilities['available_count']}/{capabilities['total_count']} تكاملات متاحة - {capabilities['availability_percentage']:.1f}%)")
    
    # عرض حالة كل تكامل
    status = get_integration_status()
    print("\n🌐 حالة التكاملات الخارجية:")
    for category, info in status.items():
        status_icon = "✅" if info['available'] else "❌"
        critical_icon = "🔴" if info['critical'] else "🟡"
        print(f"{status_icon} {critical_icon} {category}")
        print(f"   📝 {info['description']}")
        print(f"   🔧 {info['functions_count']} دالة متاحة")
