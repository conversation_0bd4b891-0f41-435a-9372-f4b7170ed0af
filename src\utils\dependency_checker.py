#!/usr/bin/env python3
"""
أداة فحص وإصلاح التبعيات والمكتبات
تتحقق من وجود جميع المكتبات المطلوبة وتقترح حلول للمشاكل
"""

import ast
import os
import sys
import subprocess
import importlib
import logging
from typing import List, Dict, Tuple, Set
from pathlib import Path

# إعداد السجل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DependencyChecker:
    """فاحص التبعيات والمكتبات"""
    
    def __init__(self, project_root: str = None):
        """
        تهيئة فاحص التبعيات
        
        Args:
            project_root: مسار جذر المشروع
        """
        if project_root:
            self.project_root = Path(project_root)
        else:
            # إذا كان الملف في src/utils، فالجذر هو مستويين أعلى
            current_file = Path(__file__).resolve()
            if 'src' in current_file.parts:
                # العثور على مجلد src والانتقال إلى الجذر
                src_index = current_file.parts.index('src')
                self.project_root = Path(*current_file.parts[:src_index])
            else:
                self.project_root = current_file.parent.parent

        self.src_path = self.project_root / "src"
        self.requirements_file = self.src_path / "requirements.txt"
        
        # قائمة المكتبات الأساسية المطلوبة
        self.critical_packages = {
            'telegram': 'python-telegram-bot',
            'firebase_admin': 'firebase-admin',
            'cryptography': 'cryptography',
            'google.generativeai': 'google-generativeai',
            'pandas': 'pandas',
            'numpy': 'numpy',
            'requests': 'requests',
            'aiohttp': 'aiohttp'
        }
        
        # قائمة المكتبات الاختيارية
        self.optional_packages = {
            'talib': 'TA-Lib',
            'redis': 'redis',
            'numba': 'numba',
            'jwt': 'PyJWT'
        }
        
        # قائمة المكتبات المدمجة في Python
        self.builtin_modules = {
            'os', 'sys', 'json', 'time', 'datetime', 'logging', 'asyncio',
            'threading', 'traceback', 'tempfile', 'uuid', 'gc', 're',
            'hmac', 'hashlib', 'base64', 'secrets', 'urllib', 'collections',
            'dataclasses', 'functools', 'typing', 'pathlib', 'subprocess',
            'importlib', 'ast'
        }

    def scan_imports_in_file(self, file_path: Path) -> Set[str]:
        """
        فحص الاستيرادات في ملف واحد
        
        Args:
            file_path: مسار الملف
            
        Returns:
            مجموعة من أسماء المكتبات المستوردة
        """
        imports = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # تحليل الكود باستخدام AST
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name.split('.')[0])
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.add(node.module.split('.')[0])
                        
        except Exception as e:
            logger.warning(f"خطأ في تحليل الملف {file_path}: {str(e)}")
            
        return imports

    def scan_all_imports(self) -> Dict[str, Set[str]]:
        """
        فحص جميع الاستيرادات في المشروع
        
        Returns:
            قاموس يحتوي على الاستيرادات لكل ملف
        """
        all_imports = {}
        
        # فحص جميع ملفات Python
        for py_file in self.src_path.rglob("*.py"):
            if py_file.name.startswith('.'):
                continue
                
            imports = self.scan_imports_in_file(py_file)
            if imports:
                relative_path = py_file.relative_to(self.src_path)
                all_imports[str(relative_path)] = imports
                
        return all_imports

    def get_unique_imports(self) -> Set[str]:
        """
        الحصول على قائمة فريدة من جميع الاستيرادات
        
        Returns:
            مجموعة من أسماء المكتبات الفريدة
        """
        all_imports = self.scan_all_imports()
        unique_imports = set()
        
        for file_imports in all_imports.values():
            unique_imports.update(file_imports)
            
        return unique_imports

    def check_package_availability(self, package_name: str) -> Tuple[bool, str]:
        """
        فحص توفر مكتبة معينة
        
        Args:
            package_name: اسم المكتبة
            
        Returns:
            (متوفرة, رسالة الحالة)
        """
        try:
            importlib.import_module(package_name)
            return True, "متوفرة"
        except ImportError:
            return False, "غير متوفرة"
        except Exception as e:
            return False, f"خطأ: {str(e)}"

    def read_requirements(self) -> Set[str]:
        """
        قراءة المكتبات من ملف requirements.txt
        
        Returns:
            مجموعة من أسماء المكتبات في requirements.txt
        """
        requirements = set()
        
        if not self.requirements_file.exists():
            logger.warning(f"ملف requirements.txt غير موجود: {self.requirements_file}")
            return requirements
            
        try:
            with open(self.requirements_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # استخراج اسم المكتبة (قبل == أو >= أو <=)
                        package_name = line.split('==')[0].split('>=')[0].split('<=')[0].strip()
                        requirements.add(package_name)
                        
        except Exception as e:
            logger.error(f"خطأ في قراءة requirements.txt: {str(e)}")
            
        return requirements

    def generate_missing_packages_report(self) -> Dict[str, List[str]]:
        """
        إنشاء تقرير بالمكتبات المفقودة
        
        Returns:
            قاموس يحتوي على تصنيف المكتبات المفقودة
        """
        unique_imports = self.get_unique_imports()
        requirements = self.read_requirements()
        
        report = {
            'critical_missing': [],
            'optional_missing': [],
            'unknown_imports': [],
            'available_packages': [],
            'requirements_not_imported': []
        }
        
        # فحص المكتبات المستوردة
        for import_name in unique_imports:
            # تخطي المكتبات المدمجة
            if import_name in self.builtin_modules:
                continue
                
            # تخطي الوحدات المحلية
            if (self.src_path / f"{import_name}.py").exists() or \
               (self.src_path / import_name).is_dir():
                continue
                
            is_available, status = self.check_package_availability(import_name)
            
            if is_available:
                report['available_packages'].append(import_name)
            else:
                # تحديد نوع المكتبة
                if import_name in self.critical_packages:
                    report['critical_missing'].append(import_name)
                elif import_name in self.optional_packages:
                    report['optional_missing'].append(import_name)
                else:
                    report['unknown_imports'].append(import_name)
        
        # فحص المكتبات في requirements.txt التي لم يتم استيرادها
        imported_package_names = set()
        for import_name in unique_imports:
            if import_name in self.critical_packages:
                imported_package_names.add(self.critical_packages[import_name])
            elif import_name in self.optional_packages:
                imported_package_names.add(self.optional_packages[import_name])
            else:
                imported_package_names.add(import_name)
        
        for req_package in requirements:
            if req_package not in imported_package_names:
                # تحقق من أن المكتبة ليست مكتبة فرعية
                is_sub_package = any(req_package.startswith(imp + '-') or 
                                   req_package.startswith(imp + '_') 
                                   for imp in imported_package_names)
                if not is_sub_package:
                    report['requirements_not_imported'].append(req_package)
        
        return report

    def suggest_installation_commands(self, missing_packages: List[str]) -> List[str]:
        """
        اقتراح أوامر التثبيت للمكتبات المفقودة
        
        Args:
            missing_packages: قائمة المكتبات المفقودة
            
        Returns:
            قائمة أوامر التثبيت
        """
        commands = []
        
        for package in missing_packages:
            if package in self.critical_packages:
                pip_name = self.critical_packages[package]
            elif package in self.optional_packages:
                pip_name = self.optional_packages[package]
            else:
                pip_name = package
                
            commands.append(f"pip install {pip_name}")
            
        return commands

    def run_full_check(self) -> Dict[str, any]:
        """
        تشغيل فحص شامل للتبعيات
        
        Returns:
            تقرير شامل عن حالة التبعيات
        """
        logger.info("🔍 بدء فحص التبعيات الشامل...")
        
        # فحص المكتبات المفقودة
        missing_report = self.generate_missing_packages_report()
        
        # إنشاء أوامر التثبيت
        critical_commands = self.suggest_installation_commands(missing_report['critical_missing'])
        optional_commands = self.suggest_installation_commands(missing_report['optional_missing'])
        
        # إحصائيات
        total_imports = len(self.get_unique_imports())
        available_count = len(missing_report['available_packages'])
        missing_count = len(missing_report['critical_missing']) + len(missing_report['optional_missing'])
        
        return {
            'summary': {
                'total_imports': total_imports,
                'available_packages': available_count,
                'missing_packages': missing_count,
                'success_rate': f"{(available_count / max(total_imports, 1)) * 100:.1f}%"
            },
            'missing_report': missing_report,
            'installation_commands': {
                'critical': critical_commands,
                'optional': optional_commands
            }
        }

def main():
    """الدالة الرئيسية"""
    checker = DependencyChecker()
    
    print("🔍 فحص التبعيات والمكتبات")
    print("=" * 50)
    
    # تشغيل الفحص الشامل
    report = checker.run_full_check()
    
    # عرض النتائج
    summary = report['summary']
    print(f"\n📊 ملخص النتائج:")
    print(f"   إجمالي الاستيرادات: {summary['total_imports']}")
    print(f"   المكتبات المتوفرة: {summary['available_packages']}")
    print(f"   المكتبات المفقودة: {summary['missing_packages']}")
    print(f"   معدل النجاح: {summary['success_rate']}")
    
    missing = report['missing_report']
    
    # المكتبات الحرجة المفقودة
    if missing['critical_missing']:
        print(f"\n❌ مكتبات حرجة مفقودة ({len(missing['critical_missing'])}):")
        for package in missing['critical_missing']:
            print(f"   - {package}")
        
        print(f"\n🔧 أوامر التثبيت المطلوبة:")
        for cmd in report['installation_commands']['critical']:
            print(f"   {cmd}")
    
    # المكتبات الاختيارية المفقودة
    if missing['optional_missing']:
        print(f"\n⚠️ مكتبات اختيارية مفقودة ({len(missing['optional_missing'])}):")
        for package in missing['optional_missing']:
            print(f"   - {package}")
        
        print(f"\n🔧 أوامر التثبيت الاختيارية:")
        for cmd in report['installation_commands']['optional']:
            print(f"   {cmd}")
    
    # المكتبات المتوفرة
    if missing['available_packages']:
        print(f"\n✅ مكتبات متوفرة ({len(missing['available_packages'])}):")
        for package in sorted(missing['available_packages'])[:10]:  # أول 10 فقط
            print(f"   - {package}")
        if len(missing['available_packages']) > 10:
            print(f"   ... و {len(missing['available_packages']) - 10} مكتبة أخرى")
    
    # تحديد حالة النظام
    if missing['critical_missing']:
        print(f"\n🚨 النظام غير جاهز للتشغيل - يجب تثبيت المكتبات الحرجة")
        return 1
    elif missing['optional_missing']:
        print(f"\n⚠️ النظام جاهز للتشغيل الأساسي - بعض الميزات قد لا تعمل")
        return 0
    else:
        print(f"\n🎉 جميع المكتبات متوفرة - النظام جاهز للتشغيل")
        return 0

if __name__ == "__main__":
    sys.exit(main())
