#!/usr/bin/env python3
"""
اختبار سريع لنظام الأخبار المحدث
"""

import asyncio
import logging
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_news_system():
    """اختبار نظام الأخبار"""
    print("🧪 اختبار نظام الأخبار المحدث...")
    print("=" * 60)
    
    try:
        # اختبار 1: تهيئة نظام الأخبار
        print("\n📰 اختبار تهيئة نظام الأخبار...")
        from services.news_system import NewsSystem
        news_system = NewsSystem()
        print("✅ تم تهيئة نظام الأخبار بنجاح")
        
        # اختبار 2: جلب الأخبار من المصادر المختلفة
        print("\n🔄 اختبار جلب الأخبار...")
        
        # اختبار Binance
        print("  📊 اختبار Binance...")
        binance_news = await news_system.fetch_news_from_binance()
        print(f"  ✅ تم جلب {len(binance_news)} خبر من Binance")
        
        # اختبار CoinDesk
        print("  📊 اختبار CoinDesk...")
        coindesk_news = await news_system.fetch_news_from_coindesk()
        print(f"  ✅ تم جلب {len(coindesk_news)} خبر من CoinDesk")
        
        # اختبار CoinGecko الجديد
        print("  📊 اختبار CoinGecko...")
        coingecko_news = await news_system.fetch_news_from_coingecko()
        print(f"  ✅ تم جلب {len(coingecko_news)} خبر من CoinGecko")
        
        # اختبار 3: تحليل خبر بالذكاء الاصطناعي
        print("\n🤖 اختبار تحليل الأخبار بالذكاء الاصطناعي...")
        if binance_news:
            sample_news = binance_news[0]
            print(f"  📝 تحليل الخبر: {sample_news.title[:50]}...")
            
            analyzed_news = await news_system.analyze_news_with_ai(sample_news)
            if analyzed_news and analyzed_news.ai_analysis:
                print(f"  ✅ تم تحليل الخبر بنجاح")
                print(f"  📊 التحليل: {analyzed_news.ai_analysis[:100]}...")
            else:
                print(f"  ⚠️ لم يتم تحليل الخبر (قد يكون مفتاح Gemini غير متوفر)")
        
        # اختبار 4: تهيئة نظام الجدولة
        print("\n⏰ اختبار نظام الجدولة...")
        from services.automatic_news_scheduler import AutomaticNewsScheduler
        scheduler = AutomaticNewsScheduler(news_system=news_system)
        print("✅ تم تهيئة نظام الجدولة بنجاح")
        
        # اختبار حالة النظام
        status = scheduler.get_scheduler_status()
        print(f"  📊 حالة النظام: {status['status']}")
        
        # اختبار 5: تهيئة نظام الإشعارات
        print("\n🔔 اختبار نظام الإشعارات...")
        from services.automatic_news_notifications import AutomaticNewsNotifications
        notifications = AutomaticNewsNotifications()
        print("✅ تم تهيئة نظام الإشعارات بنجاح")
        
        print("\n" + "=" * 60)
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 ملخص النتائج:")
        print(f"  • Binance: {len(binance_news)} خبر")
        print(f"  • CoinDesk: {len(coindesk_news)} خبر")
        print(f"  • CoinGecko: {len(coingecko_news)} خبر")
        print(f"  • نظام الجدولة: {status['status']}")
        print(f"  • نظام الإشعارات: متاح")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

async def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار نظام الأخبار الذكي")
    print("=" * 60)
    
    success = await test_news_system()
    
    if success:
        print(f"\n✅ تم اختبار النظام بنجاح!")
        print("🎯 النظام جاهز للاستخدام")
    else:
        print(f"\n❌ فشل في اختبار النظام")

if __name__ == "__main__":
    asyncio.run(main())
