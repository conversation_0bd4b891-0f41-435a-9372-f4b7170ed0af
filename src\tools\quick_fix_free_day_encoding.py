"""
إصلاح سريع لمشاكل ترميز رسائل اليوم المجاني
يركز على إصلاح الرموز التعبيرية المعطوبة في رسائل انتهاء اليوم المجاني
"""

import os
import sys
import logging

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_free_day_encoding():
    """إصلاح سريع لمشاكل ترميز رسائل اليوم المجاني"""
    print("🔧 بدء الإصلاح السريع لمشاكل ترميز رسائل اليوم المجاني")
    print("="*60)
    
    fixes_applied = []
    
    # الملفات المطلوب إصلاحها
    files_to_fix = [
        'src/localization/translations.py',
        'src/services/free_day_system.py'
    ]
    
    # قاموس الإصلاحات
    encoding_fixes = {
        # رموز تعبيرية معطوبة
        '��': '🎁',  # رمز الهدية
        '�': '🎁',   # رمز الهدية (نسخة أخرى)
        
        # كلمات عربية معطوبة
        'ا��ممنوح': 'الممنوح',
        'ا��': 'ال',
        
        # رموز أخرى معطوبة
        '��': '⏰',  # رمز الساعة
        '��': '🔔',  # رمز الجرس
        '��': '✅',  # رمز الصح
        '��': '❌',  # رمز الخطأ
    }
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"⚠️ الملف غير موجود: {file_path}")
            continue
        
        print(f"\n🔍 فحص الملف: {file_path}")
        
        try:
            # قراءة الملف
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            original_content = content
            file_fixes = []
            
            # تطبيق الإصلاحات
            for broken, fixed in encoding_fixes.items():
                if broken in content:
                    content = content.replace(broken, fixed)
                    file_fixes.append(f"'{broken}' -> '{fixed}'")
                    print(f"  🔧 إصلاح: '{broken}' -> '{fixed}'")
            
            # إذا تم تطبيق إصلاحات، حفظ الملف
            if content != original_content:
                # إنشاء نسخة احتياطية
                backup_path = file_path + '.backup'
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                print(f"  💾 تم إنشاء نسخة احتياطية: {backup_path}")
                
                # حفظ الملف المصلح
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                fixes_applied.append({
                    'file': file_path,
                    'fixes': file_fixes,
                    'backup': backup_path
                })
                
                print(f"  ✅ تم إصلاح الملف: {file_path}")
            else:
                print(f"  ✅ الملف سليم: {file_path}")
                
        except Exception as e:
            print(f"  ❌ خطأ في إصلاح الملف {file_path}: {str(e)}")
    
    # ملخص النتائج
    print(f"\n" + "="*60)
    print("📋 ملخص الإصلاحات")
    print("="*60)
    
    if fixes_applied:
        print(f"✅ تم إصلاح {len(fixes_applied)} ملف:")
        for fix_info in fixes_applied:
            print(f"\n📄 {fix_info['file']}")
            for fix in fix_info['fixes']:
                print(f"  🔧 {fix}")
            print(f"  💾 نسخة احتياطية: {fix_info['backup']}")
        
        print(f"\n💡 التوصيات:")
        print("  1. اختبر رسائل اليوم المجاني للتأكد من الإصلاح")
        print("  2. إذا كان كل شيء يعمل بشكل صحيح، احذف النسخ الاحتياطية")
        print("  3. إذا ظهرت مشاكل، استعد النسخ الاحتياطية")
        
    else:
        print("✅ لم يتم العثور على مشاكل ترميز!")
        print("🎉 جميع الملفات سليمة!")
    
    return fixes_applied

def test_fixes():
    """اختبار الإصلاحات المطبقة"""
    print(f"\n🧪 اختبار الإصلاحات...")
    
    try:
        # اختبار رسائل الترجمة
        from localization.translations import get_text
        
        # اختبار الرسالة المشكلة
        temp_free_day_msg = get_text('temp_free_day_ended', 'ar')
        print(f"📝 رسالة انتهاء اليوم المجاني المؤقت:")
        print(f"   {temp_free_day_msg}")
        
        # فحص وجود رموز معطوبة
        if '��' in temp_free_day_msg or '�' in temp_free_day_msg:
            print("❌ لا تزال هناك رموز معطوبة!")
            return False
        else:
            print("✅ الرسالة تظهر بشكل صحيح!")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return False

def restore_backups(fixes_applied):
    """استعادة النسخ الاحتياطية"""
    print(f"\n🔄 استعادة النسخ الاحتياطية...")
    
    for fix_info in fixes_applied:
        try:
            file_path = fix_info['file']
            backup_path = fix_info['backup']
            
            if os.path.exists(backup_path):
                # استعادة النسخة الاحتياطية
                with open(backup_path, 'r', encoding='utf-8') as f:
                    backup_content = f.read()
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(backup_content)
                
                print(f"✅ تم استعادة {file_path}")
            
        except Exception as e:
            print(f"❌ خطأ في استعادة {fix_info['file']}: {str(e)}")

def cleanup_backups(fixes_applied):
    """حذف النسخ الاحتياطية"""
    print(f"\n🗑️ حذف النسخ الاحتياطية...")
    
    for fix_info in fixes_applied:
        try:
            backup_path = fix_info['backup']
            if os.path.exists(backup_path):
                os.remove(backup_path)
                print(f"🗑️ تم حذف {backup_path}")
        except Exception as e:
            print(f"❌ خطأ في حذف {fix_info['backup']}: {str(e)}")

def main():
    """تشغيل الإصلاح السريع"""
    try:
        # تطبيق الإصلاحات
        fixes_applied = fix_free_day_encoding()
        
        if fixes_applied:
            # اختبار الإصلاحات
            test_success = test_fixes()
            
            if test_success:
                print(f"\n🎉 تم الإصلاح بنجاح!")
                
                # سؤال المستخدم عن حذف النسخ الاحتياطية
                response = input(f"\n❓ هل تريد حذف النسخ الاحتياطية؟ (y/n): ")
                if response.lower() in ['y', 'yes', 'نعم']:
                    cleanup_backups(fixes_applied)
                    print("✅ تم حذف النسخ الاحتياطية")
                else:
                    print("💾 تم الاحتفاظ بالنسخ الاحتياطية")
            else:
                print(f"\n⚠️ الاختبار فشل!")
                
                response = input(f"\n❓ هل تريد استعادة النسخ الاحتياطية؟ (y/n): ")
                if response.lower() in ['y', 'yes', 'نعم']:
                    restore_backups(fixes_applied)
                    print("🔄 تم استعادة النسخ الاحتياطية")
        else:
            print(f"\n✅ لا توجد مشاكل للإصلاح!")
        
        print(f"\n📋 الخطوات التالية:")
        print("  1. اختبر رسائل اليوم المجاني في البوت")
        print("  2. تأكد من ظهور الرموز التعبيرية بشكل صحيح")
        print("  3. إذا ظهرت مشاكل أخرى، شغل: python fix_encoding_issues.py")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الإصلاح: {str(e)}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
