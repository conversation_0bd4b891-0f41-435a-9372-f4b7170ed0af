"""
نظام إدارة الاشتراكات
"""
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Union

logger = logging.getLogger(__name__)

class SubscriptionSystem:
    def __init__(self, db=None, free_day_system=None, api_manager=None):
        """تهيئة نظام الاشتراكات"""
        self.db = db
        self.free_day_system = free_day_system
        self.api_manager = api_manager
        self.cache_timeout = 3600  # ساعة واحدة

        # تهيئة قاعدة البيانات المحسنة
        self.optimized_db = None
        try:
            from core.system_initialization_extended import _system_components_cache
            if _system_components_cache and 'optimized_db' in _system_components_cache:
                self.optimized_db = _system_components_cache['optimized_db']
                logger.info("✅ تم تفعيل قاعدة البيانات المحسنة في نظام الاشتراكات")
        except Exception as e:
            logger.warning(f"فشل في تفعيل قاعدة البيانات المحسنة: {str(e)}")

    # متغيرات لتخزين البيانات في الذاكرة المحلية
    _subscription_cache = {}
    _subscription_cache_expiry = {}
    _settings_cache = {}
    _settings_expiry = {}
    _free_usage_cache = {}
    _free_usage_expiry = {}

    async def has_api_keys(self, user_id: str, api_type: str) -> bool:
        """التحقق من وجود مفاتيح API للمستخدم"""
        if self.api_manager:
            return await self.api_manager.has_api_keys(user_id, api_type)
        return False

    def clear_user_cache(self, user_id: str):
        """مسح ذاكرة التخزين المؤقت للمستخدم"""
        try:
            # مسح ذاكرة الاشتراكات
            if user_id in self._subscription_cache:
                del self._subscription_cache[user_id]
            if user_id in self._subscription_cache_expiry:
                del self._subscription_cache_expiry[user_id]
                
            # مسح ذاكرة الإعدادات
            if user_id in self._settings_cache:
                del self._settings_cache[user_id]
            if user_id in self._settings_expiry:
                del self._settings_expiry[user_id]
                
            # مسح ذاكرة الاستخدام المجاني
            if user_id in self._free_usage_cache:
                del self._free_usage_cache[user_id]
            if user_id in self._free_usage_expiry:
                del self._free_usage_expiry[user_id]
                
            logger.debug(f"تم مسح ذاكرة التخزين المؤقت للمستخدم {user_id}")
        except Exception as e:
            logger.error(f"خطأ في مسح ذاكرة التخزين المؤقت للمستخدم {user_id}: {str(e)}")

    def get_free_features(self) -> list:
        """الحصول على قائمة الميزات المجانية"""
        return [
            'basic_analysis',
            'price_alerts_limited',
            'basic_indicators'
        ]

    def get_premium_features(self) -> list:
        """الحصول على قائمة الميزات المميزة"""
        return [
            'enhanced_analysis',
            'multi_timeframe_analysis',
            'advanced_indicators',
            'unlimited_alerts',
            'ai_predictions',
            'trading_strategies',
            'portfolio_analysis',
            'risk_management',
            'custom_indicators',
            'priority_support'
        ]

    def get_user_features(self, user_id: str) -> dict:
        """الحصول على ميزات المستخدم بناءً على حالة الاشتراك"""
        try:
            # التحقق من حالة الاشتراك
            is_subscribed = self.is_subscribed_sync(user_id)

            # التحقق من اليوم المجاني باستخدام الدالة الموحدة
            is_free_day = False
            try:
                if self.free_day_system:
                    is_free_day = self.free_day_system.has_active_free_day(user_id)
            except Exception as e:
                logger.error(f"خطأ في التحقق من اليوم المجاني: {str(e)}")

            # تحديد الميزات والمؤشرات المتاحة
            if is_subscribed or is_free_day:
                # المستخدمون المشتركون أو الذين لديهم يوم مجاني
                features = self.get_premium_features()
                indicators = [
                    'rsi', 'macd', 'bollinger_bands', 'moving_averages',
                    'stochastic', 'williams_r', 'cci', 'adx', 'atr',
                    'fibonacci', 'support_resistance', 'volume_analysis'
                ]
            else:
                # المستخدمون غير المشتركين
                features = self.get_free_features()
                indicators = ['rsi', 'macd', 'moving_averages']  # مؤشرات أساسية فقط

            return {
                'features': features,
                'indicators': indicators,
                'is_subscribed': is_subscribed,
                'is_free_day': is_free_day
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على ميزات المستخدم {user_id}: {str(e)}")
            # إرجاع الميزات الأساسية في حالة الخطأ
            return {
                'features': self.get_free_features(),
                'indicators': ['rsi', 'macd', 'moving_averages'],
                'is_subscribed': False,
                'is_free_day': False
            }

    async def get_subscription_info(self, user_id: str, lang: str = 'ar') -> str:
        """الحصول على معلومات الاشتراك للمستخدم"""
        try:
            # التحقق من حالة الاشتراك
            is_subscribed = await self.is_subscribed(user_id, full_details=True)

            # التحقق من مفاتيح API
            has_binance_api = await self.has_api_keys(user_id, 'binance')
            has_gemini_api = await self.has_api_keys(user_id, 'gemini')

            # تحديد تاريخ الانتهاء
            expiry_date = "غير محدد"
            if isinstance(is_subscribed, dict) and is_subscribed.get('is_active'):
                expiry = is_subscribed.get('expiry')
                if expiry:
                    try:
                        if isinstance(expiry, str):
                            expiry_dt = datetime.fromisoformat(expiry.replace('Z', '+00:00'))
                        else:
                            expiry_dt = expiry
                        expiry_date = expiry_dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        expiry_date = str(expiry)

            if lang == 'ar':
                status = "مشترك ✅" if (isinstance(is_subscribed, dict) and is_subscribed.get('is_active')) or is_subscribed else "غير مشترك ❌"
                api_status = (
                    f"• Binance API: {'متصل ✅' if has_binance_api else 'غير متصل ❌'}\n"
                    f"• Gemini API: {'متصل ✅' if has_gemini_api else 'غير متصل ❌'}"
                )

                return f"""💎 نظام الاشتراكات

معرف المستخدم: {user_id}
الحالة: {status}
تاريخ الانتهاء: {expiry_date if (isinstance(is_subscribed, dict) and is_subscribed.get('is_active')) or is_subscribed else 'غير متوفر'}

🔑 حالة API:
{api_status}

⚠️ ملاحظات مهمة:
• لا يوجد دعم فني - اتبع التعليمات بدقة
• أنت المسؤول الوحيد عن عملية التحويل
• الاشتراك غير قابل للاسترداد
• التحويل يعني قبول جميع الشروط
• عدم اتباع الخطوات قد يؤدي لفقدان الاشتراك"""
            else:
                status = "Subscribed ✅" if (isinstance(is_subscribed, dict) and is_subscribed.get('is_active')) or is_subscribed else "Not Subscribed ❌"
                api_status = (
                    f"• Binance API: {'Connected ✅' if has_binance_api else 'Not Connected ❌'}\n"
                    f"• Gemini API: {'Connected ✅' if has_gemini_api else 'Not Connected ❌'}"
                )

                return f"""💎 Subscription System

User ID: {user_id}
Status: {status}
Expiry Date: {expiry_date if (isinstance(is_subscribed, dict) and is_subscribed.get('is_active')) or is_subscribed else 'Not Available'}

🔑 API Status:
{api_status}

⚠️ Important Notes:
• No technical support - follow instructions carefully
• You are solely responsible for the transfer process
• Subscription is non-refundable
• Transfer means accepting all terms
• Not following steps may result in subscription loss"""

        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الاشتراك للمستخدم {user_id}: {str(e)}")
            if lang == 'ar':
                return f"❌ خطأ في الحصول على معلومات الاشتراك: {str(e)}"
            else:
                return f"❌ Error getting subscription info: {str(e)}"

    async def is_subscribed(self, user_id: str, full_details: bool = False) -> Union[bool, Dict[str, Any]]:
        """التحقق من حالة الاشتراك للمستخدم مع دعم الذاكرة المحلية"""
        try:
            current_time = datetime.now()

            # التحقق من الذاكرة المحلية أولاً
            if user_id in self._subscription_cache and user_id in self._subscription_cache_expiry:
                if current_time < self._subscription_cache_expiry[user_id]:
                    cached_data = self._subscription_cache[user_id]

                    # التحقق من اليوم المجاني
                    try:
                        # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                        if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                            # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                            temp_subscription = cached_data.copy()
                            temp_subscription['is_active'] = True
                            temp_subscription['is_free_day'] = True
                            temp_subscription['features'] = self.get_premium_features()

                            if full_details:
                                return temp_subscription
                            return True
                    except Exception as free_day_error:
                        logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                    if full_details:
                        return cached_data
                    return cached_data.get('is_active', False)

            # التحقق من Firestore
            try:
                if not self.db:
                    logger.error("قاعدة البيانات غير متوفرة")
                    return False

                user_ref = self.db.collection('subscriptions').document(user_id)
                user_data = user_ref.get()

                if not user_data.exists:
                    # لا يوجد اشتراك في Firestore
                    subscription_data = {
                        'is_active': False,
                        'expiry': None,
                        'features': self.get_free_features()
                    }
                    # تخزين في الذاكرة المحلية
                    self._subscription_cache[user_id] = subscription_data
                    self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                    # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                    try:
                        if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                            # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                            temp_subscription = subscription_data.copy()
                            temp_subscription['is_active'] = True
                            temp_subscription['is_free_day'] = True
                            temp_subscription['features'] = self.get_premium_features()

                            if full_details:
                                return temp_subscription
                            return True
                    except Exception as free_day_error:
                        logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                    if full_details:
                        return subscription_data
                    return False

                subscription = user_data.to_dict()

                # التحقق من صحة البيانات
                if not subscription:
                    subscription_data = {
                        'is_active': False,
                        'expiry': None,
                        'features': self.get_free_features()
                    }
                    # تخزين في الذاكرة المحلية
                    self._subscription_cache[user_id] = subscription_data
                    self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                    # التحقق من اليوم المجاني
                    try:
                        # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                        if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                            # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                            temp_subscription = subscription_data.copy()
                            temp_subscription['is_active'] = True
                            temp_subscription['is_free_day'] = True
                            temp_subscription['features'] = self.get_premium_features()

                            if full_details:
                                return temp_subscription
                            return True
                    except Exception as free_day_error:
                        logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                    if full_details:
                        return subscription_data
                    return False

                # التحقق من تاريخ انتهاء الاشتراك
                expiry = subscription.get('expiry')
                is_active = subscription.get('is_active', False)

                if expiry:
                    try:
                        if isinstance(expiry, str):
                            expiry_date = datetime.fromisoformat(expiry.replace('Z', '+00:00'))
                        else:
                            expiry_date = expiry

                        # التحقق من انتهاء الاشتراك
                        if expiry_date <= current_time:
                            is_active = False
                    except Exception as date_error:
                        logger.error(f"خطأ في تحليل تاريخ انتهاء الاشتراك للمستخدم {user_id}: {str(date_error)}")
                        is_active = False

                # إعداد بيانات الاشتراك للذاكرة المحلية
                subscription_data = {
                    'is_active': is_active,
                    'expiry': expiry,
                    'features': self.get_premium_features() if is_active else self.get_free_features(),
                    'subscription_date': subscription.get('subscription_date'),
                    'transaction_id': subscription.get('transaction_id'),
                    'lang': subscription.get('lang', 'ar')
                }

                # تخزين في الذاكرة المحلية
                self._subscription_cache[user_id] = subscription_data
                self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                try:
                    if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                        # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                        temp_subscription = subscription_data.copy()
                        temp_subscription['is_active'] = True
                        temp_subscription['is_free_day'] = True
                        temp_subscription['features'] = self.get_premium_features()

                        if full_details:
                            return temp_subscription
                        return True
                except Exception as free_day_error:
                    logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                if full_details:
                    return subscription_data
                return is_active

            except Exception as firestore_error:
                logger.error(f"خطأ في الوصول إلى Firestore للمستخدم {user_id}: {str(firestore_error)}")

                # في حالة فشل الوصول إلى Firestore، نعيد البيانات الافتراضية
                default_data = {
                    'is_active': False,
                    'expiry': None,
                    'features': self.get_free_features()
                }

                # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                try:
                    if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                        # اليوم المجاني مفعل، نعيد حالة اشتراك مؤقتة
                        temp_subscription = default_data.copy()
                        temp_subscription['is_active'] = True
                        temp_subscription['is_free_day'] = True
                        temp_subscription['features'] = self.get_premium_features()

                        if full_details:
                            return temp_subscription
                        return True
                except Exception as free_day_error:
                    logger.error(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(free_day_error)}")

                if full_details:
                    return default_data
                return False

        except Exception as e:
            logger.error(f"خطأ عام في التحقق من الاشتراك للمستخدم {user_id}: {str(e)}")

            # في حالة حدوث خطأ عام، نعيد False
            if full_details:
                return {
                    'is_active': False,
                    'expiry': None,
                    'features': self.get_free_features(),
                    'error': str(e)
                }
            return False

    def get_user_settings(self, user_id: str) -> dict:
        """الحصول على إعدادات المستخدم مع دعم الذاكرة المحلية"""
        try:
            current_time = datetime.now()

            # استخدام قاعدة البيانات المحسنة إذا كانت متاحة
            if self.optimized_db:
                try:
                    settings = self.optimized_db.get_user_settings_optimized(user_id)
                    if settings:
                        logger.debug(f"✅ تم الحصول على إعدادات المستخدم {user_id} من قاعدة البيانات المحسنة")
                        return settings
                except Exception as opt_error:
                    logger.warning(f"فشل في استخدام قاعدة البيانات المحسنة: {str(opt_error)}")

            # التحقق من الذاكرة المحلية أولاً
            if user_id in self._settings_cache and user_id in self._settings_expiry:
                if current_time < self._settings_expiry[user_id]:
                    return self._settings_cache[user_id]

            # الحصول على الإعدادات من Firestore
            if not self.db:
                return {}

            user_ref = self.db.collection('user_settings').document(user_id)
            user_data = user_ref.get()

            if user_data.exists:
                settings = user_data.to_dict()
            else:
                # إعدادات افتراضية للمستخدمين الجدد
                # التحقق من حالة الاشتراك لتحديد نوع التحليل الافتراضي
                is_subscribed = self.is_subscribed_sync(user_id)
                default_analysis_type = 'ai' if is_subscribed else 'traditional'

                # محاولة تحديد اللغة من معلومات Telegram إذا كانت متوفرة
                default_lang = 'ar'  # افتراضي
                try:
                    # يمكن تمرير معلومات المستخدم من Telegram لتحديد اللغة
                    # هذا سيتم تحسينه لاحقاً عند توفر معلومات المستخدم
                    pass
                except:
                    pass

                settings = {
                    'language': default_lang,
                    'lang': default_lang,  # للتوافق مع الكود الحالي
                    'trading_style': 'swing',
                    'analysis_type': default_analysis_type,
                    'notifications': True,
                    'timezone': 'UTC',
                    'lang_selected': False,  # لم يختر اللغة بعد
                    'terms_accepted': False,  # لم يوافق على الشروط بعد
                    'created_at': datetime.now().isoformat(),
                    'updated_at': datetime.now().isoformat()
                }
                # حفظ الإعدادات الافتراضية
                user_ref.set(settings)

            # تخزين في الذاكرة المحلية
            self._settings_cache[user_id] = settings
            self._settings_expiry[user_id] = current_time + timedelta(hours=1)

            return settings

        except Exception as e:
            logger.error(f"خطأ في الحصول على إعدادات المستخدم {user_id}: {str(e)}")
            return {}

    def update_user_settings(self, user_id: str, settings: dict = None, **kwargs) -> bool:
        """تحديث إعدادات المستخدم"""
        try:
            if not self.db:
                return False

            # دمج الإعدادات من المعامل والكلمات المفتاحية
            if settings is None:
                settings = {}
            settings.update(kwargs)

            if not settings:
                logger.warning(f"لا توجد إعدادات لتحديثها للمستخدم {user_id}")
                return False

            user_ref = self.db.collection('user_settings').document(user_id)
            user_ref.set(settings, merge=True)

            # تحديث الذاكرة المحلية
            current_time = datetime.now()
            if user_id in self._settings_cache:
                self._settings_cache[user_id].update(settings)
            else:
                self._settings_cache[user_id] = settings
            self._settings_expiry[user_id] = current_time + timedelta(hours=1)

            logger.info(f"✅ تم تحديث إعدادات المستخدم {user_id}: {settings}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث إعدادات المستخدم {user_id}: {str(e)}")
            return False

    def get_free_usage(self, user_id: str) -> dict:
        """الحصول على الاستخدام المجاني للمستخدم مع ضمان التزامن"""
        try:
            # إعداد افتراضي
            default_usage = {
                'date': datetime.now().date().isoformat(),
                'analyses': 3,
                'alerts': 1
            }

            if not self.db:
                logger.warning("قاعدة البيانات غير متوفرة، استخدام القيم الافتراضية")
                return default_usage

            # جلب من Firestore مباشرة لضمان الحصول على أحدث البيانات
            usage_ref = self.db.collection('free_usage').document(user_id)
            usage_doc = usage_ref.get()

            if usage_doc.exists:
                usage = usage_doc.to_dict()
                # التحقق من تاريخ الاستخدام
                today = datetime.now().date().isoformat()
                if usage.get('date') != today:
                    # إعادة تعيين للاستخدام اليومي
                    usage = default_usage
                    usage_ref.set(usage)
                    logger.info(f"تم إعادة تعيين الاستخدام المجاني للمستخدم {user_id} لليوم الجديد")
            else:
                # إنشاء سجل جديد
                usage = default_usage
                usage_ref.set(usage)
                logger.info(f"تم إنشاء سجل استخدام مجاني جديد للمستخدم {user_id}")

            # تحديث الذاكرة المحلية مع البيانات الجديدة
            current_time = datetime.now()
            self._free_usage_cache[user_id] = usage
            self._free_usage_expiry[user_id] = current_time + timedelta(minutes=30)  # تقليل مدة التخزين المؤقت

            logger.info(f"الاستخدام المجاني للمستخدم {user_id}: {usage}")
            return usage

        except Exception as e:
            logger.error(f"خطأ في الحصول على الاستخدام المجاني للمستخدم {user_id}: {str(e)}")
            return default_usage

    def get_subscription_status(self, user_id: str, full_details: bool = False) -> Union[bool, dict]:
        """
        الحصول على حالة الاشتراك مع تفاصيل إضافية

        Args:
            user_id: معرف المستخدم
            full_details: إذا كان True، يعيد تفاصيل كاملة، وإلا يعيد True/False فقط

        Returns:
            bool أو dict حسب قيمة full_details
        """
        try:
            current_time = datetime.now()

            # التحقق من الذاكرة المحلية أولاً
            if user_id in self._subscription_cache and user_id in self._subscription_cache_expiry:
                if current_time < self._subscription_cache_expiry[user_id]:
                    cached_data = self._subscription_cache[user_id]

                    # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                    has_free_day = False
                    try:
                        has_free_day = self.free_day_system.has_active_free_day(user_id) if self.free_day_system else False
                    except Exception:
                        pass

                    is_active = cached_data.get('is_active', False) or has_free_day

                    if full_details:
                        return {
                            'is_active': is_active,
                            'is_free_day': has_free_day,
                            'expiry': cached_data.get('expiry'),
                            'features': cached_data.get('features', self.get_free_features())
                        }
                    return is_active

            # إذا لم تكن البيانات في الذاكرة المؤقتة، استخدم الطريقة غير المتزامنة
            import asyncio
            try:
                # محاولة استخدام الحلقة الحالية
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # إذا كانت الحلقة تعمل، استخدم الطريقة المتزامنة البديلة
                    if not full_details:
                        return self.is_subscribed_sync(user_id)
                    else:
                        # التحقق من اليوم المجاني للتفاصيل الكاملة باستخدام الدالة الموحدة
                        has_free_day = False
                        try:
                            has_free_day = self.free_day_system.has_active_free_day(user_id) if self.free_day_system else False
                        except Exception:
                            pass

                        is_subscribed = self.is_subscribed_sync(user_id)

                        return {
                            'is_active': is_subscribed or has_free_day,
                            'is_free_day': has_free_day,
                            'expiry': None,
                            'features': self.get_premium_features() if (is_subscribed or has_free_day) else self.get_free_features()
                        }
                else:
                    # إذا لم تكن الحلقة تعمل، يمكن تشغيل الطريقة غير المتزامنة
                    result = loop.run_until_complete(self.is_subscribed(user_id, full_details=full_details))
                    return result
            except RuntimeError:
                # إذا لم تكن هناك حلقة أحداث، استخدم الطريقة المتزامنة البديلة
                if not full_details:
                    return self.is_subscribed_sync(user_id)
                else:
                    # التحقق من اليوم المجاني للتفاصيل الكاملة باستخدام الدالة الموحدة
                    has_free_day = False
                    try:
                        has_free_day = self.free_day_system.has_active_free_day(user_id) if self.free_day_system else False
                    except Exception:
                        pass

                    is_subscribed = self.is_subscribed_sync(user_id)

                    return {
                        'is_active': is_subscribed or has_free_day,
                        'is_free_day': has_free_day,
                        'expiry': None,
                        'features': self.get_premium_features() if (is_subscribed or has_free_day) else self.get_free_features()
                    }

        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة الاشتراك للمستخدم {user_id}: {str(e)}")
            if full_details:
                # حتى في حالة الخطأ، نحاول التحقق من اليوم المجاني باستخدام الدالة الموحدة
                has_free_day = False
                try:
                    has_free_day = self.free_day_system.has_active_free_day(user_id) if self.free_day_system else False
                except Exception:
                    pass

                return {
                    'is_active': has_free_day,
                    'is_free_day': has_free_day,
                    'expiry': None,
                    'features': self.get_premium_features() if has_free_day else self.get_free_features()
                }

            # للحالة البسيطة، نحاول التحقق من اليوم المجاني أيضاً باستخدام الدالة الموحدة
            try:
                return self.free_day_system.has_active_free_day(user_id) if self.free_day_system else False
            except Exception:
                return False

    def is_subscribed_sync(self, user_id: str) -> bool:
        """التحقق من حالة الاشتراك بطريقة متزامنة (لتجنب مشاكل حلقة الأحداث)"""
        try:
            current_time = datetime.now()

            # التحقق من الذاكرة المحلية أولاً
            if user_id in self._subscription_cache and user_id in self._subscription_cache_expiry:
                if current_time < self._subscription_cache_expiry[user_id]:
                    cached_data = self._subscription_cache[user_id]

                    # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                    try:
                        if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                            return True
                    except Exception:
                        pass

                    return cached_data.get('is_active', False)

            # التحقق من Firestore بطريقة متزامنة
            if self.db:
                try:
                    subscription_ref = self.db.collection('subscriptions').document(user_id)
                    subscription_doc = subscription_ref.get()

                    if subscription_doc.exists:
                        subscription_data = subscription_doc.to_dict()
                        is_active = subscription_data.get('is_active', False)

                        # التحقق من تاريخ انتهاء الاشتراك
                        expiry = subscription_data.get('expiry')
                        if expiry and is_active:
                            try:
                                if isinstance(expiry, str):
                                    expiry_date = datetime.fromisoformat(expiry.replace('Z', '+00:00'))
                                else:
                                    expiry_date = expiry

                                if expiry_date <= current_time:
                                    is_active = False
                            except Exception:
                                is_active = False

                        # حفظ في الذاكرة المحلية
                        cache_data = {
                            'is_active': is_active,
                            'expiry': expiry,
                            'features': self.get_premium_features() if is_active else self.get_free_features()
                        }
                        self._subscription_cache[user_id] = cache_data
                        self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                        # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                        try:
                            if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                                return True
                        except Exception:
                            pass

                        return is_active
                except Exception as e:
                    logger.error(f"خطأ في التحقق المتزامن من الاشتراك للمستخدم {user_id}: {str(e)}")

            # التحقق من اليوم المجاني كحل أخير باستخدام الدالة الموحدة
            try:
                if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                    return True
            except Exception:
                pass

            return False

        except Exception as e:
            logger.error(f"خطأ عام في التحقق المتزامن من الاشتراك للمستخدم {user_id}: {str(e)}")
            return False

    def can_use_free_analysis(self, user_id: str) -> bool:
        """التحقق من إمكانية استخدام تحليل مجاني"""
        try:
            # التحقق من حالة الاشتراك أولاً باستخدام الطريقة المتزامنة
            if self.is_subscribed_sync(user_id):
                return True

            # التحقق من اليوم المجاني - إذا كان نشطاً، السماح بالاستخدام غير المحدود
            try:
                if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                    logger.info(f"المستخدم {user_id} لديه يوم مجاني نشط - السماح بالتحليل غير المحدود")
                    return True
            except Exception as e:
                logger.warning(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(e)}")

            # للمستخدمين غير المشتركين وبدون يوم مجاني، التحقق من الاستخدام المجاني
            usage = self.get_free_usage(user_id)
            return usage.get('analyses', 0) > 0

        except Exception as e:
            logger.error(f"خطأ في التحقق من إمكانية استخدام تحليل مجاني للمستخدم {user_id}: {str(e)}")
            return False

    def can_use_free_alert(self, user_id: str) -> bool:
        """التحقق من إمكانية استخدام تنبيه مجاني"""
        try:
            # التحقق من حالة الاشتراك أولاً
            if self.is_subscribed_sync(user_id):
                return True

            # التحقق من اليوم المجاني - إذا كان نشطاً، السماح بالاستخدام غير المحدود
            try:
                if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                    logger.info(f"المستخدم {user_id} لديه يوم مجاني نشط - السماح بالتنبيهات غير المحدودة")
                    return True
            except Exception as e:
                logger.warning(f"خطأ في التحقق من اليوم المجاني للمستخدم {user_id}: {str(e)}")

            current_time = datetime.now()
            today = current_time.date()

            # التحقق من الذاكرة المحلية أولاً
            cache_key = f"{user_id}_{today}"
            if cache_key in self._free_usage_cache and cache_key in self._free_usage_expiry:
                if current_time < self._free_usage_expiry[cache_key]:
                    usage_data = self._free_usage_cache[cache_key]
                    return usage_data.get('alerts_used', 0) < 3  # حد أقصى 3 تنبيهات مجانية يومياً

            # الحصول على البيانات من Firestore
            if not self.db:
                return True  # السماح بالاستخدام في حالة عدم توفر قاعدة البيانات

            usage_ref = self.db.collection('free_usage').document(user_id)
            usage_data_doc = usage_ref.get()

            if usage_data_doc.exists:
                usage_data = usage_data_doc.to_dict()
                last_usage_date = usage_data.get('last_usage_date')

                # التحقق من التاريخ
                if last_usage_date:
                    if isinstance(last_usage_date, str):
                        last_date = datetime.fromisoformat(last_usage_date).date()
                    else:
                        last_date = last_usage_date.date()

                    if last_date == today:
                        alerts_used = usage_data.get('alerts_used', 0)
                        can_use = alerts_used < 3

                        # تخزين في الذاكرة المحلية
                        self._free_usage_cache[cache_key] = usage_data
                        self._free_usage_expiry[cache_key] = current_time + timedelta(hours=1)

                        return can_use

            # إذا لم يتم الاستخدام اليوم، يمكن الاستخدام
            return True

        except Exception as e:
            logger.error(f"خطأ في التحقق من استخدام التنبيه المجاني للمستخدم {user_id}: {str(e)}")
            return True  # السماح بالاستخدام في حالة الخطأ

    def use_free_analysis(self, user_id: str) -> bool:
        """استخدام تحليل مجاني"""
        try:
            current_time = datetime.now()
            today = current_time.date()

            if not self.db:
                return False

            usage_ref = self.db.collection('free_usage').document(user_id)
            usage_data_doc = usage_ref.get()

            if usage_data_doc.exists:
                usage_data = usage_data_doc.to_dict()
                last_usage_date = usage_data.get('date')

                # التحقق من التاريخ
                if last_usage_date:
                    if isinstance(last_usage_date, str):
                        last_date = datetime.fromisoformat(last_usage_date).date()
                    else:
                        last_date = last_usage_date.date()

                    if last_date == today:
                        analyses_used = usage_data.get('analyses', 3)  # القيمة الافتراضية 3
                        if analyses_used <= 0:
                            return False  # تم الوصول للحد الأقصى

                        # تقليل العداد
                        usage_data['analyses'] = analyses_used - 1
                        usage_data['date'] = today.isoformat()
                    else:
                        # يوم جديد، إعادة تعيين العداد
                        usage_data = {
                            'analyses': 2,  # 3 - 1 = 2 متبقية
                            'alerts': usage_data.get('alerts', 1),  # الحفاظ على التنبيهات
                            'date': today.isoformat()
                        }
                else:
                    # أول استخدام
                    usage_data = {
                        'analyses': 2,  # 3 - 1 = 2 متبقية
                        'alerts': 1,
                        'date': today.isoformat()
                    }
            else:
                # أول استخدام للمستخدم
                usage_data = {
                    'analyses': 2,  # 3 - 1 = 2 متبقية
                    'alerts': 1,
                    'date': today.isoformat()
                }

            # حفظ التحديث
            usage_ref.set(usage_data)

            # تحديث الذاكرة المحلية مع مدة أقصر لضمان التزامن
            self._free_usage_cache[user_id] = usage_data
            self._free_usage_expiry[user_id] = current_time + timedelta(minutes=30)

            # مسح ذاكرة القائمة الرئيسية لإجبار التحديث
            try:
                from utils.text_helpers import _menu_cache, _menu_cache_expiry
                cache_keys_to_remove = [key for key in _menu_cache.keys() if key.startswith(f"{user_id}_")]
                for key in cache_keys_to_remove:
                    _menu_cache.pop(key, None)
                    _menu_cache_expiry.pop(key, None)
                logger.info(f"تم مسح ذاكرة القائمة الرئيسية للمستخدم {user_id}")
            except Exception as cache_error:
                logger.warning(f"فشل في مسح ذاكرة القائمة الرئيسية: {str(cache_error)}")

            logger.info(f"✅ تم استخدام تحليل مجاني للمستخدم {user_id}. المتبقي: {usage_data.get('analyses', 0)}")
            return True

        except Exception as e:
            logger.error(f"خطأ في استخدام تحليل مجاني للمستخدم {user_id}: {str(e)}")
            return False

    def use_free_alert(self, user_id: str) -> bool:
        """استخدام تنبيه مجاني"""
        try:
            current_time = datetime.now()
            today = current_time.date()

            if not self.db:
                return False

            usage_ref = self.db.collection('free_usage').document(user_id)
            usage_data_doc = usage_ref.get()

            if usage_data_doc.exists:
                usage_data = usage_data_doc.to_dict()
                last_usage_date = usage_data.get('last_usage_date')

                # التحقق من التاريخ
                if last_usage_date:
                    if isinstance(last_usage_date, str):
                        last_date = datetime.fromisoformat(last_usage_date).date()
                    else:
                        last_date = last_usage_date.date()

                    if last_date == today:
                        alerts_used = usage_data.get('alerts_used', 0)
                        if alerts_used >= 3:
                            return False  # تم الوصول للحد الأقصى

                        # زيادة العداد
                        usage_data['alerts_used'] = alerts_used + 1
                    else:
                        # يوم جديد، إعادة تعيين العداد
                        usage_data = {
                            'alerts_used': 1,
                            'last_usage_date': today.isoformat()
                        }
                else:
                    # أول استخدام
                    usage_data = {
                        'alerts_used': 1,
                        'last_usage_date': today.isoformat()
                    }
            else:
                # أول استخدام للمستخدم
                usage_data = {
                    'alerts_used': 1,
                    'last_usage_date': today.isoformat()
                }

            # حفظ البيانات
            usage_ref.set(usage_data)

            # تحديث الذاكرة المحلية
            cache_key = f"{user_id}_{today}"
            self._free_usage_cache[cache_key] = usage_data
            self._free_usage_expiry[cache_key] = current_time + timedelta(hours=1)

            return True

        except Exception as e:
            logger.error(f"خطأ عام في استخدام تنبيه مجاني للمستخدم {user_id}: {str(e)}")
            return False

    async def activate_subscription(self, user_id: str, transaction_id: str, lang: str = 'ar') -> bool:
        """دالة موحدة لتفعيل الاشتراك"""
        try:
            # حساب تاريخ انتهاء الاشتراك
            current_time = datetime.now()
            expiry_date = current_time + timedelta(days=7)

            # إنشاء بيانات الاشتراك
            subscription_data = {
                'status': 'active',
                'subscription_date': current_time.isoformat(),
                'expiry': expiry_date.isoformat(),
                'transaction_id': transaction_id,
                'lang': lang,
                'features': self.get_premium_features()
            }

            if not self.db:
                logger.error("قاعدة البيانات غير متوفرة")
                return False

            # حفظ في Firestore
            user_ref = self.db.collection('subscriptions').document(user_id)
            user_ref.set(subscription_data)

            # تحديث الذاكرة المحلية
            cache_data = {
                'is_active': True,
                'expiry': expiry_date.isoformat(),
                'features': self.get_premium_features(),
                'subscription_date': current_time.isoformat(),
                'transaction_id': transaction_id,
                'lang': lang
            }
            self._subscription_cache[user_id] = cache_data
            self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

            logger.info(f"✅ تم تفعيل الاشتراك للمستخدم {user_id} بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في تفعيل الاشتراك للمستخدم {user_id}: {str(e)}")
            return False

    def add_subscription(self, user_id: str, lang: str = 'ar', transaction_id: str = None) -> bool:
        """إضافة اشتراك جديد (واجهة متوافقة مع الكود القديم)"""
        try:
            current_time = datetime.now()
            expiry_date = current_time + timedelta(days=7)

            subscription_data = {
                'status': 'active',
                'subscription_date': current_time.isoformat(),
                'expiry': expiry_date.isoformat(),
                'transaction_id': transaction_id,
                'lang': lang,
                'features': self.get_premium_features()
            }

            if not self.db:
                logger.error("قاعدة البيانات غير متوفرة")
                return False

            # حفظ في Firestore
            user_ref = self.db.collection('subscriptions').document(user_id)
            user_ref.set(subscription_data)

            # تحديث الذاكرة المحلية
            cache_data = {
                'is_active': True,
                'expiry': expiry_date.isoformat(),
                'features': self.get_premium_features(),
                'subscription_date': current_time.isoformat(),
                'transaction_id': transaction_id,
                'lang': lang
            }
            self._subscription_cache[user_id] = cache_data
            self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

            logger.info(f"✅ تم إضافة اشتراك للمستخدم {user_id} بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في إضافة اشتراك للمستخدم {user_id}: {str(e)}")
            return False

    def remove_subscription(self, user_id: str) -> bool:
        """إزالة اشتراك المستخدم"""
        try:
            if not self.db:
                logger.error("قاعدة البيانات غير متوفرة")
                return False

            # حذف من Firestore
            user_ref = self.db.collection('subscriptions').document(user_id)
            user_ref.delete()

            # مسح من الذاكرة المحلية
            self.clear_user_cache(user_id)

            logger.info(f"✅ تم إزالة اشتراك المستخدم {user_id} بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في إزالة اشتراك المستخدم {user_id}: {str(e)}")
            return False

    def extend_subscription(self, user_id: str, days: int = 7) -> bool:
        """تمديد اشتراك المستخدم"""
        try:
            if not self.db:
                logger.error("قاعدة البيانات غير متوفرة")
                return False

            user_ref = self.db.collection('subscriptions').document(user_id)
            user_data = user_ref.get()

            if user_data.exists:
                subscription = user_data.to_dict()
                current_expiry = subscription.get('expiry')

                if current_expiry:
                    if isinstance(current_expiry, str):
                        expiry_date = datetime.fromisoformat(current_expiry.replace('Z', '+00:00'))
                    else:
                        expiry_date = current_expiry
                else:
                    expiry_date = datetime.now()

                # تمديد الاشتراك
                new_expiry = expiry_date + timedelta(days=days)
                subscription['expiry'] = new_expiry.isoformat()
                subscription['status'] = 'active'

                # حفظ التحديث
                user_ref.set(subscription)

                # تحديث الذاكرة المحلية
                cache_data = {
                    'is_active': True,
                    'expiry': new_expiry.isoformat(),
                    'features': self.get_premium_features(),
                    'subscription_date': subscription.get('subscription_date'),
                    'transaction_id': subscription.get('transaction_id'),
                    'lang': subscription.get('lang', 'ar')
                }
                self._subscription_cache[user_id] = cache_data
                self._subscription_cache_expiry[user_id] = datetime.now() + timedelta(hours=1)

                logger.info(f"✅ تم تمديد اشتراك المستخدم {user_id} لـ {days} أيام")
                return True
            else:
                logger.warning(f"لا يوجد اشتراك للمستخدم {user_id} لتمديده")
                return False

        except Exception as e:
            logger.error(f"خطأ في تمديد اشتراك المستخدم {user_id}: {str(e)}")
            return False

    async def test_subscription(self, update, context):
        """اختبار نظام الاشتراكات (للمطورين فقط)"""
        try:
            user_id = str(update.effective_user.id)

            # التحقق من صلاحيات المطور
            from config import SystemConfig
            if user_id != str(SystemConfig.DEVELOPER_ID):
                await update.message.reply_text("❌ هذا الأمر متاح للمطورين فقط")
                return

            # استخراج معاملات الأمر
            args = context.args
            if len(args) < 2:
                await update.message.reply_text(
                    "❌ الاستخدام الصحيح:\n"
                    "/test_subscription <user_id> <status>\n"
                    "حيث status يمكن أن يكون: active أو inactive"
                )
                return

            target_user_id = args[0]
            status = args[1].lower()

            # تحويل الحالة إلى الصيغة المناسبة
            if status.lower() in ['active', 'مشترك']:
                status = 'مشترك'
                is_active = True
            elif status.lower() in ['inactive', 'غير مشترك']:
                status = 'غير مشترك'
                is_active = False
            else:
                await update.message.reply_text("❌ الحالة غير صحيحة. الرجاء استخدام 'active' أو 'inactive'")
                return

            if not self.db:
                await update.message.reply_text("❌ قاعدة البيانات غير متوفرة")
                return

            # تحديث حالة الاشتراك في جدول users
            users_ref = self.db.collection('users').document(target_user_id)

            # حساب تاريخ انتهاء الاشتراك (أسبوع من الآن)
            expiry_date = (datetime.now() + timedelta(days=7)).isoformat() if is_active else None

            # تحديث بيانات المستخدم
            user_data = {
                'subscriptionStatus': status,
                'subscriptionExpiry': expiry_date,
                'lastUpdated': datetime.now().isoformat()
            }
            users_ref.set(user_data, merge=True)

            # تحديث في جدول subscriptions أيضاً
            subscription_data = {
                'is_active': is_active,
                'expiry': expiry_date,
                'features': self.get_premium_features() if is_active else self.get_free_features()
            }

            if is_active:
                subscription_data.update({
                    'status': 'active',
                    'subscription_date': datetime.now().isoformat(),
                })
            else:
                subscription_data.update({
                    'status': 'inactive',
                })

            self.db.collection('subscriptions').document(target_user_id).set(subscription_data)

            # مسح الذاكرة المؤقتة للمستخدم
            self.clear_user_cache(target_user_id)

            # إرسال رسالة تأكيد
            await update.message.reply_text(
                f"✅ تم تحديث حالة المستخدم {target_user_id} إلى {status}\n"
                f"📅 تاريخ الانتهاء: {expiry_date if is_active else 'غير محدد'}"
            )

        except Exception as e:
            logger.error(f"خطأ في اختبار الاشتراك: {str(e)}")
            await update.message.reply_text(f"❌ حدث خطأ: {str(e)}")

    def get_subscription_details(self, user_id: str) -> dict:
        """الحصول على تفاصيل الاشتراك للمستخدم"""
        try:
            if not self.db:
                return {}

            # التحقق من الذاكرة المحلية أولاً
            current_time = datetime.now()
            if user_id in self._subscription_cache and user_id in self._subscription_cache_expiry:
                if current_time < self._subscription_cache_expiry[user_id]:
                    cached_data = self._subscription_cache[user_id]

                    # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                    try:
                        if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                            # إضافة معلومات اليوم المجاني
                            temp_details = cached_data.copy()
                            temp_details['is_free_day'] = True
                            temp_details['is_active'] = True
                            temp_details['features'] = self.get_premium_features()
                            return temp_details
                    except Exception:
                        pass

                    return cached_data

            # الحصول من قاعدة البيانات
            subscription_ref = self.db.collection('subscriptions').document(user_id)
            subscription_doc = subscription_ref.get()

            if subscription_doc.exists:
                subscription_data = subscription_doc.to_dict()

                # التحقق من صحة الاشتراك
                is_active = subscription_data.get('is_active', False)
                expiry = subscription_data.get('expiry')

                if expiry and is_active:
                    try:
                        if isinstance(expiry, str):
                            expiry_date = datetime.fromisoformat(expiry.replace('Z', '+00:00'))
                        else:
                            expiry_date = expiry

                        if expiry_date <= current_time:
                            is_active = False
                            subscription_data['is_active'] = False
                    except Exception:
                        is_active = False
                        subscription_data['is_active'] = False

                # إضافة الميزات
                subscription_data['features'] = self.get_premium_features() if is_active else self.get_free_features()

                # حفظ في الذاكرة المحلية
                self._subscription_cache[user_id] = subscription_data
                self._subscription_cache_expiry[user_id] = current_time + timedelta(hours=1)

                # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                try:
                    if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                        # إضافة معلومات اليوم المجاني
                        subscription_data['is_free_day'] = True
                        subscription_data['is_active'] = True
                        subscription_data['features'] = self.get_premium_features()
                except Exception:
                    pass

                return subscription_data
            else:
                # لا يوجد اشتراك، إرجاع بيانات افتراضية
                default_data = {
                    'is_active': False,
                    'features': self.get_free_features(),
                    'status': 'inactive'
                }

                # التحقق من اليوم المجاني باستخدام الدالة الموحدة
                try:
                    if self.free_day_system and self.free_day_system.has_active_free_day(user_id):
                        # إضافة معلومات اليوم المجاني
                        default_data['is_free_day'] = True
                        default_data['is_active'] = True
                        default_data['features'] = self.get_premium_features()
                except Exception:
                    pass

                return default_data

        except Exception as e:
            logger.error(f"خطأ في الحصول على تفاصيل الاشتراك للمستخدم {user_id}: {str(e)}")
            return {}

    def get_subscription_stats(self) -> dict:
        """الحصول على إحصائيات الاشتراكات"""
        try:
            if not self.db:
                return {}

            # جمع إحصائيات من Firestore
            subscriptions_ref = self.db.collection('subscriptions')
            all_subscriptions = subscriptions_ref.get()

            stats = {
                'total_users': 0,
                'active_subscriptions': 0,
                'expired_subscriptions': 0,
                'free_users': 0
            }

            current_time = datetime.now()

            for doc in all_subscriptions:
                stats['total_users'] += 1
                subscription = doc.to_dict()

                is_active = subscription.get('is_active', False)
                expiry = subscription.get('expiry')

                if expiry and is_active:
                    try:
                        if isinstance(expiry, str):
                            expiry_date = datetime.fromisoformat(expiry.replace('Z', '+00:00'))
                        else:
                            expiry_date = expiry

                        if expiry_date > current_time:
                            stats['active_subscriptions'] += 1
                        else:
                            stats['expired_subscriptions'] += 1
                    except:
                        stats['expired_subscriptions'] += 1
                else:
                    stats['free_users'] += 1

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الاشتراكات: {str(e)}")
            return {}


# دالة مساعدة للحصول على كائن النظام (Singleton Pattern)
_subscription_system_instance = None

def get_subscription_system(db=None, free_day_system=None, api_manager=None):
    """الحصول على كائن نظام الاشتراكات (Singleton)"""
    global _subscription_system_instance
    if _subscription_system_instance is None:
        _subscription_system_instance = SubscriptionSystem(db, free_day_system, api_manager)
    else:
        # تحديث التبعيات إذا تم تمريرها
        if db is not None:
            _subscription_system_instance.db = db
        if free_day_system is not None:
            _subscription_system_instance.free_day_system = free_day_system
        if api_manager is not None:
            _subscription_system_instance.api_manager = api_manager
    return _subscription_system_instance

def initialize_subscription_system(db, free_day_system=None, api_manager=None):
    """تهيئة نظام الاشتراكات"""
    global _subscription_system_instance
    _subscription_system_instance = SubscriptionSystem(db, free_day_system, api_manager)
    logger.info("✅ تم تهيئة نظام الاشتراكات بنجاح")
    return _subscription_system_instance

def check_subscription_sync(user_id: str) -> bool:
    """دالة مساعدة للتحقق من الاشتراك بطريقة متزامنة"""
    global _subscription_system_instance
    if _subscription_system_instance:
        return _subscription_system_instance.is_subscribed_sync(user_id)
    return False
