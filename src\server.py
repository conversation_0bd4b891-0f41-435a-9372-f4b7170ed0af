"""
ملف خادم بسيط للاستماع على المنفذ المحدد من قبل Koyeb
"""

import os
import logging
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import time

# إعداد السجلات
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

class HealthCheckHandler(BaseHTTPRequestHandler):
    """معالج طلبات فحص الصحة"""
    
    def do_GET(self):
        """معالجة طلبات GET"""
        if self.path == '/health' or self.path == '/':
            self.send_response(200)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'Bot is running')
        else:
            self.send_response(404)
            self.send_header('Content-type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'Not Found')
    
    def log_message(self, format, *args):
        """تعطيل سجلات الخادم"""
        return

def start_health_server():
    """بدء خادم فحص الصحة"""
    port = int(os.environ.get('PORT', 8000))
    server = HTTPServer(('0.0.0.0', port), HealthCheckHandler)
    logger.info(f"🌐 بدء خادم فحص الصحة على المنفذ {port}")
    server.serve_forever()

def run_health_server():
    """تشغيل خادم فحص الصحة في خيط منفصل"""
    server_thread = threading.Thread(target=start_health_server)
    server_thread.daemon = True
    server_thread.start()
    logger.info("✅ تم بدء خادم فحص الصحة في الخلفية")
    return server_thread
