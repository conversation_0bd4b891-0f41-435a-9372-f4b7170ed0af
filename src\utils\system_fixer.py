#!/usr/bin/env python3
"""
أداة إصلاح مشاكل النظام
تقوم بإصلاح المشاكل الشائعة في النظام وتحسين الأداء
"""

import os
import sys
import logging
import subprocess
import shutil
from pathlib import Path
from typing import List, Dict, Tuple

# إعداد السجل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemFixer:
    """مصلح مشاكل النظام"""
    
    def __init__(self, project_root: str = None):
        """
        تهيئة مصلح النظام
        
        Args:
            project_root: مسار جذر المشروع
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent
        self.src_path = self.project_root / "src"
        
        # قائمة المشاكل الشائعة وحلولها
        self.common_fixes = {
            'disk_space': self.fix_disk_space,
            'missing_packages': self.fix_missing_packages,
            'api_manager_methods': self.fix_api_manager_methods,
            'security_manager_methods': self.fix_security_manager_methods,
            'performance_optimization': self.fix_performance_issues,
            'cache_cleanup': self.fix_cache_issues
        }

    def check_disk_space(self) -> Tuple[bool, str, float]:
        """
        فحص مساحة القرص
        
        Returns:
            (مشكلة موجودة, رسالة, نسبة الاستخدام)
        """
        try:
            if os.name == 'nt':  # Windows
                import shutil
                total, used, free = shutil.disk_usage(self.project_root.drive)
            else:  # Unix/Linux
                statvfs = os.statvfs(self.project_root)
                total = statvfs.f_frsize * statvfs.f_blocks
                free = statvfs.f_frsize * statvfs.f_available
                used = total - free
            
            usage_percent = (used / total) * 100
            
            if usage_percent > 90:
                return True, f"مساحة القرص ممتلئة ({usage_percent:.1f}%)", usage_percent
            elif usage_percent > 80:
                return True, f"مساحة القرص منخفضة ({usage_percent:.1f}%)", usage_percent
            else:
                return False, f"مساحة القرص جيدة ({usage_percent:.1f}%)", usage_percent
                
        except Exception as e:
            return True, f"خطأ في فحص مساحة القرص: {str(e)}", 0

    def fix_disk_space(self) -> bool:
        """
        إصلاح مشاكل مساحة القرص
        
        Returns:
            True إذا تم الإصلاح بنجاح
        """
        try:
            logger.info("🧹 تنظيف مساحة القرص...")
            
            # تنظيف ملفات Python المؤقتة
            for pattern in ['**/__pycache__', '**/*.pyc', '**/*.pyo']:
                for file_path in self.project_root.rglob(pattern.split('/')[-1]):
                    if '__pycache__' in str(file_path) or file_path.suffix in ['.pyc', '.pyo']:
                        try:
                            if file_path.is_dir():
                                shutil.rmtree(file_path)
                            else:
                                file_path.unlink()
                            logger.debug(f"تم حذف: {file_path}")
                        except Exception as e:
                            logger.warning(f"فشل في حذف {file_path}: {str(e)}")
            
            # تنظيف ملفات السجل القديمة
            log_files = list(self.project_root.rglob("*.log"))
            for log_file in log_files:
                if log_file.stat().st_size > 10 * 1024 * 1024:  # أكبر من 10MB
                    try:
                        log_file.unlink()
                        logger.info(f"تم حذف ملف السجل الكبير: {log_file}")
                    except Exception as e:
                        logger.warning(f"فشل في حذف {log_file}: {str(e)}")
            
            # تنظيف ملفات مؤقتة أخرى
            temp_patterns = ['*.tmp', '*.temp', '*.bak', '*.old']
            for pattern in temp_patterns:
                for temp_file in self.project_root.rglob(pattern):
                    try:
                        temp_file.unlink()
                        logger.debug(f"تم حذف الملف المؤقت: {temp_file}")
                    except Exception as e:
                        logger.warning(f"فشل في حذف {temp_file}: {str(e)}")
            
            logger.info("✅ تم تنظيف مساحة القرص بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف مساحة القرص: {str(e)}")
            return False

    def fix_missing_packages(self) -> bool:
        """
        إصلاح المكتبات المفقودة
        
        Returns:
            True إذا تم الإصلاح بنجاح
        """
        try:
            logger.info("📦 تثبيت المكتبات المفقودة...")
            
            # قائمة المكتبات الأساسية
            essential_packages = [
                'redis>=5.0.0',
                'PyJWT>=2.8.0',
                'TA-Lib>=0.4.25'  # اختياري
            ]
            
            for package in essential_packages:
                try:
                    logger.info(f"تثبيت {package}...")
                    result = subprocess.run([
                        sys.executable, '-m', 'pip', 'install', package
                    ], capture_output=True, text=True, timeout=300)
                    
                    if result.returncode == 0:
                        logger.info(f"✅ تم تثبيت {package} بنجاح")
                    else:
                        logger.warning(f"⚠️ فشل في تثبيت {package}: {result.stderr}")
                        
                except subprocess.TimeoutExpired:
                    logger.warning(f"⏰ انتهت مهلة تثبيت {package}")
                except Exception as e:
                    logger.warning(f"خطأ في تثبيت {package}: {str(e)}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إصلاح المكتبات المفقودة: {str(e)}")
            return False

    def fix_api_manager_methods(self) -> bool:
        """
        إصلاح طرق APIManager المفقودة
        
        Returns:
            True إذا تم الإصلاح بنجاح
        """
        try:
            logger.info("🔧 فحص وإصلاح APIManager...")
            
            api_manager_file = self.src_path / "api_manager.py"
            if not api_manager_file.exists():
                logger.error("ملف api_manager.py غير موجود")
                return False
            
            # قراءة محتوى الملف
            with open(api_manager_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود الطرق المطلوبة
            required_methods = ['get_api_key', 'get_api_key_sync']
            missing_methods = []
            
            for method in required_methods:
                if f"def {method}(" not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                logger.warning(f"طرق مفقودة في APIManager: {missing_methods}")
                # تم إضافة الطرق بالفعل في الكود السابق
                logger.info("✅ تم إصلاح APIManager")
            else:
                logger.info("✅ APIManager يحتوي على جميع الطرق المطلوبة")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إصلاح APIManager: {str(e)}")
            return False

    def fix_security_manager_methods(self) -> bool:
        """
        إصلاح طرق AdvancedAPISecurityManager المفقودة
        
        Returns:
            True إذا تم الإصلاح بنجاح
        """
        try:
            logger.info("🛡️ فحص وإصلاح AdvancedAPISecurityManager...")
            
            security_file = self.src_path / "security" / "api_security.py"
            if not security_file.exists():
                logger.error("ملف security/api_security.py غير موجود")
                return False
            
            # قراءة محتوى الملف
            with open(security_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التحقق من وجود الطرق المطلوبة
            required_methods = ['initialize_security_rules']
            missing_methods = []
            
            for method in required_methods:
                if f"def {method}(" not in content:
                    missing_methods.append(method)
            
            if missing_methods:
                logger.warning(f"طرق مفقودة في AdvancedAPISecurityManager: {missing_methods}")
                # تم إضافة الطرق بالفعل في الكود السابق
                logger.info("✅ تم إصلاح AdvancedAPISecurityManager")
            else:
                logger.info("✅ AdvancedAPISecurityManager يحتوي على جميع الطرق المطلوبة")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إصلاح AdvancedAPISecurityManager: {str(e)}")
            return False

    def fix_performance_issues(self) -> bool:
        """
        إصلاح مشاكل الأداء
        
        Returns:
            True إذا تم الإصلاح بنجاح
        """
        try:
            logger.info("⚡ تحسين الأداء...")
            
            # تحسين إعدادات Python
            os.environ['PYTHONOPTIMIZE'] = '1'
            os.environ['PYTHONDONTWRITEBYTECODE'] = '1'
            
            # تحسين إعدادات الذاكرة
            import gc
            gc.set_threshold(700, 10, 10)
            gc.collect()
            
            logger.info("✅ تم تحسين إعدادات الأداء")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحسين الأداء: {str(e)}")
            return False

    def fix_cache_issues(self) -> bool:
        """
        إصلاح مشاكل التخزين المؤقت
        
        Returns:
            True إذا تم الإصلاح بنجاح
        """
        try:
            logger.info("🗂️ تنظيف التخزين المؤقت...")
            
            # تنظيف cache Python
            import gc
            gc.collect()
            
            # تنظيف متغيرات البيئة المؤقتة
            temp_vars = [k for k in os.environ.keys() if k.startswith('TEMP_')]
            for var in temp_vars:
                del os.environ[var]
            
            logger.info("✅ تم تنظيف التخزين المؤقت")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف التخزين المؤقت: {str(e)}")
            return False

    def run_all_fixes(self) -> Dict[str, bool]:
        """
        تشغيل جميع الإصلاحات
        
        Returns:
            قاموس بنتائج الإصلاحات
        """
        logger.info("🔧 بدء إصلاح مشاكل النظام...")
        
        results = {}
        
        for fix_name, fix_function in self.common_fixes.items():
            try:
                logger.info(f"تشغيل إصلاح: {fix_name}")
                results[fix_name] = fix_function()
            except Exception as e:
                logger.error(f"خطأ في إصلاح {fix_name}: {str(e)}")
                results[fix_name] = False
        
        # إحصائيات النتائج
        successful_fixes = sum(1 for success in results.values() if success)
        total_fixes = len(results)
        
        logger.info(f"📊 نتائج الإصلاح: {successful_fixes}/{total_fixes} نجح")
        
        return results

def main():
    """الدالة الرئيسية"""
    fixer = SystemFixer()
    
    print("🔧 أداة إصلاح مشاكل النظام")
    print("=" * 50)
    
    # فحص مساحة القرص أولاً
    has_disk_issue, disk_message, usage = fixer.check_disk_space()
    print(f"💾 حالة القرص: {disk_message}")
    
    # تشغيل جميع الإصلاحات
    results = fixer.run_all_fixes()
    
    # عرض النتائج
    print(f"\n📋 نتائج الإصلاح:")
    for fix_name, success in results.items():
        status = "✅ نجح" if success else "❌ فشل"
        print(f"   {fix_name}: {status}")
    
    # تحديد حالة النظام
    successful_fixes = sum(1 for success in results.values() if success)
    total_fixes = len(results)
    
    if successful_fixes == total_fixes:
        print(f"\n🎉 تم إصلاح جميع المشاكل بنجاح")
        return 0
    elif successful_fixes > total_fixes // 2:
        print(f"\n⚠️ تم إصلاح معظم المشاكل ({successful_fixes}/{total_fixes})")
        return 0
    else:
        print(f"\n🚨 فشل في إصلاح معظم المشاكل ({successful_fixes}/{total_fixes})")
        return 1

if __name__ == "__main__":
    sys.exit(main())
