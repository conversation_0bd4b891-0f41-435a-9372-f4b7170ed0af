# handlers/__init__.py
"""
معالجات واجهة المستخدم - TradingTelegram
تحتوي على جميع معالجات القوائم والإعدادات المنقولة من main.py
"""

# استيراد معالجات القوائم
from .menu_handlers import (
    show_language_selection,
    show_terms_and_conditions,
    show_enhanced_settings,
    show_trading_style_options,
    show_analysis_type_options,
    show_enhanced_analysis_explanation,
    show_analysis_comparison,
    show_upgrade_info
)

# استيراد معالجات الإعدادات
from .settings_handlers import (
    set_trading_style,
    set_analysis_type,
    reset_enhanced_settings,
    set_language
)

__all__ = [
    # معالجات القوائم
    'show_language_selection',
    'show_terms_and_conditions', 
    'show_enhanced_settings',
    'show_trading_style_options',
    'show_analysis_type_options',
    'show_enhanced_analysis_explanation',
    'show_analysis_comparison',
    'show_upgrade_info',
    
    # معالجات الإعدادات
    'set_trading_style',
    'set_analysis_type',
    'reset_enhanced_settings',
    'set_language'
]
