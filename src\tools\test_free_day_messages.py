"""
اختبار رسائل اليوم المجاني للتأكد من عدم وجود مشاكل ترميز
"""

import sys
import os

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_translation_messages():
    """اختبار رسائل الترجمة"""
    print("🧪 اختبار رسائل اليوم المجاني من ملف الترجمات")
    print("="*60)
    
    try:
        from localization.translations import get_text
        
        # اختبار الرسائل العربية
        print("📝 الرسائل العربية:")
        print("-" * 30)
        
        messages_ar = [
            'free_day_available',
            'free_day_granted', 
            'free_day_reminder',
            'free_day_ended',
            'temp_free_day_ended'
        ]
        
        for msg_key in messages_ar:
            try:
                if msg_key in ['free_day_available', 'free_day_reminder']:
                    message = get_text(msg_key, 'ar', day_name='الاثنين', days_until=3)
                else:
                    message = get_text(msg_key, 'ar')
                
                print(f"✅ {msg_key}:")
                print(f"   {message}")
                print()
                
                # فحص وجود رموز معطوبة
                if '��' in message or '�' in message:
                    print(f"❌ تم العثور على رموز معطوبة في {msg_key}")
                
            except Exception as e:
                print(f"❌ خطأ في {msg_key}: {str(e)}")
        
        # اختبار الرسائل الإنجليزية
        print("\n📝 الرسائل الإنجليزية:")
        print("-" * 30)
        
        for msg_key in messages_ar:
            try:
                if msg_key in ['free_day_available', 'free_day_reminder']:
                    message = get_text(msg_key, 'en', day_name='Monday', days_until=3)
                else:
                    message = get_text(msg_key, 'en')
                
                print(f"✅ {msg_key}:")
                print(f"   {message}")
                print()
                
                # فحص وجود رموز معطوبة
                if '��' in message or '�' in message:
                    print(f"❌ تم العثور على رموز معطوبة في {msg_key}")
                
            except Exception as e:
                print(f"❌ خطأ في {msg_key}: {str(e)}")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار رسائل الترجمة: {str(e)}")

def test_free_day_system_messages():
    """اختبار رسائل نظام اليوم المجاني"""
    print("\n🧪 اختبار رسائل نظام اليوم المجاني المباشرة")
    print("="*60)
    
    try:
        # رسائل مباشرة من النظام (النصوص الاحتياطية)
        messages = {
            'temp_free_day_ended_ar': "⏰ *انتهى اليوم المجاني المؤقت*\n\nانتهت فترة اليوم المجاني الممنوح لك.\n\nيمكنك الاشتراك للحصول على جميع الميزات بشكل دائم، أو انتظار يومك المجاني الأسبوعي يوم الاثنين.",
            'temp_free_day_ended_en': "⏰ *Temporary Free Day Ended*\n\nYour granted free day period has ended.\n\nYou can subscribe to get all features permanently, or wait for your weekly free day on Monday.",
            'free_day_granted_ar': "🎁 *تم منحك يوم مجاني!*\n\nيمكنك الآن الاستمتاع بجميع الميزات المدفوعة مجانًا لمدة 24 ساعة.\n\nاستفد من هذه الفرصة لتجربة جميع الميزات المتقدمة!",
            'free_day_granted_en': "🎁 *You've Been Granted a Free Day!*\n\nYou can now enjoy all premium features for free for 24 hours.\n\nTake advantage of this opportunity to try all advanced features!"
        }
        
        for msg_key, message in messages.items():
            print(f"✅ {msg_key}:")
            print(f"   {message}")
            print()
            
            # فحص وجود رموز معطوبة
            if '��' in message or '�' in message:
                print(f"❌ تم العثور على رموز معطوبة في {msg_key}")
            else:
                print(f"✅ لا توجد رموز معطوبة في {msg_key}")
            print()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار رسائل النظام: {str(e)}")

def test_emoji_display():
    """اختبار عرض الرموز التعبيرية"""
    print("\n🧪 اختبار عرض الرموز التعبيرية")
    print("="*60)
    
    emojis_to_test = [
        ('🎁', 'رمز الهدية'),
        ('⏰', 'رمز الساعة'),
        ('🔔', 'رمز الجرس'),
        ('✅', 'رمز الصح'),
        ('❌', 'رمز الخطأ'),
        ('📊', 'رمز الإحصائيات'),
        ('🚀', 'رمز الصاروخ'),
        ('💡', 'رمز المصباح'),
        ('🔧', 'رمز المفتاح'),
        ('🌟', 'رمز النجمة'),
        ('💰', 'رمز المال'),
        ('📈', 'رمز الرسم البياني'),
        ('🔍', 'رمز العدسة المكبرة')
    ]
    
    for emoji, description in emojis_to_test:
        print(f"{emoji} - {description}")
        
        # فحص إذا كان الرمز يظهر كرموز معطوبة
        if emoji in ['��', '�']:
            print(f"❌ الرمز معطوب: {emoji}")
        else:
            print(f"✅ الرمز يظهر بشكل صحيح")
    
    print("\n" + "="*60)

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار رسائل اليوم المجاني")
    
    # اختبار رسائل الترجمة
    test_translation_messages()
    
    # اختبار رسائل النظام المباشرة
    test_free_day_system_messages()
    
    # اختبار عرض الرموز التعبيرية
    test_emoji_display()
    
    print("\n🎉 تم إكمال جميع الاختبارات!")
    print("\n💡 إذا رأيت رموز معطوبة (��)، شغل أداة إصلاح الترميز:")
    print("   python fix_encoding_issues.py")

if __name__ == "__main__":
    main()
