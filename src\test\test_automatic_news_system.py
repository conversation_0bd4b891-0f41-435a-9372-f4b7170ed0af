"""
اختبار شامل لنظام الأخبار التلقائي
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logger = logging.getLogger(__name__)

class AutomaticNewsSystemTester:
    """كلاس اختبار نظام الأخبار التلقائي"""
    
    def __init__(self):
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': []
        }
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبارات نظام الأخبار التلقائي الشامل...")
        print("=" * 60)
        
        # اختبار نظام إدارة معدل الطلبات
        await self.test_smart_rate_limiter()
        
        # اختبار نظام التخزين المؤقت
        await self.test_intelligent_cache()
        
        # اختبار نظام الجدولة
        await self.test_automatic_scheduler()
        
        # اختبار نظام الإشعارات
        await self.test_notifications_system()
        
        # اختبار التكامل الشامل
        await self.test_system_integration()
        
        # عرض النتائج النهائية
        self.display_final_results()
    
    async def test_smart_rate_limiter(self):
        """اختبار نظام إدارة معدل الطلبات الذكي"""
        print("\n📊 اختبار نظام إدارة معدل الطلبات الذكي...")
        
        try:
            from services.smart_rate_limiter import SmartRateLimiter
            
            # إنشاء نسخة للاختبار
            rate_limiter = SmartRateLimiter()
            
            # اختبار 1: التحقق من إمكانية الطلب
            can_request, reason = await rate_limiter.can_make_request('binance', 'normal')
            self.add_test_result("Rate Limiter - Can Make Request", can_request, "يجب أن يسمح بالطلب الأول")
            
            # اختبار 2: تسجيل طلب
            await rate_limiter.record_request('binance', '/api/v3/ticker/price', True, 150.0)
            self.add_test_result("Rate Limiter - Record Request", True, "تسجيل الطلب نجح")
            
            # اختبار 3: الحصول على حالة المنصة
            platform_status = await rate_limiter.get_platform_status('binance')
            has_status = platform_status and 'platform' in platform_status
            self.add_test_result("Rate Limiter - Platform Status", has_status, "الحصول على حالة المنصة")
            
            # اختبار 4: تحليلات الاستخدام
            analytics = await rate_limiter.get_usage_analytics('binance', days=1)
            has_analytics = isinstance(analytics, dict)
            self.add_test_result("Rate Limiter - Usage Analytics", has_analytics, "تحليلات الاستخدام")
            
            print("✅ اختبارات نظام إدارة معدل الطلبات مكتملة")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار نظام إدارة معدل الطلبات: {str(e)}")
            self.add_test_result("Rate Limiter - System Error", False, str(e))
    
    async def test_intelligent_cache(self):
        """اختبار نظام التخزين المؤقت الذكي"""
        print("\n💾 اختبار نظام التخزين المؤقت الذكي...")
        
        try:
            from services.intelligent_news_cache import IntelligentNewsCache
            
            # إنشاء نسخة للاختبار
            cache = IntelligentNewsCache(max_size_mb=10)
            
            # اختبار 1: حفظ البيانات
            test_data = {"title": "اختبار", "content": "محتوى اختبار", "source": "test"}
            success = await cache.set("test_key", test_data, source="test")
            self.add_test_result("Cache - Set Data", success, "حفظ البيانات في التخزين المؤقت")
            
            # اختبار 2: استرجاع البيانات
            retrieved_data = await cache.get("test_key")
            data_matches = retrieved_data == test_data
            self.add_test_result("Cache - Get Data", data_matches, "استرجاع البيانات من التخزين المؤقت")
            
            # اختبار 3: البحث حسب المصدر
            source_data = await cache.get_by_source("test", limit=5)
            has_source_data = len(source_data) > 0
            self.add_test_result("Cache - Get by Source", has_source_data, "البحث حسب المصدر")
            
            # اختبار 4: الإحصائيات
            stats = await cache.get_cache_stats()
            has_stats = isinstance(stats, dict) and 'total_entries' in stats
            self.add_test_result("Cache - Statistics", has_stats, "إحصائيات التخزين المؤقت")
            
            # اختبار 5: تقييم الصحة
            health = await cache.get_cache_health()
            has_health = isinstance(health, dict) and 'health_score' in health
            self.add_test_result("Cache - Health Check", has_health, "تقييم صحة التخزين المؤقت")
            
            print("✅ اختبارات نظام التخزين المؤقت مكتملة")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار نظام التخزين المؤقت: {str(e)}")
            self.add_test_result("Cache - System Error", False, str(e))
    
    async def test_automatic_scheduler(self):
        """اختبار نظام الجدولة التلقائية"""
        print("\n⏰ اختبار نظام الجدولة التلقائية...")
        
        try:
            from services.automatic_news_scheduler import AutomaticNewsScheduler
            
            # إنشاء نسخة للاختبار
            scheduler = AutomaticNewsScheduler()
            
            # اختبار 1: إضافة مشترك
            scheduler.add_subscriber("test_user_123")
            has_subscriber = "test_user_123" in scheduler.subscribers
            self.add_test_result("Scheduler - Add Subscriber", has_subscriber, "إضافة مشترك")
            
            # اختبار 2: إزالة مشترك
            scheduler.remove_subscriber("test_user_123")
            subscriber_removed = "test_user_123" not in scheduler.subscribers
            self.add_test_result("Scheduler - Remove Subscriber", subscriber_removed, "إزالة مشترك")
            
            # اختبار 3: الحصول على حالة المجدول
            status = scheduler.get_scheduler_status()
            has_status = isinstance(status, dict) and 'status' in status
            self.add_test_result("Scheduler - Get Status", has_status, "حالة المجدول")
            
            # اختبار 4: مدير معدل الطلبات
            rate_manager = scheduler.rate_manager
            can_request = rate_manager.can_make_request('binance')
            self.add_test_result("Scheduler - Rate Manager", isinstance(can_request, bool), "مدير معدل الطلبات")
            
            print("✅ اختبارات نظام الجدولة مكتملة")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار نظام الجدولة: {str(e)}")
            self.add_test_result("Scheduler - System Error", False, str(e))
    
    async def test_notifications_system(self):
        """اختبار نظام الإشعارات التلقائية"""
        print("\n🔔 اختبار نظام الإشعارات التلقائية...")
        
        try:
            from services.automatic_news_notifications import AutomaticNewsNotifications, NotificationType, NotificationPriority
            
            # إنشاء نسخة للاختبار
            notifications = AutomaticNewsNotifications()
            
            # اختبار 1: إضافة قاعدة إشعار
            success = await notifications.add_user_rule(
                user_id="test_user_456",
                notification_type=NotificationType.BREAKING_NEWS,
                min_priority=NotificationPriority.HIGH
            )
            self.add_test_result("Notifications - Add Rule", success, "إضافة قاعدة إشعار")
            
            # اختبار 2: التحقق من وجود القاعدة
            has_rules = "test_user_456" in notifications.user_rules
            self.add_test_result("Notifications - Rule Exists", has_rules, "وجود قاعدة الإشعار")
            
            # اختبار 3: إزالة قاعدة إشعار
            success = await notifications.remove_user_rule(
                user_id="test_user_456",
                notification_type=NotificationType.BREAKING_NEWS
            )
            self.add_test_result("Notifications - Remove Rule", success, "إزالة قاعدة إشعار")
            
            # اختبار 4: الإحصائيات
            stats = await notifications.get_notification_stats()
            has_stats = isinstance(stats, dict)
            self.add_test_result("Notifications - Statistics", has_stats, "إحصائيات الإشعارات")
            
            print("✅ اختبارات نظام الإشعارات مكتملة")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار نظام الإشعارات: {str(e)}")
            self.add_test_result("Notifications - System Error", False, str(e))
    
    async def test_system_integration(self):
        """اختبار التكامل الشامل للنظام"""
        print("\n🔗 اختبار التكامل الشامل للنظام...")
        
        try:
            from services.automatic_news_integration import AutomaticNewsIntegration
            
            # إنشاء نسخة للاختبار
            integration = AutomaticNewsIntegration()
            
            # اختبار 1: تهيئة الأنظمة
            init_success = await integration.initialize_all_systems()
            self.add_test_result("Integration - Initialize Systems", init_success, "تهيئة الأنظمة")
            
            # اختبار 2: حالة التكامل
            status = integration.get_integration_status()
            has_status = isinstance(status, dict) and 'initialized' in status
            self.add_test_result("Integration - Status Check", has_status, "حالة التكامل")
            
            # اختبار 3: صحة النظام
            health = await integration.get_system_health()
            has_health = isinstance(health, dict) and 'overall_status' in health
            self.add_test_result("Integration - Health Check", has_health, "صحة النظام")
            
            print("✅ اختبارات التكامل الشامل مكتملة")
            
        except Exception as e:
            print(f"❌ خطأ في اختبار التكامل الشامل: {str(e)}")
            self.add_test_result("Integration - System Error", False, str(e))
    
    def add_test_result(self, test_name: str, passed: bool, description: str):
        """إضافة نتيجة اختبار"""
        self.test_results['total_tests'] += 1
        
        if passed:
            self.test_results['passed_tests'] += 1
            status = "✅ نجح"
        else:
            self.test_results['failed_tests'] += 1
            status = "❌ فشل"
        
        self.test_results['test_details'].append({
            'name': test_name,
            'status': status,
            'description': description,
            'passed': passed
        })
        
        print(f"  {status} - {test_name}: {description}")
    
    def display_final_results(self):
        """عرض النتائج النهائية"""
        print("\n" + "=" * 60)
        print("📊 نتائج الاختبارات النهائية")
        print("=" * 60)
        
        total = self.test_results['total_tests']
        passed = self.test_results['passed_tests']
        failed = self.test_results['failed_tests']
        success_rate = (passed / total * 100) if total > 0 else 0
        
        print(f"إجمالي الاختبارات: {total}")
        print(f"الاختبارات الناجحة: {passed}")
        print(f"الاختبارات الفاشلة: {failed}")
        print(f"معدل النجاح: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("\n🎉 النظام يعمل بشكل ممتاز!")
        elif success_rate >= 75:
            print("\n👍 النظام يعمل بشكل جيد")
        elif success_rate >= 50:
            print("\n⚠️ النظام يحتاج إلى تحسينات")
        else:
            print("\n🚨 النظام يحتاج إلى إصلاحات جوهرية")
        
        # عرض الاختبارات الفاشلة
        failed_tests = [test for test in self.test_results['test_details'] if not test['passed']]
        if failed_tests:
            print("\n❌ الاختبارات الفاشلة:")
            for test in failed_tests:
                print(f"  - {test['name']}: {test['description']}")

async def main():
    """الدالة الرئيسية للاختبار"""
    # إعداد التسجيل
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # تشغيل الاختبارات
    tester = AutomaticNewsSystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
