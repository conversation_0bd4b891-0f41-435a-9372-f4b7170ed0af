# 🔧 دليل صيانة النظام - System Maintenance Guide

## 📋 نظرة عامة

يحتوي هذا الدليل على جميع الأدوات والسكريبتات المطلوبة لصيانة وإصلاح النظام بشكل شامل.

---

## 🚀 الإصلاح السريع - Quick Fix

### تشغيل الإصلاح الشامل (موصى به):
```bash
python fix_system.py
```

هذا السكريبت سيقوم بـ:
- ✅ فحص وتثبيت المكتبات المفقودة
- ✅ تحديث المكتبات الموجودة
- ✅ إصلاح مشاكل النظام
- ✅ تنظيف الملفات المؤقتة
- ✅ اختبار النظام بعد الإصلاح

---

## 🛠️ الأدوات المتخصصة

### 1. 🔍 فاحص التبعيات - Dependency Checker

**الملف:** `src/utils/dependency_checker.py`

**الوظائف:**
- فحص جميع الاستيرادات في المشروع
- تحديد المكتبات المفقودة والمتوفرة
- تصنيف المكتبات (حرجة، اختيارية، مدمجة)
- اقتراح أوامر التثبيت

**الاستخدام:**
```bash
cd src
python utils/dependency_checker.py
```

**مثال على المخرجات:**
```
🔍 فحص التبعيات والمكتبات
==================================================

📊 ملخص النتائج:
   إجمالي الاستيرادات: 45
   المكتبات المتوفرة: 38
   المكتبات المفقودة: 7
   معدل النجاح: 84.4%

❌ مكتبات حرجة مفقودة (2):
   - redis
   - jwt

🔧 أوامر التثبيت المطلوبة:
   pip install redis>=5.0.0
   pip install PyJWT>=2.8.0
```

### 2. 🔧 مصلح النظام - System Fixer

**الملف:** `src/utils/system_fixer.py`

**الوظائف:**
- تنظيف مساحة القرص
- تثبيت المكتبات المفقودة
- إصلاح طرق الكلاسات المفقودة
- تحسين إعدادات الأداء
- تنظيف التخزين المؤقت

**الاستخدام:**
```bash
cd src
python utils/system_fixer.py
```

**الإصلاحات المتاحة:**
- `disk_space`: تنظيف مساحة القرص
- `missing_packages`: تثبيت المكتبات المفقودة
- `api_manager_methods`: إصلاح APIManager
- `security_manager_methods`: إصلاح SecurityManager
- `performance_optimization`: تحسين الأداء
- `cache_cleanup`: تنظيف التخزين المؤقت

### 3. 🚀 الإصلاح الشامل - Complete System Fix

**الملف:** `fix_system.py`

**الوظائف:**
- تنفيذ جميع خطوات الإصلاح بالتسلسل
- تقرير مفصل عن كل خطوة
- اختبار النظام بعد الإصلاح
- حفظ سجل مفصل

**الاستخدام:**
```bash
python fix_system.py
```

**خطوات الإصلاح:**
1. تثبيت المكتبات المفقودة
2. تحديث المكتبات الموجودة
3. فحص التبعيات
4. إصلاح مشاكل النظام
5. تنظيف النظام
6. اختبار النظام

---

## 📊 تشخيص المشاكل الشائعة

### 1. مشكلة APIManager
**الخطأ:** `'APIManager' object has no attribute 'get_api_key'`

**الحل:**
```bash
python src/utils/system_fixer.py
# أو
python fix_system.py
```

### 2. مشكلة SecurityManager
**الخطأ:** `'AdvancedAPISecurityManager' object has no attribute 'initialize_security_rules'`

**الحل:**
```bash
python src/utils/system_fixer.py
# أو
python fix_system.py
```

### 3. مكتبات مفقودة
**الخطأ:** `ModuleNotFoundError: No module named 'redis'`

**الحل:**
```bash
python src/utils/dependency_checker.py
# ثم تنفيذ الأوامر المقترحة
```

### 4. مساحة القرص ممتلئة
**الخطأ:** `disk usage = 90.2%`

**الحل:**
```bash
python src/utils/system_fixer.py
# سيقوم بتنظيف الملفات المؤقتة تلقائياً
```

### 5. مشكلة aioredis
**الخطأ:** `TypeError: duplicate base class TimeoutError`

**الحل:**
```bash
pip uninstall aioredis -y
pip install redis>=5.0.0
```

---

## 📝 سجلات النظام

### ملفات السجل:
- `system_fix.log`: سجل الإصلاح الشامل
- `dependency_check.log`: سجل فحص التبعيات
- `system_fixer.log`: سجل إصلاح النظام

### مستويات السجل:
- `INFO`: معلومات عامة
- `WARNING`: تحذيرات
- `ERROR`: أخطاء
- `DEBUG`: تفاصيل للمطورين

---

## ⚡ نصائح الأداء

### 1. تشغيل دوري للصيانة:
```bash
# أسبوعياً
python fix_system.py

# يومياً (فحص سريع)
python src/utils/dependency_checker.py
```

### 2. مراقبة مساحة القرص:
```bash
# فحص مساحة القرص
python -c "from src.utils.system_fixer import SystemFixer; fixer = SystemFixer(); print(fixer.check_disk_space())"
```

### 3. تحديث المكتبات:
```bash
# تحديث جميع المكتبات
pip install --upgrade -r src/requirements.txt
```

---

## 🔒 الأمان والنسخ الاحتياطي

### قبل تشغيل الإصلاحات:
1. **إنشاء نسخة احتياطية** من المشروع
2. **التأكد من حفظ** جميع التغييرات
3. **اختبار النظام** في بيئة تطوير أولاً

### بعد الإصلاحات:
1. **اختبار جميع الوظائف** الأساسية
2. **مراجعة السجلات** للتأكد من عدم وجود أخطاء
3. **تشغيل البوت** والتأكد من عمله بشكل صحيح

---

## 🆘 الدعم والمساعدة

### في حالة فشل الإصلاحات:
1. **مراجعة ملفات السجل** للتفاصيل
2. **تشغيل الأدوات منفردة** لتحديد المشكلة
3. **التحقق من متطلبات النظام** والأذونات
4. **إعادة تثبيت المكتبات** يدوياً إذا لزم الأمر

### أوامر الطوارئ:
```bash
# إعادة تثبيت جميع المكتبات
pip uninstall -r src/requirements.txt -y
pip install -r src/requirements.txt

# تنظيف شامل
find . -name "__pycache__" -type d -exec rm -rf {} +
find . -name "*.pyc" -delete

# اختبار الاستيرادات الأساسية
python -c "import telegram, firebase_admin, cryptography; print('✅ المكتبات الأساسية متوفرة')"
```

---

## 📈 مؤشرات الأداء

### معايير النجاح:
- **معدل توفر المكتبات**: > 90%
- **استخدام القرص**: < 80%
- **زمن بدء التشغيل**: < 60 ثانية
- **معدل نجاح الإصلاحات**: > 80%

### مراقبة مستمرة:
```bash
# فحص سريع للنظام
python -c "
from src.utils.dependency_checker import DependencyChecker
from src.utils.system_fixer import SystemFixer

checker = DependencyChecker()
fixer = SystemFixer()

# فحص التبعيات
report = checker.run_full_check()
print(f'معدل توفر المكتبات: {report[\"summary\"][\"success_rate\"]}')

# فحص مساحة القرص
has_issue, message, usage = fixer.check_disk_space()
print(f'استخدام القرص: {usage:.1f}%')
"
```
