"""
اختبارات نظام اللغة المحسن - نسخة مبسطة
يختبر منطق نظام تحديد وحفظ لغة المستخدم
"""

import pytest
import asyncio

class MockLanguageSystem:
    """نسخة وهمية من نظام اللغة للاختبار"""
    
    def __init__(self):
        self._mock_subscription_data = {}
        self._mock_settings_data = {}
        self._mock_telegram_langs = {}
    
    async def _get_user_language(self, user_id: str) -> str:
        """محاكاة دالة الحصول على لغة المستخدم"""
        if not user_id:
            return 'ar'
        
        # البحث في نظام الاشتراكات
        subscription_lang = self._mock_subscription_data.get(user_id, {}).get('lang')
        if subscription_lang and subscription_lang in ['ar', 'en']:
            return subscription_lang
        
        # البحث في user_settings
        settings_lang = self._mock_settings_data.get(user_id, {}).get('lang')
        if settings_lang and settings_lang in ['ar', 'en']:
            return settings_lang
        
        return 'ar'  # افتراضي
    
    async def verify_and_fix_user_language_consistency(self, user_id: str) -> str:
        """محاكاة فحص تناسق اللغة"""
        if not user_id:
            return 'ar'
        
        # جمع البيانات من مصادر مختلفة
        sources = {}
        
        subscription_lang = self._mock_subscription_data.get(user_id, {}).get('lang')
        if subscription_lang and subscription_lang in ['ar', 'en']:
            sources['subscription'] = subscription_lang
        
        settings_lang = self._mock_settings_data.get(user_id, {}).get('lang')
        if settings_lang and settings_lang in ['ar', 'en']:
            sources['settings'] = settings_lang
        
        # إذا كانت جميع المصادر متفقة
        if len(set(sources.values())) <= 1:
            return list(sources.values())[0] if sources else 'ar'
        
        # إذا كان هناك تناقض، اختر الأولوية
        if 'subscription' in sources:
            correct_lang = sources['subscription']
        elif 'settings' in sources:
            correct_lang = sources['settings']
        else:
            correct_lang = 'ar'
        
        # تزامن البيانات
        await self._sync_user_language_across_collections(user_id, correct_lang)
        return correct_lang
    
    async def _sync_user_language_across_collections(self, user_id: str, lang: str):
        """محاكاة تزامن اللغة"""
        if not user_id or lang not in ['ar', 'en']:
            return False
        
        # تحديث جميع المصادر
        if user_id not in self._mock_subscription_data:
            self._mock_subscription_data[user_id] = {}
        if user_id not in self._mock_settings_data:
            self._mock_settings_data[user_id] = {}
        
        self._mock_subscription_data[user_id]['lang'] = lang
        self._mock_settings_data[user_id]['lang'] = lang
        
        return True

class TestLanguageSystem:
    """اختبارات نظام اللغة"""
    
    @pytest.fixture
    def language_system(self):
        """إنشاء نظام اللغة للاختبار"""
        return MockLanguageSystem()
    
    @pytest.mark.asyncio
    async def test_get_user_language_from_subscription_system(self, language_system):
        """اختبار الحصول على اللغة من نظام الاشتراكات"""
        user_id = "123456789"
        expected_lang = "en"
        
        # إعداد بيانات وهمية
        language_system._mock_subscription_data = {
            user_id: {'lang': expected_lang}
        }
        
        result = await language_system._get_user_language(user_id)
        assert result == expected_lang
    
    @pytest.mark.asyncio
    async def test_language_consistency_check(self, language_system):
        """اختبار فحص تناسق اللغة"""
        user_id = "123456789"
        
        # إعداد بيانات متناقضة
        language_system._mock_subscription_data = {user_id: {'lang': 'en'}}
        language_system._mock_settings_data = {user_id: {'lang': 'ar'}}
        
        # يجب أن يختار نظام الاشتراكات (الأولوية الأعلى)
        result = await language_system.verify_and_fix_user_language_consistency(user_id)
        assert result == 'en'
        
        # التحقق من أن البيانات تم تزامنها
        assert language_system._mock_subscription_data[user_id]['lang'] == 'en'
        assert language_system._mock_settings_data[user_id]['lang'] == 'en'
    
    @pytest.mark.asyncio
    async def test_default_language_fallback(self, language_system):
        """اختبار الرجوع للغة الافتراضية"""
        user_id = "123456789"
        
        # لا توجد بيانات لغة للمستخدم
        language_system._mock_subscription_data = {}
        language_system._mock_settings_data = {}
        
        result = await language_system._get_user_language(user_id)
        assert result == "ar"  # يجب أن يعود للعربية كافتراضي
    
    def test_language_validation(self):
        """اختبار التحقق من صحة اللغة"""
        # اللغات الصالحة
        assert 'ar' in ['ar', 'en']
        assert 'en' in ['ar', 'en']
        
        # اللغات غير الصالحة
        assert 'fr' not in ['ar', 'en']
        assert 'de' not in ['ar', 'en']

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
