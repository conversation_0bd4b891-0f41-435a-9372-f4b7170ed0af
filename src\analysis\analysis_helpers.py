"""
دوال التحليل المساعدة - Analysis Helpers

هذا الملف يحتوي على الدوال المساعدة للتحليل التي تم نقلها من main.py
في إطار المرحلة 6 من خطة تحسين الهيكل.

الدوال المتضمنة:
- create_analysis_text(): إنشاء نص التحليل
- generate_stats_report(): إنشاء تقرير إحصائي
- get_analysis_type_name(): الحصول على اسم نوع التحليل
"""

import logging
from datetime import datetime
from typing import Optional

# إعداد التسجيل
logger = logging.getLogger(__name__)

# متغيرات عامة سيتم تعيينها من main.py
subscription_system = None
api_manager = None
enhanced_analyzer = None
db = None

def initialize_analysis_helpers(sub_system, api_mgr, enhanced_analyzer_instance, database):
    """تهيئة دوال التحليل المساعدة مع التبعيات المطلوبة"""
    global subscription_system, api_manager, enhanced_analyzer, db
    subscription_system = sub_system
    api_manager = api_mgr
    enhanced_analyzer = enhanced_analyzer_instance
    db = database
    logger.info("✅ تم تهيئة دوال التحليل المساعدة بنجاح")


async def create_analysis_text(symbol: str, market_data: dict, lang: str, user_id: str = None, analysis_type: str = None) -> str:
    """إنشاء نص التحليل"""
    try:
        logger.info(f"🔍 create_analysis_text استُدعيت للمستخدم {user_id} مع analysis_type={analysis_type}")

        # تحسين الأداء: التحقق السريع من نوع التحليل أولاً
        if analysis_type == 'traditional':
            logger.info(f"⚡ استخدام التحليل التقليدي المباشر للمستخدم {user_id}")
            from analysis.traditional_analysis import create_traditional_analysis
            return create_traditional_analysis(symbol, market_data, lang)

        # تحديد المؤشرات المسموح بها للمستخدم
        allowed_indicators = []
        is_subscribed = False
        if user_id:
            is_subscribed = await subscription_system.is_subscribed(user_id)
            user_features = subscription_system.get_user_features(user_id)
            allowed_indicators = user_features['indicators']

        # للمستخدمين غير المشتركين، استخدام التحليل التقليدي فقط
        if not is_subscribed:
            logger.info(f"⚡ مستخدم غير مشترك - استخدام التحليل التقليدي للمستخدم {user_id}")
            from analysis.traditional_analysis import create_traditional_analysis
            return create_traditional_analysis(symbol, market_data, lang)

        # التحقق من وجود تفضيل لنوع التحليل (تقليدي، ذكاء اصطناعي، أو محسن)
        settings = {}
        if subscription_system is not None:
            settings = subscription_system.get_user_settings(user_id)
            if not settings:
                settings = {}

        # إذا تم تحديد نوع التحليل مباشرة، استخدمه، وإلا استخدم إعدادات المستخدم
        if analysis_type is None:
            # التحقق من حالة الاشتراك أولاً
            is_subscribed = subscription_system.is_subscribed_sync(user_id) if subscription_system else False

            # طباعة الإعدادات للتشخيص
            logger.info(f"🔍 إعدادات المستخدم {user_id}: {settings}")

            if is_subscribed:
                # للمشتركين: استخدام إعدادات المستخدم أو القيمة الافتراضية 'ai'
                analysis_type = settings.get('analysis_type', 'ai')
                logger.info(f"🔧 المستخدم {user_id} مشترك - قراءة analysis_type من الإعدادات: {settings.get('analysis_type')} -> {analysis_type}")
            else:
                # لغير المشتركين: استخدام التحليل التقليدي دائماً
                analysis_type = 'traditional'
                logger.info(f"🔧 المستخدم {user_id} غير مشترك - استخدام التحليل التقليدي")

            logger.info(f"🔧 نوع التحليل المحدد من الإعدادات للمستخدم {user_id}: {analysis_type} (مشترك: {is_subscribed})")

        # التحقق من نوع التحليل المطلوب أولاً
        if analysis_type == 'traditional':
            # استخدام التحليل التقليدي إذا كان المستخدم يفضل ذلك
            logger.info(f"🔧 استخدام التحليل التقليدي للمستخدم {user_id} - اللغة: {lang}")
            from analysis.traditional_analysis import create_traditional_analysis
            return create_traditional_analysis(symbol, market_data, lang)

        # للمستخدمين المشتركين، التحقق من وجود مفتاح API (فقط للتحليل بالذكاء الاصطناعي والمحسن)
        has_gemini_api = False
        try:
            has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
        except Exception as e:
            logger.error(f"خطأ في التحقق من وجود مفتاح Gemini API: {str(e)}")
            has_gemini_api = False

        # إذا لم يكن هناك مفتاح API ونوع التحليل يتطلب ذلك، استخدام التحليل التقليدي مع رسالة تنبيه
        if not has_gemini_api and analysis_type in ['ai', 'enhanced']:
            from analysis.traditional_analysis import create_traditional_analysis
            traditional_analysis = create_traditional_analysis(symbol, market_data, lang)

            # إضافة رسالة تنبيه للمستخدم المشترك
            api_warning = "\n\n⚠️ لم يتم العثور على مفتاح Gemini API. يرجى إضافة مفتاح API للحصول على تحليل متقدم." if lang == 'ar' else "\n\n⚠️ No Gemini API key found. Please add your API key to get advanced analysis."
            return traditional_analysis + api_warning

        elif analysis_type == 'enhanced':
            # استخدام النظام المحسن متعدد الإطارات الزمنية
            logger.info(f"🚀 استخدام التحليل المحسن للمستخدم {user_id} - اللغة: {lang}")
            try:
                if enhanced_analyzer is None:
                    logger.warning("النظام المحسن غير متاح، سيتم استخدام التحليل التقليدي")
                    from analysis.traditional_analysis import create_traditional_analysis
                    traditional_analysis = create_traditional_analysis(symbol, market_data, lang)
                    enhanced_warning = "\n\n⚠️ النظام المحسن غير متاح حالياً. تم استخدام التحليل التقليدي." if lang == 'ar' else "\n\n⚠️ Enhanced system not available. Traditional analysis used."
                    return traditional_analysis + enhanced_warning

                # تحديد نمط التداول المفضل للمستخدم
                trading_style = settings.get('trading_style', 'day_trading')

                # تشغيل التحليل المحسن
                enhanced_result = await enhanced_analyzer.analyze_crypto_enhanced(symbol, user_id, trading_style, lang)

                if enhanced_result and enhanced_result.get('success', False):
                    # إرجاع ملخص التحليل المحسن مع تطبيق حل التنسيق
                    summary = enhanced_result.get('summary', 'تحليل محسن غير متاح')

                    # تطبيق حل التنسيق المبهر على النتيجة
                    try:
                        from utils.utils import fix_bold_formatting
                        summary = fix_bold_formatting(summary, lang)
                    except ImportError:
                        logger.warning("لم يتم العثور على دالة fix_bold_formatting")

                    return summary
                else:
                    # في حالة فشل التحليل المحسن، استخدام التحليل التقليدي
                    logger.warning(f"فشل التحليل المحسن لـ {symbol}: {enhanced_result.get('error', 'خطأ غير معروف')}")
                    from analysis.traditional_analysis import create_traditional_analysis
                    traditional_analysis = create_traditional_analysis(symbol, market_data, lang)
                    enhanced_error = "\n\n⚠️ فشل التحليل المحسن، تم استخدام التحليل التقليدي." if lang == 'ar' else "\n\n⚠️ Enhanced analysis failed, traditional analysis used."
                    return traditional_analysis + enhanced_error

            except Exception as e:
                logger.error(f"خطأ في التحليل المحسن: {str(e)}")
                # في حالة الخطأ، استخدام التحليل التقليدي
                from analysis.traditional_analysis import create_traditional_analysis
                traditional_analysis = create_traditional_analysis(symbol, market_data, lang)
                enhanced_error = "\n\n⚠️ حدث خطأ في التحليل المحسن، تم استخدام التحليل التقليدي." if lang == 'ar' else "\n\n⚠️ Enhanced analysis error, traditional analysis used."
                return traditional_analysis + enhanced_error

        # تحسين الأداء: تجنب استدعاءات Gemini غير الضرورية
        # التحقق من وجود مفتاح API قبل المحاولة
        has_gemini_api = False
        try:
            from analysis.gemini_analysis import get_user_api_client
            # فحص سريع لوجود مفتاح API
            if user_id:
                from services.api_manager import api_manager
                if api_manager:
                    has_gemini_api = await api_manager.has_api_keys(user_id, 'gemini')
        except:
            has_gemini_api = False

        # إذا لم يوجد مفتاح API، استخدم التحليل التقليدي مباشرة
        if not has_gemini_api:
            logger.info(f"⚡ لا يوجد مفتاح Gemini API - استخدام التحليل التقليدي للمستخدم {user_id}")
            from analysis.traditional_analysis import create_traditional_analysis
            return create_traditional_analysis(symbol, market_data, lang)

        # استخدام تحليل Gemini للمستخدمين المشتركين مع مفتاح API (التحليل الافتراضي)
        logger.info(f"🤖 استخدام تحليل الذكاء الاصطناعي (Gemini) للمستخدم {user_id} - اللغة: {lang}")
        try:
            model = await get_user_api_client(user_id, 'gemini')

            if model is None:
                # في حالة فشل الحصول على نموذج Gemini، استخدام التحليل التقليدي
                logger.warning(f"فشل في الحصول على نموذج Gemini للمستخدم {user_id}")
                from analysis.traditional_analysis import create_traditional_analysis
                traditional_analysis = create_traditional_analysis(symbol, market_data, lang)
                api_warning = "\n\n⚠️ حدث خطأ في تهيئة نموذج Gemini. يرجى المحاولة مرة أخرى لاحقًا." if lang == 'ar' else "\n\n⚠️ Error initializing Gemini model. Please try again later."
                return traditional_analysis + api_warning

            from analysis.gemini_analysis import analyze_with_gemini
            ai_analysis = await analyze_with_gemini(model, market_data, lang)

            if ai_analysis and len(ai_analysis) > 100:
                # إضافة سجل للتشخيص
                logger.info(f"تم الحصول على تحليل Gemini بنجاح باللغة {lang}")
                logger.info(f"طول التحليل: {len(ai_analysis)} حرف")

                # تطبيق حل التنسيق المبهر على تحليل Gemini
                try:
                    from utils.utils import fix_bold_formatting
                    ai_analysis = fix_bold_formatting(ai_analysis, lang)
                except ImportError:
                    logger.warning("لم يتم العثور على دالة fix_bold_formatting")

                return ai_analysis
            else:
                logger.error(f"تحليل Gemini قصير جدًا أو فارغ: {ai_analysis}")
                # في حالة فشل تحليل Gemini، استخدام التحليل التقليدي
                from analysis.traditional_analysis import create_traditional_analysis
                traditional_analysis = create_traditional_analysis(symbol, market_data, lang)

                # إضافة رسالة تنبيه
                ai_error = "\n\n⚠️ فشل في الحصول على تحليل متقدم. يرجى المحاولة مرة أخرى لاحقًا." if lang == 'ar' else "\n\n⚠️ Failed to get advanced analysis. Please try again later."
                return traditional_analysis + ai_error
        except Exception as e:
            # استيراد دالة log_error من services.error_handler
            from services.error_handler import log_error
            log_error("GEMINI_ANALYSIS_ERROR", f"خطأ في تحليل Gemini: {str(e)}", user_id=user_id, context="gemini_analysis", exception=e)
            # في حالة الخطأ، استخدام التحليل التقليدي
            from analysis.traditional_analysis import create_traditional_analysis
            return create_traditional_analysis(symbol, market_data, lang)

    except Exception as e:
        # استيراد دالة log_error من services.error_handler
        from services.error_handler import log_error
        log_error("ANALYSIS_TEXT_CREATION_ERROR", f"Error creating analysis text: {str(e)}", user_id=user_id, context="analysis_text_creation", exception=e)
        return "Error creating analysis" if lang == 'en' else "خطأ في إنشاء التحليل"


async def generate_stats_report() -> str:
    """إنشاء تقرير إحصائي عن حالة النظام"""
    try:
        # جلب الإحصائيات من Firestore
        stats_ref = db.collection('stats').document('global')
        stats_doc = stats_ref.get()

        if stats_doc.exists:
            stats = stats_doc.to_dict()
        else:
            stats = {
                'total_users': 0,
                'active_subscribers': 0,
                'total_analyses': 0,
                'total_alerts': 0,
                'last_update': datetime.now().isoformat()
            }
            stats_ref.set(stats)

        # تحديث الإحصائيات
        users_ref = db.collection('user_settings')
        total_users = len(list(users_ref.get()))

        subs_ref = db.collection('subscriptions')
        active_subs = len([doc for doc in subs_ref.get() if datetime.fromisoformat(doc.to_dict()['expiry']) > datetime.now()])

        stats.update({
            'total_users': total_users,
            'active_subscribers': active_subs,
            'last_update': datetime.now().isoformat()
        })

        stats_ref.set(stats)

        # إنشاء التقرير
        report = f"""📊 *تقرير حالة النظام*

📈 *إحصائيات المستخدمين:*
• إجمالي المستخدمين: {total_users}
• المشتركين: {active_subs}
• غير المشتركين: {total_users - active_subs}

🔍 *إحصائيات النشاط:*
• إجمالي التحليلات: {stats.get('total_analyses', 0)}
• إجمالي التنبيهات: {stats.get('total_alerts', 0)}

⏰ *آخر تحديث:*
{datetime.fromisoformat(stats['last_update']).strftime('%Y-%m-%d %H:%M:%S')}"""

        return report
    except Exception as e:
        logger.error(f"Error generating stats report: {str(e)}")
        return "❌ حدث خطأ أثناء إنشاء التقرير"


def get_analysis_type_name(analysis_type: str, lang: str) -> str:
    """الحصول على اسم نوع التحليل"""
    names = {
        'traditional': 'التقليدي' if lang == 'ar' else 'Traditional',
        'ai': 'الذكاء الاصطناعي' if lang == 'ar' else 'AI',
        'enhanced': 'المحسن' if lang == 'ar' else 'Enhanced'
    }
    return names.get(analysis_type, analysis_type)
