#!/usr/bin/env python3
"""
سكريبت اختبار سريع لنظام الأخبار الذكي
للتأكد من أن إصلاح اللغة يعمل بشكل صحيح
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('news_system_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

class NewsSystemTester:
    """فئة لاختبار نظام الأخبار"""
    
    def __init__(self):
        self.db = None
        self.firebase_available = False
        
    def initialize_firebase(self):
        """تهيئة Firebase"""
        try:
            from integrations.firebase_init import initialize_firebase
            self.db = initialize_firebase()
            if self.db:
                self.firebase_available = True
                logger.info("✅ تم تهيئة Firebase بنجاح")
                return True
            else:
                logger.warning("⚠️ فشل في تهيئة Firebase")
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة Firebase: {str(e)}")
            return False
    
    async def test_language_detection(self):
        """اختبار آلية اكتشاف اللغة"""
        if not self.firebase_available:
            logger.warning("Firebase غير متوفر - لا يمكن اختبار اكتشاف اللغة")
            return
        
        print("\n🔍 اختبار آلية اكتشاف اللغة...")
        print("="*50)
        
        try:
            # استيراد نظام الأخبار
            from services.automatic_news_notifications import AutomaticNewsNotifications
            news_system = AutomaticNewsNotifications()
            
            # الحصول على عينة من المستخدمين
            subscription_docs = list(self.db.collection('subscription_system').limit(3).stream())
            
            for doc in subscription_docs:
                user_id = doc.id
                data = doc.to_dict()
                
                print(f"\n👤 المستخدم: {user_id}")
                print(f"   🏷️ اللغة المحفوظة: {data.get('lang', 'غير محدد')}")
                
                # اختبار اكتشاف اللغة
                try:
                    detected_lang = await news_system._get_user_language(user_id)
                    print(f"   🔍 اللغة المكتشفة: {detected_lang}")
                    
                    if data.get('lang') == detected_lang:
                        print(f"   ✅ تطابق صحيح!")
                    else:
                        print(f"   ⚠️ عدم تطابق: محفوظة={data.get('lang')}, مكتشفة={detected_lang}")
                        
                except Exception as e:
                    print(f"   ❌ خطأ في اكتشاف اللغة: {str(e)}")
            
        except Exception as e:
            logger.error(f"خطأ في اختبار اكتشاف اللغة: {str(e)}")
    
    async def test_news_formatting(self):
        """اختبار تنسيق الأخبار حسب اللغة"""
        print("\n📰 اختبار تنسيق الأخبار...")
        print("="*50)
        
        # خبر تجريبي
        test_news = {
            'title_ar': 'ارتفاع أسعار البيتكوين',
            'title_en': 'Bitcoin Prices Rise',
            'content_ar': 'شهدت أسعار البيتكوين ارتفاعاً ملحوظاً اليوم',
            'content_en': 'Bitcoin prices saw a notable increase today',
            'timestamp': datetime.now(),
            'category': 'crypto'
        }
        
        # اختبار التنسيق للغات المختلفة
        languages = ['ar', 'en']
        
        for lang in languages:
            print(f"\n🌐 اختبار اللغة: {lang}")
            
            try:
                from services.automatic_news_notifications import AutomaticNewsNotifications
                news_system = AutomaticNewsNotifications()
                
                # تنسيق الخبر
                formatted_news = news_system._format_news_for_language(test_news, lang)
                
                print(f"   📰 العنوان: {formatted_news.get('title', 'غير متوفر')}")
                print(f"   📝 المحتوى: {formatted_news.get('content', 'غير متوفر')[:50]}...")
                print(f"   ✅ تم التنسيق بنجاح")
                
            except Exception as e:
                print(f"   ❌ خطأ في التنسيق: {str(e)}")
    
    async def check_subscription_system_status(self):
        """فحص حالة نظام الاشتراكات"""
        if not self.firebase_available:
            return
        
        print("\n📊 فحص حالة نظام الاشتراكات...")
        print("="*50)
        
        try:
            # إحصائيات subscription_system
            subscription_docs = list(self.db.collection('subscription_system').stream())
            
            total_users = len(subscription_docs)
            active_users = sum(1 for doc in subscription_docs if doc.to_dict().get('active', False))
            
            # توزيع اللغات
            lang_distribution = {}
            for doc in subscription_docs:
                lang = doc.to_dict().get('lang', 'unknown')
                lang_distribution[lang] = lang_distribution.get(lang, 0) + 1
            
            print(f"📈 إجمالي المستخدمين: {total_users}")
            print(f"✅ المستخدمين النشطين: {active_users}")
            print(f"📊 توزيع اللغات:")
            for lang, count in lang_distribution.items():
                percentage = (count / total_users * 100) if total_users > 0 else 0
                print(f"   {lang}: {count} مستخدم ({percentage:.1f}%)")
            
            # فحص تناسق البيانات
            inconsistent_users = 0
            for doc in subscription_docs:
                user_id = doc.id
                subscription_lang = doc.to_dict().get('lang')
                
                # فحص user_settings
                try:
                    settings_doc = self.db.collection('user_settings').document(user_id).get()
                    if settings_doc.exists:
                        settings_lang = settings_doc.to_dict().get('lang')
                        if subscription_lang != settings_lang:
                            inconsistent_users += 1
                except:
                    pass
            
            if inconsistent_users == 0:
                print("✅ جميع البيانات متناسقة")
            else:
                print(f"⚠️ {inconsistent_users} مستخدم لديهم عدم تناسق في البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في فحص نظام الاشتراكات: {str(e)}")
    
    async def run_comprehensive_test(self):
        """تشغيل اختبار شامل"""
        print("🧪 بدء الاختبار الشامل لنظام الأخبار الذكي")
        print("="*60)
        
        if not self.initialize_firebase():
            print("❌ لا يمكن المتابعة بدون Firebase")
            return
        
        try:
            # 1. فحص حالة نظام الاشتراكات
            await self.check_subscription_system_status()
            
            # 2. اختبار اكتشاف اللغة
            await self.test_language_detection()
            
            # 3. اختبار تنسيق الأخبار
            await self.test_news_formatting()
            
            print("\n" + "="*60)
            print("✅ تم إكمال جميع الاختبارات بنجاح!")
            print("🎉 نظام الأخبار الذكي جاهز للعمل")
            
        except Exception as e:
            print(f"\n❌ خطأ في الاختبار الشامل: {str(e)}")
            logger.error(f"خطأ عام: {str(e)}")

async def main():
    """الدالة الرئيسية"""
    tester = NewsSystemTester()
    
    try:
        await tester.run_comprehensive_test()
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الاختبار: {str(e)}")
        logger.error(f"خطأ عام: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
