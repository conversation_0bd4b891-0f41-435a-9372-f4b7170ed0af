"""
ملف غلاف لتشغيل البوت مع معالجة مشاكل التوافق
"""
import os
import sys
import logging
import asyncio
import time
import signal
import threading
import aiohttp
import schedule

# إعداد السجلات
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

# متغير عالمي للإشارة إلى أن البرنامج يجب أن يستمر في التشغيل
keep_running = True

def signal_handler(sig, frame):
    """معالج إشارات التوقف"""
    global keep_running
    logger.info("تم استلام إشارة توقف، جاري إيقاف البوت...")
    keep_running = False

# تسجيل معالج الإشارات
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

async def run_bot():
    """تشغيل البوت الرئيسي بشكل مستمر"""
    try:
        # التحقق من متغيرات البيئة الأساسية
        import os
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.error("❌ BOT_TOKEN غير محدد في متغيرات البيئة")
            return False

        logger.info(f"✅ تم العثور على BOT_TOKEN: {bot_token[:10]}...")

        # استيراد وتشغيل البوت
        logger.info("🔄 جاري استيراد main module...")
        import main
        logger.info("✅ تم استيراد main module بنجاح")

        # تشغيل البوت مباشرة من main - سيستمر إلى أجل غير مسمى
        logger.info("🚀 جاري تشغيل البوت من main...")
        await main.run_bot()

        # هذا السطر لن يتم الوصول إليه في الظروف العادية
        # لأن run_bot() في main.py تستمر إلى أجل غير مسمى
        logger.info("✅ انتهى تشغيل البوت")
        return True

    except ImportError as import_error:
        logger.error(f"❌ خطأ في استيراد البوت: {str(import_error)}")
        import traceback
        logger.error(traceback.format_exc())
        return False
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

async def ping_url(url: str, timeout: int = 10) -> bool:
    """
    تشغيل رابط محدد والتحقق من استجابته

    Args:
        url: الرابط المراد تشغيله
        timeout: مهلة الانتظار بالثواني

    Returns:
        True إذا كان الرابط يعمل، False خلاف ذلك
    """
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=timeout) as response:
                status = response.status
                return status == 200
    except Exception as e:
        return False

def ping_koyeb_health_check():
    """تشغيل رابط فحص الصحة لـ Koyeb كل 30 ثانية"""
    url = "https://rural-genvieve-hoy-50612c34.koyeb.app/health"

    # إنشاء حلقة أحداث مؤقتة لتشغيل الدالة غير المتزامنة
    loop = asyncio.new_event_loop()
    result = loop.run_until_complete(ping_url(url))
    loop.close()

    return result

def setup_ping_scheduler():
    """إعداد مجدول لتشغيل رابط فحص الصحة كل 30 ثانية مع آلية للمحاولة المتكررة"""
    schedule.every(30).seconds.do(ping_koyeb_health_check)

    # تشغيل المجدول في خيط منفصل
    def run_scheduler():
        while keep_running:
            schedule.run_pending()
            time.sleep(1)

    scheduler_thread = threading.Thread(target=run_scheduler)
    scheduler_thread.daemon = True
    scheduler_thread.start()

    # تشغيل الرابط مرة واحدة عند بدء التشغيل
    ping_koyeb_health_check()


def run_health_server_only():
    """تشغيل خادم فحص الصحة فقط"""
    from http.server import HTTPServer, BaseHTTPRequestHandler
    import threading

    class HealthHandler(BaseHTTPRequestHandler):
        def do_GET(self):
            if self.path == "/health" or self.path == "/":
                self.send_response(200)
                self.send_header("Content-type", "text/plain")
                self.end_headers()
                self.wfile.write(b"Health check OK (Bot not running due to compatibility issues)")
            else:
                self.send_response(404)
                self.end_headers()

        def log_message(self, format, *args):
            return

    def start_health_server():
        port = int(os.environ.get('PORT', 8000))
        server = HTTPServer(('0.0.0.0', port), HealthHandler)
        logger.info(f"🌐 بدء خادم فحص الصحة على المنفذ {port}")
        server.serve_forever()

    # تشغيل الخادم في خيط منفصل
    server_thread = threading.Thread(target=start_health_server)
    server_thread.daemon = True
    server_thread.start()

    # إعداد مجدول لتشغيل رابط فحص الصحة
    setup_ping_scheduler()

    # الانتظار إلى أجل غير مسمى
    global keep_running
    while keep_running:
        time.sleep(1)

def main():
    """
    دالة رئيسية لتشغيل البوت مع معالجة مشاكل التوافق
    """
    try:
        # تشغيل خادم فحص الصحة أولاً
        from server import run_health_server
        health_server_thread = run_health_server()
        logger.info("✅ تم بدء خادم فحص الصحة")

        # إعداد مجدول لتشغيل رابط فحص الصحة كل 30 ثانية
        setup_ping_scheduler()

        # محاولة استيراد وتشغيل البوت
        logger.info("🔄 جاري محاولة تشغيل البوت...")

        # تهيئة حلقة الأحداث
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # تشغيل البوت
        try:
            # تشغيل البوت بشكل مستمر - لا نستخدم run_until_complete
            # بل نستخدم run_forever للحفاظ على حلقة الأحداث نشطة
            task = loop.create_task(run_bot())

            # تشغيل حلقة الأحداث إلى أجل غير مسمى
            logger.info("🔄 تشغيل حلقة الأحداث بشكل مستمر...")
            loop.run_forever()

        except KeyboardInterrupt:
            logger.info("تم إيقاف البوت بواسطة المستخدم")
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل البوت: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
        finally:
            # إيقاف المهام المعلقة
            if 'task' in locals() and not task.done():
                task.cancel()

            # إغلاق حلقة الأحداث بشكل آمن
            try:
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    task.cancel()
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
            except Exception as cleanup_error:
                logger.warning(f"تحذير في تنظيف المهام: {cleanup_error}")
            finally:
                loop.close()

        # الانتظار إلى أجل غير مسمى (حتى لو فشل البوت، نحافظ على خادم الصحة)
        logger.info("🔄 الحفاظ على خادم الصحة يعمل...")
        global keep_running
        while keep_running:
            time.sleep(1)

    except ImportError as e:
        logger.error(f"❌ خطأ في استيراد البوت: {str(e)}")

        # إذا كان الخطأ متعلقًا بـ cryptography، نقوم بتشغيل خادم فحص الصحة فقط
        if "cryptography" in str(e) or "_cffi_backend" in str(e):
            logger.warning("⚠️ مشكلة في مكتبة cryptography، سيتم تشغيل خادم فحص الصحة فقط")
            run_health_server_only()
        else:
            # إعادة رفع الخطأ إذا لم يكن متعلقًا بـ cryptography
            raise
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

        # تشغيل خادم فحص الصحة على الأقل
        logger.warning("⚠️ حدث خطأ غير متوقع، سيتم تشغيل خادم فحص الصحة فقط")
        run_health_server_only()

if __name__ == "__main__":
    main()
