#!/usr/bin/env python3
"""
اختبار سياسة الإشعارات الجديدة
يوضح كيف يرسل النظام الأخبار العاجلة والعملات الجديدة لجميع المستخدمين
"""

import asyncio
import logging
from datetime import datetime
from services.automatic_news_notifications import AutomaticNewsNotifications
from config.notification_types import NotificationType, NotificationPriority

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('notification_policy_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

class MockNewsItem:
    """عنصر أخبار وهمي للاختبار"""
    def __init__(self, title, content, symbols=None):
        self.id = f"test_{hash(title)}"
        self.title = title
        self.content = content
        self.url = "https://example.com/news"
        self.symbols = symbols or []
        self.sentiment = "neutral"
        self.ai_analysis = "تحليل تجريبي"
        self.published_at = datetime.now()

async def test_notification_policy():
    """اختبار سياسة الإشعارات"""
    
    logger.info("🧪 بدء اختبار سياسة الإشعارات الجديدة")
    
    # إنشاء نظام الإشعارات (بدون قاعدة بيانات للاختبار)
    notifications_system = AutomaticNewsNotifications(db=None, bot=None)
    
    # إنشاء أخبار تجريبية
    breaking_news = MockNewsItem(
        title="🚨 عاجل: انهيار مفاجئ في سعر البيتكوين",
        content="شهد سعر البيتكوين انهياراً مفاجئاً بنسبة 15% خلال الساعة الماضية",
        symbols=["BTC", "BITCOIN"]
    )
    
    new_coin_news = MockNewsItem(
        title="🆕 إطلاق عملة جديدة على منصة Binance",
        content="تم إدراج عملة NEWCOIN الجديدة على منصة Binance للتداول",
        symbols=["NEWCOIN"]
    )
    
    market_analysis = MockNewsItem(
        title="📊 تحليل السوق الأسبوعي",
        content="تحليل شامل لحركة السوق خلال الأسبوع الماضي",
        symbols=["BTC", "ETH"]
    )
    
    # اختبار تحليل أهمية الأخبار
    logger.info("🔍 اختبار تحليل أهمية الأخبار...")
    
    # اختبار الأخبار العاجلة
    breaking_type, breaking_priority = await notifications_system._analyze_news_importance(breaking_news)
    logger.info(f"📰 الأخبار العاجلة - النوع: {breaking_type}, الأولوية: {breaking_priority}")
    
    # اختبار العملات الجديدة
    new_coin_type, new_coin_priority = await notifications_system._analyze_news_importance(new_coin_news)
    logger.info(f"🆕 العملات الجديدة - النوع: {new_coin_type}, الأولوية: {new_coin_priority}")
    
    # اختبار تحليل السوق
    analysis_type, analysis_priority = await notifications_system._analyze_news_importance(market_analysis)
    logger.info(f"📊 تحليل السوق - النوع: {analysis_type}, الأولوية: {analysis_priority}")
    
    # محاكاة قائمة مستخدمين
    mock_users = ["user1", "user2", "user3", "user4", "user5"]
    
    # تجاوز دالة الحصول على المستخدمين النشطين للاختبار
    async def mock_get_all_active_users():
        return mock_users
    
    notifications_system._get_all_active_users = mock_get_all_active_users
    
    # تجاوز دالة فحص حدود الإرسال للاختبار
    async def mock_check_general_rate_limit(user_id, notification_type):
        return True  # السماح لجميع المستخدمين
    
    notifications_system._check_general_rate_limit = mock_check_general_rate_limit
    
    # اختبار العثور على المستخدمين المهتمين
    logger.info("👥 اختبار العثور على المستخدمين المهتمين...")
    
    # الأخبار العاجلة - يجب أن ترسل لجميع المستخدمين
    breaking_users = await notifications_system._find_interested_users(breaking_news, NotificationType.BREAKING_NEWS)
    logger.info(f"🚨 الأخبار العاجلة ستُرسل لـ {len(breaking_users)} مستخدم: {breaking_users}")
    
    # العملات الجديدة - يجب أن ترسل لجميع المستخدمين
    new_coin_users = await notifications_system._find_interested_users(new_coin_news, NotificationType.NEW_COIN)
    logger.info(f"🆕 العملات الجديدة ستُرسل لـ {len(new_coin_users)} مستخدم: {new_coin_users}")
    
    # تحليل السوق - يرسل للمشتركين فقط (قائمة فارغة في هذا الاختبار)
    analysis_users = await notifications_system._find_interested_users(market_analysis, NotificationType.MARKET_ANALYSIS)
    logger.info(f"📊 تحليل السوق سيُرسل لـ {len(analysis_users)} مستخدم مشترك: {analysis_users}")
    
    # إنشاء تقرير السياسة
    logger.info("📋 إنشاء تقرير سياسة الإشعارات...")
    policy_report = await notifications_system.generate_notification_policy_report()
    logger.info(f"📄 تقرير السياسة:\n{policy_report}")
    
    # اختبار معالجة الأخبار
    logger.info("⚙️ اختبار معالجة الأخبار...")
    test_news = [breaking_news, new_coin_news, market_analysis]
    
    # تجاوز دالة الإرسال للاختبار
    async def mock_send_notification(notification):
        logger.info(f"📤 محاكاة إرسال إشعار: {notification.title} للمستخدم {notification.user_id}")
        return True
    
    notifications_system._send_notification = mock_send_notification
    
    # معالجة الأخبار
    await notifications_system.process_news_for_notifications(test_news)
    
    logger.info("✅ انتهى اختبار سياسة الإشعارات بنجاح")
    
    return {
        'breaking_news_users': len(breaking_users),
        'new_coin_users': len(new_coin_users),
        'analysis_users': len(analysis_users),
        'total_users': len(mock_users),
        'policy_confirmed': len(breaking_users) == len(mock_users) and len(new_coin_users) == len(mock_users)
    }

async def main():
    """الدالة الرئيسية"""
    try:
        logger.info("🚀 بدء اختبار نظام الإشعارات")
        
        results = await test_notification_policy()
        
        logger.info("📊 نتائج الاختبار:")
        logger.info(f"  • الأخبار العاجلة: {results['breaking_news_users']} مستخدم")
        logger.info(f"  • العملات الجديدة: {results['new_coin_users']} مستخدم")
        logger.info(f"  • تحليل السوق: {results['analysis_users']} مستخدم")
        logger.info(f"  • إجمالي المستخدمين: {results['total_users']} مستخدم")
        logger.info(f"  • تأكيد السياسة: {'✅ صحيح' if results['policy_confirmed'] else '❌ خطأ'}")
        
        if results['policy_confirmed']:
            logger.info("🎉 النظام يعمل بشكل صحيح - الأخبار العاجلة والعملات الجديدة ترسل لجميع المستخدمين!")
        else:
            logger.error("❌ هناك مشكلة في النظام - يرجى مراجعة الكود")
            
    except Exception as e:
        logger.error(f"خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())