# Core Dependencies
python-telegram-bot==20.8
ccxt==4.2.15
python-dotenv==1.0.1
pandas>=2.2.3
numpy>=1.26.4

# HTTP and Network
requests==2.32.3
aiohttp==3.9.3
yarl==1.9.4
multidict==6.0.5
attrs==23.2.0
charset-normalizer==3.3.2
frozenlist==1.4.1
aiosignal==1.3.1
async-timeout==4.0.3
aiodns==3.1.1
pycares==4.4.0
idna==3.6
certifi==2024.2.2
urllib3==2.2.1
brotli==1.1.0

# JSON and Data Processing
json5==0.9.17
typing_extensions==4.10.0

# Scheduling and Time
APScheduler==3.10.4
pytz==2024.1
schedule==1.2.2

# Cryptography and Security
cryptography>=39.0.1
cffi==1.15.1
pycparser==2.21
PyJWT>=2.8.0

# Database and Cloud
firebase-admin==6.5.0
web3==6.11.0
google-cloud-firestore==2.15.0

# Performance and Monitoring - تم حذف مراقبة الذاكرة لأن الاستضافة تتولى هذه المهام
concurrent-log-handler>=0.9.20
numba

# API Integrations
python-binance==1.0.19
google-generativeai>=0.3.0

# Data Validation
pydantic==1.10.8

# Cache and Storage - تم حذف Redis واستبداله بتخزين محلي
# Local caching system built-in

# Technical Analysis - تم حذف TA-Lib واستبداله بمؤشرات محلية محسنة
# Local technical indicators built-in
