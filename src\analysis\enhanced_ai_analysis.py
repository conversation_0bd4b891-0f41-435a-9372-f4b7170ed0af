"""
وحدة تحليل الذكاء الاصطناعي المحسن - Enhanced AI Analysis
تحتوي على نظام تحليل AI متقدم مع تحسينات الأداء والدقة

الميزات المطلوبة:
- تحليل AI متقدم مع تخزين مؤقت ذكي
- تقييم جودة التحليل ومقاييس أداء متقدمة
- دعم نماذج Gemini متعددة
- تقليل وقت الاستجابة بنسبة 60%
- زيادة دقة التحليل بنسبة 35%
"""

import asyncio
import logging
import time
import hashlib
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import threading
from collections import defaultdict, deque
import pickle
import zlib

logger = logging.getLogger(__name__)

@dataclass
class AnalysisQuality:
    """تقييم جودة التحليل"""
    accuracy_score: float
    confidence_level: float
    data_completeness: float
    signal_strength: float
    consistency_score: float
    overall_quality: float

@dataclass
class PerformanceMetrics:
    """مقاييس الأداء"""
    execution_time: float
    cache_hit_rate: float
    model_response_time: float
    data_processing_time: float
    total_requests: int
    successful_requests: int
    error_rate: float

@dataclass
class AIModelConfig:
    """تكوين نموذج الذكاء الاصطناعي"""
    model_name: str
    max_tokens: int
    temperature: float
    timeout: int
    retry_attempts: int
    fallback_model: Optional[str] = None

class SmartCache:
    """نظام التخزين المؤقت الذكي"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        تهيئة التخزين المؤقت الذكي
        
        Args:
            max_size: الحد الأقصى لعدد العناصر
            ttl: مدة البقاء بالثواني
        """
        self.max_size = max_size
        self.ttl = ttl
        self.cache: Dict[str, Dict] = {}
        self.access_times: Dict[str, float] = {}
        self.hit_count = 0
        self.miss_count = 0
        self.lock = threading.RLock()
        
    def _generate_key(self, symbol: str, timeframe: str, analysis_type: str, **kwargs) -> str:
        """إنشاء مفتاح فريد للتخزين المؤقت"""
        key_data = {
            'symbol': symbol,
            'timeframe': timeframe,
            'analysis_type': analysis_type,
            **kwargs
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get(self, symbol: str, timeframe: str, analysis_type: str, **kwargs) -> Optional[Any]:
        """الحصول على البيانات من التخزين المؤقت"""
        key = self._generate_key(symbol, timeframe, analysis_type, **kwargs)
        
        with self.lock:
            if key in self.cache:
                # التحقق من انتهاء الصلاحية
                if time.time() - self.cache[key]['timestamp'] < self.ttl:
                    self.access_times[key] = time.time()
                    self.hit_count += 1
                    return self.cache[key]['data']
                else:
                    # إزالة البيانات المنتهية الصلاحية
                    del self.cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
            
            self.miss_count += 1
            return None
    
    def set(self, symbol: str, timeframe: str, analysis_type: str, data: Any, **kwargs):
        """حفظ البيانات في التخزين المؤقت"""
        key = self._generate_key(symbol, timeframe, analysis_type, **kwargs)
        
        with self.lock:
            # إزالة العناصر القديمة إذا تجاوز الحد الأقصى
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            # ضغط البيانات الكبيرة
            compressed_data = self._compress_data(data)
            
            self.cache[key] = {
                'data': compressed_data,
                'timestamp': time.time(),
                'size': len(str(data))
            }
            self.access_times[key] = time.time()
    
    def _compress_data(self, data: Any) -> Any:
        """ضغط البيانات إذا كانت كبيرة"""
        try:
            data_str = json.dumps(data) if not isinstance(data, str) else data
            if len(data_str) > 1024:  # ضغط البيانات أكبر من 1KB
                compressed = zlib.compress(data_str.encode())
                return {'compressed': True, 'data': compressed}
            return {'compressed': False, 'data': data}
        except:
            return {'compressed': False, 'data': data}
    
    def _decompress_data(self, cached_item: Dict) -> Any:
        """إلغاء ضغط البيانات"""
        try:
            if cached_item.get('compressed', False):
                decompressed = zlib.decompress(cached_item['data']).decode()
                return json.loads(decompressed)
            return cached_item['data']
        except:
            return cached_item['data']
    
    def _evict_oldest(self):
        """إزالة أقدم العناصر"""
        if not self.access_times:
            return
        
        oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        if oldest_key in self.cache:
            del self.cache[oldest_key]
        del self.access_times[oldest_key]
    
    def get_stats(self) -> Dict:
        """إحصائيات التخزين المؤقت"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache),
            'max_size': self.max_size
        }
    
    def clear(self):
        """مسح التخزين المؤقت"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.hit_count = 0
            self.miss_count = 0

class AnalysisQualityEvaluator:
    """مقيم جودة التحليل"""

    def __init__(self):
        self.quality_history: deque = deque(maxlen=1000)
        self.quality_thresholds = {
            'excellent': 0.9,
            'good': 0.75,
            'fair': 0.6,
            'poor': 0.4
        }

    def evaluate_analysis_quality(self, analysis_data: Dict, market_data: Dict) -> AnalysisQuality:
        """تقييم جودة التحليل"""
        try:
            # تقييم دقة البيانات
            accuracy_score = self._evaluate_data_accuracy(analysis_data, market_data)

            # تقييم مستوى الثقة
            confidence_level = self._evaluate_confidence_level(analysis_data)

            # تقييم اكتمال البيانات
            data_completeness = self._evaluate_data_completeness(market_data)

            # تقييم قوة الإشارة
            signal_strength = self._evaluate_signal_strength(analysis_data)

            # تقييم الاتساق
            consistency_score = self._evaluate_consistency(analysis_data)

            # حساب الجودة الإجمالية
            overall_quality = self._calculate_overall_quality(
                accuracy_score, confidence_level, data_completeness,
                signal_strength, consistency_score
            )

            quality = AnalysisQuality(
                accuracy_score=accuracy_score,
                confidence_level=confidence_level,
                data_completeness=data_completeness,
                signal_strength=signal_strength,
                consistency_score=consistency_score,
                overall_quality=overall_quality
            )

            # حفظ في التاريخ
            self.quality_history.append({
                'timestamp': time.time(),
                'quality': quality
            })

            return quality

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة التحليل: {str(e)}")
            return AnalysisQuality(0.5, 0.5, 0.5, 0.5, 0.5, 0.5)

    def _evaluate_data_accuracy(self, analysis_data: Dict, market_data: Dict) -> float:
        """تقييم دقة البيانات"""
        try:
            accuracy_factors = []

            # التحقق من وجود البيانات الأساسية
            required_fields = ['price', 'volume', 'indicators']
            for field in required_fields:
                if field in market_data and market_data[field] is not None:
                    accuracy_factors.append(1.0)
                else:
                    accuracy_factors.append(0.0)

            # التحقق من جودة المؤشرات
            if 'indicators' in market_data:
                indicators = market_data['indicators']
                valid_indicators = sum(1 for v in indicators.values() if v is not None and not np.isnan(float(v)))
                total_indicators = len(indicators)
                if total_indicators > 0:
                    accuracy_factors.append(valid_indicators / total_indicators)

            return np.mean(accuracy_factors) if accuracy_factors else 0.5

        except Exception as e:
            logger.error(f"خطأ في تقييم دقة البيانات: {str(e)}")
            return 0.5

    def _evaluate_confidence_level(self, analysis_data: Dict) -> float:
        """تقييم مستوى الثقة"""
        try:
            confidence_factors = []

            # التحقق من وجود توصيات واضحة
            if 'recommendation' in analysis_data:
                recommendation = analysis_data['recommendation'].lower()
                if any(word in recommendation for word in ['قوي', 'strong', 'واضح', 'clear']):
                    confidence_factors.append(0.9)
                elif any(word in recommendation for word in ['متوسط', 'moderate', 'محتمل', 'possible']):
                    confidence_factors.append(0.7)
                else:
                    confidence_factors.append(0.5)

            # التحقق من وجود نقاط دخول وخروج
            if 'entry_points' in analysis_data and analysis_data['entry_points']:
                confidence_factors.append(0.8)

            # التحقق من وجود تحليل المخاطر
            if 'risk_analysis' in analysis_data:
                confidence_factors.append(0.7)

            return np.mean(confidence_factors) if confidence_factors else 0.5

        except Exception as e:
            logger.error(f"خطأ في تقييم مستوى الثقة: {str(e)}")
            return 0.5

    def _evaluate_data_completeness(self, market_data: Dict) -> float:
        """تقييم اكتمال البيانات"""
        try:
            required_data = [
                'price', 'volume', 'high', 'low', 'open', 'close',
                'indicators', 'timeframe_data'
            ]

            available_data = sum(1 for field in required_data if field in market_data and market_data[field] is not None)
            return available_data / len(required_data)

        except Exception as e:
            logger.error(f"خطأ في تقييم اكتمال البيانات: {str(e)}")
            return 0.5

    def _evaluate_signal_strength(self, analysis_data: Dict) -> float:
        """تقييم قوة الإشارة"""
        try:
            signal_factors = []

            # التحقق من قوة الاتجاه
            if 'trend_strength' in analysis_data:
                signal_factors.append(float(analysis_data['trend_strength']))

            # التحقق من تأكيد المؤشرات
            if 'indicator_confirmation' in analysis_data:
                signal_factors.append(float(analysis_data['indicator_confirmation']))

            # التحقق من مستوى التقلبات
            if 'volatility' in analysis_data:
                volatility = float(analysis_data['volatility'])
                # التقلبات المتوسطة أفضل للتداول
                if 0.3 <= volatility <= 0.7:
                    signal_factors.append(0.8)
                else:
                    signal_factors.append(0.4)

            return np.mean(signal_factors) if signal_factors else 0.5

        except Exception as e:
            logger.error(f"خطأ في تقييم قوة الإشارة: {str(e)}")
            return 0.5

    def _evaluate_consistency(self, analysis_data: Dict) -> float:
        """تقييم الاتساق"""
        try:
            # التحقق من اتساق التوصيات مع المؤشرات
            consistency_score = 0.5

            if 'recommendation' in analysis_data and 'indicators' in analysis_data:
                recommendation = analysis_data['recommendation'].lower()
                indicators = analysis_data.get('indicators', {})

                bullish_indicators = 0
                bearish_indicators = 0

                # تحليل المؤشرات
                for indicator, value in indicators.items():
                    if value is None:
                        continue

                    try:
                        if 'rsi' in indicator.lower():
                            if float(value) > 70:
                                bearish_indicators += 1
                            elif float(value) < 30:
                                bullish_indicators += 1
                        elif 'macd' in indicator.lower():
                            if float(value) > 0:
                                bullish_indicators += 1
                            else:
                                bearish_indicators += 1
                    except:
                        continue

                # مقارنة مع التوصية
                if 'شراء' in recommendation or 'buy' in recommendation:
                    if bullish_indicators > bearish_indicators:
                        consistency_score = 0.9
                    elif bullish_indicators == bearish_indicators:
                        consistency_score = 0.6
                    else:
                        consistency_score = 0.3
                elif 'بيع' in recommendation or 'sell' in recommendation:
                    if bearish_indicators > bullish_indicators:
                        consistency_score = 0.9
                    elif bearish_indicators == bullish_indicators:
                        consistency_score = 0.6
                    else:
                        consistency_score = 0.3

            return consistency_score

        except Exception as e:
            logger.error(f"خطأ في تقييم الاتساق: {str(e)}")
            return 0.5

    def _calculate_overall_quality(self, accuracy: float, confidence: float,
                                 completeness: float, signal: float, consistency: float) -> float:
        """حساب الجودة الإجمالية"""
        # أوزان مختلفة للعوامل
        weights = {
            'accuracy': 0.25,
            'confidence': 0.20,
            'completeness': 0.15,
            'signal': 0.25,
            'consistency': 0.15
        }

        overall = (
            accuracy * weights['accuracy'] +
            confidence * weights['confidence'] +
            completeness * weights['completeness'] +
            signal * weights['signal'] +
            consistency * weights['consistency']
        )

        return min(max(overall, 0.0), 1.0)  # ضمان القيمة بين 0 و 1

    def get_quality_trend(self, hours: int = 24) -> Dict:
        """الحصول على اتجاه الجودة"""
        try:
            cutoff_time = time.time() - (hours * 3600)
            recent_qualities = [
                item['quality'] for item in self.quality_history
                if item['timestamp'] > cutoff_time
            ]

            if not recent_qualities:
                return {'trend': 'no_data', 'average_quality': 0.5}

            qualities = [q.overall_quality for q in recent_qualities]
            avg_quality = np.mean(qualities)

            # تحديد الاتجاه
            if len(qualities) >= 2:
                recent_avg = np.mean(qualities[-10:])  # آخر 10 تحليلات
                older_avg = np.mean(qualities[:-10]) if len(qualities) > 10 else avg_quality

                if recent_avg > older_avg + 0.05:
                    trend = 'improving'
                elif recent_avg < older_avg - 0.05:
                    trend = 'declining'
                else:
                    trend = 'stable'
            else:
                trend = 'insufficient_data'

            return {
                'trend': trend,
                'average_quality': avg_quality,
                'total_analyses': len(recent_qualities)
            }

        except Exception as e:
            logger.error(f"خطأ في حساب اتجاه الجودة: {str(e)}")
            return {'trend': 'error', 'average_quality': 0.5}

class AIModelManager:
    """مدير النماذج المتعددة للذكاء الاصطناعي"""

    def __init__(self):
        self.models = {
            'gemini-2.0-flash-exp': AIModelConfig(
                model_name='gemini-2.0-flash-exp',
                max_tokens=8192,
                temperature=0.3,
                timeout=30,
                retry_attempts=3,
                fallback_model='gemini-2.0-flash'
            ),
            'gemini-2.0-flash': AIModelConfig(
                model_name='gemini-2.0-flash',
                max_tokens=4096,
                temperature=0.4,
                timeout=25,
                retry_attempts=2,
                fallback_model=None
            )
        }

        self.model_performance: Dict[str, Dict] = defaultdict(lambda: {
            'total_requests': 0,
            'successful_requests': 0,
            'avg_response_time': 0.0,
            'error_count': 0,
            'last_used': 0
        })

        self.current_model = 'gemini-2.0-flash-exp'
        self.fallback_threshold = 0.7  # نسبة النجاح المطلوبة

    def get_best_model(self) -> str:
        """اختيار أفضل نموذج بناءً على الأداء"""
        try:
            best_model = self.current_model
            best_score = 0.0

            for model_name, stats in self.model_performance.items():
                if stats['total_requests'] == 0:
                    continue

                success_rate = stats['successful_requests'] / stats['total_requests']
                response_time_score = max(0, 1 - (stats['avg_response_time'] / 60))  # تطبيع وقت الاستجابة

                # حساب النقاط الإجمالية
                score = (success_rate * 0.7) + (response_time_score * 0.3)

                if score > best_score and success_rate >= self.fallback_threshold:
                    best_score = score
                    best_model = model_name

            return best_model

        except Exception as e:
            logger.error(f"خطأ في اختيار أفضل نموذج: {str(e)}")
            return self.current_model

    def record_model_performance(self, model_name: str, success: bool, response_time: float):
        """تسجيل أداء النموذج"""
        try:
            stats = self.model_performance[model_name]
            stats['total_requests'] += 1
            stats['last_used'] = time.time()

            if success:
                stats['successful_requests'] += 1
                # تحديث متوسط وقت الاستجابة
                current_avg = stats['avg_response_time']
                total_successful = stats['successful_requests']
                stats['avg_response_time'] = ((current_avg * (total_successful - 1)) + response_time) / total_successful
            else:
                stats['error_count'] += 1

            # التحقق من الحاجة للتبديل
            success_rate = stats['successful_requests'] / stats['total_requests']
            if success_rate < self.fallback_threshold and stats['total_requests'] >= 5:
                self._switch_to_fallback(model_name)

        except Exception as e:
            logger.error(f"خطأ في تسجيل أداء النموذج: {str(e)}")

    def _switch_to_fallback(self, failed_model: str):
        """التبديل إلى النموذج البديل"""
        try:
            if failed_model in self.models:
                fallback = self.models[failed_model].fallback_model
                if fallback and fallback in self.models:
                    logger.warning(f"تبديل من {failed_model} إلى {fallback} بسبب ضعف الأداء")
                    self.current_model = fallback

        except Exception as e:
            logger.error(f"خطأ في التبديل للنموذج البديل: {str(e)}")

    def get_model_config(self, model_name: str = None) -> AIModelConfig:
        """الحصول على تكوين النموذج"""
        model_name = model_name or self.get_best_model()
        return self.models.get(model_name, self.models[self.current_model])

    def get_performance_stats(self) -> Dict:
        """إحصائيات أداء جميع النماذج"""
        stats = {}
        for model_name, performance in self.model_performance.items():
            if performance['total_requests'] > 0:
                success_rate = performance['successful_requests'] / performance['total_requests']
                stats[model_name] = {
                    'success_rate': success_rate,
                    'avg_response_time': performance['avg_response_time'],
                    'total_requests': performance['total_requests'],
                    'error_count': performance['error_count']
                }
        return stats

class EnhancedAIAnalyzer:
    """المحلل المحسن للذكاء الاصطناعي"""

    def __init__(self, api_manager=None, db=None):
        """
        تهيئة المحلل المحسن

        Args:
            api_manager: مدير API
            db: قاعدة البيانات
        """
        self.api_manager = api_manager
        self.db = db

        # تهيئة المكونات
        self.cache = SmartCache(max_size=1000, ttl=3600)
        self.quality_evaluator = AnalysisQualityEvaluator()
        self.model_manager = AIModelManager()

        # إحصائيات الأداء
        self.performance_stats = {
            'total_analyses': 0,
            'cache_hits': 0,
            'avg_response_time': 0.0,
            'quality_scores': deque(maxlen=100)
        }

        # إعدادات التحسين
        self.optimization_settings = {
            'enable_caching': True,
            'enable_quality_check': True,
            'enable_parallel_processing': True,
            'max_concurrent_requests': 3
        }

        self.executor = ThreadPoolExecutor(max_workers=5)
        self.is_initialized = False

    async def initialize(self):
        """تهيئة المحلل"""
        try:
            if self.is_initialized:
                return True

            logger.info("🚀 جاري تهيئة المحلل المحسن للذكاء الاصطناعي...")

            # التحقق من المتطلبات
            if not self.api_manager:
                logger.warning("⚠️ مدير API غير متوفر - بعض الميزات قد لا تعمل")

            # تهيئة النماذج
            await self._initialize_models()

            # تحميل إعدادات التحسين من قاعدة البيانات
            await self._load_optimization_settings()

            self.is_initialized = True
            logger.info("✅ تم تهيئة المحلل المحسن بنجاح")
            return True

        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة المحلل المحسن: {str(e)}")
            return False

    async def _initialize_models(self):
        """تهيئة النماذج"""
        try:
            # اختبار النماذج المتاحة
            for model_name in self.model_manager.models.keys():
                try:
                    # اختبار بسيط للنموذج
                    if self.api_manager:
                        test_result = await self._test_model(model_name)
                        if test_result:
                            logger.info(f"✅ النموذج {model_name} متاح ويعمل")
                        else:
                            logger.warning(f"⚠️ النموذج {model_name} غير متاح")
                except Exception as e:
                    logger.error(f"❌ خطأ في اختبار النموذج {model_name}: {str(e)}")

        except Exception as e:
            logger.error(f"خطأ في تهيئة النماذج: {str(e)}")

    async def _test_model(self, model_name: str) -> bool:
        """اختبار النموذج"""
        try:
            # اختبار بسيط للنموذج
            if not self.api_manager:
                return False

            # محاولة الحصول على مفتاح API
            api_key = await self.api_manager.get_api_key('test_user', 'gemini')
            if not api_key:
                return False

            return True

        except Exception as e:
            logger.error(f"خطأ في اختبار النموذج {model_name}: {str(e)}")
            return False

    async def _load_optimization_settings(self):
        """تحميل إعدادات التحسين"""
        try:
            if not self.db:
                return

            # تحميل الإعدادات من قاعدة البيانات
            settings_ref = self.db.collection('system_settings').document('enhanced_ai_analyzer')
            settings_doc = settings_ref.get()

            if settings_doc.exists:
                saved_settings = settings_doc.to_dict()
                self.optimization_settings.update(saved_settings)
                logger.info("✅ تم تحميل إعدادات التحسين من قاعدة البيانات")
            else:
                # حفظ الإعدادات الافتراضية
                settings_ref.set(self.optimization_settings)
                logger.info("✅ تم حفظ الإعدادات الافتراضية")

        except Exception as e:
            logger.error(f"خطأ في تحميل إعدادات التحسين: {str(e)}")

    async def analyze_symbol(self, symbol: str, market_data: Dict, user_id: str,
                           analysis_type: str = 'comprehensive') -> Dict:
        """
        تحليل العملة باستخدام الذكاء الاصطناعي المحسن

        Args:
            symbol: رمز العملة
            market_data: بيانات السوق
            user_id: معرف المستخدم
            analysis_type: نوع التحليل

        Returns:
            نتيجة التحليل المحسن
        """
        start_time = time.time()

        try:
            if not self.is_initialized:
                await self.initialize()

            logger.info(f"🔍 بدء التحليل المحسن للعملة {symbol} للمستخدم {user_id}")

            # التحقق من التخزين المؤقت
            cached_result = None
            if self.optimization_settings['enable_caching']:
                cached_result = self.cache.get(symbol, '4h', analysis_type, user_id=user_id)
                if cached_result:
                    self.performance_stats['cache_hits'] += 1
                    logger.info(f"✅ تم العثور على نتيجة محفوظة للعملة {symbol}")
                    return self._decompress_cached_result(cached_result)

            # تحليل جديد
            analysis_result = await self._perform_enhanced_analysis(
                symbol, market_data, user_id, analysis_type
            )

            # تقييم الجودة
            if self.optimization_settings['enable_quality_check']:
                quality = self.quality_evaluator.evaluate_analysis_quality(
                    analysis_result, market_data
                )
                analysis_result['quality_metrics'] = asdict(quality)
                self.performance_stats['quality_scores'].append(quality.overall_quality)

            # حفظ في التخزين المؤقت
            if self.optimization_settings['enable_caching']:
                self.cache.set(symbol, '4h', analysis_type, analysis_result, user_id=user_id)

            # تحديث الإحصائيات
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time, True)

            logger.info(f"✅ تم إكمال التحليل المحسن للعملة {symbol} في {execution_time:.2f} ثانية")

            return analysis_result

        except Exception as e:
            execution_time = time.time() - start_time
            self._update_performance_stats(execution_time, False)
            logger.error(f"❌ خطأ في التحليل المحسن للعملة {symbol}: {str(e)}")

            # إرجاع تحليل أساسي في حالة الخطأ
            return await self._fallback_analysis(symbol, market_data, user_id)

    async def _perform_enhanced_analysis(self, symbol: str, market_data: Dict,
                                       user_id: str, analysis_type: str) -> Dict:
        """تنفيذ التحليل المحسن"""
        try:
            # اختيار أفضل نموذج
            best_model = self.model_manager.get_best_model()
            model_config = self.model_manager.get_model_config(best_model)

            logger.info(f"🤖 استخدام النموذج {best_model} للتحليل")

            # إعداد البيانات للتحليل
            analysis_prompt = await self._prepare_analysis_prompt(
                symbol, market_data, analysis_type
            )

            # تنفيذ التحليل
            model_start_time = time.time()
            analysis_result = await self._call_ai_model(
                best_model, analysis_prompt, user_id
            )
            model_response_time = time.time() - model_start_time

            # تسجيل أداء النموذج
            self.model_manager.record_model_performance(
                best_model, True, model_response_time
            )

            # معالجة وتحسين النتيجة
            enhanced_result = await self._enhance_analysis_result(
                analysis_result, market_data, symbol
            )

            return enhanced_result

        except Exception as e:
            logger.error(f"خطأ في تنفيذ التحليل المحسن: {str(e)}")
            # تسجيل فشل النموذج
            if 'best_model' in locals():
                self.model_manager.record_model_performance(best_model, False, 0)
            raise

    async def _prepare_analysis_prompt(self, symbol: str, market_data: Dict, analysis_type: str) -> str:
        """إعداد prompt للتحليل"""
        try:
            # استخراج البيانات المهمة
            price = market_data.get('price', 0)
            volume = market_data.get('volume', 0)
            indicators = market_data.get('indicators', {})

            prompt = f"""
            قم بتحليل العملة الرقمية {symbol} بناءً على البيانات التالية:

            السعر الحالي: {price}
            الحجم: {volume}

            المؤشرات الفنية:
            """

            # إضافة المؤشرات
            for indicator, value in indicators.items():
                if value is not None:
                    prompt += f"- {indicator}: {value}\n"

            # إضافة نوع التحليل المطلوب
            if analysis_type == 'comprehensive':
                prompt += """

                المطلوب تحليل شامل يتضمن:
                1. تحليل الاتجاه العام
                2. نقاط الدخول والخروج
                3. مستويات الدعم والمقاومة
                4. تقييم المخاطر
                5. التوصية النهائية

                يرجى تقديم التحليل باللغة العربية بشكل مفصل ومنظم.
                """

            return prompt

        except Exception as e:
            logger.error(f"خطأ في إعداد prompt: {str(e)}")
            return f"تحليل العملة {symbol}"

    async def _call_ai_model(self, model_name: str, prompt: str, user_id: str) -> str:
        """استدعاء نموذج الذكاء الاصطناعي"""
        try:
            if not self.api_manager:
                raise Exception("مدير API غير متوفر")

            # الحصول على مفتاح API
            api_key = await self.api_manager.get_api_key(user_id, 'gemini')
            if not api_key:
                raise Exception("مفتاح Gemini API غير متوفر")

            # استيراد وحدة Gemini
            from analysis.gemini_analysis import analyze_with_gemini

            # استدعاء النموذج
            result = await analyze_with_gemini(
                prompt,
                api_key,
                model_name=model_name
            )

            # تطبيق حل التنسيق المبهر على النتيجة
            try:
                from utils.utils import fix_bold_formatting
                result = fix_bold_formatting(result, 'ar')
            except ImportError:
                logger.warning("لم يتم العثور على دالة fix_bold_formatting")

            return result

        except Exception as e:
            logger.error(f"خطأ في استدعاء نموذج AI: {str(e)}")
            raise

    async def _enhance_analysis_result(self, analysis_result: str, market_data: Dict, symbol: str) -> Dict:
        """تحسين وتنظيم نتيجة التحليل"""
        try:
            enhanced_result = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'analysis_type': 'enhanced_ai',
                'raw_analysis': analysis_result,
                'market_data_snapshot': {
                    'price': market_data.get('price'),
                    'volume': market_data.get('volume'),
                    'timestamp': market_data.get('timestamp')
                }
            }

            # استخراج المعلومات المهيكلة من التحليل
            structured_data = await self._extract_structured_data(analysis_result)
            enhanced_result.update(structured_data)

            # إضافة معلومات إضافية
            enhanced_result['performance_metrics'] = {
                'cache_hit_rate': self.cache.get_stats()['hit_rate'],
                'model_used': self.model_manager.get_best_model(),
                'quality_trend': self.quality_evaluator.get_quality_trend()
            }

            return enhanced_result

        except Exception as e:
            logger.error(f"خطأ في تحسين نتيجة التحليل: {str(e)}")
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'analysis_type': 'enhanced_ai',
                'raw_analysis': analysis_result,
                'error': str(e)
            }

    async def _extract_structured_data(self, analysis_text: str) -> Dict:
        """استخراج البيانات المهيكلة من النص"""
        try:
            structured = {
                'recommendation': 'انتظار',
                'confidence_level': 0.5,
                'risk_level': 'متوسط',
                'entry_points': [],
                'exit_points': [],
                'support_levels': [],
                'resistance_levels': []
            }

            # استخراج التوصية
            text_lower = analysis_text.lower()
            if any(word in text_lower for word in ['شراء قوي', 'strong buy', 'شراء']):
                structured['recommendation'] = 'شراء'
                structured['confidence_level'] = 0.8
            elif any(word in text_lower for word in ['بيع قوي', 'strong sell', 'بيع']):
                structured['recommendation'] = 'بيع'
                structured['confidence_level'] = 0.8
            elif any(word in text_lower for word in ['انتظار', 'hold', 'محايد']):
                structured['recommendation'] = 'انتظار'
                structured['confidence_level'] = 0.6

            # استخراج مستوى المخاطر
            if any(word in text_lower for word in ['مخاطر عالية', 'high risk']):
                structured['risk_level'] = 'عالي'
            elif any(word in text_lower for word in ['مخاطر منخفضة', 'low risk']):
                structured['risk_level'] = 'منخفض'

            return structured

        except Exception as e:
            logger.error(f"خطأ في استخراج البيانات المهيكلة: {str(e)}")
            return {}

    async def _fallback_analysis(self, symbol: str, market_data: Dict, user_id: str) -> Dict:
        """تحليل احتياطي في حالة فشل التحليل المحسن"""
        try:
            logger.info(f"🔄 تنفيذ تحليل احتياطي للعملة {symbol}")

            # تحليل أساسي بناءً على المؤشرات
            indicators = market_data.get('indicators', {})
            price = market_data.get('price', 0)

            recommendation = 'انتظار'
            confidence = 0.5

            # تحليل بسيط بناءً على RSI
            if 'rsi' in indicators:
                rsi = float(indicators['rsi'])
                if rsi < 30:
                    recommendation = 'شراء'
                    confidence = 0.7
                elif rsi > 70:
                    recommendation = 'بيع'
                    confidence = 0.7

            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'analysis_type': 'fallback',
                'recommendation': recommendation,
                'confidence_level': confidence,
                'raw_analysis': f'تحليل احتياطي للعملة {symbol} - السعر: {price}',
                'is_fallback': True
            }

        except Exception as e:
            logger.error(f"خطأ في التحليل الاحتياطي: {str(e)}")
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'analysis_type': 'error',
                'error': str(e)
            }

    def _update_performance_stats(self, execution_time: float, success: bool):
        """تحديث إحصائيات الأداء"""
        try:
            self.performance_stats['total_analyses'] += 1

            if success:
                # تحديث متوسط وقت الاستجابة
                current_avg = self.performance_stats['avg_response_time']
                total_analyses = self.performance_stats['total_analyses']

                self.performance_stats['avg_response_time'] = (
                    (current_avg * (total_analyses - 1)) + execution_time
                ) / total_analyses

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الأداء: {str(e)}")

    def _decompress_cached_result(self, cached_data: Dict) -> Dict:
        """إلغاء ضغط النتيجة المحفوظة"""
        try:
            if cached_data.get('compressed', False):
                return self.cache._decompress_data(cached_data)
            return cached_data
        except Exception as e:
            logger.error(f"خطأ في إلغاء ضغط البيانات المحفوظة: {str(e)}")
            return cached_data

    def get_performance_summary(self) -> Dict:
        """ملخص الأداء"""
        try:
            cache_stats = self.cache.get_stats()
            model_stats = self.model_manager.get_performance_stats()
            quality_trend = self.quality_evaluator.get_quality_trend()

            avg_quality = 0.0
            if self.performance_stats['quality_scores']:
                avg_quality = np.mean(list(self.performance_stats['quality_scores']))

            return {
                'total_analyses': self.performance_stats['total_analyses'],
                'avg_response_time': self.performance_stats['avg_response_time'],
                'cache_hit_rate': cache_stats['hit_rate'],
                'avg_quality_score': avg_quality,
                'quality_trend': quality_trend['trend'],
                'model_performance': model_stats,
                'optimization_settings': self.optimization_settings
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء ملخص الأداء: {str(e)}")
            return {'error': str(e)}

    async def cleanup(self):
        """تنظيف الموارد"""
        try:
            logger.info("🧹 تنظيف موارد المحلل المحسن...")

            # إغلاق ThreadPoolExecutor
            if hasattr(self, 'executor'):
                self.executor.shutdown(wait=True)

            # مسح التخزين المؤقت
            self.cache.clear()

            # حفظ الإحصائيات
            if self.db:
                await self._save_performance_stats()

            logger.info("✅ تم تنظيف موارد المحلل المحسن")

        except Exception as e:
            logger.error(f"خطأ في تنظيف الموارد: {str(e)}")

    async def _save_performance_stats(self):
        """حفظ إحصائيات الأداء"""
        try:
            stats_ref = self.db.collection('system_stats').document('enhanced_ai_analyzer')
            stats_data = {
                'performance_summary': self.get_performance_summary(),
                'last_updated': datetime.now().isoformat()
            }
            stats_ref.set(stats_data)

        except Exception as e:
            logger.error(f"خطأ في حفظ إحصائيات الأداء: {str(e)}")

# متغيرات عامة للتكامل
_enhanced_ai_analyzer = None

def get_enhanced_ai_analyzer() -> Optional[EnhancedAIAnalyzer]:
    """الحصول على مثيل المحلل المحسن"""
    global _enhanced_ai_analyzer
    return _enhanced_ai_analyzer

def initialize_enhanced_ai_analyzer(api_manager=None, db=None) -> EnhancedAIAnalyzer:
    """تهيئة المحلل المحسن"""
    global _enhanced_ai_analyzer

    if _enhanced_ai_analyzer is None:
        _enhanced_ai_analyzer = EnhancedAIAnalyzer(api_manager, db)

    return _enhanced_ai_analyzer
