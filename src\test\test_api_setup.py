#!/usr/bin/env python3
"""
اختبار نظام إعداد مفاتيح API
"""

import asyncio
import logging
from unittest.mock import Mock, AsyncMock
from telegram import Update, CallbackQuery, User, Message, Chat
from telegram.ext import CallbackContext

# إعداد السجل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestAPISetup:
    """فئة اختبار نظام إعداد مفاتيح API"""
    
    def __init__(self):
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """إعداد بيئة الاختبار"""
        # إنشاء mock objects
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 123456789
        self.mock_user.first_name = "Test User"
        
        self.mock_chat = Mock(spec=Chat)
        self.mock_chat.id = 123456789
        
        self.mock_message = Mock(spec=Message)
        self.mock_message.chat = self.mock_chat
        self.mock_message.from_user = self.mock_user
        self.mock_message.reply_text = AsyncMock()
        self.mock_message.edit_text = AsyncMock()
        
        self.mock_callback_query = Mock(spec=CallbackQuery)
        self.mock_callback_query.from_user = self.mock_user
        self.mock_callback_query.message = self.mock_message
        self.mock_callback_query.answer = AsyncMock()
        self.mock_callback_query.edit_message_text = AsyncMock()
        
        self.mock_update = Mock(spec=Update)
        self.mock_update.effective_user = self.mock_user
        self.mock_update.callback_query = self.mock_callback_query
        self.mock_update.message = self.mock_message
        
        self.mock_context = Mock(spec=CallbackContext)
        
        logger.info("✅ تم إعداد بيئة الاختبار")
    
    def test_callback_patterns(self):
        """اختبار patterns الخاصة بـ callback queries"""
        import re
        
        # Pattern المستخدم في telegram_bot.py
        api_pattern = r'^(setup_api_keys|select_platform|setup_.*_api|delete_api_keys|delete_.*_api)$'
        
        # قائمة callback_data المتوقعة
        test_callbacks = [
            'setup_api_keys',
            'select_platform',
            'setup_binance_api',
            'setup_gemini_api',
            'setup_kucoin_api',
            'setup_coinbase_api',
            'setup_bybit_api',
            'setup_okx_api',
            'setup_kraken_api',
            'delete_api_keys',
            'delete_binance_api',
            'delete_gemini_api',
            'delete_kucoin_api',
            'delete_coinbase_api',
            'delete_bybit_api',
            'delete_okx_api',
            'delete_kraken_api'
        ]
        
        # اختبار كل callback
        pattern = re.compile(api_pattern)
        failed_tests = []
        
        for callback in test_callbacks:
            if not pattern.match(callback):
                failed_tests.append(callback)
                logger.error(f"❌ فشل في مطابقة: {callback}")
            else:
                logger.info(f"✅ نجح في مطابقة: {callback}")
        
        if failed_tests:
            logger.error(f"❌ فشل في اختبار patterns: {failed_tests}")
            return False
        else:
            logger.info("✅ جميع patterns تعمل بشكل صحيح")
            return True
    
    async def test_api_setup_flow(self):
        """اختبار تدفق إعداد API"""
        try:
            # محاكاة النقر على زر Gemini
            self.mock_callback_query.data = 'setup_gemini_api'
            
            # محاكاة استيراد الدوال المطلوبة
            logger.info("🔄 اختبار تدفق إعداد Gemini API...")
            
            # التحقق من أن callback_data يطابق pattern
            import re
            api_pattern = r'^(setup_api_keys|select_platform|setup_.*_api|delete_api_keys|delete_.*_api)$'
            if re.match(api_pattern, self.mock_callback_query.data):
                logger.info("✅ callback_data يطابق pattern")
            else:
                logger.error("❌ callback_data لا يطابق pattern")
                return False
            
            # محاكاة معالجة الطلب
            logger.info("✅ تم اختبار تدفق إعداد API بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار تدفق API: {str(e)}")
            return False
    
    def test_user_states_management(self):
        """اختبار إدارة حالات المستخدم"""
        try:
            # محاكاة user_states
            user_states = {}
            user_id = str(self.mock_user.id)
            
            # اختبار تعيين حالة API setup
            user_states[user_id] = {'api_setup_state': 'gemini_key'}
            
            # التحقق من الحالة
            if user_id in user_states and 'api_setup_state' in user_states[user_id]:
                logger.info("✅ تم تعيين حالة المستخدم بنجاح")
                
                # اختبار تحديث الحالة
                user_states[user_id] = {'api_setup_state': 'gemini_secret'}
                
                if user_states[user_id]['api_setup_state'] == 'gemini_secret':
                    logger.info("✅ تم تحديث حالة المستخدم بنجاح")
                    return True
                else:
                    logger.error("❌ فشل في تحديث حالة المستخدم")
                    return False
            else:
                logger.error("❌ فشل في تعيين حالة المستخدم")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار إدارة حالات المستخدم: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🚀 بدء تشغيل اختبارات نظام إعداد API...")
        
        tests = [
            ("اختبار patterns", self.test_callback_patterns),
            ("اختبار تدفق إعداد API", self.test_api_setup_flow),
            ("اختبار إدارة حالات المستخدم", self.test_user_states_management)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"🔄 تشغيل {test_name}...")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                if result:
                    logger.info(f"✅ {test_name}: نجح")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: فشل")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ {test_name}: خطأ - {str(e)}")
                failed += 1
        
        logger.info(f"📊 نتائج الاختبار: {passed} نجح، {failed} فشل")
        
        if failed == 0:
            logger.info("🎉 جميع الاختبارات نجحت!")
            return True
        else:
            logger.error("❌ بعض الاختبارات فشلت")
            return False

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = TestAPISetup()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 جميع اختبارات نظام إعداد API نجحت!")
        print("✅ النظام جاهز للاستخدام")
    else:
        print("\n❌ بعض اختبارات نظام إعداد API فشلت")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    asyncio.run(main())
