#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الإشعارات التلقائية الجديد
"""

import asyncio
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.automatic_news_notifications import AutomaticNewsNotifications, NotificationType, NotificationPriority
from src.integrations.firebase_init import initialize_firebase

async def test_automatic_notifications():
    """اختبار نظام الإشعارات التلقائية"""
    print("🔔 بدء اختبار نظام الإشعارات التلقائية...")
    
    try:
        # تهيئة قاعدة البيانات
        db = initialize_firebase()
        if not db:
            print("❌ فشل في تهيئة قاعدة البيانات")
            return
        
        # إنشاء نظام الإشعارات
        notifications_system = AutomaticNewsNotifications(db=db)
        
        # تحميل قواعد المستخدمين
        await notifications_system.load_user_rules()
        print("✅ تم تحميل قواعد المستخدمين")
        
        # اختبار الحصول على المستخدمين النشطين
        active_users = await notifications_system._get_all_active_users()
        print(f"👥 عدد المستخدمين النشطين: {len(active_users)}")
        
        # اختبار تفعيل الإشعارات لجميع المستخدمين
        print("🔄 تفعيل الإشعارات لجميع المستخدمين...")
        success = await notifications_system.enable_automatic_notifications_for_all_users()
        
        if success:
            print("✅ تم تفعيل الإشعارات لجميع المستخدمين بنجاح")
        else:
            print("⚠️ حدثت مشاكل في تفعيل الإشعارات")
        
        # عرض إحصائيات النظام
        stats = await notifications_system.get_notification_stats()
        print(f"📊 إحصائيات النظام:")
        print(f"   - المستخدمين النشطين: {stats.get('active_users', 0)}")
        print(f"   - إجمالي القواعد: {stats.get('total_rules', 0)}")
        print(f"   - الإشعارات المرسلة: {stats.get('total_sent', 0)}")
        print(f"   - معدل النجاح: {stats.get('success_rate', 0):.1f}%")
        
        print("✅ اكتمل اختبار نظام الإشعارات التلقائية بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام الإشعارات: {str(e)}")

async def test_user_notification_setup():
    """اختبار إعداد إشعارات مستخدم جديد"""
    print("\n🆕 اختبار إعداد إشعارات مستخدم جديد...")
    
    try:
        db = initialize_firebase()
        notifications_system = AutomaticNewsNotifications(db=db)
        
        # اختبار تفعيل الإشعارات لمستخدم جديد
        test_user_id = "test_user_12345"
        success = await notifications_system.enable_automatic_notifications_for_user(test_user_id)
        
        if success:
            print(f"✅ تم تفعيل الإشعارات للمستخدم {test_user_id}")
            
            # التحقق من القواعد المضافة
            if test_user_id in notifications_system.user_rules:
                rules = notifications_system.user_rules[test_user_id]
                print(f"📋 تم إضافة {len(rules)} قاعدة إشعار:")
                for rule in rules:
                    print(f"   - {rule.notification_type.value}: {rule.min_priority.value}")
            else:
                print("⚠️ لم يتم العثور على قواعد للمستخدم")
        else:
            print(f"❌ فشل في تفعيل الإشعارات للمستخدم {test_user_id}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار إعداد المستخدم: {str(e)}")

if __name__ == "__main__":
    print("🚀 بدء اختبارات نظام الإشعارات التلقائية الجديد")
    print("=" * 60)
    
    asyncio.run(test_automatic_notifications())
    asyncio.run(test_user_notification_setup())
    
    print("=" * 60)
    print("🎉 انتهت جميع الاختبارات")
