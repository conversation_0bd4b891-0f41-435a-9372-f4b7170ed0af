#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة اللغة في رسائل إعداد API
"""

import asyncio
import logging
from unittest.mock import Mock, AsyncMock

# إعداد السجل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestLanguageFix:
    """فئة اختبار إصلاح مشكلة اللغة"""
    
    def __init__(self):
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """إعداد بيئة الاختبار"""
        # محاكاة subscription_system
        self.mock_subscription_system = Mock()
        
        logger.info("✅ تم إعداد بيئة الاختبار")
    
    def test_language_detection_arabic(self):
        """اختبار تحديد اللغة العربية"""
        try:
            # محاكاة مستخدم اختار العربية
            user_id = "123456789"
            self.mock_subscription_system.get_user_settings.return_value = {
                'lang': 'ar',
                'analysis_type': 'ai'
            }
            
            # الحصول على إعدادات المستخدم
            settings = self.mock_subscription_system.get_user_settings(user_id)
            lang = settings.get('lang', 'ar') if settings else 'ar'
            
            # التحقق من اللغة
            if lang == 'ar':
                logger.info("✅ تم تحديد اللغة العربية بشكل صحيح")
                
                # اختبار رسالة نجاح Gemini بالعربية
                success_message = (
                    "✅ تم إعداد Gemini API بنجاح! يمكنك الآن استخدام ميزات التحليل المتقدم." if lang == 'ar' else
                    "✅ Gemini API setup successful! You can now use advanced analysis features."
                )
                
                if "تم إعداد Gemini API بنجاح" in success_message:
                    logger.info("✅ رسالة نجاح Gemini بالعربية صحيحة")
                    return True
                else:
                    logger.error("❌ رسالة نجاح Gemini بالعربية خاطئة")
                    return False
            else:
                logger.error(f"❌ تم تحديد لغة خاطئة: {lang}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار تحديد اللغة العربية: {str(e)}")
            return False
    
    def test_language_detection_english(self):
        """اختبار تحديد اللغة الإنجليزية"""
        try:
            # محاكاة مستخدم اختار الإنجليزية
            user_id = "987654321"
            self.mock_subscription_system.get_user_settings.return_value = {
                'lang': 'en',
                'analysis_type': 'ai'
            }
            
            # الحصول على إعدادات المستخدم
            settings = self.mock_subscription_system.get_user_settings(user_id)
            lang = settings.get('lang', 'ar') if settings else 'ar'
            
            # التحقق من اللغة
            if lang == 'en':
                logger.info("✅ تم تحديد اللغة الإنجليزية بشكل صحيح")
                
                # اختبار رسالة نجاح Gemini بالإنجليزية
                success_message = (
                    "✅ تم إعداد Gemini API بنجاح! يمكنك الآن استخدام ميزات التحليل المتقدم." if lang == 'ar' else
                    "✅ Gemini API setup successful! You can now use advanced analysis features."
                )
                
                if "Gemini API setup successful" in success_message:
                    logger.info("✅ رسالة نجاح Gemini بالإنجليزية صحيحة")
                    return True
                else:
                    logger.error("❌ رسالة نجاح Gemini بالإنجليزية خاطئة")
                    return False
            else:
                logger.error(f"❌ تم تحديد لغة خاطئة: {lang}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار تحديد اللغة الإنجليزية: {str(e)}")
            return False
    
    def test_language_priority(self):
        """اختبار أولوية مصدر اللغة"""
        try:
            # محاكاة حالة تضارب في مصادر اللغة
            user_id = "555666777"
            
            # إعدادات قاعدة البيانات (المصدر الصحيح)
            self.mock_subscription_system.get_user_settings.return_value = {
                'lang': 'en',
                'analysis_type': 'ai'
            }
            
            # إعدادات محلية قديمة (يجب تجاهلها)
            local_user_settings = {
                user_id: {
                    'lang': 'ar',  # لغة قديمة
                    'currencies': [],
                    'indicators': []
                }
            }
            
            # الحصول على اللغة من قاعدة البيانات (المصدر الصحيح)
            settings = self.mock_subscription_system.get_user_settings(user_id)
            lang_from_db = settings.get('lang', 'ar') if settings else 'ar'
            
            # الحصول على اللغة من الإعدادات المحلية (المصدر القديم)
            lang_from_local = local_user_settings[user_id].get('lang', 'ar')
            
            # التحقق من أن اللغة من قاعدة البيانات لها الأولوية
            if lang_from_db == 'en' and lang_from_local == 'ar':
                logger.info("✅ أولوية اللغة من قاعدة البيانات صحيحة")
                
                # بعد الإصلاح، يجب استخدام lang_from_db
                final_lang = lang_from_db  # هذا ما يحدث بعد الإصلاح
                
                if final_lang == 'en':
                    logger.info("✅ تم استخدام اللغة الصحيحة من قاعدة البيانات")
                    return True
                else:
                    logger.error(f"❌ تم استخدام لغة خاطئة: {final_lang}")
                    return False
            else:
                logger.error("❌ فشل في إعداد اختبار أولوية اللغة")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار أولوية اللغة: {str(e)}")
            return False
    
    def test_gemini_messages_all_languages(self):
        """اختبار رسائل Gemini بجميع اللغات"""
        try:
            test_cases = [
                {
                    'lang': 'ar',
                    'expected_success': "تم إعداد Gemini API بنجاح",
                    'expected_error': "مفتاح API غير صالح"
                },
                {
                    'lang': 'en', 
                    'expected_success': "Gemini API setup successful",
                    'expected_error': "API key is invalid"
                }
            ]
            
            all_passed = True
            
            for case in test_cases:
                lang = case['lang']
                
                # رسالة النجاح
                success_message = (
                    "✅ تم إعداد Gemini API بنجاح! يمكنك الآن استخدام ميزات التحليل المتقدم." if lang == 'ar' else
                    "✅ Gemini API setup successful! You can now use advanced analysis features."
                )
                
                # رسالة الخطأ
                error_message = (
                    f"❌ مفتاح API غير صالح. يرجى التحقق والمحاولة مرة أخرى." if lang == 'ar' else
                    f"❌ API key is invalid. Please check and try again."
                )
                
                # التحقق من رسالة النجاح
                if case['expected_success'] in success_message:
                    logger.info(f"✅ رسالة نجاح Gemini باللغة {lang} صحيحة")
                else:
                    logger.error(f"❌ رسالة نجاح Gemini باللغة {lang} خاطئة")
                    all_passed = False
                
                # التحقق من رسالة الخطأ
                if case['expected_error'] in error_message:
                    logger.info(f"✅ رسالة خطأ Gemini باللغة {lang} صحيحة")
                else:
                    logger.error(f"❌ رسالة خطأ Gemini باللغة {lang} خاطئة")
                    all_passed = False
            
            return all_passed
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار رسائل Gemini: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🚀 بدء تشغيل اختبارات إصلاح مشكلة اللغة...")
        
        tests = [
            ("اختبار تحديد اللغة العربية", self.test_language_detection_arabic),
            ("اختبار تحديد اللغة الإنجليزية", self.test_language_detection_english),
            ("اختبار أولوية مصدر اللغة", self.test_language_priority),
            ("اختبار رسائل Gemini بجميع اللغات", self.test_gemini_messages_all_languages)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"🔄 تشغيل {test_name}...")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                if result:
                    logger.info(f"✅ {test_name}: نجح")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: فشل")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ {test_name}: خطأ - {str(e)}")
                failed += 1
        
        logger.info(f"📊 نتائج الاختبار: {passed} نجح، {failed} فشل")
        
        if failed == 0:
            logger.info("🎉 جميع اختبارات إصلاح اللغة نجحت!")
            return True
        else:
            logger.error("❌ بعض اختبارات إصلاح اللغة فشلت")
            return False

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = TestLanguageFix()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 جميع اختبارات إصلاح اللغة نجحت!")
        print("✅ مشكلة اللغة في رسائل إعداد API تم حلها")
        print("🌐 الآن ستظهر الرسائل باللغة الصحيحة التي اختارها المستخدم")
    else:
        print("\n❌ بعض اختبارات إصلاح اللغة فشلت")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    asyncio.run(main())
