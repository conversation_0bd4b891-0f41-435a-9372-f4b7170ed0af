#!/usr/bin/env python3
"""
سكريبت للتحقق من حالة قاعدة البيانات ومجموعات المستخدمين
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_check.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

class DatabaseChecker:
    """فئة للتحقق من حالة قاعدة البيانات"""
    
    def __init__(self):
        self.db = None
        self.firebase_available = False
        
    def initialize_firebase(self):
        """تهيئة Firebase"""
        try:
            from integrations.firebase_init import initialize_firebase
            self.db = initialize_firebase()
            if self.db:
                self.firebase_available = True
                logger.info("✅ تم تهيئة Firebase بنجاح")
                return True
            else:
                logger.warning("⚠️ فشل في تهيئة Firebase")
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة Firebase: {str(e)}")
            return False
    
    async def check_collection_stats(self, collection_name: str) -> dict:
        """فحص إحصائيات مجموعة معينة"""
        if not self.firebase_available:
            return {}
        
        try:
            collection_ref = self.db.collection(collection_name)
            docs = list(collection_ref.limit(1000).stream())  # حد أقصى 1000 مستند للفحص
            
            stats = {
                'total_documents': len(docs),
                'documents_with_lang': 0,
                'documents_with_language': 0,
                'lang_values': {},
                'language_values': {},
                'sample_documents': []
            }
            
            for doc in docs[:10]:  # عينة من أول 10 مستندات
                data = doc.to_dict()
                doc_info = {
                    'id': doc.id,
                    'has_lang': 'lang' in data,
                    'has_language': 'language' in data,
                    'lang_value': data.get('lang', 'N/A'),
                    'language_value': data.get('language', 'N/A')
                }
                stats['sample_documents'].append(doc_info)
                
                if 'lang' in data:
                    stats['documents_with_lang'] += 1
                    lang_val = data['lang']
                    stats['lang_values'][lang_val] = stats['lang_values'].get(lang_val, 0) + 1
                
                if 'language' in data:
                    stats['documents_with_language'] += 1
                    lang_val = data['language']
                    stats['language_values'][lang_val] = stats['language_values'].get(lang_val, 0) + 1
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في فحص المجموعة {collection_name}: {str(e)}")
            return {}
    
    async def check_all_collections(self):
        """فحص جميع المجموعات المهمة"""
        collections_to_check = [
            'subscription_system',
            'user_settings', 
            'users',
            'notifications',
            'user_preferences'
        ]
        
        logger.info("🔍 فحص المجموعات في قاعدة البيانات...")
        print("\n" + "="*80)
        
        for collection_name in collections_to_check:
            logger.info(f"📂 فحص المجموعة: {collection_name}")
            stats = await self.check_collection_stats(collection_name)
            
            if stats:
                print(f"\n📊 إحصائيات المجموعة: {collection_name}")
                print(f"   📄 إجمالي المستندات: {stats['total_documents']}")
                print(f"   🏷️ مستندات بحقل 'lang': {stats['documents_with_lang']}")
                print(f"   🏷️ مستندات بحقل 'language': {stats['documents_with_language']}")
                
                if stats['lang_values']:
                    print(f"   📈 قيم 'lang': {stats['lang_values']}")
                
                if stats['language_values']:
                    print(f"   📈 قيم 'language': {stats['language_values']}")
                
                if stats['sample_documents']:
                    print(f"   📋 عينة من المستندات:")
                    for i, doc in enumerate(stats['sample_documents'][:5], 1):
                        print(f"      {i}. ID: {doc['id'][:20]}...")
                        print(f"         lang: {doc['lang_value']}, language: {doc['language_value']}")
            else:
                print(f"   ❌ لا يمكن الوصول للمجموعة {collection_name} أو هي فارغة")
            
            print("-" * 60)
    
    async def check_specific_user(self, user_id: str):
        """فحص مستخدم محدد عبر جميع المجموعات"""
        if not self.firebase_available:
            logger.warning("Firebase غير متوفر")
            return
        
        collections_to_check = [
            'subscription_system',
            'user_settings', 
            'users',
            'notifications',
            'user_preferences'
        ]
        
        print(f"\n🔍 فحص المستخدم: {user_id}")
        print("="*60)
        
        for collection_name in collections_to_check:
            try:
                doc_ref = self.db.collection(collection_name).document(user_id)
                doc = doc_ref.get()
                
                if doc.exists:
                    data = doc.to_dict()
                    print(f"\n📂 {collection_name}:")
                    print(f"   ✅ المستند موجود")
                    print(f"   🏷️ lang: {data.get('lang', 'غير موجود')}")
                    print(f"   🏷️ language: {data.get('language', 'غير موجود')}")
                    
                    # عرض بعض الحقول الأخرى المهمة
                    other_fields = ['created_at', 'updated_at', 'active', 'telegram_id']
                    for field in other_fields:
                        if field in data:
                            print(f"   📋 {field}: {data[field]}")
                else:
                    print(f"\n📂 {collection_name}: ❌ المستند غير موجود")
                    
            except Exception as e:
                print(f"\n📂 {collection_name}: ❌ خطأ في الوصول - {str(e)}")
    
    async def find_sample_users(self, limit: int = 5):
        """العثور على عينة من المستخدمين للاختبار"""
        if not self.firebase_available:
            return []
        
        sample_users = []
        
        # البحث في subscription_system أولاً
        try:
            docs = self.db.collection('subscription_system').limit(limit).stream()
            for doc in docs:
                sample_users.append(doc.id)
        except Exception as e:
            logger.error(f"خطأ في البحث في subscription_system: {str(e)}")
        
        # إذا لم نجد مستخدمين، نبحث في users
        if not sample_users:
            try:
                docs = self.db.collection('users').limit(limit).stream()
                for doc in docs:
                    sample_users.append(doc.id)
            except Exception as e:
                logger.error(f"خطأ في البحث في users: {str(e)}")
        
        return sample_users

async def main():
    """الدالة الرئيسية"""
    print("🔍 بدء فحص حالة قاعدة البيانات")
    print("=" * 60)
    
    checker = DatabaseChecker()
    
    if not checker.initialize_firebase():
        print("❌ لا يمكن المتابعة بدون Firebase")
        return
    
    try:
        # فحص جميع المجموعات
        await checker.check_all_collections()
        
        # العثور على عينة من المستخدمين
        sample_users = await checker.find_sample_users(3)
        
        if sample_users:
            print(f"\n🧪 فحص عينة من المستخدمين ({len(sample_users)} مستخدمين):")
            for user_id in sample_users:
                await checker.check_specific_user(user_id)
        else:
            print("\n⚠️ لم يتم العثور على أي مستخدمين في قاعدة البيانات")
        
        print("\n✅ تم إكمال فحص قاعدة البيانات!")
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الفحص بواسطة المستخدم")
        
    except Exception as e:
        print(f"\n❌ خطأ في فحص قاعدة البيانات: {str(e)}")
        logger.error(f"خطأ عام: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
