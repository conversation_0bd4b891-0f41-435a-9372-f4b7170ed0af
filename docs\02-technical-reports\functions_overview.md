# نظرة عامة على الدوال - TradingTelegram Project

هذا المستند يقدم ملخصاً للفئات والدوال الرئيسية المعرفة في ملفات المشروع. يتم تجميع الدوال والفئات تحت اسم الملف الذي تم تعريفها فيه.

## الدوال والفئات - main.py

هذا القسم يصف الفئات والدوال المعرفة مباشرة داخل ملف `main.py` والتي تمثل المنطق الرئيسي لتشغيل البوت وتوجيه الأوامر.

## الفئات (Classes)

### `SystemConfig`
*فئة للإعدادات الأساسية للنظام*
- `get_env_var(name, default=None)`: (Static Method) دالة ثابتة للحصول على قيمة متغير بيئي.

### `BinanceManager`
*مدير اتصالات Binance API*
- `__init__(self)`: تهيئة المدير.
- `_generate_signature(self, params: dict, api_secret: str)`: إنشاء توقيع آمن للطلب (دالة داخلية).
- `check_symbol_availability(self, symbol: str)`: التحقق من توفر رمز العملة في Binance.
- `get_klines(self, symbol: str, interval: str = '4h', limit: int = 100, user_id: str = None)`: جلب بيانات الشموع (الأسعار التاريخية) مع استخدام ذاكرة تخزين مؤقت محلية.

### `BinanceTransactionVerifier`
*نظام متكامل للتحقق من معاملات Binance*
- `__init__(self)`: تهيئة نظام التحقق.
- `_generate_signature(self, params: dict, api_secret: str)`: إنشاء توقيع آمن لطلبات API (دالة داخلية).
- `_get_transaction_details(self, txid: str, user_id: str = None)`: الحصول على تفاصيل المعاملة من Binance باستخدام API (دالة داخلية).
- `verify_transaction_complete(self, txid: str, user_id: str, amount: float = 5.0)`: التحقق من اكتمال وصحة المعاملة بناءً على الشبكة، المبلغ، عدد التأكيدات، والمذكرة (memo).

### `SecureBackupSystem`
*نظام نسخ احتياطي آمن مع تشفير متقدم*
- `__init__(self)`: تهيئة نظام النسخ الاحتياطي.
- `_rotate_encryption_key(self)`: تغيير مفتاح التشفير بشكل دوري (دالة داخلية ومجدولة).
- `create_secure_backup(self, data: dict)`: إنشاء نسخة احتياطية مشفرة للبيانات وحفظها في Firestore.

### `AutomaticTransactionVerifier`
*نظام التحقق التلقائي من المعاملات (مستخدم في main.py)*
- `__init__(self)`: تهيئة النظام.
- `start_verification(self, transaction_id: str, user_id: str)`: بدء عملية التحقق الدوري لمعاملة معينة.
- `_verify_transaction(self, transaction_id: str)`: دالة التحقق الدورية التي يتم استدعاؤها بواسطة المجدول (دالة داخلية).
- `_stop_verification(self, transaction_id: str)`: إيقاف عملية التحقق وتحديث حالة المعاملة في Firestore (دالة داخلية).

### `Config`
*فئة لإدارة إعدادات النظام*
- `__new__(cls)`: (Singleton pattern) التأكد من وجود نسخة واحدة فقط من الفئة.
- `__init__(self)`: تهيئة الفئة وتحميل الإعدادات.
- `_load_config(self)`: تحميل الإعدادات من Firestore (دالة داخلية).
- `update_config(self, new_data: dict)`: تحديث الإعدادات في الذاكرة وفي Firestore.
- `bot_token(self)`: (Property) خاصية للحصول على توكن البوت.
- `owner_id(self)`: (Property) خاصية للحصول على معرف المطور.
- `backup_interval(self)`: (Property) خاصية للحصول على فترة النسخ الاحتياطي.
- `stats_interval(self)`: (Property) خاصية للحصول على فترة إرسال الإحصائيات.
- `payment_methods(self)`: (Property) خاصية للحصول على معلومات طرق الدفع.

### `SubscriptionSystem`
*نظام إدارة الاشتراكات والميزات للمستخدمين*
- `__init__(self)`: تهيئة نظام الاشتراكات وذاكرة التخزين المؤقت.
- `has_api_keys(self, user_id: str, api_type: str)`: التحقق من وجود مفاتيح API للمستخدم.
- `get_subscription_info(self, user_id: str, lang: str = 'ar')`: الحصول على معلومات الاشتراك للمستخدم بصيغة نصية.
- `get_subscription_expiry(self, user_id: str)`: الحصول على تاريخ انتهاء الاشتراك.
- `get_subscription_status(self, user_id: str, full_details: bool = False)`: دالة موحدة للتحقق من حالة الاشتراك (تأخذ في الاعتبار اليوم المجاني).
- `is_subscribed(self, user_id: str)`: التحقق من حالة اشتراك المستخدم (واجهة بسيطة).
- `get_subscription_details(self, user_id: str)`: الحصول على تفاصيل الاشتراك كاملة (واجهة بسيطة).
- `get_free_features(self)`: الحصول على قائمة الميزات المجانية.
- `get_premium_features(self)`: الحصول على قائمة الميزات المدفوعة.
- `get_user_features(self, user_id: str)`: الحصول على ميزات المستخدم بناءً على حالة اشتراكه.
- `get_daily_usage(self, user_id: str)`: الحصول على الاستخدام اليومي للمستخدم (عدد التحليلات والتنبيهات).
- `get_user_settings(self, user_id: str)`: الحصول على إعدادات المستخدم (مثل اللغة، العملات المفضلة).
- `update_user_settings(self, user_id: str, **settings)`: تحديث إعدادات المستخدم في Firestore وفي الذاكرة.
- `get_free_usage(self, user_id: str)`: الحصول على عدد الاستخدامات المجانية المتبقية للمستخدم اليوم.
- `use_free_analysis(user_id: str)`: تسجيل استخدام تحليل مجاني للمستخدم.
- `use_free_alert(user_id: str)`: تسجيل استخدام تنبيه مجاني للمستخدم (أو السماح للمشترك).
- `activate_subscription(user_id: str, transaction_id: str, lang: str = 'ar')`: دالة موحدة لتفعيل الاشتراك بعد الدفع.
- `add_subscription(user_id: str, lang: str = 'ar', transaction_id: str = None)`: إضافة اشتراك جديد (واجهة قديمة، يفضل استخدام `activate_subscription`).
- `clear_user_cache(self, user_id: str)`: مسح بيانات المستخدم من الذاكرة المؤقتة المحلية.
- `reset_user_data(self, update: Update, context: CallbackContext)`: (للمطور فقط) إعادة تعيين بيانات الاستخدام المجاني والإعدادات للمستخدم.
- `set_subscription_status(self, update: Update, context: CallbackContext)`: (للمطور فقط) تغيير حالة اشتراك المستخدم يدوياً.
- `manage_currencies(self, update: Update, context: CallbackContext)`: عرض وإدارة قائمة العملات المخصصة للمستخدم (للمشتركين).
- `add_currency(self, update: Update, context: CallbackContext)`: عرض قائمة لإضافة عملة مخصصة جديدة.
- `remove_currency(update: Update, context: CallbackContext)`: عرض قائمة لحذف عملة مخصصة.

#### المتغيرات الرئيسية:
- `_subscription_cache`: ذاكرة تخزين مؤقت لحالة الاشتراك (متغير داخلي).
- `_subscription_cache_expiry`: تواريخ انتهاء صلاحية الذاكرة المؤقتة للاشتراك (متغير داخلي).
- `_settings_cache`: ذاكرة تخزين مؤقت لإعدادات المستخدم (متغير داخلي).
- `_settings_expiry`: تواريخ انتهاء صلاحية الذاكرة المؤقتة لإعدادات المستخدم (متغير داخلي).
- `_free_usage_cache`: ذاكرة تخزين مؤقت للاستخدام المجاني (متغير داخلي).
- `_free_usage_expiry`: تواريخ انتهاء صلاحية الذاكرة المؤقتة للاستخدام المجاني (متغير داخلي).
- `_daily_usage_cache`: ذاكرة تخزين مؤقت للاستخدام اليومي (متغير داخلي).
- `_daily_usage_expiry`: تواريخ انتهاء صلاحية الذاكرة المؤقتة للاستخدام اليومي (متغير داخلي).

### `CryptoAnalysis`
*فئة مساعدة لتحليل العملات المشفرة حساب المؤشرات الفنية وإنشاء المخططات*
- `__init__(self)`: تهيئة فئة تحليل العملات.
- `get_exchange_rates(self)`: الحصول على أسعار صرف العملات الأجنبية مقابل الدولار.
- `convert_price(self, price_usd, target_currency='USD')`: تحويل السعر من الدولار إلى العملة المطلوبة.
- `calculate_ema(self, prices, period)`: حساب المتوسط المتحرك الأسي (EMA).
- `calculate_macd(self, prices, fast=12, slow=26, signal=9)`: حساب مؤشر الماكد (MACD).
- `calculate_bollinger_bands(self, prices, period=20, std=2)`: حساب نطاقات بولينجر (Bollinger Bands).
- `calculate_ichimoku_cloud(self, high, low, close, ...)`: حساب مؤشر سحابة إيشيموكو.
- `calculate_stoch_rsi(self, prices, period=14, smooth_k=3, smooth_d=3)`: حساب مؤشر القوة النسبية الاستوكاستك (Stochastic RSI).
- `calculate_adx(self, high, low, close, period=14)`: حساب مؤشر متوسط الاتجاه (ADX).
- `get_recommendation(self, market_data, lang='ar')`: الحصول على توصية (شراء/بيع/انتظار) بناءً على تحليل المؤشرات.
- `create_chart(self, symbol: str, df: pd.DataFrame, indicators: dict)`: إنشاء مخطط الشموع اليابانية مع المؤشرات الفنية وإرجاعه كبيانات صورة.
- `get_market_data(self, symbol: str, target_currency='USD', user_id=None, lang='ar', interval='4h')`: الحصول على بيانات السوق الشاملة (سعر، مؤشرات، توصيات، مخطط) مع استخدام التخزين المؤقت ومفاتيح API للمستخدم إن وجدت.
- `calculate_indicators(self, df)`: حساب جميع المؤشرات الفنية لـ DataFrame معين.
- `calculate_rsi(self, prices, period=14)`: حساب مؤشر القوة النسبية (RSI).

### `GitHubBackup`
*فئة لإدارة النسخ الاحتياطي على GitHub*
- `__init__(self)`: تهيئة الفئة ببيانات GitHub.
- `ensure_backup_folder_exists(self)`: التحقق من وجود مجلد النسخ الاحتياطي في المستودع وإنشائه إذا لم يكن موجوداً.
- `upload_to_github(self, data: dict)`: تشفير البيانات ورفعها كملف جديد إلى مجلد النسخ الاحتياطي في GitHub.
- `cleanup_old_backups(self)`: حذف النسخ الاحتياطية القديمة (أقدم من أسبوع) من GitHub ومفاتيحها من Firestore.

### `TelegramBot`
*فئة لإدارة البوت وتنظيم دورة حياته*
- `__init__(self)`: تهيئة الفئة.
- `setup(self)`: إعداد البوت (تهيئة النظام، إضافة المعالجات، جدولة المهام).
- `start(self)`: بدء تشغيل البوت (initialize, start, start_polling).
- `run_forever(self)`: حلقة تشغيل البوت المستمرة.
- `_check_settings_periodically(self, context: CallbackContext)`: دالة دورية للتحقق من صحة تخزين الإعدادات (عامة/حساسة) (دالة داخلية ومجدولة).
- `stop(self)`: إيقاف البوت بشكل آمن.

### `TransactionManager`
*فئة لإدارة المعاملات (المدفوعات وغيرها)*
- `__init__(self)`: تهيئة مدير المعاملات ومجدول التنظيف.
- `initialize(self)`: بدء تشغيل مجدول تنظيف المعاملات الفاشلة.
- `mark_transaction_failed(self, transaction_id: str, reason: str = None)`: تحديد المعاملة كـ "فاشلة" وحذفها مباشرةً.
- `cleanup_failed_transactions(self)`: دالة دورية لتنظيف المعاملات الفاشلة.

## الدوال (Functions)

- `get_or_create_encryption_key()`: الحصول على مفتاح التشفير من Firestore، أو من ملف .env، أو إنشاء مفتاح جديد وتخزينه.
- `show_main_menu(update: Update, context: CallbackContext, new_message=False)`: عرض القائمة الرئيسية للمستخدم مع الأزرار المناسبة بناءً على حالته وإعداداته.
- `start(update: Update, context: CallbackContext)`: معالج أمر `/start`، يضيف المستخدم لقاعدة البيانات ويعرض القائمة الرئيسية.
- `button_click(update: Update, context: CallbackContext)`: معالج النقر على جميع أزرار InlineKeyboard.
- `analyze_symbol(update: Update, context: CallbackContext, symbol: str, message=None, target_currency=None)`: تحليل رمز عملة معين (جلب البيانات، إنشاء النص والصورة، إرسال الرد).
- `help_command(update: Update, context: CallbackContext)`: معالج أمر `/help`، يعرض رسالة المساعدة.
- `alert_command(update: Update, context: CallbackContext)`: معالج أمر `/alert`، يطلب من المستخدم إدخال رمز العملة للتنبيه.
- `handle_message(update: Update, context: CallbackContext)`: معالج الرسائل النصية العادية، يوجهها حسب حالة المستخدم (إدخال API، رمز عملة، دردشة AI، إلخ).
- `load_alerts()`: تحميل جميع تنبيهات الأسعار من Firestore إلى الذاكرة (تستخدم عند بدء التشغيل).
- `save_alerts()`: حفظ جميع تنبيهات الأسعار من الذاكرة إلى Firestore (تستخدم دورياً أو عند الإيقاف).
- `save_user_settings(user_id: str, **settings)`: حفظ إعدادات مستخدم معين في Firestore.
- `load_user_settings(user_id: str)`: تحميل إعدادات مستخدم معين من Firestore، مع إنشاء إعدادات افتراضية إذا لم تكن موجودة.
- `check_alerts(context: CallbackContext)`: مهمة مجدولة للتحقق من جميع التنبيهات النشطة وإرسال إشعارات للمستخدمين عند تحقق الشروط.
- `verify_payment_transaction(user_id: str, transaction_id: str, lang: str = 'ar')`: التحقق من صحة معاملة دفع مسجلة في Firestore وتفعيل الاشتراك.
- `create_payment_transaction(user_id: str, binance_txid: str = None)`: إنشاء سجل معاملة دفع جديدة في Firestore بحالة "pending".
- `update_transaction_with_binance_id(temp_txid: str, binance_txid: str)`: نقل بيانات معاملة من معرف مؤقت إلى معرف Binance الفعلي.
- `cleanup_pending_transactions(context: CallbackContext)`: مهمة مجدولة لحذف معاملات الدفع المعلقة التي تجاوزت مدة الصلاحية (ساعة).
- `notify_expiring_transactions(context: CallbackContext)`: مهمة مجدولة لإرسال إشعارات للمستخدمين قبل انتهاء صلاحية معاملاتهم المعلقة.
- `send_transaction_expiry_notification(context: ContextTypes.DEFAULT_TYPE, user_id: str, transaction_id: str, expires_at: datetime)`: إرسال إشعار للمستخدم قبل 30 دقيقة من انتهاء صلاحية معاملته المعلقة.
- `extend_transaction(update: Update, context: ContextTypes.DEFAULT_TYPE)`: معالج زر لتمديد صلاحية معاملة معلقة لمدة ساعة أخرى.
- `complete_payment(update: Update, context: ContextTypes.DEFAULT_TYPE)`: معالج زر لعرض رابط الدفع (PayPal) للمستخدم لإكمال معاملة معلقة.
- `verify_payment(update: Update, context: ContextTypes.DEFAULT_TYPE)`: معالج زر للتحقق من حالة دفع معاملة معلقة عبر PayPal.
- `get_text(key: str, lang: str = 'ar')`: الحصول على النص المترجم من قاموس الترجمات بناءً على المفتاح واللغة.
- `set_language(update: Update, context: CallbackContext, lang: str)`: تغيير لغة المستخدم في إعداداته وتحديث القائمة الرئيسية.
- `get_main_menu_text(user_id: str, lang: str)`: إنشاء النص HTML للقائمة الرئيسية بناءً على حالة اشتراك المستخدم، الاستخدام المجاني، حالة API، واللغة.
- `get_main_menu_keyboard(user_id: str, lang: str)`: إنشاء لوحة أزرار InlineKeyboard للقائمة الرئيسية.
- `add_indicator(update: Update, context: CallbackContext, symbol: str, indicator_id: str)`: إضافة مؤشر فني إلى إعدادات المستخدم (لم تعد مستخدمة بشكل مباشر، يتم عبر `customize_indicators`).
- `add_custom_currency(update: Update, context: CallbackContext, symbol: str, currency: str)`: إضافة عملة مخصصة لإعدادات المستخدم.
- `stop(update: Update, context: CallbackContext)`: معالج أمر `/stop` (لا يقوم بإيقاف البوت فعلياً، فقط يزيل حالة المستخدم ويرسل رسالة وداع).
- `error_handler(update: Update, context: CallbackContext)`: معالج الأخطاء العام للبوت، يسجل الخطأ ويرسل رسالة خطأ عامة للمستخدم.
- `remove_indicator(update: Update, context: CallbackContext, symbol: str, indicator_id: str)`: إزالة مؤشر فني من إعدادات المستخدم (لم تعد مستخدمة بشكل مباشر).
- `create_analysis_text(symbol: str, market_data: dict, lang: str, user_id: str = None)`: إنشاء نص التحليل الفني إما باستخدام التحليل التقليدي أو تحليل Gemini AI بناءً على اشتراك المستخدم وتفضيلاته ووجود مفتاح API.
- `analyze_command(update: Update, context: CallbackContext)`: معالج أمر `/analyze` لتحليل زوج عملات معين.
- `generate_stats_report()`: إنشاء تقرير إحصائي عن حالة النظام (عدد المستخدمين، المشتركين، التحليلات، إلخ).
- `load_previous_stats()`: تحميل إحصائيات المستخدمين السابقة من ملف `subscription_data.json` (لم تعد مستخدمة).
- `save_previous_stats()`: حفظ الإحصائيات السابقة في ملف `subscription_data.json` (لم تعد مستخدمة).
- `manage_free_day_settings(update: Update, context: CallbackContext)`: عرض قائمة إدارة إعدادات اليوم المجاني للمستخدم.
- `set_free_day(update: Update, context: CallbackContext)`: تعيين اليوم المجاني المفضل للمستخدم.
- `send_daily_report(context: CallbackContext)`: مهمة مجدولة لإرسال تقرير إحصائي إلى المطور كل 12 ساعة.
- `encrypt_file(file_path: str, key: bytes)`: تشفير محتويات ملف باستخدام مفتاح Fernet.
- `backup_subscription_data(context: CallbackContext)`: مهمة مجدولة لإنشاء نسخة احتياطية مشفرة لملف `subscription_data.json` وإرسالها للمطور (لم تعد مستخدمة).
- `test_subscription(update: Update, context: ContextTypes.DEFAULT_TYPE)`: معالج أمر `/test_subscription` لعرض حالة اشتراك المستخدم الحالي.
- `cast(update: Update, context: CallbackContext)`: (للمطور فقط) معالج أمر `/cast` لإرسال رسالة جماعية لجميع المستخدمين النشطين.
- `ban_user(update: Update, context: CallbackContext)`: (للمطور فقط) معالج أمر `/ban` لحظر مستخدم.
- `unban_user(update: Update, context: CallbackContext)`: (للمطور فقط) معالج أمر `/unban` لإلغاء حظر مستخدم.
- `system_info(update: Update, context: CallbackContext)`: (للمطور فقط) معالج أمر `/system_info` لعرض معلومات وحالة النظام.
- `grant_free_day_command(update: Update, context: CallbackContext)`: (للمطور فقط) معالج أمر `/free_day` لمنح يوم مجاني لمستخدم أو للجميع.
- `cleanup_system(update: Update, context: CallbackContext)`: (للمطور فقط) معالج أمر `/cleanup` لتنظيف البيانات القديمة (معاملات فاشلة، نسخ احتياطية، تنبيهات، بيانات مؤقتة).
- `backup_data(context: CallbackContext)`: مهمة مجدولة لإنشاء نسخة احتياطية مشفرة لبيانات Firestore وإرسالها للمطور.
- `initialize_firestore()`: تهيئة مجموعات Firestore الأساسية إذا لم تكن موجودة.
- `check_firestore_connection()`: التحقق من القدرة على الاتصال والكتابة والقراءة من Firestore.
- `initialize_system()`: تهيئة النظام بالكامل (Firestore، مفتاح التشفير، مدير API).
- `cancel_transaction(transaction_id: str, user_id: str)`: إلغاء معاملة دفع معلقة وتحديث حالتها في Firestore.
- `delete_message_after_delay(bot, chat_id, message_id, delay_seconds)`: حذف رسالة تليجرام بعد فترة زمنية محددة.
- `handle_payment_verification(update: Update, context: CallbackContext)`: معالج زر التحقق من الدفع (يستدعي `verify_paypal_transaction`).
- `verify_paypal_transaction(user_id: str, amount: float = 5.0, transaction_id: str = None)`: التحقق الفعلي من معاملة PayPal عبر API أو من قاعدة البيانات.
- `activate_subscription(user_id: str, transaction_id: str)`: تفعيل اشتراك المستخدم بعد التحقق من الدفع (تستدعي دالة النظام).
- `setup_price_alert(update: Update, context: CallbackContext, symbol: str)`: عرض قائمة إعداد تنبيه سعري سريع (بنسب مئوية).
- `handle_custom_alert(update: Update, context: CallbackContext, symbol: str)`: عرض واجهة طلب سعر مخصص للتنبيه (للمشتركين).
- `process_custom_alert(update: Update, context: CallbackContext)`: معالجة السعر المخصص الذي أدخله المستخدم وإنشاء التنبيه.
- `customize_indicators(update: Update, context: CallbackContext, symbol: str)`: عرض واجهة تخصيص المؤشرات (لم تعد مستخدمة بشكل كامل).
- `refresh_analysis(update: Update, context: CallbackContext, symbol: str)`: معالج زر لتحديث تحليل عملة معينة.
- `perform_backup(update: Update = None, context: CallbackContext = None)`: (للمطور فقط عبر الأمر، أو مجدول) تنفيذ عملية النسخ الاحتياطي على GitHub.
- `stop_all_scheduled_tasks(update: Update, context: CallbackContext)`: (للمطور فقط) إيقاف جميع المهام المجدولة وإزالة التنبيهات.
- `show_upgrade_info(update: Update, context: CallbackContext)`: عرض معلومات وشروط الترقية للمستخدم غير المشترك.
- `api_setup_command(update: Update, context: CallbackContext, preselect_platform=None)`: معالج أمر `/setup_api`. يمكن تمرير معلمة `preselect_platform` لتوجيه المستخدم مباشرة إلى صفحة إعداد منصة محددة.
- `api_info_command(update: Update, context: CallbackContext)`: معالج أمر `/api_info`.
- `delete_api_command(update: Update, context: CallbackContext)`: معالج أمر `/delete_api`.
- `show_user_stats(update: Update, context: CallbackContext)`: (للمطور فقط) معالج أمر `/stats` لعرض إحصائيات المستخدمين.
- `add_user_to_users_collection(user_id: str, username: str)`: إضافة مستخدم جديد إلى مجموعة `users` في Firestore عند أول تفاعل.
- `check_expired_subscriptions(context: CallbackContext)`: مهمة مجدولة للتحقق من الاشتراكات المنتهية وتحديث حالتها.
- `_update_expired_subscription(user_id: str)`: دالة مساعدة لتحديث حالة الاشتراك المنتهي في Firestore والذاكرة (دالة داخلية).
- `notify_expiring_subscriptions(context: CallbackContext)`: مهمة مجدولة لإرسال إشعارات للمستخدمين قبل انتهاء اشتراكاتهم.
- `_send_expiry_notification(context: ContextTypes.DEFAULT_TYPE, user_id: str, notification_type: str, hours_left: float = 0)`: دالة مساعدة لإرسال إشعار انتهاء الاشتراك (دالة داخلية).
- `ping_url(url: str, timeout: int = 10)`: دالة مساعدة لإرسال طلب GET إلى رابط معين للتحقق من أنه يعمل.
- `ping_koyeb_app()`: دالة مجدولة لتشغيل رابط فحص الصحة لمنصة Koyeb (تم تعطيلها في هذا الملف).
- `get_trading_strategy_analysis(update: Update, context: CallbackContext, symbol: str)`: (للمشتركين مع API) الحصول على استراتيجية تداول مقترحة من Gemini.
- `get_price_prediction_analysis(update: Update, context: CallbackContext, symbol: str)`: (للمشتركين مع API) الحصول على تنبؤات سعرية من Gemini.
- `get_multi_timeframe_analysis_view(update: Update, context: CallbackContext, symbol: str)`: (للمشتركين مع API) الحصول على تحليل متعدد الإطارات الزمنية من Gemini.
- `run_bot()`: الدالة الرئيسية لتشغيل البوت (تهيئة، بدء المجدولات، بدء البوت).
- `migrate_config_to_database()`: نقل الإعدادات من متغيرات البيئة إلى Firestore عند أول تشغيل أو عند الحاجة.
- `main()`: نقطة الدخول الرئيسية للتطبيق، تهيئ حلقة الأحداث وتشغل البوت.

## الدوال والفئات - ai_chat.py

### الدوال (Functions)

- `initialize(api_mgr)`: تهيئة وحدة الدردشة مع الذكاء الاصطناعي وتمرير مدير API.
- `chat_with_ai(user_id: str, message: str, lang: str = 'ar') -> str`: الوظيفة الرئيسية للدردشة مع الذكاء الاصطناعي. تحصل على نموذج Gemini للمستخدم، تتحقق إذا كانت الرسالة عن عملة معينة، تجلب بيانات السوق إذا لزم الأمر، تنشئ سياقًا (prompt) مناسبًا، تستدعي النموذج، وتعيد الرد.
- `extract_crypto_symbol(message: str) -> Optional[str]`: استخراج رمز العملة أو السلعة من رسالة المستخدم باستخدام قائمة معرفة مسبقاً وتعبيرات منتظمة.
- `get_market_data_for_symbol(symbol: str, user_id: str, target_currency: str = 'USD') -> Optional[Dict[str, Any]]`: الحصول على بيانات السوق لرمز عملة أو سلعة معينة، باستخدام مفاتيح API الخاصة بالمستخدم إن وجدت، وإلا باستخدام API العام. (ملاحظة: دعم السلع معطل حالياً).
- `get_market_data_with_public_api(symbol: str, interval: str = '1d', limit: int = 100, target_currency: str = 'USD') -> Optional[Dict[str, Any]]`: الحصول على بيانات السوق باستخدام API العام لبينانس فقط.
- `create_enhanced_prompt(message: str, market_data: Dict[str, Any], symbol: str, lang: str) -> str`: إنشاء سياق (prompt) محسن لنموذج Gemini يتضمن بيانات السوق الحالية للعملة المطلوبة.
- `create_regular_prompt(message: str, lang: str) -> str`: إنشاء سياق (prompt) عادي لنموذج Gemini للدردشة العامة أو عند عدم توفر بيانات سوق.
- `get_error_message(lang: str) -> str`: الحصول على رسالة خطأ عامة موحدة بناءً على لغة المستخدم.

## الدوال والفئات - api_manager.py

### الفئات (Classes)

### `APIManager`
*فئة لإدارة مفاتيح API للمستخدمين. توفر وظائف لتخزين واسترجاع وإدارة مفاتيح API الخاصة بالمستخدمين.*
- `__init__(self, db: firestore.Client, encryption_key: str = None)`: تهيئة مدير API.
- `_initialize_collection(self)`: تهيئة مجموعة مفاتيح API في Firestore إذا لم تكن موجودة (دالة داخلية).
- `encrypt_data(self, data: str) -> Optional[str]`: تشفير البيانات باستخدام مفتاح التشفير.
- `decrypt_data(self, encrypted_data: str) -> Optional[str]`: فك تشفير البيانات المشفرة.
- `save_api_key(self, user_id: str, api_type: str, api_key: str, api_secret: str = None) -> bool`: تخزين مفتاح API بشكل آمن في Firestore.
- `get_api_keys(self, user_id: str, api_type: str) -> Tuple[Optional[str], Optional[str]]`: استرجاع مفاتيح API للمستخدم من Firestore.
- `delete_api_keys(self, user_id: str, api_type: str) -> bool`: حذف مفاتيح API للمستخدم من Firestore.
- `has_api_keys(self, user_id: str, api_type: str) -> bool`: التحقق من وجود مفاتيح API للمستخدم.
- `get_api_keys_sync(self, user_id: str, api_type: str) -> Tuple[Optional[str], Optional[str]]`: استرجاع مفاتيح API للمستخدم بطريقة متزامنة (غير متزامنة).
- `get_api_info(self, user_id: str) -> Dict[str, Any]`: الحصول على معلومات API للمستخدم (حالة المفاتيح وتواريخ التحديث).

## الدوال والفئات - automatic_payment_verification.py

### الفئات (Classes)

### `AutomaticPaymentVerifier`
*فئة للتحقق التلقائي من المدفوعات والتجديد التلقائي للاشتراكات (تستخدم في main.py لتنفيذ عمليات الدفع المتكررة عبر PayPal)*
- `__init__(self, db, paypal_client_id: str, paypal_secret: str, is_sandbox: bool = False, bot_token: str = None)`: تهيئة المتحقق التلقائي من المدفوعات.
- `start(self)`: بدء المتحقق التلقائي من المدفوعات والتجديد التلقائي للاشتراكات.
- `stop(self)`: إيقاف المتحقق التلقائي من المدفوعات.
- `get_access_token(self) -> str`: الحصول على رمز الوصول من PayPal API.
- `verify_transaction(self, transaction_id: str, user_id: str, amount: float = 5.0) -> bool`: التحقق من معاملة PayPal.
- `verify_pending_transactions(self)`: التحقق من جميع المعاملات المعلقة (دالة مجدولة).
- `activate_subscription(self, user_id: str, transaction_id: str) -> bool`: تفعيل الاشتراك بعد التحقق من الدفع.
- `send_subscription_notification(self, user_id: str, notification_type: str = 'activated') -> bool`: إرسال إشعار للمستخدم بخصوص الاشتراك (تفعيل، قرب انتهاء، انتهاء، فشل/نجاح التجديد).
- `send_pending_notifications(self)`: إرسال الإشعارات المعلقة للمستخدمين (دالة مجدولة).
- `check_expiring_subscriptions(self)`: التحقق من الاشتراكات التي ستنتهي قريباً وإرسال إشعارات (دالة مجدولة).
- `_update_expired_subscription(self, user_id: str)`: تحديث حالة الاشتراك المنتهي في قاعدة البيانات (دالة داخلية).
- `attempt_auto_renewal(self, user_id: str) -> bool`: محاولة تجديد الاشتراك تلقائياً.

## الدوال والفئات - firestore_cache.py

### الفئات (Classes)

### `FirestoreCache`
*فئة لإدارة التخزين المؤقت باستخدام Firestore. توفر واجهة بسيطة لتخزين واسترجاع البيانات المؤقتة.*
- `__init__(self, db: firestore.Client, collection_prefix: str = "cache")`: تهيئة نظام التخزين المؤقت.
- `_initialize_collections(self)`: تهيئة مجموعات التخزين المؤقت في Firestore إذا لم تكن موجودة (دالة داخلية).
- `_get_collection(self, cache_type: str) -> str`: الحصول على اسم المجموعة المناسب لنوع التخزين المؤقت (دالة داخلية).
- `_serialize_value(self, value: Any) -> str`: تحويل القيمة إلى سلسلة JSON للتخزين (دالة داخلية).
- `_deserialize_value(self, value: str) -> Any`: تحويل سلسلة JSON إلى القيمة الأصلية بعد الاسترجاع (دالة داخلية).
- `set(self, key: str, value: Any, ex: int = 3600, cache_type: str = "system_data") -> bool`: تخزين قيمة في التخزين المؤقت مع تحديد مفتاح، قيمة، مدة صلاحية، ونوع التخزين المؤقت.
- `get(self, key: str, cache_type: str = "system_data") -> Optional[Any]`: استرجاع قيمة من التخزين المؤقت بناءً على المفتاح ونوع التخزين المؤقت، مع التحقق من انتهاء الصلاحية.
- `delete(self, key: str, cache_type: str = "system_data") -> bool`: حذف قيمة محددة من التخزين المؤقت.
- `clear_expired(self, cache_type: str = None) -> int`: حذف جميع القيم منتهية الصلاحية من مجموعة واحدة أو جميع مجموعات التخزين المؤقت.
- `clear_all(self, cache_type: str = None) -> int`: حذف جميع القيم من مجموعة واحدة أو جميع مجموعات التخزين المؤقت.

## الدوال والفئات - format_indicators.py

### الدوال (Functions)

- `format_technical_indicators_rtl(analysis: str, market_data: Dict[str, Any]) -> str`: تنسيق قسم المؤشرات الفنية في نص التحليل ليظهر بشكل صحيح مع لغات اليمين إلى اليسار (RTL).
- `format_gemini_analysis(analysis: str, symbol: str, price: float, price_change: float, lang: str = 'ar') -> str`: تنسيق تحليل Gemini بالكامل، بما في ذلك إضافة عنوان، معلومات السعر، تنسيق المؤشرات للغة العربية، وإزالة التنبيهات المكررة والأسطر الفارغة.

## الدوال والفئات - exchange_validators.py

### الدوال (Functions)

- `verify_kucoin_api(api_key: str, api_secret: str, api_passphrase: str = None) -> Tuple[bool, Optional[str]]`: التحقق من صحة مفاتيح KuCoin API.
- `verify_coinbase_api(api_key: str, api_secret: str) -> Tuple[bool, Optional[str]]`: التحقق من صحة مفاتيح Coinbase API.
- `verify_bybit_api(api_key: str, api_secret: str) -> Tuple[bool, Optional[str]]`: التحقق من صحة مفاتيح Bybit API.
- `verify_okx_api(api_key: str, api_secret: str, api_passphrase: str = None) -> Tuple[bool, Optional[str]]`: التحقق من صحة مفاتيح OKX API.
- `verify_kraken_api(api_key: str, api_secret: str) -> Tuple[bool, Optional[str]]`: التحقق من صحة مفاتيح Kraken API.
- `verify_exchange_api(exchange_type: str, api_key: str, api_secret: str, api_passphrase: str = None) -> Tuple[bool, Optional[str]]`: دالة موحدة للتحقق من صحة مفاتيح API لمختلف المنصات المدعومة (Binance, Gemini, KuCoin, Coinbase, Bybit, OKX, Kraken).

## الدوال والفئات - handle_payment_verification.py

### الدوال (Functions)

- `handle_payment_verification(update: Update, context: CallbackContext)`: معالج حدث الضغط على زر "التحقق من الدفع". يقوم بإظهار رسالة انتظار، ثم يستدعي دالة التحقق الفعلية، ويحدث رسالة المستخدم بناءً على نتيجة التحقق (نجاح/فشل).

## الدوال والفئات - paypal_payment.py

هذا القسم يصف الفئات والدوال المعرفة داخل ملف `paypal_payment.py` والتي توفر الوظائف الأساسية للتفاعل مع PayPal API (الحصول على رمز الوصول، إنشاء الطلبات، التحقق من الطلبات، إلخ). كما يحتوي على دالة رئيسية للتحقق من معاملة PayPal ووظيفة تفعيل الاشتراك بعد الدفع.

### الفئات (Classes)

### `PayPalAPI`
*واجهة برمجة تطبيقات PayPal. توفر دوال للتفاعل المباشر مع PayPal REST API.*
- `__init__(self, client_id, client_secret, is_sandbox=False)`: تهيئة واجهة API ببيانات الاعتماد وتحديد بيئة العمل.
- `get_access_token(self)`: الحصول على رمز الوصول (Access Token) من PayPal.
- `verify_payment(self, order_id)`: التحقق من حالة طلب دفع باستخدام معرف الطلب.
- `create_order(self, amount, currency="USD", return_url=None, cancel_url=None, user_id=None, description=None, allow_guest_checkout=True)`: إنشاء طلب دفع جديد في PayPal.
- `capture_payment(self, order_id)`: تأكيد (Capture) الدفع لطلب تمت الموافقة عليه.
- `get_payment_details(self, payment_id)`: الحصول على تفاصيل دفع تم تحصيله.

### `AutomaticPaymentVerifier`
*نظام التحقق التلقائي من المدفوعات المعلقة وتنظيف المعاملات القديمة (مستخدم في paypal_payment.py)*
- `__init__(self, db, paypal_client_id, paypal_secret, is_sandbox=False)`: تهيئة نظام التحقق التلقائي.
- `start(self)`: بدء نظام التحقق التلقائي وجدولة المهام.
- `stop(self)`: إيقاف نظام التحقق التلقائي.
- `_verify_pending_transactions(self)`: مهمة مجدولة للتحقق من المعاملات المعلقة في PayPal (ميثود داخلية ومجدولة).
- `_cleanup_expired_transactions(self)`: مهمة مجدولة لتنظيف المعاملات المعلقة والمنتهية الصلاحية (ميثود داخلية ومجدولة).

### الدوال (Functions)

- `verify_paypal_transaction(user_id, amount=5.0, transaction_id=None)`: التحقق من صحة معاملة PayPal للمستخدم. تقوم بالبحث عن المعاملة في Firestore، الحصول على تفاصيل الطلب من PayPal، والتحقق من حالته ومبلغه.
- `activate_subscription(user_id, transaction_id)`: تفعيل اشتراك المستخدم في نظام الاشتراكات بعد التحقق من الدفع، وتحديث حالة المعاملة في Firestore.

## الدوال والفئات - gemini_analysis.py

### الدوال (Functions)

- `set_firestore_db(firestore_db)`: تعيين كائن قاعدة بيانات Firestore للاستخدام داخل هذه الوحدة.
- `analyze_with_gemini(model, market_data: Dict[str, Any], lang: str = 'ar') -> Optional[str]`: الوظيفة الرئيسية للتحليل المتقدم باستخدام نموذج Gemini. تأخذ بيانات السوق والمؤشرات الفنية وتقوم بإنشاء سياق (prompt) للنموذج للحصول على تحليل فني مفصل.
- `create_analysis_prompt(symbol: str, price_data: list, indicators: Dict[str, Any], market_info: Dict[str, Any], lang: str) -> str`: إنشاء نص السياق (prompt) الذي سيتم إرساله إلى نموذج Gemini لتحليل عملة معينة.
- `clean_and_format_analysis(analysis: str, lang: str) -> str`: تنظيف وتنسيق النص الناتج من تحليل Gemini (إزالة الأسطر الفارغة، إضافة عنوان).
- `generate_smart_alerts(model, symbol: str, current_price: float, indicators: Dict[str, Any], lang: str = 'ar') -> Dict[str, float]`: استخدام نموذج Gemini لاقتراح مستويات سعرية مهمة للتنبيهات (دعم، مقاومة، تغير اتجاه).
- `get_user_api_client(user_id: str, api_type: str = 'gemini')`: الحصول على عميل API لنموذج Gemini للمستخدم، مع محاولة استخدام مفتاح API المخصص للمستخدم أولاً، ثم المفتاح الافتراضي.
- `_initialize_gemini_client(api_key: str, user_id: str)`: تهيئة عميل (نموذج) Gemini باستخدام مفتاح API محدد، مع محاولة استخدام نماذج مختلفة (gemini-2.0-flash-exp, gemini-1.5-flash) (دالة داخلية).
- `verify_gemini_api(api_key: str) -> Tuple[bool, str]`: التحقق من صحة مفتاح API لنموذج Gemini عن طريق محاولة استدعاء النموذج.
- `get_trading_strategy(model, market_data: Dict[str, Any], lang: str = 'ar') -> Optional[str]`: استخدام نموذج Gemini لاقتراح استراتيجية تداول آلية بناءً على بيانات السوق.
- `get_price_prediction(model, market_data: Dict[str, Any], lang: str = 'ar') -> Optional[str]`: استخدام نموذج Gemini لتقديم تنبؤات سعرية للفترات القادمة.
- `get_multi_timeframe_analysis(model, market_data: Dict[str, Any], timeframes_data: Dict[str, Dict[str, Any]], lang: str = 'ar') -> Optional[str]`: استخدام نموذج Gemini لتحليل العملة عبر إطارات زمنية متعددة وتقديم خلاصة متكاملة.

## الدوال والفئات - traditional_analysis.py

هذا القسم يصف الدوال المعرفة داخل ملف `traditional_analysis.py` والتي تقوم بإنشاء تحليل فني تقليدي للعملات بناءً على بيانات المؤشرات الفنية دون استخدام الذكاء الاصطناعي.

### الدوال (Functions)

- `create_traditional_analysis(symbol: str, market_data: Dict[str, Any], lang: str = 'ar') -> str`: الوظيفة الرئيسية لإنشاء نص التحليل الفني التقليدي. تستخرج بيانات السعر والمؤشرات من `market_data`، تحلل حالة كل مؤشر بناءً على قيم محددة (مثل مناطق ذروة الشراء/البيع لـ RSI)، تحدد توصية عامة بناءً على إشارات المؤشرات، وتقوم بتنسيق كل هذه المعلومات في نص مقروء باللغة المطلوبة.
- `get_currency_flag(symbol: str) -> str`: دالة مساعدة للحصول على رمز تعبيري (إيموجي) علم العملة أو رمزها المشفر بناءً على رمز العملة (مثل BTC, ETH, USD, SAR).

## الدوال والفئات - free_day_system.py

### الفئات (Classes)

### `FreeDaySystem`
*نظام إدارة اليوم المجاني الأسبوعي. يتيح للمستخدمين تجربة الميزات المدفوعة مجانًا ليوم واحد في الأسبوع.*
- `__init__(self, db=None)`: تهيئة نظام اليوم المجاني، ومحاولة الحصول على قاعدة بيانات Firestore.
- `get_user_free_day_status(self, user_id: str) -> Dict[str, Any]`: الحصول على حالة اليوم المجاني للمستخدم (اليوم المختار، آخر استخدام، هل هو نشط اليوم، اليوم المجاني القادم).
- `_get_default_free_day_status(self, lang: str = 'ar') -> Dict[str, Any]`: الحصول على حالة افتراضية لليوم المجاني (دالة داخلية).
- `_get_day_name(self, day_index: int, lang: str = 'ar') -> str`: الحصول على اسم اليوم (الاثنين-الأحد) باللغة العربية أو الإنجليزية (دالة داخلية).
- `_calculate_next_free_day(self, free_day: int, last_used: Optional[datetime] = None) -> datetime`: حساب تاريخ اليوم المجاني القادم بناءً على اليوم المختار وآخر استخدام (دالة داخلية).
- `is_today_free_day(self, user_id: str) -> bool`: التحقق مما إذا كان اليوم الحالي هو اليوم المجاني للمستخدم.
- `activate_free_day(self, user_id: str) -> bool`: تفعيل اليوم المجاني للمستخدم في قاعدة البيانات.
- `deactivate_free_day(self, user_id: str) -> bool`: إلغاء تفعيل اليوم المجاني للمستخدم في قاعدة البيانات.
- `set_free_day(self, user_id: str, day_index: int) -> bool`: تعيين اليوم المجاني المفضل للمستخدم.
- `check_and_update_free_days(self) -> Tuple[int, int]`: التحقق من وتحديث حالة الأيام المجانية لجميع المستخدمين (تفعيل اليوم لمن يحق له، إلغاء تفعيل اليوم لمن انتهى).
- `send_free_day_notification(self, user_id: str, notification_type: str) -> bool`: إرسال إشعار للمستخدم متعلق باليوم المجاني (متاح، تذكير، انتهى).
- `send_free_day_reminders(self) -> int`: إرسال تذكيرات باليوم المجاني القادم للمستخدمين.
- `clear_cache(self, user_id: str = None) -> int`: مسح الذاكرة المؤقتة لبيانات اليوم المجاني.

### المتغيرات الرئيسية:

- `free_day_system`: نسخة عامة (global instance) من فئة `FreeDaySystem` يتم إنشاؤها عند استيراد الوحدة.

## الدوال والفئات - api_ui.py

### الدوال (Functions)

- `setup_api_keys(update: Update, context: CallbackContext, api_manager, subscription_system)`: إعداد مفاتيح API للمستخدم، عرض واجهة اختيار المنصة.
- `show_api_instructions(update: Update, context: CallbackContext, api_type: str, lang: str = 'ar')`: عرض تعليمات الحصول على مفاتيح API لمنصة معينة.
- `delete_api_keys_ui(update: Update, context: CallbackContext, api_manager, subscription_system)`: واجهة حذف مفاتيح API.
- `show_api_info(update: Update, context: CallbackContext, api_manager, subscription_system)`: عرض معلومات API المخزنة للمستخدم.
- `show_platform_selection(update: Update, context: CallbackContext, api_manager, subscription_system)`: عرض واجهة اختيار منصة التداول لإعداد API.

## الدوال والفئات - api_validators.py

### الدوال (Functions)

- `verify_binance_api(api_key: str, api_secret: str) -> Tuple[bool, Optional[str]]`: التحقق من صحة مفاتيح Binance API.
- `verify_gemini_api(api_key: str) -> Tuple[bool, Optional[str]]`: التحقق من صحة مفتاح Gemini API.
- `get_binance_client(api_key: str, api_secret: str)`: إنشاء عميل Binance باستخدام مفاتيح API محددة.

## الدوال والفئات - server.py

### الفئات (Classes)

### `HealthCheckHandler`
*معالج طلبات HTTP بسيط يستجيب لطلبات GET على المسارين `/health` و `/` برسالة "Bot is running" وحالة 200 OK.*
- `do_GET(self)`: معالجة طلبات GET.
- `log_message(self, format, *args)`: تعطيل تسجيل الرسائل الافتراضي للخادم (ميثود داخلية).

### الدوال (Functions)

- `start_health_server()`: بدء خادم فحص الصحة.
- `run_health_server()`: تشغيل خادم فحص الصحة في خيط منفصل.

## الدوال والفئات - firebase_init.py

### الدوال (Functions)

- `initialize_firebase()`: تهيئة Firebase وإرجاع كائن قاعدة البيانات Firestore.

## متغيرات التكوين - config.py

هذا الملف يحتوي على تعريفات للمتغيرات والثوابت المستخدمة في تكوين البوت وإعداداته المختلفة. يتم تحميل هذه القيم غالباً من متغيرات البيئة (environment variables).

### المتغيرات الرئيسية:

- `BOT_TOKEN`: رمز توكن بوت تيليجرام.
- `OWNER_ID`: معرف تيليجرام الخاص بمالك/مطور البوت.
- `DEFAULT_BINANCE_API_KEY`: مفتاح Binance API الافتراضي (قيمته حالياً None).
- `DEFAULT_BINANCE_API_SECRET`: مفتاح Binance API السري الافتراضي (قيمته حالياً None).
- `DEFAULT_GEMINI_API_KEY`: مفتاح Gemini API الافتراضي (قيمته حالياً None).
- `REQUIRE_API_KEYS_FOR_AI_ANALYSIS`: (Boolean) هل يتطلب تحليل الذكاء الاصطناعي وجود مفاتيح API للمستخدم؟
- `REQUIRE_API_KEYS_FOR_MARKET_DATA`: (Boolean) هل يتطلب جلب بيانات السوق وجود مفاتيح API للمستخدم؟
- `ENABLE_ADVANCED_FEATURES`: (Boolean) تمكين الميزات المتقدمة بشكل عام.
- `ADVANCED_FEATURES`: (Dictionary) قاموس يحدد الميزات المتقدمة الممكنة (`trading_strategy`, `price_prediction`, `multi_timeframe`).
- `PAYPAL_CLIENT_ID`: معرف العميل لحساب PayPal.
- `PAYPAL_CLIENT_SECRET`: المفتاح السري لحساب PayPal.
- `PAYPAL_SANDBOX_MODE`: (Boolean) هل يتم استخدام بيئة اختبار PayPal؟
- `PAYPAL_LINK`: (String) رابط الدفع اليدوي لـ PayPal (سيتم استبداله بالرابط الآلي).
- `ENCRYPTION_KEY`: مفتاح التشفير المستخدم لتشفير البيانات الحساسة (مثل مفاتيح API).
- `GITHUB_TOKEN`: رمز وصول GitHub المستخدم للنسخ الاحتياطي.
- `GITHUB_REPO`: اسم مستودع GitHub المستخدم للنسخ الاحتياطي.
- `GITHUB_OWNER`: اسم مالك مستودع GitHub المستخدم للنسخ الاحتياطي.
- `SUBSCRIPTION_PRICE`: سعر الاشتراك (بالدولار).
- `SUBSCRIPTION_DURATION`: مدة الاشتراك (بالأيام).
- `CACHE_TIMEOUT`: مدة صلاحية التخزين المؤقت (بالثواني).
- `MAX_FREE_ANALYSES`: الحد الأقصى لعدد التحليلات المجانية يومياً للمستخدم الواحد.
- `MAX_FREE_ALERTS`: الحد الأقصى لعدد التنبيهات المجانية النشطة للمستخدم الواحد.
- `ENABLE_USER_API`: (Boolean) تمكين استخدام مفاتيح API الخاصة بالمستخدم بشكل عام.

## الدوال والفئات - main_wrapper.py

هذا القسم يصف الدوال المعرفة داخل ملف `main_wrapper.py` والتي تعمل كغلاف لتشغيل البوت الرئيسي مع معالجة مشاكل التوافق وتشغيل خادم فحص الصحة.

### الفئات (Classes)

### `HealthHandler(BaseHTTPRequestHandler)`
*فئة مساعدة لإنشاء معالج طلبات HTTP لخادم فحص الصحة (داخل دالة `run_health_server_only`)*
#### Methods:
- `do_GET(self)`: معالجة طلبات GET.
- `log_message(self, format, *args)`: تعطيل تسجيل الرسائل الافتراضي للخادم (ميثود داخلية).

### الدوال (Functions)

- `signal_handler(sig, frame)`: معالج إشارات التوقف (SIGINT, SIGTERM) لإيقاف البوت بشكل آمن.
- `run_bot()`: دالة غير متزامنة لاستيراد وتشغيل البوت الرئيسي من ملف `main.py`.
- `ping_url(url: str, timeout: int = 10) -> bool`: دالة غير متزامنة لإرسال طلب GET إلى رابط محدد والتحقق من استجابته.
- `ping_koyeb_health_check()`: دالة تقوم بتشغيل رابط فحص الصحة لمنصة Koyeb باستخدام `ping_url` داخل حلقة أحداث مؤقتة.
- `setup_ping_scheduler()`: إعداد مجدول (باستخدام مكتبة `schedule`) لتشغيل دالة `ping_koyeb_health_check` بشكل دوري في خيط منفصل.
- `run_health_server_only()`: تشغيل خادم HTTP بسيط يستجيب لطلبات فحص الصحة فقط، دون تشغيل البوت الرئيسي. يستخدم في حالة وجود مشاكل توافق.
- `main()`: الدالة الرئيسية لنقطة الدخول في `main_wrapper.py`. تقوم ببدء خادم فحص الصحة، وإعداد المجدول، ومحاولة تشغيل البوت الرئيسي، وتدير معالجة الأخطاء لا سيما تلك المتعلقة بمكتبة `cryptography`.

### المتغيرات الرئيسية:

- `keep_running`: متغير منطقي (boolean) يتحكم في استمرار تشغيل الحلقات الرئيسية.

## الدوال والفئات - user_market_data.py

### الدوال (Functions)

- `get_market_data_with_user_api(client, symbol: str, timeframe: str = '1d', limit: int = 100, target_currency: str = 'USD') -> Optional[Dict[str, Any]]`: الحصول على بيانات السوق باستخدام API المستخدم. تستخدم عميل Binance المهيأ بمفاتيح API المستخدم للحصول على بيانات الشموع والمؤشرات الفنية.
- `calculate_indicators(df: pd.DataFrame) -> Dict[str, Any]`: حساب المؤشرات الفنية من DataFrame يحتوي على بيانات الأسعار. تحسب مؤشرات مثل RSI، EMA، MACD، Bollinger Bands، Stochastic، ADX وتقدم توصية عامة بناءً على إشارات المؤشرات.

## الدوال والفئات - system_settings.py

### الفئات (Classes)

### `SystemSettings`
*فئة لإدارة الإعدادات العامة للنظام*
- `__init__(self, db: firestore.Client = None)`: تهيئة نظام الإعدادات العامة.
- `set_db(self, db: firestore.Client)`: تعيين كائن قاعدة بيانات Firestore.
- `_initialize_settings(self)`: تهيئة مجموعة الإعدادات العامة (دالة داخلية).
- `_refresh_cache_if_needed(self)`: تحديث الذاكرة المؤقتة إذا مر وقت كافٍ منذ آخر تحديث (دالة داخلية).
- `set(self, key: str, value: Any, sensitive: bool = False) -> bool`: تعيين قيمة إعداد.
- `get(self, key: str, default: Any = None, sensitive: bool = False) -> Any`: الحصول على قيمة إعداد.
- `delete(self, key: str, sensitive: bool = False) -> bool`: حذف إعداد.
- `get_all(self, sensitive: bool = False) -> Dict[str, Any]`: الحصول على جميع الإعدادات.
- `clear_cache(self)`: مسح الذاكرة المؤقتة.

### المتغيرات الرئيسية:

- `system_settings`: نسخة عامة (global instance) من فئة `SystemSettings` يتم إنشاؤها عند استيراد الوحدة.

## الدوال والفئات - handle_paypal_payment.py

### الدوال (Functions)

- `handle_paypal_payment(update: Update, context: CallbackContext)`: معالج حدث الضغط على زر "الدفع عبر PayPal". يقوم بالتحقق من وجود معاملات معلقة للمستخدم واستخدامها إن وجدت، أو إنشاء معاملة جديدة. ثم يقوم بإنشاء طلب دفع في PayPal (مع دعم الدفع كضيف)، وعرض رابط الدفع للمستخدم مع تعليمات مفصلة عن خطوات الدفع.

## الدوال والفئات - paypal_manager.py

### الفئات (Classes)

### `PayPalManager`
*فئة لإدارة التفاعلات مع PayPal API بشكل متكامل*
- `__init__(self, client_id: str, client_secret: str, is_sandbox: bool = True)`: تهيئة مدير PayPal مع بيانات الاعتماد وتحديد بيئة العمل.
- `get_access_token(self)`: الحصول على رمز الوصول من PayPal API مع التخزين المؤقت.
- `create_payment_link(self, amount: float, currency: str = "USD", description: str = "اشتراك في بوت التحليل الفني", return_url: str = None, cancel_url: str = None, user_id: str = None, bot_username: str = None, subscription_type: str = "weekly") -> Tuple[Optional[str], Optional[str]]`: إنشاء رابط دفع جديد مع دعم أنواع مختلفة من الاشتراكات.
- `verify_payment(self, order_id: str) -> Tuple[bool, Dict[str, Any]]`: التحقق من حالة الدفع باستخدام معرف الطلب.
- `capture_payment(self, order_id: str) -> Tuple[bool, Dict[str, Any]]`: تحصيل الدفع بعد الموافقة عليه من قبل المستخدم.
- `get_payment_details(self, order_id: str) -> Tuple[bool, Dict[str, Any]]`: الحصول على تفاصيل الدفع.
- `verify_transaction_by_id(self, transaction_id: str, user_id: str = None, amount: float = None) -> Tuple[bool, Dict[str, Any]]`: التحقق من معاملة PayPal باستخدام معرف المعاملة.
- `get_transactions(self, start_date: datetime = None, end_date: datetime = None, limit: int = 100) -> Tuple[bool, List[Dict[str, Any]]]`: الحصول على قائمة المعاملات في فترة زمنية محددة.
- `process_webhook_event(self, event_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], Optional[str]]`: معالجة أحداث Webhook من PayPal (مثل اكتمال الدفع، الموافقة على الطلب، إلخ).

## الدوال والفئات - utils.py

### الدوال (Functions)

- `get_text(key: str, lang: str = 'ar', default: str = None, **kwargs) -> str`: الحصول على نص مترجم من قاموس `translations`. يبحث أولاً عن اللغة المطلوبة، ثم اللغة العربية، ثم القيمة الافتراضية، وأخيراً المفتاح نفسه. يدعم تمرير الوسائط للتنسيق.
- `fix_bold_formatting(text: str) -> str`: إصلاح تنسيق النص الغامق في رسائل تيليجرام بإزالة النقطتين (:) من داخل علامات التنسيق (`**`) ووضعها خارجها (مثال: `**عنوان:**` -> `**عنوان**:`) لضمان العرض الصحيح.

## الدوال والفئات - trading_education.py

هذا الملف يحتوي على المنطق الخاص بميزة التعلم التفاعلي باستخدام الذكاء الاصطناعي (Gemini).

### المتغيرات الرئيسية

- `db`: كائن قاعدة بيانات Firestore (يتم تعيينه بواسطة `set_firestore_db`).
- `user_education_state`: قاموس لتخزين حالة التقدم التعليمي للمستخدمين (الفصل الحالي، حالة الاختبار، إلخ).
- `CHAPTERS`: قاموس يحتوي على مواضيع فصول الدورة التعليمية الأساسية (10 فصول) باللغتين العربية والإنجليزية.

### الدوال (Functions)

- `set_firestore_db(firestore_db)`: تعيين كائن قاعدة بيانات Firestore للاستخدام داخل هذه الوحدة.
- `_(text, lang='ar', **kwargs)`: دالة مختصرة للحصول على النص المترجم باستخدام `utils.get_text`.
- `get_user_language(user_id: str) -> str`: جلب اللغة المفضلة للمستخدم من Firestore.
- `handle_learn_trading_ai(update: Update, context: ContextTypes.DEFAULT_TYPE)`: معالج الأمر `/learn_trading_ai`. يبدأ الدورة التعليمية، يتحقق من مفتاح Gemini API، ويوجه المستخدم إلى الدرس المناسب.
- `check_gemini_api_key(user_id: str) -> str | None`: التحقق من وجود مفتاح Gemini API صالح للمستخدم (حالياً يتحقق فقط من القدرة على تهيئة العميل).
- `get_gemini_api_for_user(user_id: str)`: الحصول على كائن نموذج Gemini API مهيأ للمستخدم المحدد.
- `handle_message_for_ai_tutor(update: Update, context: ContextTypes.DEFAULT_TYPE)`: معالجة الرسائل النصية من المستخدم عندما يكون في وضع "سؤال المدرس". يرسل السؤال إلى Gemini للحصول على إجابة.
- `start_or_continue_lesson(update: Update, context: ContextTypes.DEFAULT_TYPE)`: عرض رسالة الترحيب والدرس الحالي للمستخدم أو بدء الدرس الأول.
- `generate_and_send_chapter(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, chapter_number: int, lang: str = 'ar')`: توليد محتوى فصل معين باستخدام Gemini وإرساله للمستخدم مع أزرار التنقل.
- `start_quiz(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str)`: بدء الاختبار التفاعلي للمستخدم بعد إكمال الفصول الأساسية.
- `show_quiz_results_or_next_steps(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str)`: عرض نتائج الاختبار للمستخدم، أو توجيهه للخطوات التالية (مواد مراجعة، فصول تكميلية، أو العودة للقائمة).
- `button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None`: معالج الضغط على أزرار InlineKeyboard الخاصة بالدورة التعليمية (الانتقال بين الفصول، بدء الاختبار، سؤال المدرس، إلخ).
- `generate_quiz_questions(user_id: str, lang: str) -> list`: توليد أسئلة الاختبار باستخدام Gemini بناءً على محتوى الفصول التي درسها المستخدم.
- `send_quiz_question(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str, question_index: int)`: إرسال سؤال الاختبار التالي للمستخدم كاستطلاع رأي (Poll) في تيليجرام.
- `handle_quiz_answer(update: Update, context: ContextTypes.DEFAULT_TYPE)`: معالجة إجابة المستخدم على سؤال الاختبار (Poll).
- `generate_review_material(user_id: str, lang: str, topics: list)`: توليد مواد مراجعة مخصصة باستخدام Gemini بناءً على المواضيع التي أخطأ فيها المستخدم في الاختبار.
- `handle_ask_ai_tutor_button(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str)`: معالجة الضغط على زر "اسأل مدرس الذكاء الاصطناعي"، ووضع المستخدم في حالة استقبال السؤال.
- `generate_chapter_content(user_id: str, chapter_number: int, lang: str = 'ar')`: توليد محتوى نصي لفصل معين باستخدام Gemini.
- `send_chapter(update: Update, context: CallbackContext, chapter_number: int)`: إرسال محتوى فصل معين إلى المستخدم (تستخدم داخلياً).
- `show_supplementary_chapters(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, lang: str)`: عرض قائمة بالفصول التكميلية المقترحة للمستخدم بناءً على نتيجة الاختبار.
- `determine_supplementary_chapters(user_id: str, lang: str, percentage: float)`: تحديد قائمة بالفصول التكميلية المقترحة بناءً على نسبة الإجابات الصحيحة في الاختبار.
- `generate_and_send_supplementary_chapter(update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, chapter_id: str, lang: str)`: توليد وإرسال محتوى فصل تكميلي محدد للمستخدم.
- `handle_ai_conversation(update: Update, context: CallbackContext)`: معالجة الرسائل النصية العامة الموجهة إلى الذكاء الاصطناعي في سياق التعلم (مثل سؤال المدرس).