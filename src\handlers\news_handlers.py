"""
معالجات واجهة المستخدم لنظام الأخبار
"""

import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from telegram.constants import ParseMode
from datetime import datetime
from typing import List

from services.news_system import news_system, NewsType, NewsItem
from utils.text_helpers import get_text

# استيراد subscription_system بشكل آمن
try:
    from services.subscription_system import subscription_system
except ImportError:
    subscription_system = None

# إعداد التسجيل
logger = logging.getLogger(__name__)

async def show_news_menu(update: Update, context: CallbackContext):
    """عرض قائمة الأخبار التلقائية الجديدة"""
    try:
        user_id = str(update.effective_user.id)

        # الحصول على لغة المستخدم
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')

        # التحقق من الاشتراك أو اليوم المجاني
        is_premium = False
        if subscription_system:
            is_premium = subscription_system.is_subscribed_sync(user_id)
            if not is_premium and hasattr(subscription_system, 'free_day_system') and subscription_system.free_day_system:
                is_premium = subscription_system.free_day_system.has_active_free_day(user_id)

        # التحقق من حالة التنبيهات التلقائية
        from services.automatic_news_notifications import automatic_news_notifications
        notifications_enabled = False
        if automatic_news_notifications and user_id in automatic_news_notifications.user_rules:
            notifications_enabled = any(rule.enabled for rule in automatic_news_notifications.user_rules[user_id])

        if lang == 'ar':
            title = "📰 *نظام الأخبار التلقائي*"
            if is_premium:
                description = "🤖 النظام يعمل تلقائياً لجلب وتحليل الأخبار\n"
                description += f"🔔 التنبيهات: {'🟢 مفعلة' if notifications_enabled else '🔴 معطلة'}\n\n"
                description += "يمكنك مشاهدة آخر الأخبار أو إدارة التنبيهات:"
            else:
                description = "🤖 النظام التلقائي متاح للمشتركين فقط\n"
                description += "يمكنك مشاهدة الأخبار العامة مجاناً أو الاشتراك للحصول على:\n"
                description += "• تنبيهات تلقائية للأخبار المهمة\n"
                description += "• تحليلات بالذكاء الاصطناعي\n"
                description += "• تنبيهات العملات الجديدة"
        else:
            title = "📰 *Automatic News System*"
            if is_premium:
                description = "🤖 System automatically fetches and analyzes news\n"
                description += f"🔔 Notifications: {'🟢 Enabled' if notifications_enabled else '🔴 Disabled'}\n\n"
                description += "You can view latest news or manage notifications:"
            else:
                description = "🤖 Automatic system available for subscribers only\n"
                description += "You can view general news for free or subscribe to get:\n"
                description += "• Automatic alerts for important news\n"
                description += "• AI-powered analysis\n"
                description += "• New coin alerts"

        # إنشاء الأزرار الجديدة
        keyboard = []

        if is_premium:
            if lang == 'ar':
                keyboard.extend([
                    [InlineKeyboardButton("📊 آخر الأخبار", callback_data="news_latest")],
                    [InlineKeyboardButton("🔥 الأخبار العاجلة", callback_data="news_breaking")],
                    [InlineKeyboardButton("🆕 العملات الجديدة", callback_data="news_new_coins")],
                    [InlineKeyboardButton("🔔 إدارة التنبيهات", callback_data="news_notifications_settings")],
                    [InlineKeyboardButton("📈 إحصائيات النظام", callback_data="news_system_stats")]
                ])
            else:
                keyboard.extend([
                    [InlineKeyboardButton("📊 Latest News", callback_data="news_latest")],
                    [InlineKeyboardButton("🔥 Breaking News", callback_data="news_breaking")],
                    [InlineKeyboardButton("🆕 New Coins", callback_data="news_new_coins")],
                    [InlineKeyboardButton("🔔 Notification Settings", callback_data="news_notifications_settings")],
                    [InlineKeyboardButton("📈 System Stats", callback_data="news_system_stats")]
                ])
        else:
            if lang == 'ar':
                keyboard.extend([
                    [InlineKeyboardButton("📊 الأخبار العامة (مجاني)", callback_data="news_general_free")],
                    [InlineKeyboardButton("💎 الاشتراك للنظام التلقائي", callback_data="subscription_menu")]
                ])
            else:
                keyboard.extend([
                    [InlineKeyboardButton("📊 General News (Free)", callback_data="news_general_free")],
                    [InlineKeyboardButton("💎 Subscribe for Auto System", callback_data="subscription_menu")]
                ])

        # زر العودة
        if lang == 'ar':
            keyboard.append([InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")])
        else:
            keyboard.append([InlineKeyboardButton("🔙 Back to Main Menu", callback_data="main_menu")])

        reply_markup = InlineKeyboardMarkup(keyboard)
        
        message_text = f"{title}\n\n{description}"
        
        if update.callback_query:
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
        else:
            await update.message.reply_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )
            
    except Exception as e:
        logger.error(f"خطأ في عرض قائمة الأخبار: {str(e)}")
        error_msg = "حدث خطأ في عرض الأخبار" if lang == 'ar' else "Error displaying news"
        if update.callback_query:
            await update.callback_query.answer(error_msg, show_alert=True)
        else:
            await update.message.reply_text(error_msg)

async def show_price_predictions(update: Update, context: CallbackContext):
    """عرض توقعات الأسعار"""
    try:
        user_id = str(update.effective_user.id)
        
        # الحصول على لغة المستخدم
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')
        
        # التحقق من الصلاحيات
        is_premium = False
        if subscription_system:
            is_premium = subscription_system.is_subscribed_sync(user_id)
            if not is_premium and hasattr(subscription_system, 'free_day_system') and subscription_system.free_day_system:
                is_premium = subscription_system.free_day_system.has_active_free_day(user_id)
        
        if not is_premium:
            error_msg = "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else "This feature is for subscribers only"
            await update.callback_query.answer(error_msg, show_alert=True)
            return
        
        # إرسال رسالة تحميل
        loading_msg = "🔄 جاري تحليل الأخبار وتوقعات الأسعار..." if lang == 'ar' else "🔄 Analyzing news and price predictions..."
        await update.callback_query.answer(loading_msg)
        
        # الحصول على توقعات الأسعار
        if news_system:
            predictions = await news_system.get_price_predictions(lang=lang, limit=5)
        else:
            predictions = []
        
        if not predictions:
            no_news_msg = "لا توجد توقعات أسعار متاحة حالياً" if lang == 'ar' else "No price predictions available currently"
            await update.callback_query.edit_message_text(
                text=no_news_msg,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back", 
                        callback_data="news_menu"
                    )
                ]])
            )
            return
        
        # تنسيق الرسالة
        if lang == 'ar':
            message_text = "📈 *توقعات الأسعار*\n\n"
        else:
            message_text = "📈 *Price Predictions*\n\n"
        
        for i, prediction in enumerate(predictions[:3], 1):
            message_text += format_news_item(prediction, lang, show_analysis=True)
            if i < len(predictions[:3]):
                message_text += "\n" + "─" * 30 + "\n\n"
        
        # إضافة أزرار
        keyboard = []
        if len(predictions) > 3:
            if lang == 'ar':
                keyboard.append([InlineKeyboardButton("📄 عرض المزيد", callback_data="news_more_predictions")])
            else:
                keyboard.append([InlineKeyboardButton("📄 Show More", callback_data="news_more_predictions")])
        
        keyboard.append([InlineKeyboardButton(
            "🔙 العودة" if lang == 'ar' else "🔙 Back", 
            callback_data="news_menu"
        )])
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
        
    except Exception as e:
        logger.error(f"خطأ في عرض توقعات الأسعار: {str(e)}")
        error_msg = "حدث خطأ في جلب توقعات الأسعار" if lang == 'ar' else "Error fetching price predictions"
        await update.callback_query.answer(error_msg, show_alert=True)

async def show_new_coin_alerts(update: Update, context: CallbackContext):
    """عرض تنبيهات العملات الجديدة"""
    try:
        user_id = str(update.effective_user.id)
        
        # الحصول على لغة المستخدم
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')
        
        # التحقق من الصلاحيات
        is_premium = False
        if subscription_system:
            is_premium = subscription_system.is_subscribed_sync(user_id)
            if not is_premium and hasattr(subscription_system, 'free_day_system') and subscription_system.free_day_system:
                is_premium = subscription_system.free_day_system.has_active_free_day(user_id)
        
        if not is_premium:
            error_msg = "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else "This feature is for subscribers only"
            await update.callback_query.answer(error_msg, show_alert=True)
            return
        
        # إرسال رسالة تحميل
        loading_msg = "🔄 جاري البحث عن العملات الجديدة..." if lang == 'ar' else "🔄 Searching for new coins..."
        await update.callback_query.answer(loading_msg)
        
        # الحصول على تنبيهات العملات الجديدة
        if news_system:
            new_coins = await news_system.get_new_coin_alerts(lang=lang, limit=3)
        else:
            new_coins = []
        
        if not new_coins:
            no_news_msg = "لا توجد عملات جديدة متاحة حالياً" if lang == 'ar' else "No new coins available currently"
            await update.callback_query.edit_message_text(
                text=no_news_msg,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back", 
                        callback_data="news_menu"
                    )
                ]])
            )
            return
        
        # تنسيق الرسالة
        if lang == 'ar':
            message_text = "🆕 *تنبيهات العملات الجديدة*\n\n"
        else:
            message_text = "🆕 *New Coin Alerts*\n\n"
        
        for i, coin_news in enumerate(new_coins, 1):
            message_text += format_news_item(coin_news, lang, show_analysis=True)
            if i < len(new_coins):
                message_text += "\n" + "─" * 30 + "\n\n"
        
        # إضافة أزرار
        keyboard = [[InlineKeyboardButton(
            "🔙 العودة" if lang == 'ar' else "🔙 Back", 
            callback_data="news_menu"
        )]]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
        
    except Exception as e:
        logger.error(f"خطأ في عرض تنبيهات العملات الجديدة: {str(e)}")
        error_msg = "حدث خطأ في جلب تنبيهات العملات الجديدة" if lang == 'ar' else "Error fetching new coin alerts"
        await update.callback_query.answer(error_msg, show_alert=True)

def format_news_item(news_item: NewsItem, lang: str = 'ar', show_analysis: bool = False) -> str:
    """تنسيق عنصر الخبر للعرض"""
    try:
        # تنسيق التاريخ
        time_ago = get_time_ago(news_item.published_at, lang)
        
        # رمز المشاعر
        sentiment_emoji = {
            'positive': '📈',
            'negative': '📉',
            'neutral': '➡️'
        }.get(news_item.sentiment, '➡️')
        
        # تنسيق الرسالة
        if lang == 'ar':
            formatted_text = f"{sentiment_emoji} *{news_item.title}*\n"
            formatted_text += f"📅 {time_ago}\n"
            formatted_text += f"📰 المصدر: {news_item.source.value}\n"
            
            if news_item.symbols:
                formatted_text += f"💰 العملات: {', '.join(news_item.symbols)}\n"
            
            if show_analysis and news_item.ai_analysis:
                formatted_text += f"\n🤖 *التحليل:*\n{news_item.ai_analysis[:200]}...\n"
            
            if news_item.trading_recommendation and news_item.trading_recommendation != 'HOLD':
                action_emoji = {'BUY': '🟢', 'SELL': '🔴'}.get(news_item.trading_recommendation, '🟡')
                confidence_percent = int(news_item.confidence_score * 100) if news_item.confidence_score else 0
                formatted_text += f"\n{action_emoji} *التوصية:* {news_item.trading_recommendation} ({confidence_percent}%)\n"
        else:
            formatted_text = f"{sentiment_emoji} *{news_item.title}*\n"
            formatted_text += f"📅 {time_ago}\n"
            formatted_text += f"📰 Source: {news_item.source.value}\n"
            
            if news_item.symbols:
                formatted_text += f"💰 Coins: {', '.join(news_item.symbols)}\n"
            
            if show_analysis and news_item.ai_analysis:
                formatted_text += f"\n🤖 *Analysis:*\n{news_item.ai_analysis[:200]}...\n"
            
            if news_item.trading_recommendation and news_item.trading_recommendation != 'HOLD':
                action_emoji = {'BUY': '🟢', 'SELL': '🔴'}.get(news_item.trading_recommendation, '🟡')
                confidence_percent = int(news_item.confidence_score * 100) if news_item.confidence_score else 0
                formatted_text += f"\n{action_emoji} *Recommendation:* {news_item.trading_recommendation} ({confidence_percent}%)\n"
        
        return formatted_text
        
    except Exception as e:
        logger.error(f"خطأ في تنسيق الخبر: {str(e)}")
        return news_item.title if news_item.title else "خبر غير متاح"

async def show_general_news(update: Update, context: CallbackContext, is_free: bool = False):
    """عرض الأخبار العامة"""
    try:
        user_id = str(update.effective_user.id)

        # الحصول على لغة المستخدم
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')

        # التحقق من الصلاحيات للنسخة المدفوعة
        if not is_free:
            is_premium = False
            if subscription_system:
                is_premium = subscription_system.is_subscribed_sync(user_id)
                if not is_premium and hasattr(subscription_system, 'free_day_system') and subscription_system.free_day_system:
                    is_premium = subscription_system.free_day_system.has_active_free_day(user_id)

            if not is_premium:
                error_msg = "هذه الميزة متاحة للمشتركين فقط" if lang == 'ar' else "This feature is for subscribers only"
                await update.callback_query.answer(error_msg, show_alert=True)
                return

        # إرسال رسالة تحميل
        loading_msg = "🔄 جاري جلب آخر الأخبار..." if lang == 'ar' else "🔄 Fetching latest news..."
        await update.callback_query.answer(loading_msg)

        # الحصول على الأخبار العامة
        if news_system:
            if is_free:
                # نسخة مجانية - أخبار بدون تحليل AI
                general_news = await news_system.get_latest_news(limit=5)
            else:
                # نسخة مدفوعة - أخبار مع تحليل AI
                general_news = await news_system.get_analyzed_news(NewsType.GENERAL_CRYPTO, limit=5)
        else:
            general_news = []

        if not general_news:
            no_news_msg = "لا توجد أخبار متاحة حالياً" if lang == 'ar' else "No news available currently"
            await update.callback_query.edit_message_text(
                text=no_news_msg,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back",
                        callback_data="news_menu"
                    )
                ]])
            )
            return

        # تنسيق الرسالة
        if lang == 'ar':
            if is_free:
                message_text = "📊 *الأخبار العامة (مجاني)*\n\n"
            else:
                message_text = "📊 *الأخبار العامة*\n\n"
        else:
            if is_free:
                message_text = "📊 *General News (Free)*\n\n"
            else:
                message_text = "📊 *General News*\n\n"

        for i, news in enumerate(general_news[:3], 1):
            message_text += format_news_item(news, lang, show_analysis=not is_free)
            if i < len(general_news[:3]):
                message_text += "\n" + "─" * 30 + "\n\n"

        # إضافة أزرار
        keyboard = []
        if len(general_news) > 3:
            if lang == 'ar':
                keyboard.append([InlineKeyboardButton("📄 عرض المزيد", callback_data="news_more_general")])
            else:
                keyboard.append([InlineKeyboardButton("📄 Show More", callback_data="news_more_general")])

        if is_free:
            if lang == 'ar':
                keyboard.append([InlineKeyboardButton("💎 الاشتراك للمزيد", callback_data="subscription_menu")])
            else:
                keyboard.append([InlineKeyboardButton("💎 Subscribe for More", callback_data="subscription_menu")])

        keyboard.append([InlineKeyboardButton(
            "🔙 العودة" if lang == 'ar' else "🔙 Back",
            callback_data="news_menu"
        )])

        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض الأخبار العامة: {str(e)}")
        error_msg = "حدث خطأ في جلب الأخبار" if lang == 'ar' else "Error fetching news"
        await update.callback_query.answer(error_msg, show_alert=True)

async def handle_news_callback(update: Update, context: CallbackContext):
    """معالج callbacks الأخبار"""
    try:
        query = update.callback_query
        data = query.data

        if data == "news_menu":
            await show_news_menu(update, context)
        elif data == "news_latest":
            await show_latest_news(update, context)
        elif data == "news_breaking":
            await show_breaking_news(update, context)
        elif data == "news_new_coins":
            await show_new_coin_alerts(update, context)
        elif data == "news_notifications_settings":
            await show_notifications_settings(update, context)
        elif data == "news_system_stats":
            await show_system_stats(update, context)
        elif data == "news_general_free":
            await show_general_news(update, context, is_free=True)
        elif data.startswith("notif_"):
            await handle_notification_toggle(update, context)
        # الوظائف القديمة للتوافق
        elif data == "news_price_predictions":
            await show_price_predictions(update, context)
        elif data == "news_general":
            await show_general_news(update, context, is_free=False)
        elif data == "news_refresh":
            await show_news_menu(update, context)
        else:
            # معالجة callbacks أخرى
            pass

    except Exception as e:
        logger.error(f"خطأ في معالجة callback الأخبار: {str(e)}")
        await query.answer("❌ حدث خطأ في معالجة الطلب")

async def show_latest_news(update: Update, context: CallbackContext):
    """عرض آخر الأخبار من النظام التلقائي"""
    try:
        user_id = str(update.effective_user.id)
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')

        # جلب آخر الأخبار من التخزين المؤقت
        from services.intelligent_news_cache import intelligent_news_cache

        if intelligent_news_cache:
            recent_news = await intelligent_news_cache.get_recent(minutes=60, limit=10)
        else:
            recent_news = []

        if not recent_news:
            no_news_msg = "لا توجد أخبار حديثة متاحة حالياً" if lang == 'ar' else "No recent news available currently"
            await update.callback_query.edit_message_text(
                text=no_news_msg,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back",
                        callback_data="news_menu"
                    )
                ]])
            )
            return

        # تنسيق الرسالة
        if lang == 'ar':
            message_text = "📊 *آخر الأخبار التلقائية*\n\n"
        else:
            message_text = "📊 *Latest Automatic News*\n\n"

        for i, (key, news_data) in enumerate(recent_news[:5], 1):
            if isinstance(news_data, dict):
                title = news_data.get('title', 'بدون عنوان')
                source = news_data.get('source', 'unknown')
                message_text += f"{i}. **{title[:80]}...**\n"
                message_text += f"   📡 المصدر: {source}\n\n"

        # إضافة أزرار
        keyboard = [
            [InlineKeyboardButton(
                "🔄 تحديث" if lang == 'ar' else "🔄 Refresh",
                callback_data="news_latest"
            )],
            [InlineKeyboardButton(
                "🔙 العودة" if lang == 'ar' else "🔙 Back",
                callback_data="news_menu"
            )]
        ]

        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض آخر الأخبار: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ في تحميل الأخبار")

async def show_notifications_settings(update: Update, context: CallbackContext):
    """عرض إعدادات التنبيهات"""
    try:
        user_id = str(update.effective_user.id)
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')

        # التحقق من حالة التنبيهات
        from services.automatic_news_notifications import automatic_news_notifications, NotificationType

        if not automatic_news_notifications:
            await update.callback_query.answer("❌ نظام التنبيهات غير متوفر")
            return

        user_rules = automatic_news_notifications.user_rules.get(user_id, [])

        if lang == 'ar':
            message_text = "🔔 *إعدادات التنبيهات التلقائية*\n\n"
            message_text += "يمكنك تفعيل أو إلغاء التنبيهات لأنواع مختلفة من الأخبار:\n\n"
        else:
            message_text = "🔔 *Automatic Notification Settings*\n\n"
            message_text += "You can enable or disable notifications for different types of news:\n\n"

        # عرض حالة كل نوع من التنبيهات
        notification_types = {
            NotificationType.BREAKING_NEWS: ("🚨 الأخبار العاجلة", "🚨 Breaking News"),
            NotificationType.NEW_COIN: ("🆕 العملات الجديدة", "🆕 New Coins"),
            NotificationType.MARKET_ANALYSIS: ("📊 تحليلات السوق", "📊 Market Analysis"),
            NotificationType.DAILY_SUMMARY: ("📋 الملخص اليومي", "📋 Daily Summary")
        }

        for ntype, (ar_name, en_name) in notification_types.items():
            name = ar_name if lang == 'ar' else en_name
            enabled = any(rule.notification_type == ntype and rule.enabled for rule in user_rules)
            status = "🟢 مفعل" if enabled else "🔴 معطل"
            if lang == 'en':
                status = "🟢 Enabled" if enabled else "🔴 Disabled"
            message_text += f"• {name}: {status}\n"

        # إنشاء الأزرار
        keyboard = []

        for ntype, (ar_name, en_name) in notification_types.items():
            name = ar_name if lang == 'ar' else en_name
            enabled = any(rule.notification_type == ntype and rule.enabled for rule in user_rules)

            if enabled:
                button_text = f"🔴 إلغاء {name}" if lang == 'ar' else f"🔴 Disable {en_name}"
                callback_data = f"notif_disable_{ntype.value}"
            else:
                button_text = f"🟢 تفعيل {name}" if lang == 'ar' else f"🟢 Enable {en_name}"
                callback_data = f"notif_enable_{ntype.value}"

            keyboard.append([InlineKeyboardButton(button_text, callback_data=callback_data)])

        # زر العودة
        keyboard.append([InlineKeyboardButton(
            "🔙 العودة" if lang == 'ar' else "🔙 Back",
            callback_data="news_menu"
        )])

        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض إعدادات التنبيهات: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ في تحميل الإعدادات")

async def handle_notification_toggle(update: Update, context: CallbackContext):
    """معالجة تفعيل/إلغاء التنبيهات"""
    try:
        user_id = str(update.effective_user.id)
        query = update.callback_query
        data = query.data

        from services.automatic_news_notifications import automatic_news_notifications, NotificationType, NotificationPriority

        if not automatic_news_notifications:
            await query.answer("❌ نظام التنبيهات غير متوفر")
            return

        # استخراج نوع العملية ونوع التنبيه
        parts = data.split('_')
        action = parts[1]  # enable أو disable
        notification_type = NotificationType(parts[2])

        if action == "enable":
            # تفعيل التنبيه
            success = await automatic_news_notifications.add_user_rule(
                user_id=user_id,
                notification_type=notification_type,
                min_priority=NotificationPriority.MEDIUM
            )

            if success:
                await query.answer("✅ تم تفعيل التنبيه")
            else:
                await query.answer("❌ فشل في تفعيل التنبيه")

        elif action == "disable":
            # إلغاء التنبيه
            success = await automatic_news_notifications.remove_user_rule(
                user_id=user_id,
                notification_type=notification_type
            )

            if success:
                await query.answer("✅ تم إلغاء التنبيه")
            else:
                await query.answer("❌ فشل في إلغاء التنبيه")

        # إعادة عرض إعدادات التنبيهات
        await show_notifications_settings(update, context)

    except Exception as e:
        logger.error(f"خطأ في تبديل التنبيه: {str(e)}")
        await query.answer("❌ حدث خطأ في تحديث التنبيه")

async def show_system_stats(update: Update, context: CallbackContext):
    """عرض إحصائيات النظام التلقائي"""
    try:
        user_id = str(update.effective_user.id)
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')

        # جمع الإحصائيات من الأنظمة المختلفة
        stats = {}

        # إحصائيات المجدول
        from services.automatic_news_scheduler import automatic_news_scheduler
        if automatic_news_scheduler:
            stats['scheduler'] = automatic_news_scheduler.get_scheduler_status()

        # إحصائيات التخزين المؤقت
        from services.intelligent_news_cache import intelligent_news_cache
        if intelligent_news_cache:
            stats['cache'] = await intelligent_news_cache.get_cache_stats()

        # إحصائيات التنبيهات
        from services.automatic_news_notifications import automatic_news_notifications
        if automatic_news_notifications:
            stats['notifications'] = await automatic_news_notifications.get_notification_stats()

        # إحصائيات معدل الطلبات
        from services.smart_rate_limiter import smart_rate_limiter
        if smart_rate_limiter:
            platforms = ['binance', 'coindesk', 'coingecko', 'gemini']
            stats['api'] = {}
            for platform in platforms:
                platform_stats = await smart_rate_limiter.get_platform_status(platform)
                if platform_stats:
                    stats['api'][platform] = platform_stats

        # تنسيق الرسالة
        if lang == 'ar':
            message_text = "📈 *إحصائيات النظام التلقائي*\n\n"
        else:
            message_text = "📈 *Automatic System Statistics*\n\n"

        # إحصائيات المجدول
        if 'scheduler' in stats:
            scheduler_stats = stats['scheduler']
            if lang == 'ar':
                message_text += "⏰ **المجدول:**\n"
                message_text += f"• الحالة: {scheduler_stats.get('status', 'unknown')}\n"
                message_text += f"• المهام النشطة: {scheduler_stats.get('total_jobs', 0)}\n"
                message_text += f"• المشتركون: {scheduler_stats.get('subscribers_count', 0)}\n\n"
            else:
                message_text += "⏰ **Scheduler:**\n"
                message_text += f"• Status: {scheduler_stats.get('status', 'unknown')}\n"
                message_text += f"• Active Jobs: {scheduler_stats.get('total_jobs', 0)}\n"
                message_text += f"• Subscribers: {scheduler_stats.get('subscribers_count', 0)}\n\n"

        # إحصائيات التخزين المؤقت
        if 'cache' in stats:
            cache_stats = stats['cache']
            if lang == 'ar':
                message_text += "💾 **التخزين المؤقت:**\n"
                message_text += f"• الكفاءة: {cache_stats.get('cache_efficiency', 0):.1f}%\n"
                message_text += f"• الإصابات: {cache_stats.get('hits', 0)}\n"
                message_text += f"• الإخفاقات: {cache_stats.get('misses', 0)}\n"
                message_text += f"• الذاكرة: {cache_stats.get('memory_usage_mb', 0):.1f} MB\n\n"
            else:
                message_text += "💾 **Cache:**\n"
                message_text += f"• Efficiency: {cache_stats.get('cache_efficiency', 0):.1f}%\n"
                message_text += f"• Hits: {cache_stats.get('hits', 0)}\n"
                message_text += f"• Misses: {cache_stats.get('misses', 0)}\n"
                message_text += f"• Memory: {cache_stats.get('memory_usage_mb', 0):.1f} MB\n\n"

        # إحصائيات التنبيهات
        if 'notifications' in stats:
            notif_stats = stats['notifications']
            if lang == 'ar':
                message_text += "🔔 **التنبيهات:**\n"
                message_text += f"• المرسلة: {notif_stats.get('total_sent', 0)}\n"
                message_text += f"• الفاشلة: {notif_stats.get('total_failed', 0)}\n"
                message_text += f"• المستخدمون النشطون: {notif_stats.get('active_users', 0)}\n"
                message_text += f"• معدل النجاح: {notif_stats.get('success_rate', 0):.1f}%\n\n"
            else:
                message_text += "🔔 **Notifications:**\n"
                message_text += f"• Sent: {notif_stats.get('total_sent', 0)}\n"
                message_text += f"• Failed: {notif_stats.get('total_failed', 0)}\n"
                message_text += f"• Active Users: {notif_stats.get('active_users', 0)}\n"
                message_text += f"• Success Rate: {notif_stats.get('success_rate', 0):.1f}%\n\n"

        # إحصائيات API
        if 'api' in stats:
            if lang == 'ar':
                message_text += "📊 **استخدام API:**\n"
            else:
                message_text += "📊 **API Usage:**\n"

            for platform, platform_stats in stats['api'].items():
                usage_pct = platform_stats.get('usage_percentage', {}).get('minute', 0)
                status = platform_stats.get('status', 'unknown')
                message_text += f"• {platform.title()}: {usage_pct:.1f}% ({status})\n"

        # إضافة أزرار
        keyboard = [
            [InlineKeyboardButton(
                "🔄 تحديث" if lang == 'ar' else "🔄 Refresh",
                callback_data="news_system_stats"
            )],
            [InlineKeyboardButton(
                "🔙 العودة" if lang == 'ar' else "🔙 Back",
                callback_data="news_menu"
            )]
        ]

        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض إحصائيات النظام: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ في تحميل الإحصائيات")

async def show_breaking_news(update: Update, context: CallbackContext):
    """عرض الأخبار العاجلة"""
    try:
        user_id = str(update.effective_user.id)
        user_settings = subscription_system.get_user_settings(user_id) if subscription_system else {}
        lang = user_settings.get('lang', 'ar')

        # جلب الأخبار العاجلة من قاعدة البيانات
        from services.intelligent_news_cache import intelligent_news_cache

        breaking_news = []
        if intelligent_news_cache:
            # البحث عن الأخبار العاجلة في التخزين المؤقت
            recent_news = await intelligent_news_cache.get_recent(minutes=120, limit=20)

            # فلترة الأخبار العاجلة
            breaking_keywords = ['breaking', 'urgent', 'alert', 'عاجل', 'تحذير']
            for key, news_data in recent_news:
                if isinstance(news_data, dict):
                    title = news_data.get('title', '').lower()
                    content = news_data.get('content', '').lower()

                    if any(keyword in title or keyword in content for keyword in breaking_keywords):
                        breaking_news.append((key, news_data))

                        if len(breaking_news) >= 5:
                            break

        if not breaking_news:
            no_news_msg = "لا توجد أخبار عاجلة حالياً" if lang == 'ar' else "No breaking news currently"
            await update.callback_query.edit_message_text(
                text=no_news_msg,
                reply_markup=InlineKeyboardMarkup([[
                    InlineKeyboardButton(
                        "🔙 العودة" if lang == 'ar' else "🔙 Back",
                        callback_data="news_menu"
                    )
                ]])
            )
            return

        # تنسيق الرسالة
        if lang == 'ar':
            message_text = "🔥 *الأخبار العاجلة*\n\n"
        else:
            message_text = "🔥 *Breaking News*\n\n"

        for i, (key, news_data) in enumerate(breaking_news, 1):
            title = news_data.get('title', 'بدون عنوان')
            source = news_data.get('source', 'unknown')
            message_text += f"🚨 **{i}. {title[:100]}...**\n"
            message_text += f"   📡 {source}\n\n"

        # إضافة أزرار
        keyboard = [[InlineKeyboardButton(
            "🔙 العودة" if lang == 'ar' else "🔙 Back",
            callback_data="news_menu"
        )]]

        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.MARKDOWN
        )

    except Exception as e:
        logger.error(f"خطأ في عرض الأخبار العاجلة: {str(e)}")
        await update.callback_query.answer("❌ حدث خطأ في تحميل الأخبار العاجلة")

def get_time_ago(published_at: datetime, lang: str = 'ar') -> str:
    """حساب الوقت المنقضي منذ نشر الخبر"""
    try:
        now = datetime.now()
        if published_at.tzinfo:
            import pytz
            now = now.replace(tzinfo=pytz.UTC)

        diff = now - published_at

        if diff.days > 0:
            if lang == 'ar':
                return f"منذ {diff.days} يوم"
            else:
                return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            if lang == 'ar':
                return f"منذ {hours} ساعة"
            else:
                return f"{hours} hours ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            if lang == 'ar':
                return f"منذ {minutes} دقيقة"
            else:
                return f"{minutes} minutes ago"
        else:
            if lang == 'ar':
                return "منذ لحظات"
            else:
                return "moments ago"

    except Exception as e:
        logger.error(f"خطأ في حساب الوقت: {str(e)}")
        return "غير معروف" if lang == 'ar' else "unknown"
