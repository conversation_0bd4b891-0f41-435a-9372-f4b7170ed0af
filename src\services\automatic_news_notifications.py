"""
نظام الإشعارات التلقائية للأخبار المهمة
يرسل إشعارات ذكية للمستخدمين عند ظهور أخبار مهمة أو عملات جديدة
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass
from enum import Enum
import re
import json
from firebase_admin import firestore

logger = logging.getLogger(__name__)

class NotificationType(Enum):
    """أنواع الإشعارات"""
    BREAKING_NEWS = "breaking_news"
    NEW_COIN = "new_coin"
    PRICE_ALERT = "price_alert"
    MARKET_ANALYSIS = "market_analysis"
    DAILY_SUMMARY = "daily_summary"
    URGENT_UPDATE = "urgent_update"

class NotificationPriority(Enum):
    """أولوية الإشعارات"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4

@dataclass
class NotificationRule:
    """قاعدة إشعار"""
    user_id: str
    notification_type: NotificationType
    keywords: List[str] = None
    symbols: List[str] = None
    min_priority: NotificationPriority = NotificationPriority.MEDIUM
    enabled: bool = True
    created_at: datetime = None
    last_triggered: datetime = None

@dataclass
class NotificationMessage:
    """رسالة إشعار"""
    user_id: str
    notification_type: NotificationType
    priority: NotificationPriority
    title: str
    content: str
    data: Dict[str, Any] = None
    created_at: datetime = None
    sent_at: datetime = None
    read_at: datetime = None

class AutomaticNewsNotifications:
    """نظام الإشعارات التلقائية للأخبار"""
    
    def __init__(self, db=None, bot=None):
        self.db = db
        self.bot = bot  # Telegram bot instance
        
        # قواعد الإشعارات للمستخدمين
        self.user_rules: Dict[str, List[NotificationRule]] = {}
        
        # قائمة انتظار الإشعارات
        self.notification_queue: List[NotificationMessage] = []
        
        # إحصائيات الإشعارات
        self.stats = {
            'total_sent': 0,
            'total_failed': 0,
            'by_type': {ntype.value: 0 for ntype in NotificationType},
            'by_priority': {priority.value: 0 for priority in NotificationPriority}
        }
        
        # الكلمات المفتاحية للأخبار المهمة
        self.breaking_news_keywords = [
            # إنجليزي
            'breaking', 'urgent', 'alert', 'crash', 'surge', 'pump', 'dump',
            'hack', 'exploit', 'regulation', 'ban', 'approval', 'launch',
            'partnership', 'acquisition', 'listing', 'delisting',
            # عربي
            'عاجل', 'تحذير', 'انهيار', 'ارتفاع', 'هبوط', 'اختراق',
            'تنظيم', 'حظر', 'موافقة', 'إطلاق', 'شراكة', 'استحواذ', 'إدراج'
        ]
        
        # الكلمات المفتاحية للعملات الجديدة
        self.new_coin_keywords = [
            'new listing', 'new token', 'new coin', 'launch', 'debut',
            'إدراج جديد', 'عملة جديدة', 'رمز جديد', 'إطلاق', 'ظهور'
        ]
        
        # حدود الإرسال لتجنب الإزعاج
        self.rate_limits = {
            NotificationPriority.LOW: timedelta(hours=4),
            NotificationPriority.MEDIUM: timedelta(hours=2),
            NotificationPriority.HIGH: timedelta(minutes=30),
            NotificationPriority.URGENT: timedelta(minutes=5)
        }
        
        # سجل الإشعارات المرسلة لتجنب التكرار
        self.sent_notifications: Set[str] = set()
    
    async def add_user_rule(self, user_id: str, notification_type: NotificationType,
                           keywords: List[str] = None, symbols: List[str] = None,
                           min_priority: NotificationPriority = NotificationPriority.MEDIUM) -> bool:
        """إضافة قاعدة إشعار للمستخدم"""
        try:
            rule = NotificationRule(
                user_id=user_id,
                notification_type=notification_type,
                keywords=keywords or [],
                symbols=symbols or [],
                min_priority=min_priority,
                created_at=datetime.now()
            )
            
            if user_id not in self.user_rules:
                self.user_rules[user_id] = []
            
            self.user_rules[user_id].append(rule)
            
            # حفظ في قاعدة البيانات
            if self.db:
                await self._save_user_rule(rule)
            
            logger.info(f"تم إضافة قاعدة إشعار للمستخدم {user_id}: {notification_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة قاعدة الإشعار: {str(e)}")
            return False
    
    async def remove_user_rule(self, user_id: str, notification_type: NotificationType) -> bool:
        """إزالة قاعدة إشعار للمستخدم"""
        try:
            if user_id in self.user_rules:
                self.user_rules[user_id] = [
                    rule for rule in self.user_rules[user_id]
                    if rule.notification_type != notification_type
                ]
                
                # حذف من قاعدة البيانات
                if self.db:
                    await self._delete_user_rule(user_id, notification_type)
                
                logger.info(f"تم إزالة قاعدة إشعار للمستخدم {user_id}: {notification_type.value}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في إزالة قاعدة الإشعار: {str(e)}")
            return False
    
    async def process_news_for_notifications(self, news_items: List[Any]):
        """معالجة الأخبار لإنشاء الإشعارات"""
        try:
            # تتبع الأخبار التي تم معالجتها
            processed_news_ids = []

            for news_item in news_items:
                # تحليل الخبر لتحديد نوع الإشعار
                notification_type, priority = await self._analyze_news_importance(news_item)

                if notification_type:
                    # العثور على المستخدمين المهتمين
                    interested_users = await self._find_interested_users(news_item, notification_type)

                    # تسجيل المستخدمين المهتمين في الخبر
                    news_id = getattr(news_item, 'id', None)
                    if news_id and interested_users:
                        await self._update_news_interested_users(news_id, interested_users)

                    # إنشاء الإشعارات
                    for user_id in interested_users:
                        await self._create_notification(
                            user_id=user_id,
                            notification_type=notification_type,
                            priority=priority,
                            news_item=news_item
                        )

                    # إضافة معرف الخبر للمعالجة
                    if news_id:
                        processed_news_ids.append(news_id)

            # إرسال الإشعارات المتراكمة
            sent_notifications = await self._process_notification_queue()

            # حذف الأخبار التي تم إرسالها بنجاح
            await self._cleanup_successfully_sent_news(processed_news_ids, sent_notifications)

        except Exception as e:
            logger.error(f"خطأ في معالجة الأخبار للإشعارات: {str(e)}")
    
    async def _analyze_news_importance(self, news_item) -> Tuple[Optional[NotificationType], NotificationPriority]:
        """تحليل أهمية الخبر"""
        title = news_item.title.lower()
        content = news_item.content.lower()
        
        # فحص الأخبار العاجلة
        if any(keyword in title or keyword in content for keyword in self.breaking_news_keywords):
            # تحديد مستوى الأولوية
            urgent_keywords = ['hack', 'exploit', 'crash', 'ban', 'اختراق', 'انهيار', 'حظر']
            if any(keyword in title or keyword in content for keyword in urgent_keywords):
                return NotificationType.BREAKING_NEWS, NotificationPriority.URGENT
            else:
                return NotificationType.BREAKING_NEWS, NotificationPriority.HIGH
        
        # فحص العملات الجديدة
        if any(keyword in title or keyword in content for keyword in self.new_coin_keywords):
            return NotificationType.NEW_COIN, NotificationPriority.HIGH
        
        # فحص تحليلات السوق
        analysis_keywords = ['analysis', 'prediction', 'forecast', 'تحليل', 'توقع', 'تنبؤ']
        if any(keyword in title or keyword in content for keyword in analysis_keywords):
            return NotificationType.MARKET_ANALYSIS, NotificationPriority.MEDIUM
        
        # أخبار عادية
        return None, NotificationPriority.LOW
    
    async def _find_interested_users(self, news_item, notification_type: NotificationType) -> List[str]:
        """العثور على المستخدمين المهتمين بالخبر"""
        interested_users = []

        # إذا كان نوع الإشعار هو أخبار عاجلة أو عملات جديدة، إرسال لجميع المستخدمين
        if notification_type in [NotificationType.BREAKING_NEWS, NotificationType.NEW_COIN]:
            all_users = await self._get_all_active_users()
            for user_id in all_users:
                # فحص حدود الإرسال للأخبار العاجلة والعملات الجديدة
                if await self._check_general_rate_limit(user_id, notification_type):
                    interested_users.append(user_id)
            return interested_users

        # للأنواع الأخرى، استخدام القواعد المخصصة
        for user_id, rules in self.user_rules.items():
            for rule in rules:
                if not rule.enabled:
                    continue

                if rule.notification_type != notification_type:
                    continue

                # فحص الكلمات المفتاحية
                if rule.keywords:
                    title_content = f"{news_item.title} {news_item.content}".lower()
                    if not any(keyword.lower() in title_content for keyword in rule.keywords):
                        continue

                # فحص الرموز
                if rule.symbols and news_item.symbols:
                    if not any(symbol in news_item.symbols for symbol in rule.symbols):
                        continue

                # فحص حدود الإرسال
                if await self._check_rate_limit(user_id, rule):
                    interested_users.append(user_id)
                    break  # قاعدة واحدة كافية لكل مستخدم

        return interested_users
    
    async def _check_rate_limit(self, user_id: str, rule: NotificationRule) -> bool:
        """فحص حدود معدل الإرسال"""
        if not rule.last_triggered:
            return True
        
        time_since_last = datetime.now() - rule.last_triggered
        min_interval = self.rate_limits.get(rule.min_priority, timedelta(hours=1))
        
        return time_since_last >= min_interval

    async def _get_all_active_users(self) -> List[str]:
        """الحصول على جميع المستخدمين النشطين من قاعدة البيانات"""
        try:
            if not self.db:
                logger.warning("قاعدة البيانات غير متوفرة للحصول على المستخدمين")
                return []

            # الحصول على جميع المستخدمين من جدول users
            users_ref = self.db.collection('users')
            users_docs = users_ref.get()

            active_users = []
            for doc in users_docs:
                user_data = doc.to_dict()
                user_id = doc.id

                # التحقق من أن المستخدم ليس محظوراً
                banned_ref = self.db.collection('banned_users').document(user_id).get()
                if banned_ref.exists and banned_ref.to_dict().get('status') == 'banned':
                    continue

                active_users.append(user_id)

            logger.info(f"تم العثور على {len(active_users)} مستخدم نشط")
            return active_users

        except Exception as e:
            logger.error(f"خطأ في الحصول على المستخدمين النشطين: {str(e)}")
            return []

    async def _check_general_rate_limit(self, user_id: str, notification_type: NotificationType) -> bool:
        """فحص حدود الإرسال العامة للأخبار العاجلة والعملات الجديدة"""
        try:
            # حدود مختلفة حسب نوع الإشعار
            if notification_type == NotificationType.BREAKING_NEWS:
                # الأخبار العاجلة: حد أقصى 5 إشعارات يومياً
                max_per_day = 5
            elif notification_type == NotificationType.NEW_COIN:
                # العملات الجديدة: حد أقصى 3 إشعارات يومياً
                max_per_day = 3
            else:
                return True

            # استخدام نهج مبسط لتجنب مشكلة الفهارس المركبة
            # فحص عدد الإشعارات المرسلة اليوم من خلال إحصائيات المستخدم
            today = datetime.now().date().isoformat()
            
            if self.db:
                try:
                    # البحث في إحصائيات المستخدم بدلاً من الاستعلامات المعقدة
                    stats_doc = self.db.collection('user_notification_stats').document(user_id).get()
                    
                    if stats_doc.exists:
                        stats_data = stats_doc.to_dict()
                        daily_stats = stats_data.get('daily', {}).get(today, {})
                        by_type = daily_stats.get('by_type', {})
                        sent_today = by_type.get(notification_type.value, 0)
                        
                        logger.debug(f"المستخدم {user_id}: تم إرسال {sent_today} إشعار من نوع {notification_type.value} اليوم")
                        return sent_today < max_per_day
                    else:
                        # إذا لم توجد إحصائيات، السماح بالإرسال
                        logger.debug(f"لا توجد إحصائيات للمستخدم {user_id}, السماح بالإرسال")
                        return True
                        
                except Exception as db_error:
                    logger.warning(f"خطأ في قراءة إحصائيات المستخدم {user_id}: {str(db_error)}")
                    # في حالة خطأ قاعدة البيانات، استخدام ذاكرة محلية مؤقتة
                    return await self._check_local_rate_limit(user_id, notification_type, max_per_day)
            
            # إذا لم تكن قاعدة البيانات متوفرة، استخدام ذاكرة محلية
            return await self._check_local_rate_limit(user_id, notification_type, max_per_day)

        except Exception as e:
            logger.error(f"خطأ في فحص حدود الإرسال العامة: {str(e)}")
            return True  # في حالة الخطأ، السماح بالإرسال

    async def _check_local_rate_limit(self, user_id: str, notification_type: NotificationType, max_per_day: int) -> bool:
        """فحص حدود الإرسال باستخدام ذاكرة محلية مؤقتة"""
        try:
            # إنشاء مفتاح للذاكرة المحلية
            today = datetime.now().date().isoformat()
            cache_key = f"{user_id}_{notification_type.value}_{today}"
            
            # إنشاء ذاكرة محلية إذا لم تكن موجودة
            if not hasattr(self, '_local_rate_cache'):
                self._local_rate_cache = {}
            
            # الحصول على العدد الحالي أو تهيئته بصفر
            current_count = self._local_rate_cache.get(cache_key, 0)
            
            # فحص الحد الأقصى
            if current_count >= max_per_day:
                logger.debug(f"تم الوصول للحد الأقصى للمستخدم {user_id} لنوع {notification_type.value}: {current_count}/{max_per_day}")
                return False
            
            # زيادة العداد
            self._local_rate_cache[cache_key] = current_count + 1
            logger.debug(f"تحديث العداد المحلي للمستخدم {user_id}: {self._local_rate_cache[cache_key]}/{max_per_day}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في فحص الحدود المحلية: {str(e)}")
            return True

    async def _create_notification(self, user_id: str, notification_type: NotificationType,
                                 priority: NotificationPriority, news_item):
        """إنشاء إشعار"""
        try:
            # إنشاء معرف فريد للإشعار لتجنب التكرار
            notification_id = f"{user_id}_{notification_type.value}_{hash(news_item.title)}"
            
            if notification_id in self.sent_notifications:
                return  # تم إرسال هذا الإشعار بالفعل
            
            # تحديد لغة المستخدم مع التحقق من التناسق
            user_lang = await self.verify_and_fix_user_language_consistency(user_id)

            # إنشاء عنوان ومحتوى الإشعار
            title, content = await self._format_notification_content(notification_type, news_item, user_lang)
            
            notification = NotificationMessage(
                user_id=user_id,
                notification_type=notification_type,
                priority=priority,
                title=title,
                content=content,
                data={
                    'news_id': getattr(news_item, 'id', None),
                    'source': getattr(news_item, 'source', None),
                    'url': getattr(news_item, 'url', None),
                    'symbols': getattr(news_item, 'symbols', [])
                },
                created_at=datetime.now()
            )
            
            self.notification_queue.append(notification)
            self.sent_notifications.add(notification_id)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الإشعار: {str(e)}")
    
    async def _format_notification_content(self, notification_type: NotificationType,
                                         news_item, lang: str = 'ar') -> Tuple[str, str]:
        """تنسيق محتوى الإشعار حسب اللغة"""

        # تحديد العناوين حسب اللغة
        if lang == 'en':
            titles = {
                NotificationType.BREAKING_NEWS: "🚨 Breaking News",
                NotificationType.NEW_COIN: "🆕 New Coin",
                NotificationType.MARKET_ANALYSIS: "📊 Market Analysis",
                NotificationType.PRICE_ALERT: "🔔 Price Alert",
                NotificationType.DAILY_SUMMARY: "📊 Daily Summary",
                NotificationType.URGENT_UPDATE: "⚠️ Urgent Update"
            }
            icons = {
                NotificationType.BREAKING_NEWS: "📰",
                NotificationType.NEW_COIN: "💰",
                NotificationType.MARKET_ANALYSIS: "📈",
                NotificationType.PRICE_ALERT: "💲",
                NotificationType.DAILY_SUMMARY: "📊",
                NotificationType.URGENT_UPDATE: "⚠️"
            }
        else:
            titles = {
                NotificationType.BREAKING_NEWS: "🚨 خبر عاجل",
                NotificationType.NEW_COIN: "🆕 عملة جديدة",
                NotificationType.MARKET_ANALYSIS: "📊 تحليل السوق",
                NotificationType.PRICE_ALERT: "🔔 تنبيه سعر",
                NotificationType.DAILY_SUMMARY: "📊 الملخص اليومي",
                NotificationType.URGENT_UPDATE: "⚠️ تحديث مهم"
            }
            icons = {
                NotificationType.BREAKING_NEWS: "📰",
                NotificationType.NEW_COIN: "💰",
                NotificationType.MARKET_ANALYSIS: "📈",
                NotificationType.PRICE_ALERT: "💲",
                NotificationType.DAILY_SUMMARY: "📊",
                NotificationType.URGENT_UPDATE: "⚠️"
            }

        # الحصول على العنوان والأيقونة
        title = titles.get(notification_type, "📢 إشعار" if lang == 'ar' else "📢 Notification")
        icon = icons.get(notification_type, "📢")

        # تنسيق المحتوى
        news_title = news_item.title
        news_content = news_item.content[:200] + "..." if len(news_item.content) > 200 else news_item.content

        # ترجمة المحتوى إذا كانت اللغة إنجليزية
        if lang == 'en':
            news_title = self._translate_to_english(news_title)
            news_content = self._translate_to_english(news_content)

        content = f"{icon} {news_title}\n\n{news_content}"

        # إضافة الرموز إذا كانت متوفرة
        if hasattr(news_item, 'symbols') and news_item.symbols:
            symbols_text = " ".join([f"#{symbol}" for symbol in news_item.symbols[:3]])
            symbols_label = "🏷️" if lang == 'ar' else "🏷️ Tags:"
            content += f"\n\n{symbols_label} {symbols_text}"

        return title, content
    
    async def _process_notification_queue(self):
        """معالجة قائمة انتظار الإشعارات"""
        if not self.notification_queue:
            return []

        # ترتيب حسب الأولوية
        self.notification_queue.sort(key=lambda x: x.priority.value, reverse=True)

        sent_count = 0
        failed_count = 0
        sent_notifications = []

        for notification in self.notification_queue.copy():
            try:
                success = await self._send_notification(notification)
                if success:
                    sent_count += 1
                    self.stats['total_sent'] += 1
                    self.stats['by_type'][notification.notification_type.value] += 1
                    self.stats['by_priority'][notification.priority.value] += 1
                    sent_notifications.append(notification)

                    # تحديث حالة الإرسال في الخبر
                    await self._mark_news_sent_to_user(notification)
                else:
                    failed_count += 1
                    self.stats['total_failed'] += 1

                # إزالة من القائمة
                self.notification_queue.remove(notification)

                # تأخير قصير لتجنب الإزعاج
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"خطأ في إرسال الإشعار: {str(e)}")
                failed_count += 1
                self.stats['total_failed'] += 1

        if sent_count > 0 or failed_count > 0:
            logger.info(f"تم إرسال {sent_count} إشعار، فشل {failed_count}")

        return sent_notifications

    async def _send_notification(self, notification: NotificationMessage) -> bool:
        """إرسال الإشعار عبر Telegram مع مراعاة لغة المستخدم"""
        if not self.bot:
            logger.warning("Telegram bot غير متوفر لإرسال الإشعارات")
            return False

        try:
            # تحديد لغة المستخدم من قاعدة البيانات مع التحقق من التناسق
            user_lang = await self.verify_and_fix_user_language_consistency(notification.user_id)
            logger.debug(f"🌍 لغة المستخدم {notification.user_id} المحددة: {user_lang}")

            # التحقق من تفضيلات الإشعارات للمستخدم
            user_prefs = await self._get_user_notification_preferences(notification.user_id)

            # التحقق من أن الإشعارات مفعلة للمستخدم
            if not user_prefs.get('enabled', True):
                logger.debug(f"الإشعارات معطلة للمستخدم {notification.user_id}")
                return False

            # التحقق من أن نوع الإشعار مسموح
            allowed_types = user_prefs.get('types', [])
            if allowed_types and notification.notification_type.value not in allowed_types:
                logger.debug(f"نوع الإشعار {notification.notification_type.value} غير مسموح للمستخدم {notification.user_id}")
                return False

            # تنسيق الرسالة حسب اللغة المحفوظة في قاعدة البيانات
            if user_lang == 'en':
                # ترجمة العنوان والمحتوى للإنجليزية
                title = self._translate_to_english(notification.title)
                content = self._translate_to_english(notification.content)
                message_text = f"<b>{title}</b>\n\n{content}"
                read_more_text = "🔗 Read More"
            else:
                # استخدام النص العربي الأصلي
                message_text = f"<b>{notification.title}</b>\n\n{notification.content}"
                read_more_text = "🔗 قراءة المزيد"

            # إضافة أزرار إضافية إذا كانت متوفرة
            keyboard = None
            if notification.data and notification.data.get('url'):
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([[
                    InlineKeyboardButton(read_more_text, url=notification.data['url'])
                ]])

            # إرسال الرسالة
            logger.info(f"📤 إرسال إشعار {notification.notification_type.value} للمستخدم {notification.user_id} باللغة {user_lang}")

            await self.bot.send_message(
                chat_id=notification.user_id,
                text=message_text,
                parse_mode='HTML',
                reply_markup=keyboard,
                disable_web_page_preview=True
            )

            notification.sent_at = datetime.now()

            # تحديث إحصائيات الإشعارات في قاعدة البيانات
            await self._update_user_notification_stats(
                notification.user_id,
                notification.notification_type,
                user_lang
            )

            # حفظ الإشعار في قاعدة البيانات
            if self.db:
                await self._save_notification(notification)

            logger.info(f"✅ تم إرسال إشعار {notification.notification_type.value} للمستخدم {notification.user_id} باللغة {user_lang} بنجاح")
            return True

        except Exception as e:
            logger.error(f"خطأ في إرسال الإشعار للمستخدم {notification.user_id}: {str(e)}")
            return False

    async def _get_user_language(self, user_id: str) -> str:
        """تحديد لغة المستخدم من قاعدة البيانات بناءً على الهيكل الفعلي - محسن ومُصلح"""
        try:
            if not self.db:
                logger.warning("قاعدة البيانات غير متوفرة، استخدام اللغة الافتراضية")
                return 'ar'

            # محاولة الحصول على اللغة من نظام الاشتراكات أولاً (الأكثر دقة)
            try:
                from services.subscription_system import subscription_system
                if subscription_system:
                    user_settings = subscription_system.get_user_settings(user_id)
                    if user_settings and isinstance(user_settings, dict):
                        # البحث عن اللغة بترتيب أولوية
                        lang = user_settings.get('lang') or user_settings.get('language')
                        if lang and lang in ['ar', 'en']:
                            logger.debug(f"✅ تم العثور على لغة المستخدم {user_id} من نظام الاشتراكات: {lang}")
                            # التأكد من تزامن البيانات في جميع المواقع
                            await self._sync_user_language_across_collections(user_id, lang)
                            return lang
                        elif lang:
                            logger.warning(f"⚠️ لغة غير صحيحة {lang} في نظام الاشتراكات للمستخدم {user_id}")
            except Exception as e:
                logger.debug(f"لم يتم العثور على اللغة في نظام الاشتراكات للمستخدم {user_id}: {str(e)}")

            # البحث في مصادر متعددة وجمع النتائج للمقارنة
            found_languages = {}

            # البحث في user_settings
            try:
                settings_doc = self.db.collection('user_settings').document(user_id).get()
                if settings_doc.exists:
                    settings_data = settings_doc.to_dict()
                    lang = settings_data.get('lang') or settings_data.get('language')
                    if lang and lang in ['ar', 'en']:
                        found_languages['user_settings'] = lang
                        logger.debug(f"🔍 وُجدت لغة في user_settings للمستخدم {user_id}: {lang}")
            except Exception as e:
                logger.debug(f"خطأ في قراءة user_settings للمستخدم {user_id}: {str(e)}")

            # البحث في users collection
            try:
                user_doc = self.db.collection('users').document(user_id).get()
                if user_doc.exists:
                    user_data = user_doc.to_dict()
                    lang = user_data.get('language') or user_data.get('lang')
                    if lang and lang in ['ar', 'en']:
                        found_languages['users'] = lang
                        logger.debug(f"🔍 وُجدت لغة في users للمستخدم {user_id}: {lang}")
            except Exception as e:
                logger.debug(f"خطأ في قراءة users للمستخدم {user_id}: {str(e)}")

            # البحث في user_preferences
            try:
                prefs_doc = self.db.collection('user_preferences').document(user_id).get()
                if prefs_doc.exists:
                    prefs_data = prefs_doc.to_dict()
                    lang = prefs_data.get('language') or prefs_data.get('lang')
                    if lang and lang in ['ar', 'en']:
                        found_languages['user_preferences'] = lang
                        logger.debug(f"🔍 وُجدت لغة في user_preferences للمستخدم {user_id}: {lang}")
            except Exception as e:
                logger.debug(f"خطأ في قراءة user_preferences للمستخدم {user_id}: {str(e)}")

            # البحث في notification_preferences
            try:
                notif_prefs_doc = self.db.collection('notification_preferences').document(user_id).get()
                if notif_prefs_doc.exists:
                    notif_data = notif_prefs_doc.to_dict()
                    lang = notif_data.get('language') or notif_data.get('lang')
                    if lang and lang in ['ar', 'en']:
                        found_languages['notification_preferences'] = lang
                        logger.debug(f"🔍 وُجدت لغة في notification_preferences للمستخدم {user_id}: {lang}")
            except Exception as e:
                logger.debug(f"خطأ في قراءة notification_preferences للمستخدم {user_id}: {str(e)}")

            # تحليل النتائج واختيار اللغة الأكثر موثوقية
            if found_languages:
                # إذا كانت جميع المصادر متفقة، استخدم اللغة المتفق عليها
                unique_languages = set(found_languages.values())
                if len(unique_languages) == 1:
                    final_lang = list(unique_languages)[0]
                    logger.info(f"✅ جميع المصادر متفقة على لغة المستخدم {user_id}: {final_lang}")
                    return final_lang
                else:
                    # إذا كانت هناك تناقضات، استخدم الأولوية: user_settings > users > user_preferences > notification_preferences
                    priority_order = ['user_settings', 'users', 'user_preferences', 'notification_preferences']
                    for source in priority_order:
                        if source in found_languages:
                            final_lang = found_languages[source]
                            logger.warning(f"⚠️ تناقض في لغة المستخدم {user_id}، استخدام {source}: {final_lang}")
                            logger.info(f"🔧 جاري تزامن اللغة عبر جميع المجموعات...")
                            # تزامن اللغة عبر جميع المجموعات
                            await self._sync_user_language_across_collections(user_id, final_lang)
                            return final_lang

            # إذا لم يتم العثور على المستخدم في أي جدول، محاولة تحديد اللغة من معلومات Telegram
            logger.info(f"🔍 لم يتم العثور على لغة للمستخدم {user_id}، محاولة تحديد اللغة من مصادر أخرى...")
            detected_lang = await self._detect_user_language_from_telegram(user_id)

            # إنشاء إعدادات افتراضية مع اللغة المكتشفة
            await self._create_default_user_settings(user_id, detected_lang)

            return detected_lang

        except Exception as e:
            logger.error(f"❌ خطأ عام في تحديد لغة المستخدم {user_id}: {str(e)}")
            return 'ar'

    async def _sync_user_language_across_collections(self, user_id: str, lang: str):
        """تزامن لغة المستخدم عبر جميع المجموعات في قاعدة البيانات"""
        try:
            if not self.db or lang not in ['ar', 'en']:
                return

            logger.info(f"🔄 جاري تزامن لغة المستخدم {user_id} إلى {lang} عبر جميع المجموعات...")

            # تحديث user_settings
            try:
                settings_ref = self.db.collection('user_settings').document(user_id)
                settings_ref.set({
                    'lang': lang,
                    'language': lang,
                    'updated_at': datetime.now().isoformat()
                }, merge=True)
                logger.debug(f"✅ تم تحديث user_settings للمستخدم {user_id}")
            except Exception as e:
                logger.error(f"❌ خطأ في تحديث user_settings للمستخدم {user_id}: {str(e)}")

            # تحديث notification_preferences
            try:
                notif_ref = self.db.collection('notification_preferences').document(user_id)
                notif_ref.set({
                    'language': lang,
                    'lang': lang,
                    'updated_at': datetime.now().isoformat()
                }, merge=True)
                logger.debug(f"✅ تم تحديث notification_preferences للمستخدم {user_id}")
            except Exception as e:
                logger.error(f"❌ خطأ في تحديث notification_preferences للمستخدم {user_id}: {str(e)}")

            # تحديث نظام الاشتراكات إذا كان متاحاً
            try:
                from services.subscription_system import subscription_system
                if subscription_system:
                    subscription_system.update_user_settings(user_id, lang=lang, language=lang)
                    logger.debug(f"✅ تم تحديث نظام الاشتراكات للمستخدم {user_id}")
            except Exception as e:
                logger.error(f"❌ خطأ في تحديث نظام الاشتراكات للمستخدم {user_id}: {str(e)}")

            logger.info(f"✅ تم تزامن لغة المستخدم {user_id} بنجاح")

        except Exception as e:
            logger.error(f"❌ خطأ في تزامن لغة المستخدم {user_id}: {str(e)}")

    async def _detect_user_language_from_telegram(self, user_id: str) -> str:
        """محاولة تحديد لغة المستخدم من معلومات Telegram أو مصادر أخرى"""
        try:
            # محاولة الحصول على معلومات المستخدم من Telegram
            if self.bot:
                try:
                    chat_member = await self.bot.get_chat_member(chat_id=user_id, user_id=user_id)
                    if chat_member and chat_member.user:
                        # فحص كود اللغة من Telegram
                        telegram_lang = getattr(chat_member.user, 'language_code', None)
                        if telegram_lang:
                            # تحويل كود اللغة إلى اللغات المدعومة
                            if telegram_lang.startswith('ar'):
                                logger.info(f"🌍 تم اكتشاف اللغة العربية للمستخدم {user_id} من Telegram")
                                return 'ar'
                            elif telegram_lang.startswith('en'):
                                logger.info(f"🌍 تم اكتشاف اللغة الإنجليزية للمستخدم {user_id} من Telegram")
                                return 'en'
                except Exception as e:
                    logger.debug(f"لم يتم الحصول على معلومات اللغة من Telegram للمستخدم {user_id}: {str(e)}")

            # إذا لم يتم العثور على معلومات من Telegram، استخدام العربية كافتراضي
            logger.info(f"🔤 استخدام اللغة الافتراضية (العربية) للمستخدم {user_id}")
            return 'ar'

        except Exception as e:
            logger.error(f"❌ خطأ في اكتشاف لغة المستخدم {user_id}: {str(e)}")
            return 'ar'

    async def _create_default_user_settings(self, user_id: str, lang: str = 'ar'):
        """إنشاء إعدادات افتراضية للمستخدم الجديد - محسن"""
        try:
            if not self.db:
                logger.warning("قاعدة البيانات غير متوفرة لإنشاء الإعدادات الافتراضية")
                return

            # التأكد من أن اللغة صحيحة
            if lang not in ['ar', 'en']:
                lang = 'ar'

            logger.info(f"🆕 إنشاء إعدادات افتراضية للمستخدم {user_id} باللغة {lang}")

            # إنشاء إعدادات افتراضية في user_settings
            default_settings = {
                'lang': lang,
                'language': lang,  # للتوافق مع الحقول القديمة
                'lang_selected': False,  # يشير إلى أن المستخدم لم يختر اللغة بعد
                'notifications_enabled': True,
                'timezone': 'UTC',
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat()
            }

            settings_ref = self.db.collection('user_settings').document(user_id)
            settings_ref.set(default_settings, merge=True)

            # إنشاء تفضيلات إشعارات افتراضية
            default_notification_prefs = {
                'enabled': True,
                'language': lang,
                'lang': lang,  # للتوافق
                'types': [
                    NotificationType.BREAKING_NEWS.value,
                    NotificationType.NEW_COIN.value
                ],
                'max_daily': {
                    NotificationType.BREAKING_NEWS.value: 5,
                    NotificationType.NEW_COIN.value: 3,
                    NotificationType.MARKET_ANALYSIS.value: 2,
                    NotificationType.DAILY_SUMMARY.value: 1
                },
                'created_at': datetime.now().isoformat()
            }

            notif_prefs_ref = self.db.collection('notification_preferences').document(user_id)
            notif_prefs_ref.set(default_notification_prefs, merge=True)

            # تحديث نظام الاشتراكات أيضاً
            try:
                from services.subscription_system import subscription_system
                if subscription_system:
                    subscription_system.update_user_settings(user_id, lang=lang, language=lang, lang_selected=False)
                    logger.debug(f"✅ تم تحديث نظام الاشتراكات للمستخدم الجديد {user_id}")
            except Exception as e:
                logger.error(f"❌ خطأ في تحديث نظام الاشتراكات للمستخدم الجديد {user_id}: {str(e)}")

            logger.info(f"✅ تم إنشاء إعدادات افتراضية للمستخدم {user_id} باللغة {lang}")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء الإعدادات الافتراضية للمستخدم {user_id}: {str(e)}")

    async def verify_and_fix_user_language_consistency(self, user_id: str) -> str:
        """التحقق من تناسق بيانات اللغة للمستخدم وإصلاح أي تناقضات"""
        try:
            logger.info(f"🔍 فحص تناسق بيانات اللغة للمستخدم {user_id}...")

            # جمع بيانات اللغة من جميع المصادر
            language_sources = {}

            # فحص نظام الاشتراكات
            try:
                from services.subscription_system import subscription_system
                if subscription_system:
                    settings = subscription_system.get_user_settings(user_id)
                    if settings:
                        lang = settings.get('lang') or settings.get('language')
                        if lang and lang in ['ar', 'en']:
                            language_sources['subscription_system'] = lang
            except Exception as e:
                logger.debug(f"خطأ في فحص نظام الاشتراكات: {str(e)}")

            # فحص user_settings
            try:
                settings_doc = self.db.collection('user_settings').document(user_id).get()
                if settings_doc.exists:
                    data = settings_doc.to_dict()
                    lang = data.get('lang') or data.get('language')
                    if lang and lang in ['ar', 'en']:
                        language_sources['user_settings'] = lang
            except Exception as e:
                logger.debug(f"خطأ في فحص user_settings: {str(e)}")

            # فحص notification_preferences
            try:
                notif_doc = self.db.collection('notification_preferences').document(user_id).get()
                if notif_doc.exists:
                    data = notif_doc.to_dict()
                    lang = data.get('language') or data.get('lang')
                    if lang and lang in ['ar', 'en']:
                        language_sources['notification_preferences'] = lang
            except Exception as e:
                logger.debug(f"خطأ في فحص notification_preferences: {str(e)}")

            # تحليل النتائج
            if not language_sources:
                logger.warning(f"⚠️ لم يتم العثور على أي بيانات لغة للمستخدم {user_id}")
                # إنشاء إعدادات افتراضية
                detected_lang = await self._detect_user_language_from_telegram(user_id)
                await self._create_default_user_settings(user_id, detected_lang)
                return detected_lang

            # فحص التناسق
            unique_languages = set(language_sources.values())
            if len(unique_languages) == 1:
                # جميع المصادر متفقة
                final_lang = list(unique_languages)[0]
                logger.info(f"✅ بيانات اللغة متناسقة للمستخدم {user_id}: {final_lang}")
                return final_lang
            else:
                # هناك تناقضات - إصلاحها
                logger.warning(f"⚠️ تناقض في بيانات اللغة للمستخدم {user_id}: {language_sources}")

                # اختيار اللغة الأكثر موثوقية (نظام الاشتراكات له الأولوية)
                priority_order = ['subscription_system', 'user_settings', 'notification_preferences']
                final_lang = 'ar'  # افتراضي

                for source in priority_order:
                    if source in language_sources:
                        final_lang = language_sources[source]
                        break

                logger.info(f"🔧 إصلاح تناقض اللغة للمستخدم {user_id} إلى: {final_lang}")
                await self._sync_user_language_across_collections(user_id, final_lang)
                return final_lang

        except Exception as e:
            logger.error(f"❌ خطأ في فحص تناسق اللغة للمستخدم {user_id}: {str(e)}")
            return 'ar'

    async def fix_all_users_language_consistency(self):
        """إصلاح مشاكل اللغة لجميع المستخدمين في النظام"""
        try:
            logger.info("🔧 بدء إصلاح مشاكل اللغة لجميع المستخدمين...")

            if not self.db:
                logger.error("❌ قاعدة البيانات غير متوفرة")
                return

            # الحصول على جميع المستخدمين
            users_processed = 0
            users_fixed = 0

            # فحص user_settings
            try:
                settings_docs = self.db.collection('user_settings').get()
                for doc in settings_docs:
                    user_id = doc.id
                    if user_id.startswith('_'):  # تجاهل الوثائق الوصفية
                        continue

                    users_processed += 1
                    original_lang = await self._get_user_language(user_id)
                    fixed_lang = await self.verify_and_fix_user_language_consistency(user_id)

                    if original_lang != fixed_lang:
                        users_fixed += 1
                        logger.info(f"✅ تم إصلاح لغة المستخدم {user_id}: {original_lang} → {fixed_lang}")

                    # تجنب الحمل الزائد على قاعدة البيانات
                    if users_processed % 10 == 0:
                        await asyncio.sleep(0.1)

            except Exception as e:
                logger.error(f"❌ خطأ في معالجة user_settings: {str(e)}")

            logger.info(f"✅ تم الانتهاء من إصلاح مشاكل اللغة. معالج: {users_processed}, مُصلح: {users_fixed}")

        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح مشاكل اللغة لجميع المستخدمين: {str(e)}")

    async def _update_user_notification_stats(self, user_id: str, notification_type: NotificationType, lang: str):
        """تحديث إحصائيات الإشعارات للمستخدم في قاعدة البيانات"""
        try:
            if not self.db:
                return

            # تحديث إحصائيات المستخدم
            stats_ref = self.db.collection('user_notification_stats').document(user_id)
            today = datetime.now().date().isoformat()

            # البيانات المراد تحديثها
            update_data = {
                f'daily.{today}.total': firestore.Increment(1),
                f'daily.{today}.by_type.{notification_type.value}': firestore.Increment(1),
                f'daily.{today}.by_language.{lang}': firestore.Increment(1),
                'total_notifications': firestore.Increment(1),
                'last_notification': datetime.now().isoformat(),
                'preferred_language': lang
            }

            stats_ref.set(update_data, merge=True)
            logger.debug(f"تم تحديث إحصائيات الإشعارات للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الإشعارات للمستخدم {user_id}: {str(e)}")

    async def _get_user_notification_preferences(self, user_id: str) -> dict:
        """الحصول على تفضيلات الإشعارات للمستخدم"""
        try:
            if not self.db:
                return {'enabled': True, 'types': [], 'language': 'ar'}

            # البحث في جدول تفضيلات الإشعارات
            prefs_doc = self.db.collection('notification_preferences').document(user_id).get()
            if prefs_doc.exists:
                return prefs_doc.to_dict()

            # إنشاء تفضيلات افتراضية
            default_prefs = {
                'enabled': True,
                'types': [
                    NotificationType.BREAKING_NEWS.value,
                    NotificationType.NEW_COIN.value
                ],
                'language': await self._get_user_language(user_id),
                'max_daily': {
                    NotificationType.BREAKING_NEWS.value: 5,
                    NotificationType.NEW_COIN.value: 3
                },
                'created_at': datetime.now().isoformat()
            }

            # حفظ التفضيلات الافتراضية
            prefs_ref = self.db.collection('notification_preferences').document(user_id)
            prefs_ref.set(default_prefs)

            return default_prefs

        except Exception as e:
            logger.error(f"خطأ في الحصول على تفضيلات الإشعارات للمستخدم {user_id}: {str(e)}")
            return {'enabled': True, 'types': [], 'language': 'ar'}

    async def get_language_usage_stats(self) -> dict:
        """الحصول على إحصائيات استخدام اللغات"""
        try:
            if not self.db:
                return {}

            stats = {
                'total_users': 0,
                'by_language': {'ar': 0, 'en': 0},
                'notifications_sent': {
                    'total': 0,
                    'by_language': {'ar': 0, 'en': 0},
                    'by_type': {}
                },
                'last_updated': datetime.now().isoformat()
            }

            # إحصائيات المستخدمين حسب اللغة
            users_docs = self.db.collection('user_settings').get()
            for doc in users_docs:
                if doc.id.startswith('_'):  # تجاهل الوثائق الوصفية
                    continue

                stats['total_users'] += 1
                user_data = doc.to_dict()
                lang = user_data.get('lang', 'ar')

                if lang in stats['by_language']:
                    stats['by_language'][lang] += 1
                else:
                    stats['by_language'][lang] = 1

            # إحصائيات الإشعارات المرسلة
            today = datetime.now().date().isoformat()
            notification_stats_docs = self.db.collection('user_notification_stats').get()

            for doc in notification_stats_docs:
                if doc.id.startswith('_'):
                    continue

                user_stats = doc.to_dict()
                daily_stats = user_stats.get('daily', {}).get(today, {})

                # إجمالي الإشعارات
                total_today = daily_stats.get('total', 0)
                stats['notifications_sent']['total'] += total_today

                # حسب اللغة
                by_lang = daily_stats.get('by_language', {})
                for lang, count in by_lang.items():
                    if lang in stats['notifications_sent']['by_language']:
                        stats['notifications_sent']['by_language'][lang] += count
                    else:
                        stats['notifications_sent']['by_language'][lang] = count

                # حسب النوع
                by_type = daily_stats.get('by_type', {})
                for ntype, count in by_type.items():
                    if ntype in stats['notifications_sent']['by_type']:
                        stats['notifications_sent']['by_type'][ntype] += count
                    else:
                        stats['notifications_sent']['by_type'][ntype] = count

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات استخدام اللغات: {str(e)}")
            return {}

    async def generate_language_report(self) -> str:
        """إنشاء تقرير مفصل عن استخدام اللغات"""
        try:
            stats = await self.get_language_usage_stats()

            if not stats:
                return "❌ لا توجد إحصائيات متاحة"

            report = "📊 **تقرير استخدام اللغات في نظام الأخبار**\n\n"

            # إحصائيات المستخدمين
            report += f"👥 **إجمالي المستخدمين:** {stats['total_users']}\n\n"

            report += "🌍 **توزيع المستخدمين حسب اللغة:**\n"
            for lang, count in stats['by_language'].items():
                lang_name = "العربية" if lang == 'ar' else "English"
                percentage = (count / stats['total_users'] * 100) if stats['total_users'] > 0 else 0
                report += f"  • {lang_name}: {count} ({percentage:.1f}%)\n"

            # إحصائيات الإشعارات
            report += f"\n📨 **الإشعارات المرسلة اليوم:** {stats['notifications_sent']['total']}\n\n"

            report += "📊 **الإشعارات حسب اللغة:**\n"
            for lang, count in stats['notifications_sent']['by_language'].items():
                lang_name = "العربية" if lang == 'ar' else "English"
                percentage = (count / stats['notifications_sent']['total'] * 100) if stats['notifications_sent']['total'] > 0 else 0
                report += f"  • {lang_name}: {count} ({percentage:.1f}%)\n"

            # إحصائيات حسب النوع
            if stats['notifications_sent']['by_type']:
                report += "\n📋 **الإشعارات حسب النوع:**\n"
                for ntype, count in stats['notifications_sent']['by_type'].items():
                    type_name = {
                        'breaking_news': 'أخبار عاجلة',
                        'new_coin': 'عملات جديدة',
                        'market_analysis': 'تحليل السوق',
                        'price_alert': 'تنبيهات الأسعار'
                    }.get(ntype, ntype)
                    report += f"  • {type_name}: {count}\n"

            report += f"\n🕐 **آخر تحديث:** {stats['last_updated']}"

            return report

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير اللغات: {str(e)}")
            return "❌ حدث خطأ في إنشاء التقرير"

    def _translate_to_english(self, text: str) -> str:
        """ترجمة ذكية للنصوص من العربية للإنجليزية - محسن"""
        if not text:
            return text

        # محاولة الترجمة باستخدام Gemini AI أولاً
        try:
            ai_translation = self._translate_with_ai(text)
            if ai_translation and ai_translation != text:
                return ai_translation
        except Exception as e:
            logger.debug(f"فشل في الترجمة بالذكاء الاصطناعي، استخدام القاموس: {str(e)}")

        # قاموس ترجمة شامل للمصطلحات المالية والتقنية
        translations = {
            # العناوين الرئيسية
            '🚨 خبر عاجل': '🚨 Breaking News',
            '🆕 عملة جديدة': '🆕 New Coin',
            '📊 تحليل السوق': '📊 Market Analysis',
            '🔔 تنبيه سعر': '🔔 Price Alert',
            '📊 الملخص اليومي': '📊 Daily Summary',
            '⚠️ تحديث مهم': '⚠️ Important Update',

            # المصطلحات الأساسية
            'أخبار عاجلة': 'Breaking News',
            'عملة جديدة': 'New Coin',
            'تحليل السوق': 'Market Analysis',
            'تنبيه سعر': 'Price Alert',
            'الملخص اليومي': 'Daily Summary',
            'تحديث مهم': 'Important Update',

            # العملات الرقمية
            'البيتكوين': 'Bitcoin',
            'الإيثيريوم': 'Ethereum',
            'العملات الرقمية': 'Cryptocurrencies',
            'العملة الرقمية': 'Cryptocurrency',
            'بيتكوين': 'Bitcoin',
            'إيثيريوم': 'Ethereum',
            'ريبل': 'Ripple',
            'لايتكوين': 'Litecoin',
            'كاردانو': 'Cardano',
            'بولكادوت': 'Polkadot',
            'سولانا': 'Solana',
            'أفالانش': 'Avalanche',

            # مصطلحات السوق
            'السوق': 'Market',
            'الأسواق': 'Markets',
            'السعر': 'Price',
            'الأسعار': 'Prices',
            'التداول': 'Trading',
            'الاستثمار': 'Investment',
            'المحفظة': 'Portfolio',
            'الربح': 'Profit',
            'الأرباح': 'Profits',
            'الخسارة': 'Loss',
            'الخسائر': 'Losses',
            'المكاسب': 'Gains',
            'العوائد': 'Returns',

            # اتجاهات السوق
            'صاعد': 'Bullish',
            'هابط': 'Bearish',
            'صعودي': 'Bullish',
            'هبوطي': 'Bearish',
            'ارتفاع': 'Rise',
            'انخفاض': 'Drop',
            'هبوط': 'Fall',
            'تراجع': 'Decline',
            'نمو': 'Growth',
            'تحسن': 'Improvement',

            # التحليل الفني
            'مقاومة': 'Resistance',
            'دعم': 'Support',
            'مستوى الدعم': 'Support Level',
            'مستوى المقاومة': 'Resistance Level',
            'الاتجاه': 'Trend',
            'المؤشر': 'Indicator',
            'المؤشرات': 'Indicators',
            'التحليل الفني': 'Technical Analysis',
            'التحليل الأساسي': 'Fundamental Analysis',

            # أحداث السوق
            'إدراج': 'Listing',
            'إطلاق': 'Launch',
            'شراكة': 'Partnership',
            'استحواذ': 'Acquisition',
            'اندماج': 'Merger',
            'تحديث': 'Update',
            'ترقية': 'Upgrade',
            'تطوير': 'Development',

            # التنظيم والقانون
            'تنظيم': 'Regulation',
            'قانون': 'Law',
            'حظر': 'Ban',
            'موافقة': 'Approval',
            'ترخيص': 'License',
            'امتثال': 'Compliance',

            # الأمان والمخاطر
            'اختراق': 'Hack',
            'أمان': 'Security',
            'مخاطر': 'Risks',
            'خطر': 'Risk',
            'حماية': 'Protection',
            'تأمين': 'Security',

            # الوقت والتواريخ
            'اليوم': 'Today',
            'أمس': 'Yesterday',
            'غداً': 'Tomorrow',
            'هذا الأسبوع': 'This Week',
            'الأسبوع الماضي': 'Last Week',
            'هذا الشهر': 'This Month',
            'الشهر الماضي': 'Last Month',

            # كلمات عامة
            'جديد': 'New',
            'قديم': 'Old',
            'حديث': 'Recent',
            'مهم': 'Important',
            'عاجل': 'Urgent',
            'سريع': 'Fast',
            'بطيء': 'Slow',
            'كبير': 'Large',
            'صغير': 'Small',
            'عالي': 'High',
            'منخفض': 'Low',
            'قوي': 'Strong',
            'ضعيف': 'Weak'
        }

        # تطبيق الترجمات بترتيب الأولوية (الأطول أولاً)
        translated_text = text
        # ترتيب الترجمات حسب الطول (الأطول أولاً لتجنب الترجمة الجزئية)
        sorted_translations = sorted(translations.items(), key=lambda x: len(x[0]), reverse=True)

        for arabic, english in sorted_translations:
            translated_text = translated_text.replace(arabic, english)

        return translated_text

    def _translate_with_ai(self, text: str) -> str:
        """ترجمة النص باستخدام Gemini AI"""
        try:
            # التحقق من وجود نظام الأخبار مع Gemini
            from services.news_system import news_system
            if not news_system or not news_system.gemini_api_key:
                return None

            # إنشاء مطالبة الترجمة
            translation_prompt = f"""
            Translate the following Arabic text to English.
            Focus on cryptocurrency and financial terms.
            Keep the translation natural and professional.
            If the text is already in English, return it as is.

            Text to translate: {text}

            Provide only the English translation without any additional text or explanations.
            """

            # استدعاء Gemini للترجمة
            import google.generativeai as genai
            genai.configure(api_key=news_system.gemini_api_key)
            model = genai.GenerativeModel('gemini-pro')

            response = model.generate_content(translation_prompt)

            if response and response.text:
                translated = response.text.strip()
                # التأكد من أن الترجمة ليست نفس النص الأصلي
                if translated != text and len(translated) > 0:
                    logger.debug(f"تمت ترجمة النص بالذكاء الاصطناعي: {text[:50]}... -> {translated[:50]}...")
                    return translated

            return None

        except Exception as e:
            logger.debug(f"خطأ في الترجمة بالذكاء الاصطناعي: {str(e)}")
            return None

    async def _save_user_rule(self, rule: NotificationRule):
        """حفظ قاعدة المستخدم في قاعدة البيانات"""
        if not self.db:
            return

        try:
            rule_data = {
                'user_id': rule.user_id,
                'notification_type': rule.notification_type.value,
                'keywords': rule.keywords or [],
                'symbols': rule.symbols or [],
                'min_priority': rule.min_priority.value,
                'enabled': rule.enabled,
                'created_at': rule.created_at.isoformat(),
                'last_triggered': rule.last_triggered.isoformat() if rule.last_triggered else None
            }

            doc_id = f"{rule.user_id}_{rule.notification_type.value}"
            self.db.collection('notification_rules').document(doc_id).set(rule_data, merge=True)

        except Exception as e:
            logger.error(f"خطأ في حفظ قاعدة الإشعار: {str(e)}")

    async def _delete_user_rule(self, user_id: str, notification_type: NotificationType):
        """حذف قاعدة المستخدم من قاعدة البيانات"""
        if not self.db:
            return

        try:
            doc_id = f"{user_id}_{notification_type.value}"
            self.db.collection('notification_rules').document(doc_id).delete()

        except Exception as e:
            logger.error(f"خطأ في حذف قاعدة الإشعار: {str(e)}")

    async def _save_notification(self, notification: NotificationMessage):
        """حفظ الإشعار في قاعدة البيانات"""
        if not self.db:
            return

        try:
            notification_data = {
                'user_id': notification.user_id,
                'notification_type': notification.notification_type.value,
                'priority': notification.priority.value,
                'title': notification.title,
                'content': notification.content,
                'data': notification.data or {},
                'created_at': notification.created_at.isoformat(),
                'sent_at': notification.sent_at.isoformat() if notification.sent_at else None,
                'read_at': notification.read_at.isoformat() if notification.read_at else None
            }

            # إنشاء معرف فريد
            doc_id = f"{notification.user_id}_{int(notification.created_at.timestamp())}"
            self.db.collection('notifications').document(doc_id).set(notification_data)

        except Exception as e:
            logger.error(f"خطأ في حفظ الإشعار: {str(e)}")

    async def load_user_rules(self):
        """تحميل قواعد المستخدمين من قاعدة البيانات"""
        if not self.db:
            return

        try:
            docs = self.db.collection('notification_rules').get()

            for doc in docs:
                data = doc.to_dict()

                rule = NotificationRule(
                    user_id=data['user_id'],
                    notification_type=NotificationType(data['notification_type']),
                    keywords=data.get('keywords', []),
                    symbols=data.get('symbols', []),
                    min_priority=NotificationPriority(data.get('min_priority', 2)),
                    enabled=data.get('enabled', True),
                    created_at=datetime.fromisoformat(data['created_at']),
                    last_triggered=datetime.fromisoformat(data['last_triggered']) if data.get('last_triggered') else None
                )

                if rule.user_id not in self.user_rules:
                    self.user_rules[rule.user_id] = []

                self.user_rules[rule.user_id].append(rule)

            logger.info(f"تم تحميل قواعد الإشعارات لـ {len(self.user_rules)} مستخدم")

        except Exception as e:
            logger.error(f"خطأ في تحميل قواعد الإشعارات: {str(e)}")

    async def enable_automatic_notifications_for_user(self, user_id: str) -> bool:
        """تفعيل الإشعارات التلقائية للمستخدم الجديد"""
        try:
            # إضافة قواعد أساسية للأخبار العاجلة والعملات الجديدة
            await self.add_user_rule(
                user_id=user_id,
                notification_type=NotificationType.BREAKING_NEWS,
                keywords=[],  # بدون كلمات مفتاحية محددة
                symbols=[],   # بدون رموز محددة
                min_priority=NotificationPriority.HIGH
            )

            await self.add_user_rule(
                user_id=user_id,
                notification_type=NotificationType.NEW_COIN,
                keywords=[],
                symbols=[],
                min_priority=NotificationPriority.HIGH
            )

            logger.info(f"تم تفعيل الإشعارات التلقائية للمستخدم {user_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تفعيل الإشعارات التلقائية للمستخدم {user_id}: {str(e)}")
            return False

    async def enable_automatic_notifications_for_all_users(self) -> bool:
        """تفعيل الإشعارات التلقائية لجميع المستخدمين الحاليين"""
        try:
            all_users = await self._get_all_active_users()
            enabled_count = 0

            for user_id in all_users:
                # التحقق من وجود قواعد إشعارات للمستخدم
                if user_id not in self.user_rules or not self.user_rules[user_id]:
                    # تفعيل الإشعارات التلقائية للمستخدم
                    if await self.enable_automatic_notifications_for_user(user_id):
                        enabled_count += 1
                else:
                    # التحقق من وجود قواعد للأخبار العاجلة والعملات الجديدة
                    has_breaking_news = any(rule.notification_type == NotificationType.BREAKING_NEWS
                                          for rule in self.user_rules[user_id])
                    has_new_coin = any(rule.notification_type == NotificationType.NEW_COIN
                                     for rule in self.user_rules[user_id])

                    if not has_breaking_news:
                        await self.add_user_rule(
                            user_id=user_id,
                            notification_type=NotificationType.BREAKING_NEWS,
                            keywords=[],
                            symbols=[],
                            min_priority=NotificationPriority.HIGH
                        )

                    if not has_new_coin:
                        await self.add_user_rule(
                            user_id=user_id,
                            notification_type=NotificationType.NEW_COIN,
                            keywords=[],
                            symbols=[],
                            min_priority=NotificationPriority.HIGH
                        )

                    if not has_breaking_news or not has_new_coin:
                        enabled_count += 1

            logger.info(f"تم تفعيل الإشعارات التلقائية لـ {enabled_count} مستخدم من أصل {len(all_users)}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تفعيل الإشعارات التلقائية لجميع المستخدمين: {str(e)}")
            return False

    async def get_user_notifications(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """الحصول على إشعارات المستخدم"""
        if not self.db:
            return []

        try:
            query = self.db.collection('notifications').where(
                field_path='user_id', op_string='==', value=user_id
            ).order_by('created_at', direction='DESCENDING').limit(limit)

            docs = query.get()
            notifications = []

            for doc in docs:
                data = doc.to_dict()
                notifications.append(data)

            return notifications

        except Exception as e:
            logger.error(f"خطأ في جلب إشعارات المستخدم: {str(e)}")
            return []

    async def mark_notification_as_read(self, user_id: str, notification_id: str):
        """تمييز الإشعار كمقروء"""
        if not self.db:
            return

        try:
            self.db.collection('notifications').document(notification_id).update({
                'read_at': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"خطأ في تمييز الإشعار كمقروء: {str(e)}")

    async def get_notification_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الإشعارات"""
        return {
            **self.stats,
            'active_users': len(self.user_rules),
            'total_rules': sum(len(rules) for rules in self.user_rules.values()),
            'queue_size': len(self.notification_queue),
            'success_rate': (self.stats['total_sent'] / (self.stats['total_sent'] + self.stats['total_failed']) * 100) if (self.stats['total_sent'] + self.stats['total_failed']) > 0 else 0
        }

    async def send_daily_summary(self):
        """إرسال الملخص اليومي للمستخدمين المشتركين"""
        try:
            # العثور على المستخدمين المشتركين في الملخص اليومي
            daily_summary_users = []
            for user_id, rules in self.user_rules.items():
                for rule in rules:
                    if rule.notification_type == NotificationType.DAILY_SUMMARY and rule.enabled:
                        daily_summary_users.append(user_id)
                        break

            if not daily_summary_users:
                return

            # إنشاء الملخص اليومي
            summary_content = await self._generate_daily_summary()

            # إرسال للمستخدمين
            for user_id in daily_summary_users:
                notification = NotificationMessage(
                    user_id=user_id,
                    notification_type=NotificationType.DAILY_SUMMARY,
                    priority=NotificationPriority.LOW,
                    title="📊 الملخص اليومي",
                    content=summary_content,
                    created_at=datetime.now()
                )

                await self._send_notification(notification)
                await asyncio.sleep(1)  # تأخير لتجنب الإزعاج

            logger.info(f"تم إرسال الملخص اليومي لـ {len(daily_summary_users)} مستخدم")

        except Exception as e:
            logger.error(f"خطأ في إرسال الملخص اليومي: {str(e)}")

    async def _generate_daily_summary(self) -> str:
        """إنشاء الملخص اليومي"""
        # هذه الوظيفة ستحتاج إلى التكامل مع نظام الأخبار
        summary = "📊 **ملخص أخبار اليوم**\n\n"
        summary += f"📈 إجمالي الإشعارات المرسلة: {self.stats['total_sent']}\n"
        summary += f"🔥 الأخبار العاجلة: {self.stats['by_type'].get('breaking_news', 0)}\n"
        summary += f"🆕 العملات الجديدة: {self.stats['by_type'].get('new_coin', 0)}\n"
        summary += f"📊 تحليلات السوق: {self.stats['by_type'].get('market_analysis', 0)}\n"

        return summary

    async def _update_news_interested_users(self, news_id: str, interested_users: List[str]):
        """تحديث قائمة المستخدمين المهتمين بالخبر"""
        if not self.db or not news_id:
            return

        try:
            news_ref = self.db.collection('news').document(news_id)
            news_ref.update({
                'interested_users': interested_users,
                'total_interested_users': len(interested_users),
                'sent_to_users': [],
                'updated_at': datetime.now().isoformat()
            })
            logger.debug(f"تم تحديث قائمة المستخدمين المهتمين للخبر {news_id}: {len(interested_users)} مستخدم")
        except Exception as e:
            logger.error(f"خطأ في تحديث قائمة المستخدمين المهتمين للخبر {news_id}: {str(e)}")

    async def _mark_news_sent_to_user(self, notification: NotificationMessage):
        """تسجيل إرسال الخبر لمستخدم معين"""
        if not self.db or not notification.data or not notification.data.get('news_id'):
            return

        try:
            news_id = notification.data['news_id']
            user_id = notification.user_id

            # تحديث قائمة المستخدمين الذين تم إرسال الخبر لهم
            news_ref = self.db.collection('news').document(news_id)
            news_doc = news_ref.get()

            if news_doc.exists:
                news_data = news_doc.to_dict()
                sent_to_users = news_data.get('sent_to_users', [])

                if user_id not in sent_to_users:
                    sent_to_users.append(user_id)
                    news_ref.update({
                        'sent_to_users': sent_to_users,
                        'last_sent_at': datetime.now().isoformat()
                    })

                    logger.debug(f"تم تسجيل إرسال الخبر {news_id} للمستخدم {user_id}")
        except Exception as e:
            logger.error(f"خطأ في تسجيل إرسال الخبر: {str(e)}")

    async def _cleanup_successfully_sent_news(self, processed_news_ids: List[str], sent_notifications: List[NotificationMessage]):
        """حذف الأخبار التي تم إرسالها بنجاح لجميع المستخدمين المهتمين"""
        if not self.db or not processed_news_ids:
            return

        try:
            deleted_count = 0

            for news_id in processed_news_ids:
                try:
                    news_ref = self.db.collection('news').document(news_id)
                    news_doc = news_ref.get()

                    if news_doc.exists:
                        news_data = news_doc.to_dict()
                        interested_users = news_data.get('interested_users', [])
                        sent_to_users = news_data.get('sent_to_users', [])

                        # التحقق من إرسال الخبر لجميع المستخدمين المهتمين
                        if interested_users and len(sent_to_users) >= len(interested_users):
                            # التحقق من أن جميع المستخدمين المهتمين تلقوا الخبر
                            all_sent = all(user_id in sent_to_users for user_id in interested_users)

                            if all_sent:
                                # حذف الخبر
                                news_ref.delete()
                                deleted_count += 1
                                logger.info(f"✅ تم حذف الخبر {news_id} بعد إرساله لجميع المستخدمين المهتمين ({len(sent_to_users)} مستخدم)")
                            else:
                                logger.debug(f"الخبر {news_id} لم يتم إرساله لجميع المستخدمين المهتمين بعد")
                        else:
                            logger.debug(f"الخبر {news_id} لا يزال في انتظار الإرسال لبعض المستخدمين")

                except Exception as e:
                    logger.error(f"خطأ في معالجة الخبر {news_id} للحذف: {str(e)}")

            if deleted_count > 0:
                logger.info(f"🗑️ تم حذف {deleted_count} خبر بعد الإرسال الناجح")

        except Exception as e:
            logger.error(f"خطأ في تنظيف الأخبار المرسلة: {str(e)}")

# إنشاء نسخة عامة من النظام
automatic_news_notifications = None

def initialize_automatic_news_notifications(db=None, bot=None):
    """تهيئة نظام الإشعارات التلقائية"""
    global automatic_news_notifications
    automatic_news_notifications = AutomaticNewsNotifications(db, bot)
    logger.info("✅ تم تهيئة نظام الإشعارات التلقائية للأخبار")
    return automatic_news_notifications
