"""
أداة إصلاح مشاكل الترميز في ملفات البوت
تبحث عن الرموز التعبيرية المعطوبة وتصلحها
"""

import os
import re
import logging
from typing import List, Dict, Tuple

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EncodingFixer:
    """أداة إصلاح مشاكل الترميز"""
    
    def __init__(self):
        self.issues_found = []
        self.fixes_applied = []
        
        # قاموس الرموز التعبيرية الشائعة المعطوبة وإصلاحاتها
        self.emoji_fixes = {
            '��': '🎁',  # رمز الهدية
            '�': '🎁',   # رمز الهدية (نسخة أخرى)
            'ا��ممنوح': 'الممنوح',  # كلمة "الممنوح" معطوبة
            'ا��': 'ال',  # أداة التعريف معطوبة
            '��': '🔔',  # رمز الجرس
            '��': '⏰',  # رمز الساعة
            '��': '✅',  # رمز الصح
            '��': '❌',  # رمز الخطأ
            '��': '📊',  # رمز الإحصائيات
            '��': '🚀',  # رمز الصاروخ
            '��': '💡',  # رمز المصباح
            '��': '🔧',  # رمز المفتاح
            '��': '🌟',  # رمز النجمة
            '��': '💰',  # رمز المال
            '��': '📈',  # رمز الرسم البياني
            '��': '🔍',  # رمز العدسة المكبرة
        }
        
        # أنماط الرموز المعطوبة
        self.broken_patterns = [
            r'��+',  # رموز معطوبة متتالية
            r'�[^\s]*�',  # رموز معطوبة محاطة
            r'ا��[^\s]*',  # كلمات عربية معطوبة تبدأ بـ "ا��"
        ]
    
    def scan_files(self, directory: str = "src") -> Dict[str, List[str]]:
        """فحص الملفات للبحث عن مشاكل الترميز"""
        logger.info(f"🔍 فحص الملفات في مجلد: {directory}")
        
        files_with_issues = {}
        
        for root, dirs, files in os.walk(directory):
            # تجاهل مجلدات معينة
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            for file in files:
                if file.endswith(('.py', '.txt', '.md', '.yml', '.yaml')):
                    file_path = os.path.join(root, file)
                    issues = self._scan_file(file_path)
                    if issues:
                        files_with_issues[file_path] = issues
        
        return files_with_issues
    
    def _scan_file(self, file_path: str) -> List[str]:
        """فحص ملف واحد للبحث عن مشاكل الترميز"""
        issues = []
        
        try:
            # محاولة قراءة الملف بترميز UTF-8
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            # البحث عن الرموز المعطوبة
            for pattern in self.broken_patterns:
                matches = re.findall(pattern, content)
                for match in matches:
                    if match not in issues:
                        issues.append(match)
            
            # البحث عن رموز معطوبة محددة
            for broken, fixed in self.emoji_fixes.items():
                if broken in content:
                    if broken not in issues:
                        issues.append(broken)
        
        except Exception as e:
            logger.warning(f"خطأ في قراءة الملف {file_path}: {str(e)}")
        
        return issues
    
    def fix_file(self, file_path: str) -> bool:
        """إصلاح مشاكل الترميز في ملف واحد"""
        try:
            # قراءة الملف
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            
            original_content = content
            fixes_in_file = []
            
            # تطبيق الإصلاحات
            for broken, fixed in self.emoji_fixes.items():
                if broken in content:
                    content = content.replace(broken, fixed)
                    fixes_in_file.append(f"{broken} -> {fixed}")
            
            # إذا تم تطبيق إصلاحات، حفظ الملف
            if content != original_content:
                # إنشاء نسخة احتياطية
                backup_path = file_path + '.backup'
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # حفظ الملف المصلح
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append({
                    'file': file_path,
                    'fixes': fixes_in_file,
                    'backup': backup_path
                })
                
                logger.info(f"✅ تم إصلاح الملف: {file_path}")
                for fix in fixes_in_file:
                    logger.info(f"  - {fix}")
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في إصلاح الملف {file_path}: {str(e)}")
            return False
    
    def fix_all_files(self, directory: str = "src") -> Dict[str, any]:
        """إصلاح جميع الملفات في المجلد"""
        logger.info(f"🔧 بدء إصلاح جميع الملفات في: {directory}")
        
        # فحص الملفات أولاً
        files_with_issues = self.scan_files(directory)
        
        results = {
            'files_scanned': 0,
            'files_with_issues': len(files_with_issues),
            'files_fixed': 0,
            'total_fixes': 0,
            'failed_fixes': 0,
            'details': []
        }
        
        # إصلاح كل ملف
        for file_path, issues in files_with_issues.items():
            results['files_scanned'] += 1
            
            logger.info(f"🔧 إصلاح الملف: {file_path}")
            logger.info(f"  المشاكل المكتشفة: {', '.join(issues)}")
            
            if self.fix_file(file_path):
                results['files_fixed'] += 1
                results['total_fixes'] += len(issues)
            else:
                results['failed_fixes'] += 1
        
        return results
    
    def validate_fixes(self) -> bool:
        """التحقق من صحة الإصلاحات المطبقة"""
        logger.info("🔍 التحقق من صحة الإصلاحات...")
        
        all_valid = True
        
        for fix_info in self.fixes_applied:
            file_path = fix_info['file']
            
            # فحص الملف مرة أخرى
            remaining_issues = self._scan_file(file_path)
            
            if remaining_issues:
                logger.warning(f"⚠️ لا تزال هناك مشاكل في {file_path}: {remaining_issues}")
                all_valid = False
            else:
                logger.info(f"✅ تم إصلاح {file_path} بنجاح")
        
        return all_valid
    
    def restore_backups(self):
        """استعادة النسخ الاحتياطية"""
        logger.info("🔄 استعادة النسخ الاحتياطية...")
        
        for fix_info in self.fixes_applied:
            try:
                file_path = fix_info['file']
                backup_path = fix_info['backup']
                
                if os.path.exists(backup_path):
                    # استعادة النسخة الاحتياطية
                    with open(backup_path, 'r', encoding='utf-8') as f:
                        backup_content = f.read()
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(backup_content)
                    
                    # حذف النسخة الاحتياطية
                    os.remove(backup_path)
                    
                    logger.info(f"✅ تم استعادة {file_path}")
                
            except Exception as e:
                logger.error(f"❌ خطأ في استعادة {fix_info['file']}: {str(e)}")
    
    def print_report(self, results: Dict[str, any]):
        """طباعة تقرير الإصلاح"""
        print("\n" + "="*60)
        print("🔧 تقرير إصلاح مشاكل الترميز")
        print("="*60)
        
        print(f"📁 الملفات المفحوصة: {results['files_scanned']}")
        print(f"⚠️ الملفات التي بها مشاكل: {results['files_with_issues']}")
        print(f"✅ الملفات المصلحة: {results['files_fixed']}")
        print(f"🔧 إجمالي الإصلاحات: {results['total_fixes']}")
        print(f"❌ الإصلاحات الفاشلة: {results['failed_fixes']}")
        
        if self.fixes_applied:
            print(f"\n📋 تفاصيل الإصلاحات:")
            for fix_info in self.fixes_applied:
                print(f"  📄 {fix_info['file']}")
                for fix in fix_info['fixes']:
                    print(f"    🔧 {fix}")
                print(f"    💾 نسخة احتياطية: {fix_info['backup']}")
        
        print("\n" + "="*60)

def main():
    """تشغيل أداة إصلاح الترميز"""
    print("🚀 بدء أداة إصلاح مشاكل الترميز")
    
    try:
        fixer = EncodingFixer()
        
        # فحص وإصلاح الملفات
        results = fixer.fix_all_files()
        
        # طباعة التقرير
        fixer.print_report(results)
        
        # التحقق من صحة الإصلاحات
        if fixer.fixes_applied:
            print(f"\n🔍 التحقق من صحة الإصلاحات...")
            validation_success = fixer.validate_fixes()
            
            if validation_success:
                print("✅ جميع الإصلاحات صحيحة!")
                
                # سؤال المستخدم عن حذف النسخ الاحتياطية
                response = input("\n❓ هل تريد حذف النسخ الاحتياطية؟ (y/n): ")
                if response.lower() in ['y', 'yes', 'نعم']:
                    for fix_info in fixer.fixes_applied:
                        try:
                            os.remove(fix_info['backup'])
                            print(f"🗑️ تم حذف {fix_info['backup']}")
                        except Exception as e:
                            print(f"⚠️ خطأ في حذف {fix_info['backup']}: {str(e)}")
                else:
                    print("💾 تم الاحتفاظ بالنسخ الاحتياطية")
            else:
                print("⚠️ بعض الإصلاحات قد تحتاج مراجعة يدوية")
                
                response = input("\n❓ هل تريد استعادة النسخ الاحتياطية؟ (y/n): ")
                if response.lower() in ['y', 'yes', 'نعم']:
                    fixer.restore_backups()
                    print("🔄 تم استعادة النسخ الاحتياطية")
        else:
            print("✅ لم يتم العثور على مشاكل ترميز!")
        
        print(f"\n🎉 تم إكمال العملية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل أداة الإصلاح: {str(e)}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
