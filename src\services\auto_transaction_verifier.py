"""
نظام التحقق التلقائي من المعاملات
"""

import logging
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from integrations.binance_verifier import BinanceTransactionVerifier
from services.user_management import activate_subscription

# إعداد التسجيل
logger = logging.getLogger(__name__)

class AutomaticTransactionVerifier:
    """نظام التحقق التلقائي من المعاملات"""
    
    def __init__(self, db=None, api_manager=None):
        """
        تهيئة نظام التحقق التلقائي من المعاملات
        
        Args:
            db: قاعدة بيانات Firestore
            api_manager: مدير API للوصول إلى مفاتيح المستخدمين
        """
        self.db = db
        self.binance_verifier = BinanceTransactionVerifier(db, api_manager)
        self.verification_interval = 60  # ثانية
        self.max_verification_attempts = 5
        self.scheduler = AsyncIOScheduler()
        self.active_verifications = {}

    async def start_verification(self, transaction_id: str, user_id: str):
        """
        بدء عملية التحقق التلقائي من معاملة
        
        Args:
            transaction_id: معرف المعاملة
            user_id: معرف المستخدم
        """
        if transaction_id in self.active_verifications:
            return

        verification_data = {
            'attempts': 0,
            'user_id': user_id,
            'status': 'pending',
            'last_check': None
        }

        self.active_verifications[transaction_id] = verification_data

        self.scheduler.add_job(
            self._verify_transaction,
            'interval',
            seconds=self.verification_interval,
            args=[transaction_id],
            id=f'verify_{transaction_id}'
        )

    async def _verify_transaction(self, transaction_id: str):
        """
        دالة التحقق الدورية من المعاملة (دالة داخلية)
        
        Args:
            transaction_id: معرف المعاملة
        """
        verification_data = self.active_verifications.get(transaction_id)
        if not verification_data:
            return

        verification_data['attempts'] += 1
        verification_data['last_check'] = datetime.now()

        try:
            is_valid = await self.binance_verifier.verify_transaction_complete(
                transaction_id,
                verification_data['user_id']
            )

            if is_valid:
                if await activate_subscription(
                    verification_data['user_id'],
                    transaction_id
                ):
                    verification_data['status'] = 'completed'
                    await self._stop_verification(transaction_id)
                    return

            if verification_data['attempts'] >= self.max_verification_attempts:
                verification_data['status'] = 'failed'
                await self._stop_verification(transaction_id)

        except Exception as e:
            logger.error(f"Error in automatic verification for {transaction_id}: {str(e)}")
            verification_data['status'] = 'error'
            await self._stop_verification(transaction_id)

    async def _stop_verification(self, transaction_id: str):
        """
        إيقاف عملية التحقق وتحديث حالة المعاملة (دالة داخلية)
        
        Args:
            transaction_id: معرف المعاملة
        """
        try:
            self.scheduler.remove_job(f'verify_{transaction_id}')
        except:
            pass

        verification_data = self.active_verifications.pop(transaction_id, None)
        if verification_data and self.db:
            transaction_ref = self.db.collection('transactions').document(transaction_id)
            await transaction_ref.update({
                'status': verification_data['status'],
                'verified_at': datetime.now().isoformat(),
                'verification_attempts': verification_data['attempts']
            })
