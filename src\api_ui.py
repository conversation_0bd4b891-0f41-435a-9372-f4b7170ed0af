"""
واجهة المستخدم لإعداد وإدارة مفاتيح API
"""

import logging
from typing import Dict, Any

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext

# إعداد السجل
logger = logging.getLogger(__name__)

async def setup_api_keys(update: Update, context: CallbackContext, api_manager, subscription_system):
    """
    إعداد مفاتيح API للمستخدم

    Args:
        update: كائن التحديث
        context: سياق المحادثة
        api_manager: مدير API
        subscription_system: نظام الاشتراك
    """
    user_id = str(update.effective_user.id)
    settings = subscription_system.get_user_settings(user_id)
    lang = settings.get('lang', 'ar')

    # التحقق من حالة الاشتراك (يجب استخدام النسخة المتزامنة)
    is_subscribed = subscription_system.is_subscribed_sync(user_id)

    # الحصول على معلومات API
    api_info = await api_manager.get_api_info(user_id)

    # تسجيل معلومات API للتصحيح
    logger.info(f"API Info for user {user_id}: {api_info}")

    # إنشاء أزرار الإعداد
    keyboard = []

    # إضافة زر إضافة منصة جديدة
    keyboard.append([
        InlineKeyboardButton("➕ إضافة منصة جديدة" if lang == 'ar' else "➕ Add New Platform",
                            callback_data="select_platform")
    ])

    # إضافة زر حذف API إذا كان هناك مفاتيح مخزنة
    has_any_api = any([api_info.get(f'has_{platform}', False) for platform in ['binance', 'gemini', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken']])

    if has_any_api:
        keyboard.append([
            InlineKeyboardButton("🗑️ حذف مفاتيح API" if lang == 'ar' else "🗑️ Delete API Keys",
                                callback_data="delete_api_keys")
        ])

    # إضافة زر الرجوع
    keyboard.append([
        InlineKeyboardButton("🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu",
                            callback_data="back_to_main")
    ])

    # إنشاء نص الرسالة
    if lang == 'ar':
        message_text = "⚙️ إعداد مفاتيح API\n\n"
        message_text += "يمكنك إعداد مفاتيح API الخاصة بك لتحسين تجربة استخدام البوت.\n\n"
        message_text += "المنصات المتاحة:\n\n"

        # عرض حالة كل منصة مع روابط مباشرة
        message_text += f"🔹 <b>Binance</b>: {get_status_emoji(api_info.get('has_binance', False))}\n"
        message_text += "   <a href='https://www.binance.com/en/my/settings/api-management'>صفحة إدارة API</a>\n\n"

        # Gemini متاح لجميع المستخدمين للتعلم مع الذكاء الاصطناعي
        message_text += f"🔹 <b>Gemini</b>: {get_status_emoji(api_info.get('has_gemini', False))}\n"
        message_text += "   <a href='https://aistudio.google.com/apikey'>صفحة مفاتيح API</a>\n"
        if not is_subscribed:
            message_text += "   💡 <i>مطلوب للتعلم مع الذكاء الاصطناعي</i>\n"
        message_text += "\n"

        message_text += f"🔹 <b>KuCoin</b>: {get_status_emoji(api_info.get('has_kucoin', False))}\n"
        message_text += "   <a href='https://www.kucoin.com/account/api'>صفحة إدارة API</a>\n\n"

        message_text += f"🔹 <b>Coinbase</b>: {get_status_emoji(api_info.get('has_coinbase', False))}\n"
        message_text += "   <a href='https://www.coinbase.com/settings/api'>صفحة إدارة API</a>\n\n"

        message_text += f"🔹 <b>Bybit</b>: {get_status_emoji(api_info.get('has_bybit', False))}\n"
        message_text += "   <a href='https://www.bybit.com/app/user/api-management'>صفحة إدارة API</a>\n\n"

        message_text += f"🔹 <b>OKX</b>: {get_status_emoji(api_info.get('has_okx', False))}\n"
        message_text += "   <a href='https://www.okx.com/account/my-api'>صفحة إدارة API</a>\n\n"

        message_text += f"🔹 <b>Kraken</b>: {get_status_emoji(api_info.get('has_kraken', False))}\n"
        message_text += "   <a href='https://www.kraken.com/u/security/api'>صفحة إدارة API</a>\n\n"

        message_text += "⚠️ <b>ملاحظات مهمة</b>:\n"
        message_text += "• مفاتيح API مشفرة ومخزنة بشكل آمن\n"
        message_text += "• لا يمكن لأحد الوصول إلى مفاتيحك الخاصة\n"
        message_text += "• استخدم مفاتيح API بصلاحيات قراءة فقط\n"
    else:
        message_text = "⚙️ API Keys Setup\n\n"
        message_text += "You can set up your own API keys to enhance your bot experience.\n\n"
        message_text += "Available Platforms:\n\n"

        # عرض حالة كل منصة مع روابط مباشرة
        message_text += f"🔹 <b>Binance</b>: {get_status_emoji(api_info.get('has_binance', False), 'en')}\n"
        message_text += "   <a href='https://www.binance.com/en/my/settings/api-management'>API Management Page</a>\n\n"

        # Gemini available for all users for AI learning
        message_text += f"🔹 <b>Gemini</b>: {get_status_emoji(api_info.get('has_gemini', False), 'en')}\n"
        message_text += "   <a href='https://aistudio.google.com/apikey'>API Keys Page</a>\n"
        if not is_subscribed:
            message_text += "   💡 <i>Required for AI learning</i>\n"
        message_text += "\n"

        message_text += f"🔹 <b>KuCoin</b>: {get_status_emoji(api_info.get('has_kucoin', False), 'en')}\n"
        message_text += "   <a href='https://www.kucoin.com/account/api'>API Management Page</a>\n\n"

        message_text += f"🔹 <b>Coinbase</b>: {get_status_emoji(api_info.get('has_coinbase', False), 'en')}\n"
        message_text += "   <a href='https://www.coinbase.com/settings/api'>API Management Page</a>\n\n"

        message_text += f"🔹 <b>Bybit</b>: {get_status_emoji(api_info.get('has_bybit', False), 'en')}\n"
        message_text += "   <a href='https://www.bybit.com/app/user/api-management'>API Management Page</a>\n\n"

        message_text += f"🔹 <b>OKX</b>: {get_status_emoji(api_info.get('has_okx', False), 'en')}\n"
        message_text += "   <a href='https://www.okx.com/account/my-api'>API Management Page</a>\n\n"

        message_text += f"🔹 <b>Kraken</b>: {get_status_emoji(api_info.get('has_kraken', False), 'en')}\n"
        message_text += "   <a href='https://www.kraken.com/u/security/api'>API Management Page</a>\n\n"

        message_text += "⚠️ <b>Important Notes</b>:\n"
        message_text += "• API keys are encrypted and stored securely\n"
        message_text += "• No one can access your private keys\n"
        message_text += "• Use API keys with read-only permissions\n"

    # إرسال الرسالة
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML',
            disable_web_page_preview=False  # السماح بعرض معاينة الروابط
        )
    else:
        await update.message.reply_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML',
            disable_web_page_preview=False  # السماح بعرض معاينة الروابط
        )

async def show_platform_selection(update: Update, context: CallbackContext, api_manager, subscription_system):
    """
    عرض واجهة اختيار منصة التداول

    Args:
        update: كائن التحديث
        context: سياق المحادثة
        api_manager: مدير API
        subscription_system: نظام الاشتراك
    """
    user_id = str(update.effective_user.id)
    settings = subscription_system.get_user_settings(user_id)
    lang = settings.get('lang', 'ar')

    # التحقق من حالة الاشتراك (يجب استخدام النسخة المتزامنة)
    is_subscribed = subscription_system.is_subscribed_sync(user_id)

    # إنشاء أزرار المنصات
    keyboard = []

    # إضافة Binance دائماً
    keyboard.append([
        InlineKeyboardButton("Binance", callback_data="setup_binance_api")
    ])

    # إضافة Gemini لجميع المستخدمين (مطلوب للتعلم مع الذكاء الاصطناعي)
    keyboard.append([
        InlineKeyboardButton("Gemini 🤖", callback_data="setup_gemini_api")
    ])

    # إضافة المنصات الأخرى
    keyboard.append([
        InlineKeyboardButton("KuCoin", callback_data="setup_kucoin_api")
    ])

    keyboard.append([
        InlineKeyboardButton("Coinbase", callback_data="setup_coinbase_api")
    ])

    keyboard.append([
        InlineKeyboardButton("Bybit", callback_data="setup_bybit_api")
    ])

    keyboard.append([
        InlineKeyboardButton("OKX", callback_data="setup_okx_api")
    ])

    keyboard.append([
        InlineKeyboardButton("Kraken", callback_data="setup_kraken_api")
    ])

    # إضافة زر الرجوع
    keyboard.append([
        InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back", callback_data="setup_api_keys")
    ])

    # إنشاء نص الرسالة
    if lang == 'ar':
        message_text = "🔹 اختر منصة التداول\n\n"
        message_text += "اختر المنصة التي ترغب في إضافة مفاتيح API لها:"

        message_text += "\n\n💡 ملاحظة: Gemini مطلوب للتعلم مع الذكاء الاصطناعي"
    else:
        message_text = "🔹 Select Trading Platform\n\n"
        message_text += "Choose the platform you want to add API keys for:"
        message_text += "\n\n💡 Note: Gemini is required for AI learning"

    # إرسال الرسالة
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML',
            disable_web_page_preview=False  # السماح بعرض معاينة الروابط
        )
    else:
        await update.message.reply_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML',
            disable_web_page_preview=False  # السماح بعرض معاينة الروابط
        )

async def show_api_instructions(update: Update, context: CallbackContext, api_type: str, lang: str = 'ar'):
    """
    عرض تعليمات الحصول على مفاتيح API

    Args:
        update: كائن التحديث
        context: سياق المحادثة
        api_type: نوع API (binance أو gemini أو kucoin أو coinbase أو bybit أو okx أو kraken)
        lang: لغة العرض
    """
    if api_type == 'binance':
        if lang == 'ar':
            instructions = """
📝 <b>كيفية الحصول على مفاتيح Binance API:</b>

1. قم بتسجيل الدخول إلى حسابك في Binance
2. انتقل إلى <a href='https://www.binance.com/en/my/settings/api-management'>صفحة إدارة API</a>
3. أنشئ مفتاح API جديد
4. قم بتمكين صلاحيات القراءة فقط (Read-Only)
5. انسخ مفتاح API والسر وأرسلهما إلى البوت

⚠️ <b>ملاحظات مهمة:</b>
• لا تحتاج إلى تمكين صلاحيات السحب أو التداول
• استخدم صلاحيات القراءة فقط لأمان حسابك
• لا تشارك مفاتيح API مع أي شخص آخر

🔹 <b>الآن، أدخل مفتاح API (API Key):</b>
"""
        else:
            instructions = """
📝 <b>How to get Binance API keys:</b>

1. Log in to your Binance account
2. Go to <a href='https://www.binance.com/en/my/settings/api-management'>API Management page</a>
3. Create a new API key
4. Enable Read-Only permissions
5. Copy the API key and secret and send them to the bot

⚠️ <b>Important Notes:</b>
• You don't need to enable withdrawal or trading permissions
• Use read-only permissions for your account security
• Never share your API keys with anyone else

🔹 <b>Now, enter your API Key:</b>
"""
    elif api_type == 'gemini':
        if lang == 'ar':
            instructions = """
📝 <b>كيفية الحصول على مفتاح Gemini API:</b>

1. قم بزيارة <a href='https://aistudio.google.com/'>Google AI Studio</a>
2. قم بتسجيل الدخول بحساب Google الخاص بك
3. انتقل إلى <a href='https://aistudio.google.com/apikey'>صفحة مفاتيح API</a>
4. انقر على 'Create API key'
5. انسخ مفتاح API وأرسله إلى البوت

⚠️ <b>ملاحظات مهمة:</b>
• البوت يستخدم نموذج gemini-2.0-flash
• يمكنك استخدام الخطة المجانية التي توفر عدد محدود من الاستدعاءات يوميًا
• لا تشارك مفتاح API مع أي شخص آخر
• مفتاحك مشفر ومخزن بشكل آمن في قاعدة البيانات

🔹 <b>الآن، أدخل مفتاح Gemini API:</b>
"""
        else:
            instructions = """
📝 <b>How to get Gemini API key:</b>

1. Visit <a href='https://aistudio.google.com/'>Google AI Studio</a>
2. Sign in with your Google account
3. Go to <a href='https://aistudio.google.com/apikey'>API Keys page</a>
4. Click on 'Create API key'
5. Copy the API key and send it to the bot

⚠️ <b>Important Notes:</b>
• The Bot uses the gemini-2.0-flash model
• You can use the free tier which provides a limited number of calls per day
• Never share your API key with anyone else
• Your key is encrypted and securely stored in the database

🔹 <b>Now, enter your Gemini API Key:</b>
"""
    elif api_type == 'kucoin':
        if lang == 'ar':
            instructions = """
📝 <b>كيفية الحصول على مفاتيح KuCoin API:</b>

1. قم بتسجيل الدخول إلى حسابك في KuCoin
2. انتقل إلى <a href='https://www.kucoin.com/account/api'>صفحة إدارة API</a>
3. أنشئ مفتاح API جديد
4. قم بتمكين صلاحيات القراءة فقط
5. انسخ مفتاح API والسر وكلمة المرور وأرسلهم إلى البوت

⚠️ <b>ملاحظات مهمة:</b>
• استخدم صلاحيات القراءة فقط لأمان حسابك
• لا تشارك مفاتيح API مع أي شخص آخر

🔹 <b>الآن، أدخل مفتاح API (API Key):</b>
"""
        else:
            instructions = """
📝 <b>How to get KuCoin API keys:</b>

1. Log in to your KuCoin account
2. Go to <a href='https://www.kucoin.com/account/api'>API Management page</a>
3. Create a new API key
4. Enable Read-Only permissions
5. Copy the API key, secret and passphrase and send them to the bot

⚠️ <b>Important Notes:</b>
• Use read-only permissions for your account security
• Never share your API keys with anyone else

🔹 <b>Now, enter your API Key:</b>
"""
    elif api_type == 'coinbase':
        if lang == 'ar':
            instructions = """
📝 <b>كيفية الحصول على مفاتيح Coinbase API:</b>

1. قم بتسجيل الدخول إلى حسابك في Coinbase
2. انتقل إلى <a href='https://www.coinbase.com/settings/api'>صفحة إدارة API</a>
3. أنشئ مفتاح API جديد
4. قم بتمكين صلاحيات القراءة فقط
5. انسخ مفتاح API والسر وأرسلهما إلى البوت

⚠️ <b>ملاحظات مهمة:</b>
• استخدم صلاحيات القراءة فقط لأمان حسابك
• لا تشارك مفاتيح API مع أي شخص آخر

🔹 <b>الآن، أدخل مفتاح API (API Key):</b>
"""
        else:
            instructions = """
📝 <b>How to get Coinbase API keys:</b>

1. Log in to your Coinbase account
2. Go to <a href='https://www.coinbase.com/settings/api'>API Management page</a>
3. Create a new API key
4. Enable Read-Only permissions
5. Copy the API key and secret and send them to the bot

⚠️ <b>Important Notes:</b>
• Use read-only permissions for your account security
• Never share your API keys with anyone else

🔹 <b>Now, enter your API Key:</b>
"""
    elif api_type == 'bybit':
        if lang == 'ar':
            instructions = """
📝 <b>كيفية الحصول على مفاتيح Bybit API:</b>

1. قم بتسجيل الدخول إلى حسابك في Bybit
2. انتقل إلى <a href='https://www.bybit.com/app/user/api-management'>صفحة إدارة API</a>
3. أنشئ مفتاح API جديد
4. قم بتمكين صلاحيات القراءة فقط
5. انسخ مفتاح API والسر وأرسلهما إلى البوت

⚠️ <b>ملاحظات مهمة:</b>
• استخدم صلاحيات القراءة فقط لأمان حسابك
• لا تشارك مفاتيح API مع أي شخص آخر

🔹 <b>الآن، أدخل مفتاح API (API Key):</b>
"""
        else:
            instructions = """
📝 <b>How to get Bybit API keys:</b>

1. Log in to your Bybit account
2. Go to <a href='https://www.bybit.com/app/user/api-management'>API Management page</a>
3. Create a new API key
4. Enable Read-Only permissions
5. Copy the API key and secret and send them to the bot

⚠️ <b>Important Notes:</b>
• Use read-only permissions for your account security
• Never share your API keys with anyone else

🔹 <b>Now, enter your API Key:</b>
"""
    elif api_type == 'okx':
        if lang == 'ar':
            instructions = """
📝 <b>كيفية الحصول على مفاتيح OKX API:</b>

1. قم بتسجيل الدخول إلى حسابك في OKX
2. انتقل إلى <a href='https://www.okx.com/account/my-api'>صفحة إدارة API</a>
3. أنشئ مفتاح API جديد
4. قم بتمكين صلاحيات القراءة فقط
5. انسخ مفتاح API والسر وكلمة المرور وأرسلهم إلى البوت

⚠️ <b>ملاحظات مهمة:</b>
• استخدم صلاحيات القراءة فقط لأمان حسابك
• لا تشارك مفاتيح API مع أي شخص آخر

🔹 <b>الآن، أدخل مفتاح API (API Key):</b>
"""
        else:
            instructions = """
📝 <b>How to get OKX API keys:</b>

1. Log in to your OKX account
2. Go to <a href='https://www.okx.com/account/my-api'>API Management page</a>
3. Create a new API key
4. Enable Read-Only permissions
5. Copy the API key, secret and passphrase and send them to the bot

⚠️ <b>Important Notes:</b>
• Use read-only permissions for your account security
• Never share your API keys with anyone else

🔹 <b>Now, enter your API Key:</b>
"""
    elif api_type == 'kraken':
        if lang == 'ar':
            instructions = """
📝 <b>كيفية الحصول على مفاتيح Kraken API:</b>

1. قم بتسجيل الدخول إلى حسابك في Kraken
2. انتقل إلى <a href='https://www.kraken.com/u/security/api'>صفحة إدارة API</a>
3. أنشئ مفتاح API جديد
4. قم بتمكين صلاحيات القراءة فقط
5. انسخ مفتاح API والسر وأرسلهما إلى البوت

⚠️ <b>ملاحظات مهمة:</b>
• استخدم صلاحيات القراءة فقط لأمان حسابك
• لا تشارك مفاتيح API مع أي شخص آخر

🔹 <b>الآن، أدخل مفتاح API (API Key):</b>
"""
        else:
            instructions = """
📝 <b>How to get Kraken API keys:</b>

1. Log in to your Kraken account
2. Go to <a href='https://www.kraken.com/u/security/api'>API Management page</a>
3. Create a new API key
4. Enable Read-Only permissions
5. Copy the API key and secret and send them to the bot

⚠️ <b>Important Notes:</b>
• Use read-only permissions for your account security
• Never share your API keys with anyone else

🔹 <b>Now, enter your API Key:</b>
"""

    # إضافة أزرار
    keyboard = [[
        InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back",
                            callback_data="setup_api_keys")
    ]]

    # إرسال الرسالة
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=instructions,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML',
            disable_web_page_preview=False  # السماح بعرض معاينة الروابط
        )
    else:
        await update.message.reply_text(
            text=instructions,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML',
            disable_web_page_preview=False  # السماح بعرض معاينة الروابط
        )

async def delete_api_keys_ui(update: Update, context: CallbackContext, api_manager, subscription_system):
    """
    واجهة حذف مفاتيح API

    Args:
        update: كائن التحديث
        context: سياق المحادثة
        api_manager: مدير API
        subscription_system: نظام الاشتراك
    """
    user_id = str(update.effective_user.id)
    settings = subscription_system.get_user_settings(user_id)
    lang = settings.get('lang', 'ar')

    # الحصول على معلومات API
    api_info = await api_manager.get_api_info(user_id)

    # التحقق من وجود أي مفاتيح API
    platforms = ['binance', 'gemini', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken']
    has_any_api = any([api_info.get(f'has_{platform}', False) for platform in platforms])

    if not has_any_api:
        # لا توجد مفاتيح API مخزنة
        if lang == 'ar':
            message_text = "لا توجد مفاتيح API مخزنة لحسابك."
        else:
            message_text = "No API keys stored for your account."

        keyboard = [[
            InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back",
                                callback_data="setup_api_keys")
        ]]

        if update.callback_query:
            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML',
                disable_web_page_preview=False
            )
        else:
            await update.message.reply_text(
                text=message_text,
                reply_markup=InlineKeyboardMarkup(keyboard),
                parse_mode='HTML',
                disable_web_page_preview=False
            )

        return

    # إنشاء أزرار الحذف
    keyboard = []

    # إضافة أزرار لكل منصة متاحة
    for platform in platforms:
        if api_info.get(f'has_{platform}', False):
            platform_name = platform.capitalize()
            keyboard.append([
                InlineKeyboardButton(f"🗑️ حذف {platform_name} API" if lang == 'ar' else f"🗑️ Delete {platform_name} API",
                                    callback_data=f"delete_{platform}_api")
            ])

    keyboard.append([
        InlineKeyboardButton("🔙 رجوع" if lang == 'ar' else "🔙 Back",
                            callback_data="setup_api_keys")
    ])

    # إنشاء نص الرسالة
    if lang == 'ar':
        message_text = "🗑️ حذف مفاتيح API\n\n"
        message_text += "اختر مفتاح API الذي ترغب في حذفه:"
    else:
        message_text = "🗑️ Delete API Keys\n\n"
        message_text += "Choose which API key you want to delete:"

    # إرسال الرسالة
    if update.callback_query:
        await update.callback_query.edit_message_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML',
            disable_web_page_preview=False
        )
    else:
        await update.message.reply_text(
            text=message_text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='HTML',
            disable_web_page_preview=False
        )

async def show_api_info(update: Update, context: CallbackContext, api_manager, subscription_system):
    """
    عرض معلومات API للمستخدم

    Args:
        update: كائن التحديث
        context: سياق المحادثة
        api_manager: مدير API
        subscription_system: نظام الاشتراك
    """
    user_id = str(update.effective_user.id)
    settings = subscription_system.get_user_settings(user_id)
    lang = settings.get('lang', 'ar')

    # الحصول على معلومات API
    api_info = await api_manager.get_api_info(user_id)

    # التحقق من حالة الاشتراك (يجب استخدام النسخة المتزامنة)
    is_subscribed = subscription_system.is_subscribed_sync(user_id)

    # قائمة المنصات المدعومة
    platforms = {
        'binance': 'Binance',
        'gemini': 'Gemini',
        'kucoin': 'KuCoin',
        'coinbase': 'Coinbase',
        'bybit': 'Bybit',
        'okx': 'OKX',
        'kraken': 'Kraken'
    }

    # إنشاء نص المعلومات
    if lang == 'ar':
        info_text = "🔑 <b>معلومات API</b>\n\n"

        # عرض معلومات كل منصة
        for platform_id, platform_name in platforms.items():
            # Gemini متاح لجميع المستخدمين للتعلم مع الذكاء الاصطناعي

            has_platform = api_info.get(f'has_{platform_id}', False)
            info_text += f"• <b>{platform_name} API</b>: {get_status_emoji(has_platform)}\n"

            # إضافة رابط إلى صفحة إدارة API
            platform_urls = {
                'binance': 'https://www.binance.com/en/my/settings/api-management',
                'gemini': 'https://aistudio.google.com/apikey',
                'kucoin': 'https://www.kucoin.com/account/api',
                'coinbase': 'https://www.coinbase.com/settings/api',
                'bybit': 'https://www.bybit.com/app/user/api-management',
                'okx': 'https://www.okx.com/account/my-api',
                'kraken': 'https://www.kraken.com/u/security/api'
            }

            if platform_id in platform_urls:
                info_text += f"  <a href='{platform_urls[platform_id]}'>صفحة إدارة API</a>\n"

            if has_platform and f'{platform_id}_updated_at' in api_info:
                info_text += f"  آخر تحديث: {api_info[f'{platform_id}_updated_at']}\n"

            info_text += "\n"

        info_text += "⚠️ <b>ملاحظات مهمة</b>:\n"
        info_text += "• مفاتيح API مشفرة ومخزنة بشكل آمن\n"
        info_text += "• لا يمكن لأحد الوصول إلى مفاتيحك الخاصة\n"
        info_text += "• يمكنك تحديث مفاتيحك في أي وقت\n"

        # إضافة تلميحات للمنصات غير المتصلة
        info_text += "\nلإعداد مفاتيح API، استخدم الأمر <code>/setup_api</code>"
    else:
        info_text = "🔑 <b>API Information</b>\n\n"

        # عرض معلومات كل منصة
        for platform_id, platform_name in platforms.items():
            # Gemini available for all users for AI learning

            has_platform = api_info.get(f'has_{platform_id}', False)
            info_text += f"• <b>{platform_name} API</b>: {get_status_emoji(has_platform, 'en')}\n"

            # إضافة رابط إلى صفحة إدارة API
            platform_urls = {
                'binance': 'https://www.binance.com/en/my/settings/api-management',
                'gemini': 'https://aistudio.google.com/apikey',
                'kucoin': 'https://www.kucoin.com/account/api',
                'coinbase': 'https://www.coinbase.com/settings/api',
                'bybit': 'https://www.bybit.com/app/user/api-management',
                'okx': 'https://www.okx.com/account/my-api',
                'kraken': 'https://www.kraken.com/u/security/api'
            }

            if platform_id in platform_urls:
                info_text += f"  <a href='{platform_urls[platform_id]}'>API Management Page</a>\n"

            if has_platform and f'{platform_id}_updated_at' in api_info:
                info_text += f"  Last Updated: {api_info[f'{platform_id}_updated_at']}\n"

            info_text += "\n"

        info_text += "⚠️ <b>Important Notes</b>:\n"
        info_text += "• API keys are encrypted and stored securely\n"
        info_text += "• No one can access your private keys\n"
        info_text += "• You can update your keys at any time\n"

        # إضافة تلميحات للمنصات غير المتصلة
        info_text += "\nTo set up API keys, use <code>/setup_api</code> command"

    # إضافة أزرار
    keyboard = [
        [InlineKeyboardButton("🔑 إعداد API" if lang == 'ar' else "🔑 Setup API", callback_data="setup_api_keys")],
        [InlineKeyboardButton("🔙 القائمة الرئيسية" if lang == 'ar' else "🔙 Main Menu", callback_data="back_to_main")]
    ]

    # إرسال الرسالة
    await update.message.reply_text(
        info_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode='HTML',
        disable_web_page_preview=False  # السماح بعرض معاينة الروابط
    )

def get_status_emoji(status: bool, lang: str = 'ar') -> str:
    """
    الحصول على رمز تعبيري للحالة

    Args:
        status: الحالة (True أو False)
        lang: اللغة

    Returns:
        رمز تعبيري للحالة
    """
    if status:
        return "متصل ✅" if lang == 'ar' else "Connected ✅"
    else:
        return "غير متصل ❌" if lang == 'ar' else "Not Connected ❌"
