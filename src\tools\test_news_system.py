"""
أداة اختبار نظام الأخبار الذكي
تختبر إرسال الأخبار الملخصة للمستخدمين حسب اللغة المختارة
"""

import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsSystemTester:
    """أداة اختبار نظام الأخبار الذكي"""
    
    def __init__(self, db=None, bot=None):
        self.db = db
        self.bot = bot
        self.test_results = []
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """تشغيل اختبار شامل لنظام الأخبار"""
        logger.info("🧪 بدء اختبار شامل لنظام الأخبار الذكي...")
        
        test_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'tests_passed': 0,
            'tests_failed': 0,
            'test_details': [],
            'recommendations': []
        }
        
        try:
            # اختبار 1: فحص تهيئة النظام
            await self._test_system_initialization(test_results)
            
            # اختبار 2: فحص تحديد اللغة
            await self._test_language_detection(test_results)
            
            # اختبار 3: فحص تحليل الأخبار بالذكاء الاصطناعي
            await self._test_ai_news_analysis(test_results)
            
            # اختبار 4: فحص نظام الترجمة
            await self._test_translation_system(test_results)
            
            # اختبار 5: فحص إرسال الإشعارات
            await self._test_notification_sending(test_results)
            
            # اختبار 6: فحص النظام التلقائي
            await self._test_automatic_system(test_results)
            
            # تحديد النتيجة العامة
            test_results['overall_status'] = 'passed' if test_results['tests_failed'] == 0 else 'failed'
            
            logger.info(f"✅ تم إكمال الاختبار. النجح: {test_results['tests_passed']}, فشل: {test_results['tests_failed']}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في الاختبار: {str(e)}")
            test_results['overall_status'] = 'error'
            test_results['error'] = str(e)
        
        return test_results
    
    async def _test_system_initialization(self, results: Dict[str, Any]):
        """اختبار تهيئة النظام"""
        test_name = "تهيئة النظام"
        logger.info(f"🔍 اختبار: {test_name}")
        
        try:
            # فحص نظام الأخبار
            from services.news_system import news_system
            assert news_system is not None, "نظام الأخبار غير مهيأ"
            
            # فحص نظام الإشعارات
            from services.automatic_news_notifications import automatic_news_notifications
            assert automatic_news_notifications is not None, "نظام الإشعارات غير مهيأ"
            
            # فحص نظام الجدولة
            from services.automatic_news_scheduler import automatic_news_scheduler
            assert automatic_news_scheduler is not None, "نظام الجدولة غير مهيأ"
            
            self._add_test_result(results, test_name, True, "جميع الأنظمة مهيأة بشكل صحيح")
            
        except Exception as e:
            self._add_test_result(results, test_name, False, f"خطأ في التهيئة: {str(e)}")
            results['recommendations'].append("تشغيل setup_automatic_news.py لتهيئة النظام")
    
    async def _test_language_detection(self, results: Dict[str, Any]):
        """اختبار تحديد اللغة"""
        test_name = "تحديد اللغة"
        logger.info(f"🔍 اختبار: {test_name}")
        
        try:
            from services.automatic_news_notifications import automatic_news_notifications
            
            if not automatic_news_notifications:
                self._add_test_result(results, test_name, False, "نظام الإشعارات غير متوفر")
                return
            
            # اختبار مع مستخدم وهمي
            test_user_id = "test_user_123"
            
            # إنشاء إعدادات اختبار
            if self.db:
                test_settings = {
                    'lang': 'en',
                    'language': 'en',
                    'test_user': True,
                    'created_at': datetime.now().isoformat()
                }
                self.db.collection('user_settings').document(test_user_id).set(test_settings)
            
            # اختبار تحديد اللغة
            detected_lang = await automatic_news_notifications._get_user_language(test_user_id)
            
            if self.db:
                expected_lang = 'en'
                assert detected_lang == expected_lang, f"اللغة المكتشفة {detected_lang} لا تطابق المتوقعة {expected_lang}"
            else:
                assert detected_lang == 'ar', "اللغة الافتراضية يجب أن تكون العربية"
            
            # تنظيف البيانات الاختبارية
            if self.db:
                self.db.collection('user_settings').document(test_user_id).delete()
            
            self._add_test_result(results, test_name, True, f"تم تحديد اللغة بشكل صحيح: {detected_lang}")
            
        except Exception as e:
            self._add_test_result(results, test_name, False, f"خطأ في تحديد اللغة: {str(e)}")
            results['recommendations'].append("فحص إعدادات اللغة في قاعدة البيانات")
    
    async def _test_ai_news_analysis(self, results: Dict[str, Any]):
        """اختبار تحليل الأخبار بالذكاء الاصطناعي"""
        test_name = "تحليل الأخبار بالذكاء الاصطناعي"
        logger.info(f"🔍 اختبار: {test_name}")
        
        try:
            from services.news_system import news_system
            
            if not news_system:
                self._add_test_result(results, test_name, False, "نظام الأخبار غير متوفر")
                return
            
            if not news_system.gemini_api_key:
                self._add_test_result(results, test_name, False, "مفتاح Gemini API غير متوفر")
                results['recommendations'].append("إضافة مفتاح Gemini API")
                return
            
            # إنشاء خبر اختباري
            from services.news_system import NewsItem, NewsSource
            test_news = NewsItem(
                id="test_news_123",
                title="Bitcoin reaches new all-time high",
                content="Bitcoin has reached a new all-time high of $100,000 today.",
                source=NewsSource.BINANCE,
                published_at=datetime.now(),
                url="https://example.com/test-news"
            )
            
            # اختبار التحليل
            analyzed_news = await news_system.analyze_news_with_ai(test_news, lang='ar')
            
            assert analyzed_news is not None, "فشل في تحليل الخبر"
            assert hasattr(analyzed_news, 'ai_analysis'), "لا يوجد تحليل ذكي في النتيجة"
            
            self._add_test_result(results, test_name, True, "تم تحليل الخبر بالذكاء الاصطناعي بنجاح")
            
        except Exception as e:
            self._add_test_result(results, test_name, False, f"خطأ في تحليل الأخبار: {str(e)}")
            results['recommendations'].append("فحص إعدادات Gemini API والاتصال بالإنترنت")
    
    async def _test_translation_system(self, results: Dict[str, Any]):
        """اختبار نظام الترجمة"""
        test_name = "نظام الترجمة"
        logger.info(f"🔍 اختبار: {test_name}")
        
        try:
            from services.automatic_news_notifications import automatic_news_notifications
            
            if not automatic_news_notifications:
                self._add_test_result(results, test_name, False, "نظام الإشعارات غير متوفر")
                return
            
            # اختبار الترجمة بالقاموس
            arabic_text = "🚨 خبر عاجل عن البيتكوين"
            translated_text = automatic_news_notifications._translate_to_english(arabic_text)
            
            assert translated_text != arabic_text, "لم تتم الترجمة"
            assert "Breaking News" in translated_text, "الترجمة غير صحيحة"
            assert "Bitcoin" in translated_text, "لم يتم ترجمة البيتكوين"
            
            self._add_test_result(results, test_name, True, f"تمت الترجمة بنجاح: {arabic_text} -> {translated_text}")
            
        except Exception as e:
            self._add_test_result(results, test_name, False, f"خطأ في الترجمة: {str(e)}")
            results['recommendations'].append("فحص قاموس الترجمة ونظام الذكاء الاصطناعي")
    
    async def _test_notification_sending(self, results: Dict[str, Any]):
        """اختبار إرسال الإشعارات"""
        test_name = "إرسال الإشعارات"
        logger.info(f"🔍 اختبار: {test_name}")
        
        try:
            from services.automatic_news_notifications import automatic_news_notifications, NotificationMessage, NotificationType, NotificationPriority
            
            if not automatic_news_notifications:
                self._add_test_result(results, test_name, False, "نظام الإشعارات غير متوفر")
                return
            
            if not self.bot:
                self._add_test_result(results, test_name, False, "البوت غير متوفر للاختبار")
                results['recommendations'].append("تمرير instance البوت لأداة الاختبار")
                return
            
            # إنشاء إشعار اختباري
            test_notification = NotificationMessage(
                user_id="7839527436",  # معرف المطور للاختبار
                notification_type=NotificationType.BREAKING_NEWS,
                priority=NotificationPriority.HIGH,
                title="🧪 اختبار النظام",
                content="هذا اختبار لنظام الأخبار الذكي. إذا وصلتك هذه الرسالة، فالنظام يعمل بشكل صحيح!",
                created_at=datetime.now()
            )
            
            # محاولة إرسال الإشعار
            success = await automatic_news_notifications._send_notification(test_notification)
            
            if success:
                self._add_test_result(results, test_name, True, "تم إرسال الإشعار الاختباري بنجاح")
            else:
                self._add_test_result(results, test_name, False, "فشل في إرسال الإشعار الاختباري")
                results['recommendations'].append("فحص إعدادات البوت وصلاحيات الإرسال")
            
        except Exception as e:
            self._add_test_result(results, test_name, False, f"خطأ في إرسال الإشعارات: {str(e)}")
            results['recommendations'].append("فحص اتصال البوت وصلاحيات الإرسال")
    
    async def _test_automatic_system(self, results: Dict[str, Any]):
        """اختبار النظام التلقائي"""
        test_name = "النظام التلقائي"
        logger.info(f"🔍 اختبار: {test_name}")
        
        try:
            from services.automatic_news_integration import automatic_news_integration
            
            if not automatic_news_integration:
                self._add_test_result(results, test_name, False, "نظام التكامل التلقائي غير متوفر")
                results['recommendations'].append("تشغيل النظام التلقائي من main.py")
                return
            
            # فحص حالة النظام
            integration_status = automatic_news_integration.get_integration_status()
            system_health = await automatic_news_integration.get_system_health()
            
            assert integration_status.get('is_running', False), "النظام التلقائي لا يعمل"
            assert system_health.get('overall_status') != 'error', f"صحة النظام سيئة: {system_health.get('overall_status')}"
            
            self._add_test_result(results, test_name, True, f"النظام التلقائي يعمل بحالة: {system_health.get('overall_status')}")
            
        except Exception as e:
            self._add_test_result(results, test_name, False, f"خطأ في النظام التلقائي: {str(e)}")
            results['recommendations'].append("إعادة تشغيل النظام التلقائي")
    
    def _add_test_result(self, results: Dict[str, Any], test_name: str, passed: bool, message: str):
        """إضافة نتيجة اختبار"""
        if passed:
            results['tests_passed'] += 1
            status = "✅ نجح"
        else:
            results['tests_failed'] += 1
            status = "❌ فشل"
        
        results['test_details'].append({
            'test_name': test_name,
            'status': status,
            'passed': passed,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"{status}: {test_name} - {message}")
    
    def print_test_report(self, results: Dict[str, Any]):
        """طباعة تقرير الاختبار"""
        print("\n" + "="*60)
        print("🧪 تقرير اختبار نظام الأخبار الذكي")
        print("="*60)
        print(f"⏰ وقت الاختبار: {results['timestamp']}")
        print(f"📊 النتيجة العامة: {results['overall_status']}")
        print(f"✅ اختبارات نجحت: {results['tests_passed']}")
        print(f"❌ اختبارات فشلت: {results['tests_failed']}")
        
        print(f"\n📋 تفاصيل الاختبارات:")
        for test in results['test_details']:
            print(f"  {test['status']} {test['test_name']}")
            print(f"    📝 {test['message']}")
        
        if results['recommendations']:
            print(f"\n💡 التوصيات ({len(results['recommendations'])}):")
            for i, recommendation in enumerate(results['recommendations'], 1):
                print(f"  {i}. {recommendation}")
        
        print("\n" + "="*60)

async def main():
    """تشغيل الاختبار"""
    try:
        # تهيئة قاعدة البيانات
        from core.database import db
        
        # محاولة الحصول على البوت
        bot = None
        try:
            from core.telegram_bot import TelegramBot
            telegram_bot = TelegramBot()
            await telegram_bot.setup()
            bot = telegram_bot.application.bot
        except Exception as e:
            logger.warning(f"لم يتم تهيئة البوت للاختبار: {str(e)}")
        
        # إنشاء أداة الاختبار
        tester = NewsSystemTester(db, bot)
        
        # تشغيل الاختبار
        results = await tester.run_comprehensive_test()
        
        # طباعة التقرير
        tester.print_test_report(results)
        
        return results
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل الاختبار: {str(e)}")
        print(f"❌ خطأ في الاختبار: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
