"""
خدمة إدارة API - إدارة مفاتيح API للمنصات المختلفة
تم فصل هذه الوحدة من main.py لتحسين تنظيم الكود وسهولة الصيانة

المرحلة الخامسة من خطة تقسيم main.py
تاريخ الإنشاء: 2025-01-05
"""

import logging
from telegram import Update
from telegram.ext import CallbackContext

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

# متغيرات عامة سيتم تعيينها من main.py
api_manager = None
subscription_system = None
user_states = None

def initialize_api_management_service(api_mgr, sub_system, user_st):
    """
    تهيئة خدمة إدارة API مع التبعيات المطلوبة
    
    Args:
        api_mgr: مدير API
        sub_system: نظام الاشتراكات
        user_st: حالات المستخدمين
    """
    global api_manager, subscription_system, user_states
    api_manager = api_mgr
    subscription_system = sub_system
    user_states = user_st
    logger.info("✅ تم تهيئة خدمة إدارة API بنجاح")

async def api_setup_command(update: Update, context: CallbackContext, preselect_platform=None):
    """إعداد مفاتيح API"""
    try:
        # استيراد دوال واجهة المستخدم
        from api_ui import setup_api_keys, show_api_instructions

        await setup_api_keys(update, context, api_manager, subscription_system)

        # إذا تم تحديد منصة مسبقًا، توجيه المستخدم إلى إعداد تلك المنصة
        if preselect_platform:
            user_id = str(update.effective_user.id)
            settings = subscription_system.get_user_settings(user_id)
            lang = settings.get('lang', 'ar')
            await show_api_instructions(update, context, preselect_platform, lang)
            # تحديث حالة المستخدم
            user_states[user_id] = {'api_setup_state': f'{preselect_platform}_key'}
    except Exception as e:
        logger.error(f"خطأ في إعداد مفاتيح API: {str(e)}")
        # الحصول على لغة المستخدم لرسالة الخطأ
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        error_message = "⚠️ حدث خطأ أثناء إعداد مفاتيح API. الرجاء المحاولة مرة أخرى." if lang == 'ar' else "⚠️ An error occurred while setting up API keys. Please try again."
        await update.message.reply_text(error_message)

async def api_info_command(update: Update, context: CallbackContext):
    """عرض معلومات API"""
    try:
        # استيراد دوال واجهة المستخدم
        from api_ui import show_api_info

        await show_api_info(update, context, api_manager, subscription_system)
    except Exception as e:
        logger.error(f"خطأ في عرض معلومات API: {str(e)}")
        # الحصول على لغة المستخدم لرسالة الخطأ
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        error_message = "⚠️ حدث خطأ أثناء عرض معلومات API. الرجاء المحاولة مرة أخرى." if lang == 'ar' else "⚠️ An error occurred while displaying API information. Please try again."
        await update.message.reply_text(error_message)

async def delete_api_command(update: Update, context: CallbackContext):
    """حذف مفاتيح API"""
    try:
        # استيراد دوال واجهة المستخدم
        from api_ui import delete_api_keys_ui

        await delete_api_keys_ui(update, context, api_manager, subscription_system)
    except Exception as e:
        logger.error(f"خطأ في حذف مفاتيح API: {str(e)}")
        # الحصول على لغة المستخدم لرسالة الخطأ
        user_id = str(update.effective_user.id)
        settings = subscription_system.get_user_settings(user_id)
        lang = settings.get('lang', 'ar') if settings else 'ar'

        error_message = "⚠️ حدث خطأ أثناء حذف مفاتيح API. الرجاء المحاولة مرة أخرى." if lang == 'ar' else "⚠️ An error occurred while deleting API keys. Please try again."
        await update.message.reply_text(error_message)
