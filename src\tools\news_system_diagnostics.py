"""
أداة تشخيص نظام الأخبار الذكي
تفحص حالة النظام وتشخص المشاكل
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsSystemDiagnostics:
    """أداة تشخيص نظام الأخبار الذكي"""
    
    def __init__(self, db=None):
        self.db = db
        self.issues = []
        self.recommendations = []
    
    async def run_full_diagnosis(self) -> Dict[str, Any]:
        """تشغيل تشخيص شامل للنظام"""
        logger.info("🔍 بدء تشخيص نظام الأخبار الذكي...")
        
        diagnosis_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'unknown',
            'issues': [],
            'recommendations': [],
            'system_components': {},
            'user_language_analysis': {},
            'notification_analysis': {}
        }
        
        try:
            # فحص مكونات النظام
            diagnosis_results['system_components'] = await self._check_system_components()
            
            # فحص إعدادات اللغة للمستخدمين
            diagnosis_results['user_language_analysis'] = await self._analyze_user_languages()
            
            # فحص نظام الإشعارات
            diagnosis_results['notification_analysis'] = await self._analyze_notifications()
            
            # فحص مفاتيح API
            diagnosis_results['api_keys_status'] = await self._check_api_keys()
            
            # فحص قاعدة البيانات
            diagnosis_results['database_status'] = await self._check_database()
            
            # تحديد الحالة العامة
            diagnosis_results['overall_status'] = self._determine_overall_status(diagnosis_results)
            
            # إضافة التوصيات
            diagnosis_results['issues'] = self.issues
            diagnosis_results['recommendations'] = self.recommendations
            
            logger.info(f"✅ تم إكمال التشخيص. الحالة العامة: {diagnosis_results['overall_status']}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في التشخيص: {str(e)}")
            diagnosis_results['overall_status'] = 'error'
            diagnosis_results['error'] = str(e)
        
        return diagnosis_results
    
    async def _check_system_components(self) -> Dict[str, Any]:
        """فحص مكونات النظام"""
        components = {
            'news_system': False,
            'automatic_scheduler': False,
            'notifications_system': False,
            'cache_system': False,
            'rate_limiter': False
        }
        
        try:
            # فحص نظام الأخبار الأساسي
            try:
                from services.news_system import news_system
                components['news_system'] = news_system is not None
                if not components['news_system']:
                    self.issues.append("نظام الأخبار الأساسي غير مهيأ")
                    self.recommendations.append("تشغيل initialize_news_system()")
            except ImportError:
                self.issues.append("فشل في استيراد نظام الأخبار")
            
            # فحص نظام الجدولة التلقائية
            try:
                from services.automatic_news_scheduler import automatic_news_scheduler
                components['automatic_scheduler'] = automatic_news_scheduler is not None
                if not components['automatic_scheduler']:
                    self.issues.append("نظام الجدولة التلقائية غير مهيأ")
                    self.recommendations.append("تشغيل initialize_automatic_news_scheduler()")
            except ImportError:
                self.issues.append("فشل في استيراد نظام الجدولة")
            
            # فحص نظام الإشعارات
            try:
                from services.automatic_news_notifications import automatic_news_notifications
                components['notifications_system'] = automatic_news_notifications is not None
                if not components['notifications_system']:
                    self.issues.append("نظام الإشعارات غير مهيأ")
                    self.recommendations.append("تشغيل initialize_automatic_news_notifications()")
            except ImportError:
                self.issues.append("فشل في استيراد نظام الإشعارات")
            
            # فحص نظام التخزين المؤقت
            try:
                from services.intelligent_news_cache import intelligent_news_cache
                components['cache_system'] = intelligent_news_cache is not None
                if not components['cache_system']:
                    self.issues.append("نظام التخزين المؤقت غير مهيأ")
            except ImportError:
                self.issues.append("فشل في استيراد نظام التخزين المؤقت")
            
            # فحص نظام إدارة معدل الطلبات
            try:
                from services.smart_rate_limiter import smart_rate_limiter
                components['rate_limiter'] = smart_rate_limiter is not None
                if not components['rate_limiter']:
                    self.issues.append("نظام إدارة معدل الطلبات غير مهيأ")
            except ImportError:
                self.issues.append("فشل في استيراد نظام إدارة معدل الطلبات")
                
        except Exception as e:
            logger.error(f"خطأ في فحص مكونات النظام: {str(e)}")
            self.issues.append(f"خطأ في فحص المكونات: {str(e)}")
        
        return components
    
    async def _analyze_user_languages(self) -> Dict[str, Any]:
        """تحليل إعدادات اللغة للمستخدمين"""
        analysis = {
            'total_users': 0,
            'users_with_language': 0,
            'language_distribution': {'ar': 0, 'en': 0, 'unknown': 0},
            'issues_found': []
        }
        
        if not self.db:
            analysis['issues_found'].append("قاعدة البيانات غير متوفرة")
            return analysis
        
        try:
            # فحص جدول user_settings
            settings_collection = self.db.collection('user_settings')
            settings_docs = settings_collection.stream()
            
            for doc in settings_docs:
                analysis['total_users'] += 1
                data = doc.to_dict()
                
                # البحث عن إعداد اللغة
                lang = data.get('lang') or data.get('language')
                if lang:
                    analysis['users_with_language'] += 1
                    if lang in ['ar', 'en']:
                        analysis['language_distribution'][lang] += 1
                    else:
                        analysis['language_distribution']['unknown'] += 1
                        analysis['issues_found'].append(f"مستخدم {doc.id} لديه لغة غير صحيحة: {lang}")
                else:
                    analysis['language_distribution']['unknown'] += 1
                    analysis['issues_found'].append(f"مستخدم {doc.id} ليس لديه إعداد لغة")
            
            # إضافة توصيات
            if analysis['users_with_language'] < analysis['total_users']:
                missing_count = analysis['total_users'] - analysis['users_with_language']
                self.issues.append(f"{missing_count} مستخدم ليس لديهم إعداد لغة")
                self.recommendations.append("تحديث إعدادات اللغة للمستخدمين المفقودين")
                
        except Exception as e:
            logger.error(f"خطأ في تحليل اللغات: {str(e)}")
            analysis['issues_found'].append(f"خطأ في التحليل: {str(e)}")
        
        return analysis
    
    async def _analyze_notifications(self) -> Dict[str, Any]:
        """تحليل نظام الإشعارات"""
        analysis = {
            'recent_notifications': 0,
            'failed_notifications': 0,
            'notification_types': {},
            'last_notification_time': None,
            'issues_found': []
        }
        
        if not self.db:
            analysis['issues_found'].append("قاعدة البيانات غير متوفرة")
            return analysis
        
        try:
            # فحص الإشعارات الحديثة (آخر 24 ساعة)
            yesterday = datetime.now() - timedelta(days=1)
            
            # البحث في جدول الإشعارات إذا كان موجوداً
            try:
                notifications_collection = self.db.collection('notifications')
                recent_notifications = notifications_collection.where(
                    'created_at', '>=', yesterday.isoformat()
                ).stream()
                
                for notification in recent_notifications:
                    analysis['recent_notifications'] += 1
                    data = notification.to_dict()
                    
                    # تحليل نوع الإشعار
                    notification_type = data.get('notification_type', 'unknown')
                    analysis['notification_types'][notification_type] = analysis['notification_types'].get(notification_type, 0) + 1
                    
                    # فحص حالة الإرسال
                    if not data.get('sent', True):
                        analysis['failed_notifications'] += 1
                    
                    # تحديث آخر وقت إشعار
                    created_at = data.get('created_at')
                    if created_at and (not analysis['last_notification_time'] or created_at > analysis['last_notification_time']):
                        analysis['last_notification_time'] = created_at
                        
            except Exception as e:
                analysis['issues_found'].append(f"خطأ في قراءة الإشعارات: {str(e)}")
            
            # تحليل النتائج
            if analysis['recent_notifications'] == 0:
                self.issues.append("لا توجد إشعارات حديثة في آخر 24 ساعة")
                self.recommendations.append("فحص تشغيل نظام الإشعارات التلقائية")
            
            if analysis['failed_notifications'] > 0:
                self.issues.append(f"{analysis['failed_notifications']} إشعار فشل في الإرسال")
                self.recommendations.append("فحص إعدادات البوت وصلاحيات الإرسال")
                
        except Exception as e:
            logger.error(f"خطأ في تحليل الإشعارات: {str(e)}")
            analysis['issues_found'].append(f"خطأ في التحليل: {str(e)}")
        
        return analysis
    
    async def _check_api_keys(self) -> Dict[str, Any]:
        """فحص مفاتيح API"""
        api_status = {
            'gemini_api': False,
            'binance_api': False,
            'issues_found': []
        }
        
        try:
            # فحص مفتاح Gemini
            import os
            gemini_key = os.getenv('GEMINI_API_KEY')
            if gemini_key:
                api_status['gemini_api'] = True
            else:
                # البحث في قاعدة البيانات
                if self.db:
                    try:
                        system_settings = self.db.collection('system_settings').document('config').get()
                        if system_settings.exists:
                            settings_data = system_settings.to_dict()
                            if settings_data.get('gemini_api_key'):
                                api_status['gemini_api'] = True
                    except Exception as e:
                        api_status['issues_found'].append(f"خطأ في قراءة إعدادات النظام: {str(e)}")
            
            if not api_status['gemini_api']:
                self.issues.append("مفتاح Gemini API غير متوفر")
                self.recommendations.append("إضافة مفتاح Gemini API في متغيرات البيئة أو إعدادات النظام")
            
            # فحص مفتاح Binance (اختياري)
            binance_key = os.getenv('BINANCE_API_KEY')
            api_status['binance_api'] = bool(binance_key)
            
        except Exception as e:
            logger.error(f"خطأ في فحص مفاتيح API: {str(e)}")
            api_status['issues_found'].append(f"خطأ في الفحص: {str(e)}")
        
        return api_status
    
    async def _check_database(self) -> Dict[str, Any]:
        """فحص قاعدة البيانات"""
        db_status = {
            'connected': False,
            'collections_exist': {},
            'issues_found': []
        }
        
        if not self.db:
            db_status['issues_found'].append("قاعدة البيانات غير متوفرة")
            self.issues.append("قاعدة البيانات غير متصلة")
            return db_status
        
        try:
            # فحص الاتصال
            test_doc = self.db.collection('test').document('connection_test')
            test_doc.set({'test': True})
            test_doc.delete()
            db_status['connected'] = True
            
            # فحص وجود المجموعات المطلوبة
            required_collections = ['user_settings', 'users', 'notifications', 'news_cache']
            for collection_name in required_collections:
                try:
                    collection = self.db.collection(collection_name)
                    # محاولة قراءة وثيقة واحدة للتأكد من وجود المجموعة
                    docs = list(collection.limit(1).stream())
                    db_status['collections_exist'][collection_name] = True
                except Exception:
                    db_status['collections_exist'][collection_name] = False
                    db_status['issues_found'].append(f"مجموعة {collection_name} غير موجودة أو لا يمكن الوصول إليها")
            
        except Exception as e:
            logger.error(f"خطأ في فحص قاعدة البيانات: {str(e)}")
            db_status['issues_found'].append(f"خطأ في الفحص: {str(e)}")
            self.issues.append("مشكلة في الاتصال بقاعدة البيانات")
        
        return db_status
    
    def _determine_overall_status(self, results: Dict[str, Any]) -> str:
        """تحديد الحالة العامة للنظام"""
        if len(self.issues) == 0:
            return 'excellent'
        elif len(self.issues) <= 2:
            return 'good'
        elif len(self.issues) <= 5:
            return 'fair'
        else:
            return 'poor'
    
    def print_diagnosis_report(self, results: Dict[str, Any]):
        """طباعة تقرير التشخيص"""
        print("\n" + "="*60)
        print("🔍 تقرير تشخيص نظام الأخبار الذكي")
        print("="*60)
        print(f"⏰ وقت التشخيص: {results['timestamp']}")
        print(f"📊 الحالة العامة: {results['overall_status']}")
        
        # مكونات النظام
        print(f"\n🔧 مكونات النظام:")
        for component, status in results['system_components'].items():
            status_icon = "✅" if status else "❌"
            print(f"  {status_icon} {component}: {'يعمل' if status else 'لا يعمل'}")
        
        # تحليل اللغات
        lang_analysis = results['user_language_analysis']
        print(f"\n🌐 تحليل اللغات:")
        print(f"  👥 إجمالي المستخدمين: {lang_analysis.get('total_users', 0)}")
        print(f"  🗣️ مستخدمين لديهم إعداد لغة: {lang_analysis.get('users_with_language', 0)}")
        print(f"  📊 توزيع اللغات:")
        for lang, count in lang_analysis.get('language_distribution', {}).items():
            print(f"    - {lang}: {count}")
        
        # تحليل الإشعارات
        notif_analysis = results['notification_analysis']
        print(f"\n🔔 تحليل الإشعارات:")
        print(f"  📨 إشعارات حديثة (24 ساعة): {notif_analysis.get('recent_notifications', 0)}")
        print(f"  ❌ إشعارات فاشلة: {notif_analysis.get('failed_notifications', 0)}")
        print(f"  🕐 آخر إشعار: {notif_analysis.get('last_notification_time', 'غير محدد')}")
        
        # مفاتيح API
        api_status = results['api_keys_status']
        print(f"\n🔑 حالة مفاتيح API:")
        print(f"  🤖 Gemini API: {'✅ متوفر' if api_status.get('gemini_api') else '❌ غير متوفر'}")
        print(f"  💱 Binance API: {'✅ متوفر' if api_status.get('binance_api') else '❌ غير متوفر'}")
        
        # قاعدة البيانات
        db_status = results['database_status']
        print(f"\n💾 حالة قاعدة البيانات:")
        print(f"  🔗 الاتصال: {'✅ متصل' if db_status.get('connected') else '❌ غير متصل'}")
        
        # المشاكل
        if results['issues']:
            print(f"\n⚠️ المشاكل المكتشفة ({len(results['issues'])}):")
            for i, issue in enumerate(results['issues'], 1):
                print(f"  {i}. {issue}")
        
        # التوصيات
        if results['recommendations']:
            print(f"\n💡 التوصيات ({len(results['recommendations'])}):")
            for i, recommendation in enumerate(results['recommendations'], 1):
                print(f"  {i}. {recommendation}")
        
        print("\n" + "="*60)

async def main():
    """تشغيل التشخيص"""
    try:
        # تهيئة قاعدة البيانات
        from core.database import db
        
        # إنشاء أداة التشخيص
        diagnostics = NewsSystemDiagnostics(db)
        
        # تشغيل التشخيص
        results = await diagnostics.run_full_diagnosis()
        
        # طباعة التقرير
        diagnostics.print_diagnosis_report(results)
        
        return results
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل التشخيص: {str(e)}")
        print(f"❌ خطأ في التشخيص: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
