"""
أداة إصلاح سريع لنظام الأخبار الذكي
تصلح المشاكل الشائعة وتضمن عمل النظام بشكل صحيح
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NewsSystemFixer:
    """أداة إصلاح نظام الأخبار الذكي"""
    
    def __init__(self, db=None):
        self.db = db
        self.fixes_applied = []
        self.issues_found = []
    
    async def run_auto_fix(self) -> Dict[str, Any]:
        """تشغيل الإصلاح التلقائي"""
        logger.info("🔧 بدء الإصلاح التلقائي لنظام الأخبار الذكي...")
        
        fix_results = {
            'timestamp': datetime.now().isoformat(),
            'fixes_applied': [],
            'issues_found': [],
            'success_count': 0,
            'failure_count': 0,
            'overall_status': 'unknown'
        }
        
        try:
            # إصلاح 1: تهيئة الأنظمة المفقودة
            await self._fix_missing_systems(fix_results)
            
            # إصلاح 2: إصلاح إعدادات اللغة للمستخدمين
            await self._fix_user_language_settings(fix_results)
            
            # إصلاح 3: إنشاء تفضيلات الإشعارات المفقودة
            await self._fix_notification_preferences(fix_results)
            
            # إصلاح 4: تنظيف البيانات القديمة
            await self._cleanup_old_data(fix_results)
            
            # إصلاح 5: إعادة تشغيل النظام التلقائي
            await self._restart_automatic_system(fix_results)
            
            # تحديد النتيجة العامة
            if fix_results['failure_count'] == 0:
                fix_results['overall_status'] = 'success'
            elif fix_results['success_count'] > fix_results['failure_count']:
                fix_results['overall_status'] = 'partial_success'
            else:
                fix_results['overall_status'] = 'failed'
            
            logger.info(f"✅ تم إكمال الإصلاح. نجح: {fix_results['success_count']}, فشل: {fix_results['failure_count']}")
            
        except Exception as e:
            logger.error(f"❌ خطأ في الإصلاح: {str(e)}")
            fix_results['overall_status'] = 'error'
            fix_results['error'] = str(e)
        
        return fix_results
    
    async def _fix_missing_systems(self, results: Dict[str, Any]):
        """إصلاح الأنظمة المفقودة"""
        fix_name = "تهيئة الأنظمة المفقودة"
        logger.info(f"🔧 إصلاح: {fix_name}")
        
        try:
            systems_initialized = []
            
            # تهيئة نظام الأخبار
            try:
                from services.news_system import news_system, initialize_news_system
                if not news_system:
                    # محاولة الحصول على مفتاح Gemini
                    gemini_key = None
                    if self.db:
                        try:
                            system_settings = self.db.collection('system_settings').document('config').get()
                            if system_settings.exists:
                                settings_data = system_settings.to_dict()
                                gemini_key = settings_data.get('gemini_api_key')
                        except Exception:
                            pass
                    
                    initialize_news_system(self.db, gemini_key)
                    systems_initialized.append("نظام الأخبار")
            except Exception as e:
                logger.warning(f"فشل في تهيئة نظام الأخبار: {str(e)}")
            
            # تهيئة نظام الإشعارات
            try:
                from services.automatic_news_notifications import automatic_news_notifications, initialize_automatic_news_notifications
                if not automatic_news_notifications:
                    initialize_automatic_news_notifications(self.db, None)
                    systems_initialized.append("نظام الإشعارات")
            except Exception as e:
                logger.warning(f"فشل في تهيئة نظام الإشعارات: {str(e)}")
            
            # تهيئة نظام الجدولة
            try:
                from services.automatic_news_scheduler import automatic_news_scheduler, initialize_automatic_news_scheduler
                from services.news_system import news_system
                if not automatic_news_scheduler:
                    initialize_automatic_news_scheduler(news_system, self.db, None)
                    systems_initialized.append("نظام الجدولة")
            except Exception as e:
                logger.warning(f"فشل في تهيئة نظام الجدولة: {str(e)}")
            
            if systems_initialized:
                self._add_fix_result(results, fix_name, True, f"تم تهيئة: {', '.join(systems_initialized)}")
            else:
                self._add_fix_result(results, fix_name, True, "جميع الأنظمة مهيأة مسبقاً")
                
        except Exception as e:
            self._add_fix_result(results, fix_name, False, f"خطأ في التهيئة: {str(e)}")
    
    async def _fix_user_language_settings(self, results: Dict[str, Any]):
        """إصلاح إعدادات اللغة للمستخدمين"""
        fix_name = "إصلاح إعدادات اللغة"
        logger.info(f"🔧 إصلاح: {fix_name}")
        
        if not self.db:
            self._add_fix_result(results, fix_name, False, "قاعدة البيانات غير متوفرة")
            return
        
        try:
            fixed_users = 0
            
            # البحث عن المستخدمين بدون إعدادات لغة
            users_collection = self.db.collection('users')
            users_docs = users_collection.stream()
            
            for user_doc in users_docs:
                user_id = user_doc.id
                user_data = user_doc.to_dict()
                
                # فحص إعدادات اللغة
                settings_doc = self.db.collection('user_settings').document(user_id).get()
                
                if not settings_doc.exists:
                    # إنشاء إعدادات افتراضية
                    default_settings = {
                        'lang': 'ar',
                        'language': 'ar',
                        'lang_selected': False,
                        'notifications_enabled': True,
                        'created_at': datetime.now().isoformat(),
                        'fixed_by_auto_repair': True
                    }
                    
                    self.db.collection('user_settings').document(user_id).set(default_settings)
                    fixed_users += 1
                    
                else:
                    # فحص وإصلاح الإعدادات الموجودة
                    settings_data = settings_doc.to_dict()
                    needs_update = False
                    
                    if not settings_data.get('lang') and not settings_data.get('language'):
                        settings_data['lang'] = 'ar'
                        settings_data['language'] = 'ar'
                        needs_update = True
                    
                    if 'lang_selected' not in settings_data:
                        settings_data['lang_selected'] = False
                        needs_update = True
                    
                    if needs_update:
                        settings_data['updated_at'] = datetime.now().isoformat()
                        settings_data['fixed_by_auto_repair'] = True
                        self.db.collection('user_settings').document(user_id).set(settings_data, merge=True)
                        fixed_users += 1
            
            if fixed_users > 0:
                self._add_fix_result(results, fix_name, True, f"تم إصلاح إعدادات {fixed_users} مستخدم")
            else:
                self._add_fix_result(results, fix_name, True, "جميع إعدادات اللغة صحيحة")
                
        except Exception as e:
            self._add_fix_result(results, fix_name, False, f"خطأ في إصلاح اللغات: {str(e)}")
    
    async def _fix_notification_preferences(self, results: Dict[str, Any]):
        """إصلاح تفضيلات الإشعارات"""
        fix_name = "إصلاح تفضيلات الإشعارات"
        logger.info(f"🔧 إصلاح: {fix_name}")
        
        if not self.db:
            self._add_fix_result(results, fix_name, False, "قاعدة البيانات غير متوفرة")
            return
        
        try:
            from services.automatic_news_notifications import NotificationType
            
            fixed_preferences = 0
            
            # البحث عن المستخدمين
            users_collection = self.db.collection('users')
            users_docs = users_collection.stream()
            
            for user_doc in users_docs:
                user_id = user_doc.id
                
                # فحص تفضيلات الإشعارات
                prefs_doc = self.db.collection('notification_preferences').document(user_id).get()
                
                if not prefs_doc.exists:
                    # إنشاء تفضيلات افتراضية
                    default_prefs = {
                        'enabled': True,
                        'language': 'ar',
                        'types': [
                            NotificationType.BREAKING_NEWS.value,
                            NotificationType.NEW_COIN.value
                        ],
                        'max_daily': {
                            NotificationType.BREAKING_NEWS.value: 5,
                            NotificationType.NEW_COIN.value: 3,
                            NotificationType.MARKET_ANALYSIS.value: 2,
                            NotificationType.DAILY_SUMMARY.value: 1
                        },
                        'created_at': datetime.now().isoformat(),
                        'fixed_by_auto_repair': True
                    }
                    
                    self.db.collection('notification_preferences').document(user_id).set(default_prefs)
                    fixed_preferences += 1
            
            if fixed_preferences > 0:
                self._add_fix_result(results, fix_name, True, f"تم إنشاء تفضيلات لـ {fixed_preferences} مستخدم")
            else:
                self._add_fix_result(results, fix_name, True, "جميع تفضيلات الإشعارات موجودة")
                
        except Exception as e:
            self._add_fix_result(results, fix_name, False, f"خطأ في إصلاح التفضيلات: {str(e)}")
    
    async def _cleanup_old_data(self, results: Dict[str, Any]):
        """تنظيف البيانات القديمة"""
        fix_name = "تنظيف البيانات القديمة"
        logger.info(f"🔧 إصلاح: {fix_name}")
        
        if not self.db:
            self._add_fix_result(results, fix_name, False, "قاعدة البيانات غير متوفرة")
            return
        
        try:
            from datetime import timedelta
            
            cleaned_items = 0
            cutoff_date = datetime.now() - timedelta(days=7)  # حذف البيانات الأقدم من أسبوع
            
            # تنظيف الإشعارات القديمة
            try:
                notifications_collection = self.db.collection('notifications')
                old_notifications = notifications_collection.where(
                    'created_at', '<', cutoff_date.isoformat()
                ).stream()
                
                for notification in old_notifications:
                    notification.reference.delete()
                    cleaned_items += 1
                    
            except Exception as e:
                logger.debug(f"خطأ في تنظيف الإشعارات: {str(e)}")
            
            # تنظيف التخزين المؤقت القديم
            try:
                cache_collection = self.db.collection('news_cache')
                old_cache = cache_collection.where(
                    'created_at', '<', cutoff_date.isoformat()
                ).stream()
                
                for cache_item in old_cache:
                    cache_item.reference.delete()
                    cleaned_items += 1
                    
            except Exception as e:
                logger.debug(f"خطأ في تنظيف التخزين المؤقت: {str(e)}")
            
            if cleaned_items > 0:
                self._add_fix_result(results, fix_name, True, f"تم تنظيف {cleaned_items} عنصر قديم")
            else:
                self._add_fix_result(results, fix_name, True, "لا توجد بيانات قديمة للتنظيف")
                
        except Exception as e:
            self._add_fix_result(results, fix_name, False, f"خطأ في التنظيف: {str(e)}")
    
    async def _restart_automatic_system(self, results: Dict[str, Any]):
        """إعادة تشغيل النظام التلقائي"""
        fix_name = "إعادة تشغيل النظام التلقائي"
        logger.info(f"🔧 إصلاح: {fix_name}")
        
        try:
            # محاولة إعادة تهيئة النظام التلقائي
            try:
                from services.automatic_news_integration import automatic_news_integration, initialize_automatic_news_integration
                
                if automatic_news_integration:
                    # إيقاف النظام الحالي
                    await automatic_news_integration.stop_automatic_systems()
                    
                    # إعادة تشغيله
                    success = await automatic_news_integration.start_automatic_systems()
                    
                    if success:
                        self._add_fix_result(results, fix_name, True, "تم إعادة تشغيل النظام التلقائي بنجاح")
                    else:
                        self._add_fix_result(results, fix_name, False, "فشل في إعادة تشغيل النظام التلقائي")
                else:
                    # تهيئة النظام من جديد
                    integration = await initialize_automatic_news_integration(self.db, None)
                    if integration:
                        success = await integration.start_automatic_systems()
                        if success:
                            self._add_fix_result(results, fix_name, True, "تم تهيئة وتشغيل النظام التلقائي")
                        else:
                            self._add_fix_result(results, fix_name, False, "فشل في تشغيل النظام التلقائي الجديد")
                    else:
                        self._add_fix_result(results, fix_name, False, "فشل في تهيئة النظام التلقائي")
                        
            except Exception as e:
                self._add_fix_result(results, fix_name, False, f"خطأ في النظام التلقائي: {str(e)}")
                
        except Exception as e:
            self._add_fix_result(results, fix_name, False, f"خطأ عام: {str(e)}")
    
    def _add_fix_result(self, results: Dict[str, Any], fix_name: str, success: bool, message: str):
        """إضافة نتيجة إصلاح"""
        if success:
            results['success_count'] += 1
            status = "✅ نجح"
        else:
            results['failure_count'] += 1
            status = "❌ فشل"
        
        results['fixes_applied'].append({
            'fix_name': fix_name,
            'status': status,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"{status}: {fix_name} - {message}")
    
    def print_fix_report(self, results: Dict[str, Any]):
        """طباعة تقرير الإصلاح"""
        print("\n" + "="*60)
        print("🔧 تقرير إصلاح نظام الأخبار الذكي")
        print("="*60)
        print(f"⏰ وقت الإصلاح: {results['timestamp']}")
        print(f"📊 النتيجة العامة: {results['overall_status']}")
        print(f"✅ إصلاحات نجحت: {results['success_count']}")
        print(f"❌ إصلاحات فشلت: {results['failure_count']}")
        
        print(f"\n📋 تفاصيل الإصلاحات:")
        for fix in results['fixes_applied']:
            print(f"  {fix['status']} {fix['fix_name']}")
            print(f"    📝 {fix['message']}")
        
        print("\n" + "="*60)

async def main():
    """تشغيل الإصلاح"""
    try:
        # تهيئة قاعدة البيانات
        from core.database import db
        
        # إنشاء أداة الإصلاح
        fixer = NewsSystemFixer(db)
        
        # تشغيل الإصلاح
        results = await fixer.run_auto_fix()
        
        # طباعة التقرير
        fixer.print_fix_report(results)
        
        return results
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل الإصلاح: {str(e)}")
        print(f"❌ خطأ في الإصلاح: {str(e)}")
        return None

if __name__ == "__main__":
    asyncio.run(main())
