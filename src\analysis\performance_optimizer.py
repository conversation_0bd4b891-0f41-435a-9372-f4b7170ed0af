"""
🚀 محسن الأداء للتحليل الفني
===============================

نظام متقدم لتحسين أداء التحليل الفني وتسريع الاستجابة.
يتضمن تخزين مؤقت ذكي، معالجة متوازية، وتحسينات الشبكة.

المميزات:
- تخزين مؤقت متعدد المستويات
- معالجة متوازية للمؤشرات
- ضغط البيانات
- تحسين استدعاءات API
"""

import asyncio
import time
import json
import gzip
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from concurrent.futures import ThreadPoolExecutor
import logging

logger = logging.getLogger(__name__)

class AdvancedAnalysisCache:
    """نظام تخزين مؤقت متقدم للتحليل"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        """
        تهيئة نظام التخزين المؤقت
        
        Args:
            max_size: الحد الأقصى لعدد العناصر المخزنة
            ttl_seconds: مدة صلاحية البيانات بالثواني
        """
        self.cache = {}
        self.timestamps = {}
        self.access_count = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.hit_count = 0
        self.miss_count = 0
        
    def _generate_key(self, symbol: str, timeframe: str = '1h', indicators: List[str] = None) -> str:
        """توليد مفتاح فريد للتخزين المؤقت"""
        key_data = {
            'symbol': symbol.upper(),
            'timeframe': timeframe,
            'indicators': sorted(indicators or [])
        }
        key_string = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _is_valid(self, key: str) -> bool:
        """التحقق من صلاحية البيانات المخزنة"""
        if key not in self.timestamps:
            return False
        
        age = time.time() - self.timestamps[key]
        return age < self.ttl_seconds
    
    def _compress_data(self, data: Any) -> bytes:
        """ضغط البيانات لتوفير الذاكرة"""
        json_data = json.dumps(data, default=str)
        return gzip.compress(json_data.encode())
    
    def _decompress_data(self, compressed_data: bytes) -> Any:
        """إلغاء ضغط البيانات"""
        json_data = gzip.decompress(compressed_data).decode()
        return json.loads(json_data)
    
    def get(self, symbol: str, timeframe: str = '1h', indicators: List[str] = None) -> Optional[Dict[str, Any]]:
        """الحصول على البيانات من التخزين المؤقت"""
        key = self._generate_key(symbol, timeframe, indicators)
        
        if key in self.cache and self._is_valid(key):
            self.hit_count += 1
            self.access_count[key] = self.access_count.get(key, 0) + 1
            
            try:
                return self._decompress_data(self.cache[key])
            except Exception as e:
                logger.warning(f"خطأ في إلغاء ضغط البيانات: {str(e)}")
                del self.cache[key]
                del self.timestamps[key]
                self.access_count.pop(key, None)
        
        self.miss_count += 1
        return None
    
    def set(self, symbol: str, data: Dict[str, Any], timeframe: str = '1h', indicators: List[str] = None):
        """حفظ البيانات في التخزين المؤقت"""
        key = self._generate_key(symbol, timeframe, indicators)
        
        try:
            # ضغط البيانات قبل التخزين
            compressed_data = self._compress_data(data)
            
            # تنظيف التخزين المؤقت إذا امتلأ
            if len(self.cache) >= self.max_size:
                self._cleanup_cache()
            
            self.cache[key] = compressed_data
            self.timestamps[key] = time.time()
            self.access_count[key] = 1
            
        except Exception as e:
            logger.error(f"خطأ في حفظ البيانات في التخزين المؤقت: {str(e)}")
    
    def _cleanup_cache(self):
        """تنظيف التخزين المؤقت بحذف العناصر الأقل استخداماً"""
        # حذف العناصر المنتهية الصلاحية أولاً
        expired_keys = [
            key for key in self.timestamps
            if not self._is_valid(key)
        ]
        
        for key in expired_keys:
            self.cache.pop(key, None)
            self.timestamps.pop(key, None)
            self.access_count.pop(key, None)
        
        # إذا لم يكن هناك مساحة كافية، احذف الأقل استخداماً
        if len(self.cache) >= self.max_size:
            # ترتيب حسب عدد الوصول (الأقل استخداماً أولاً)
            sorted_keys = sorted(
                self.access_count.items(),
                key=lambda x: x[1]
            )
            
            # حذف 20% من العناصر الأقل استخداماً
            keys_to_remove = [key for key, _ in sorted_keys[:self.max_size // 5]]
            
            for key in keys_to_remove:
                self.cache.pop(key, None)
                self.timestamps.pop(key, None)
                self.access_count.pop(key, None)
    
    def get_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين المؤقت"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = (self.hit_count / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': round(hit_rate, 2),
            'cache_size': len(self.cache),
            'max_size': self.max_size
        }

class ParallelAnalysisProcessor:
    """معالج التحليل المتوازي"""
    
    def __init__(self, max_workers: int = 4):
        """
        تهيئة المعالج المتوازي
        
        Args:
            max_workers: عدد العمليات المتوازية القصوى
        """
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.max_workers = max_workers
    
    async def process_indicators_parallel(self, market_data: Dict[str, Any], indicators: List[str]) -> Dict[str, Any]:
        """معالجة المؤشرات بشكل متوازي"""
        try:
            # تقسيم المؤشرات إلى مجموعات للمعالجة المتوازية
            indicator_groups = self._group_indicators(indicators)
            
            # تشغيل المعالجة المتوازية
            tasks = []
            for group in indicator_groups:
                task = asyncio.create_task(
                    self._process_indicator_group(market_data, group)
                )
                tasks.append(task)
            
            # انتظار اكتمال جميع المهام
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # دمج النتائج
            combined_results = {}
            for result in results:
                if isinstance(result, dict):
                    combined_results.update(result)
                elif isinstance(result, Exception):
                    logger.warning(f"خطأ في معالجة مجموعة مؤشرات: {str(result)}")
            
            return combined_results
            
        except Exception as e:
            logger.error(f"خطأ في المعالجة المتوازية للمؤشرات: {str(e)}")
            return {}
    
    def _group_indicators(self, indicators: List[str]) -> List[List[str]]:
        """تقسيم المؤشرات إلى مجموعات للمعالجة المتوازية"""
        group_size = max(1, len(indicators) // self.max_workers)
        groups = []
        
        for i in range(0, len(indicators), group_size):
            group = indicators[i:i + group_size]
            groups.append(group)
        
        return groups
    
    async def _process_indicator_group(self, market_data: Dict[str, Any], indicators: List[str]) -> Dict[str, Any]:
        """معالجة مجموعة من المؤشرات"""
        loop = asyncio.get_event_loop()
        
        def calculate_group():
            results = {}
            for indicator in indicators:
                try:
                    # هنا يتم حساب كل مؤشر
                    result = self._calculate_single_indicator(market_data, indicator)
                    if result:
                        results[indicator] = result
                except Exception as e:
                    logger.warning(f"خطأ في حساب المؤشر {indicator}: {str(e)}")
            return results
        
        return await loop.run_in_executor(self.executor, calculate_group)
    
    def _calculate_single_indicator(self, market_data: Dict[str, Any], indicator: str) -> Optional[Dict[str, Any]]:
        """حساب مؤشر واحد"""
        # هذه دالة مبسطة - يجب استبدالها بالحسابات الفعلية
        try:
            if indicator == 'RSI':
                return {'value': 50.0, 'signal': 'neutral'}
            elif indicator == 'MACD':
                return {'macd': 0.1, 'signal': 0.05, 'histogram': 0.05}
            elif indicator == 'BB':
                return {'upper': 100.0, 'middle': 95.0, 'lower': 90.0}
            # إضافة المزيد من المؤشرات حسب الحاجة
            return None
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشر {indicator}: {str(e)}")
            return None

class PerformanceOptimizer:
    """محسن الأداء الرئيسي"""
    
    def __init__(self):
        """تهيئة محسن الأداء"""
        self.cache = AdvancedAnalysisCache()
        self.processor = ParallelAnalysisProcessor()
        self.performance_stats = {
            'total_analyses': 0,
            'cache_hits': 0,
            'avg_response_time': 0.0,
            'fastest_analysis': float('inf'),
            'slowest_analysis': 0.0
        }
    
    async def optimize_analysis(self, symbol: str, market_data: Dict[str, Any], 
                              indicators: List[str] = None, user_id: str = None) -> Dict[str, Any]:
        """تحسين عملية التحليل"""
        start_time = time.time()
        
        try:
            # محاولة الحصول على البيانات من التخزين المؤقت
            cached_result = self.cache.get(symbol, indicators=indicators)
            if cached_result:
                self.performance_stats['cache_hits'] += 1
                logger.info(f"✅ تم الحصول على تحليل {symbol} من التخزين المؤقت")
                return cached_result
            
            # إذا لم توجد في التخزين المؤقت، قم بالتحليل
            logger.info(f"🔄 بدء تحليل جديد لـ {symbol}")
            
            # معالجة المؤشرات بشكل متوازي
            if indicators:
                indicator_results = await self.processor.process_indicators_parallel(
                    market_data, indicators
                )
            else:
                indicator_results = {}
            
            # إنشاء النتيجة النهائية
            analysis_result = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'market_data': market_data,
                'indicators': indicator_results,
                'performance': {
                    'cache_hit': False,
                    'processing_time': time.time() - start_time
                }
            }
            
            # حفظ في التخزين المؤقت
            self.cache.set(symbol, analysis_result, indicators=indicators)
            
            # تحديث إحصائيات الأداء
            self._update_performance_stats(time.time() - start_time)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"خطأ في تحسين التحليل لـ {symbol}: {str(e)}")
            return {
                'symbol': symbol,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _update_performance_stats(self, response_time: float):
        """تحديث إحصائيات الأداء"""
        self.performance_stats['total_analyses'] += 1
        
        # تحديث متوسط وقت الاستجابة
        total = self.performance_stats['total_analyses']
        current_avg = self.performance_stats['avg_response_time']
        self.performance_stats['avg_response_time'] = (
            (current_avg * (total - 1) + response_time) / total
        )
        
        # تحديث أسرع وأبطأ تحليل
        if response_time < self.performance_stats['fastest_analysis']:
            self.performance_stats['fastest_analysis'] = response_time
        
        if response_time > self.performance_stats['slowest_analysis']:
            self.performance_stats['slowest_analysis'] = response_time
    
    def get_performance_report(self) -> Dict[str, Any]:
        """الحصول على تقرير الأداء"""
        cache_stats = self.cache.get_stats()
        
        return {
            'analysis_stats': self.performance_stats,
            'cache_stats': cache_stats,
            'optimization_level': self._calculate_optimization_level(cache_stats['hit_rate'])
        }
    
    def _calculate_optimization_level(self, hit_rate: float) -> str:
        """حساب مستوى التحسين"""
        if hit_rate >= 80:
            return 'ممتاز'
        elif hit_rate >= 60:
            return 'جيد جداً'
        elif hit_rate >= 40:
            return 'جيد'
        elif hit_rate >= 20:
            return 'متوسط'
        else:
            return 'يحتاج تحسين'

# إنشاء مثيل عام من محسن الأداء
performance_optimizer = PerformanceOptimizer()
