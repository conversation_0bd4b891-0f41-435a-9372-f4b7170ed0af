"""
نظام إدارة مفاتيح API للمستخدمين
يوفر وظائف لتخزين واسترجاع وإدارة مفاتيح API الخاصة بالمستخدمين
"""

import logging
import os
from datetime import datetime
from typing import Optional, Dict, Any, Tuple

from cryptography.fernet import Ferne<PERSON>
from firebase_admin import firestore
from google.cloud.firestore_v1 import FieldFilter

# إعداد السجل
logger = logging.getLogger(__name__)

class APIManager:
    """فئة لإدارة مفاتيح API للمستخدمين"""

    def __init__(self, db: firestore.Client, encryption_key: str = None):
        """
        تهيئة مدير API

        Args:
            db: مثيل قاعدة بيانات Firestore
            encryption_key: مفتاح التشفير (اختياري، سيتم إنشاء مفتاح جديد إذا لم يتم توفيره)
        """
        self.db = db

        # إعداد مفتاح التشفير
        try:
            if encryption_key:
                # التأكد من أن المفتاح بالتنسيق الصحيح
                if isinstance(encryption_key, str):
                    self.encryption_key = encryption_key.encode()
                else:
                    self.encryption_key = encryption_key
            else:
                # استخدام مفتاح من متغيرات البيئة أو إنشاء مفتاح جديد
                env_key = os.environ.get('API_ENCRYPTION_KEY')
                if env_key:
                    self.encryption_key = env_key.encode()
                else:
                    self.encryption_key = Fernet.generate_key()

            # إنشاء مثيل Fernet للتشفير
            self.cipher = Fernet(self.encryption_key)
        except Exception as e:
            logger.error(f"خطأ في إعداد مفتاح التشفير: {str(e)}")
            # إنشاء مفتاح جديد في حالة الخطأ
            self.encryption_key = Fernet.generate_key()
            self.cipher = Fernet(self.encryption_key)
            logger.info("تم إنشاء مفتاح تشفير جديد.")

        # تهيئة مجموعة مفاتيح API
        self._initialize_collection()

    def _initialize_collection(self):
        """تهيئة مجموعة مفاتيح API"""
        # التحقق من وجود المجموعة وإنشائها إذا لم تكن موجودة
        collection_name = 'user_api_keys'

        # إنشاء وثيقة تهيئة للمجموعة إذا لم تكن موجودة
        init_doc = {
            '_metadata': {
                'created_at': datetime.now().isoformat(),
                'collection_type': 'api_keys',
                'description': 'مفاتيح API للمستخدمين'
            }
        }

        # التحقق من وجود المجموعة
        docs = self.db.collection(collection_name).limit(1).get()
        if not list(docs):
            # إنشاء وثيقة تهيئة
            self.db.collection(collection_name).document('_init').set(init_doc)
            logger.info(f"تم إنشاء مجموعة مفاتيح API: {collection_name}")

    def encrypt_data(self, data: str) -> Optional[str]:
        """
        تشفير البيانات

        Args:
            data: البيانات المراد تشفيرها

        Returns:
            البيانات المشفرة أو None إذا كانت البيانات فارغة
        """
        if not data:
            return None

        try:
            return self.cipher.encrypt(data.encode()).decode()
        except Exception as e:
            logger.error(f"خطأ في تشفير البيانات: {str(e)}")
            return None

    def decrypt_data(self, encrypted_data: str) -> Optional[str]:
        """
        فك تشفير البيانات

        Args:
            encrypted_data: البيانات المشفرة

        Returns:
            البيانات الأصلية أو None إذا كانت البيانات فارغة أو حدث خطأ
        """
        if not encrypted_data:
            return None

        try:
            return self.cipher.decrypt(encrypted_data.encode()).decode()
        except Exception as e:
            logger.error(f"خطأ في فك تشفير البيانات: {str(e)}")
            return None

    async def save_api_key(self, user_id: str, api_type: str, api_key: str, api_secret: str = None) -> bool:
        """
        تخزين مفتاح API بشكل آمن

        Args:
            user_id: معرف المستخدم
            api_type: نوع API (binance, gemini, kucoin, coinbase, bybit, okx, kraken)
            api_key: مفتاح API
            api_secret: سر API (اختياري)

        Returns:
            True إذا تم التخزين بنجاح، False خلاف ذلك
        """
        try:
            # تشفير المفاتيح
            encrypted_key = self.encrypt_data(api_key)
            encrypted_secret = self.encrypt_data(api_secret) if api_secret else None

            if not encrypted_key:
                logger.error(f"فشل في تشفير مفتاح API لـ {user_id}")
                return False

            # تخزين في Firestore
            api_ref = self.db.collection('user_api_keys').document(user_id)

            # التحقق من وجود الوثيقة
            doc = api_ref.get()
            if doc.exists:
                # تحديث المفتاح المحدد فقط
                update_data = {
                    f"{api_type}_key": encrypted_key,
                    f"{api_type}_updated_at": datetime.now().isoformat()
                }

                if encrypted_secret:
                    update_data[f"{api_type}_secret"] = encrypted_secret

                api_ref.update(update_data)
            else:
                # إنشاء وثيقة جديدة
                data = {
                    f"{api_type}_key": encrypted_key,
                    f"{api_type}_updated_at": datetime.now().isoformat()
                }

                if encrypted_secret:
                    data[f"{api_type}_secret"] = encrypted_secret

                api_ref.set(data)

            logger.info(f"تم حفظ مفتاح {api_type} API للمستخدم {user_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حفظ مفتاح API: {str(e)}")
            return False

    async def get_api_keys(self, user_id: str, api_type: str) -> Tuple[Optional[str], Optional[str]]:
        """
        الحصول على مفاتيح API للمستخدم

        Args:
            user_id: معرف المستخدم
            api_type: نوع API (binance, gemini, kucoin, coinbase, bybit, okx, kraken)

        Returns:
            زوج من (api_key, api_secret) أو (None, None) إذا لم يتم العثور على المفاتيح
        """
        try:
            api_ref = self.db.collection('user_api_keys').document(user_id)
            doc = api_ref.get()

            if not doc.exists:
                return None, None

            api_data = doc.to_dict()

            # الحصول على المفاتيح المشفرة
            encrypted_key = api_data.get(f"{api_type}_key")
            encrypted_secret = api_data.get(f"{api_type}_secret")

            # فك تشفير المفاتيح
            api_key = self.decrypt_data(encrypted_key) if encrypted_key else None
            api_secret = self.decrypt_data(encrypted_secret) if encrypted_secret else None

            return api_key, api_secret

        except Exception as e:
            logger.error(f"خطأ في الحصول على مفاتيح API: {str(e)}")
            return None, None

    async def delete_api_keys(self, user_id: str, api_type: str) -> bool:
        """
        حذف مفاتيح API للمستخدم

        Args:
            user_id: معرف المستخدم
            api_type: نوع API (binance, gemini, kucoin, coinbase, bybit, okx, kraken)

        Returns:
            True إذا تم الحذف بنجاح، False خلاف ذلك
        """
        try:
            api_ref = self.db.collection('user_api_keys').document(user_id)

            # التحقق من وجود الوثيقة
            doc = api_ref.get()
            if not doc.exists:
                return False

            # تحديث الوثيقة لحذف المفاتيح
            update_data = {}

            # حذف المفاتيح حسب نوع المنصة
            if api_type in ['binance', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken']:
                update_data = {
                    f'{api_type}_key': firestore.DELETE_FIELD,
                    f'{api_type}_secret': firestore.DELETE_FIELD,
                    f'{api_type}_updated_at': firestore.DELETE_FIELD
                }
            elif api_type == 'gemini':
                update_data = {
                    'gemini_key': firestore.DELETE_FIELD,
                    'gemini_updated_at': firestore.DELETE_FIELD
                }

            api_ref.update(update_data)
            logger.info(f"تم حذف مفاتيح {api_type} API للمستخدم {user_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حذف مفاتيح API: {str(e)}")
            return False

    async def has_api_keys(self, user_id: str, api_type: str) -> bool:
        """
        التحقق من وجود مفاتيح API للمستخدم

        Args:
            user_id: معرف المستخدم
            api_type: نوع API (binance, gemini, kucoin, coinbase, bybit, okx, kraken)

        Returns:
            True إذا كان المستخدم لديه مفاتيح API، False خلاف ذلك
        """
        try:
            api_ref = self.db.collection('user_api_keys').document(user_id)
            doc = api_ref.get()

            if not doc.exists:
                return False

            api_data = doc.to_dict()

            # المنصات التي تتطلب مفتاح وسر
            if api_type in ['binance', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken']:
                return f'{api_type}_key' in api_data and f'{api_type}_secret' in api_data
            # المنصات التي تتطلب مفتاح فقط
            elif api_type == 'gemini':
                return f'{api_type}_key' in api_data

            return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من وجود مفاتيح API: {str(e)}")
            return False

    def get_api_keys_sync(self, user_id: str, api_type: str) -> Tuple[Optional[str], Optional[str]]:
        """
        الحصول على مفاتيح API للمستخدم بطريقة متزامنة (غير متزامنة)

        Args:
            user_id: معرف المستخدم
            api_type: نوع API (binance, gemini, kucoin, coinbase, bybit, okx, kraken)

        Returns:
            زوج من (api_key, api_secret) أو (None, None) إذا لم يتم العثور على المفاتيح
        """
        try:
            api_ref = self.db.collection('user_api_keys').document(user_id)
            doc = api_ref.get()

            if not doc.exists:
                return None, None

            api_data = doc.to_dict()

            # الحصول على المفاتيح المشفرة
            encrypted_key = api_data.get(f"{api_type}_key")
            encrypted_secret = api_data.get(f"{api_type}_secret")

            # فك تشفير المفاتيح
            api_key = self.decrypt_data(encrypted_key) if encrypted_key else None
            api_secret = self.decrypt_data(encrypted_secret) if encrypted_secret else None

            return api_key, api_secret

        except Exception as e:
            logger.error(f"خطأ في الحصول على مفاتيح API بطريقة متزامنة: {str(e)}")
            return None, None

    async def get_api_key(self, user_id: str, api_type: str) -> Optional[str]:
        """
        الحصول على مفتاح API للمستخدم (للتوافق مع الكود القديم)

        Args:
            user_id: معرف المستخدم
            api_type: نوع API (binance, gemini, kucoin, coinbase, bybit, okx, kraken)

        Returns:
            مفتاح API أو None إذا لم يتم العثور عليه
        """
        try:
            api_key, _ = await self.get_api_keys(user_id, api_type)
            return api_key
        except Exception as e:
            logger.error(f"خطأ في الحصول على مفتاح API: {str(e)}")
            return None

    def get_api_key_sync(self, user_id: str, api_type: str) -> Optional[str]:
        """
        الحصول على مفتاح API للمستخدم بطريقة متزامنة (للتوافق مع الكود القديم)

        Args:
            user_id: معرف المستخدم
            api_type: نوع API (binance, gemini, kucoin, coinbase, bybit, okx, kraken)

        Returns:
            مفتاح API أو None إذا لم يتم العثور عليه
        """
        try:
            api_key, _ = self.get_api_keys_sync(user_id, api_type)
            return api_key
        except Exception as e:
            logger.error(f"خطأ في الحصول على مفتاح API بطريقة متزامنة: {str(e)}")
            return None

    async def get_api_info(self, user_id: str) -> Dict[str, Any]:
        """
        الحصول على معلومات API للمستخدم

        Args:
            user_id: معرف المستخدم

        Returns:
            قاموس يحتوي على معلومات API
        """
        try:
            logger.info(f"جاري الحصول على معلومات API للمستخدم {user_id}")
            api_ref = self.db.collection('user_api_keys').document(user_id)
            doc = api_ref.get()

            if not doc.exists:
                logger.info(f"لا توجد وثيقة API للمستخدم {user_id}")
                return {}

            api_data = doc.to_dict()
            logger.debug(f"بيانات API للمستخدم {user_id}: {api_data.keys()}")

            # إنشاء قاموس المعلومات
            info = {}

            # قائمة المنصات المدعومة
            platforms = [
                'binance', 'gemini', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken'
            ]

            # إضافة معلومات لكل منصة
            for platform in platforms:
                # المنصات التي تتطلب مفتاح وسر
                if platform in ['binance', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken']:
                    has_key = f'{platform}_key' in api_data
                    has_secret = f'{platform}_secret' in api_data

                    # تسجيل حالة المفاتيح للتصحيح
                    logger.debug(f"المستخدم {user_id} - {platform}: has_key={has_key}, has_secret={has_secret}")

                    if has_key and has_secret:
                        info[f'has_{platform}'] = True
                    else:
                        info[f'has_{platform}'] = False
                # المنصات التي تتطلب مفتاح فقط
                elif platform == 'gemini':
                    has_key = f'{platform}_key' in api_data

                    # تسجيل حالة المفتاح للتصحيح
                    logger.debug(f"المستخدم {user_id} - {platform}: has_key={has_key}")

                    if has_key:
                        info[f'has_{platform}'] = True
                    else:
                        info[f'has_{platform}'] = False

                # إضافة معلومات التحديث
                if f'{platform}_updated_at' in api_data and info.get(f'has_{platform}', False):
                    try:
                        updated_at = datetime.fromisoformat(api_data[f'{platform}_updated_at'])
                        info[f'{platform}_updated_at'] = updated_at.strftime('%Y-%m-%d %H:%M:%S')
                    except Exception as date_error:
                        logger.error(f"خطأ في تحويل تاريخ التحديث لـ {platform}: {str(date_error)}")
                        info[f'{platform}_updated_at'] = api_data[f'{platform}_updated_at']

            logger.info(f"تم الحصول على معلومات API للمستخدم {user_id}: {info}")
            return info

        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات API للمستخدم {user_id}: {str(e)}")
            return {}

# إنشاء مثيل مدير API
# سيتم استدعاؤه من bot.py
# api_manager = APIManager(db)
