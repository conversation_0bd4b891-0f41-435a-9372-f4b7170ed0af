"""
خدمات إدارة البيانات - Data Manager Services

هذا الملف يحتوي على جميع الدوال المتعلقة بإدارة البيانات:
- تحميل وحفظ التنبيهات
- تحميل وحفظ إعدادات المستخدمين
- تحميل وحفظ الإحصائيات السابقة

تم نقل هذه الدوال من main.py كجزء من خطة إعادة الهيكلة - المرحلة الثانية
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from firebase_admin import firestore

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

# متغيرات عامة للبيانات
price_alerts = {}
previous_stats = {
    'total_users': 0,
    'subscribed_users': 0
}

class DataManager:
    """فئة إدارة البيانات الرئيسية"""
    
    def __init__(self, db: firestore.Client):
        """
        تهيئة مدير البيانات
        
        Args:
            db: كائن قاعدة بيانات Firestore
        """
        self.db = db
        self.price_alerts = {}
        self.previous_stats = {
            'total_users': 0,
            'subscribed_users': 0
        }
    
    def load_alerts(self) -> Dict[str, Any]:
        """
        تحميل التنبيهات من Firestore
        
        Returns:
            قاموس يحتوي على جميع التنبيهات
        """
        try:
            alerts_ref = self.db.collection('alerts')
            alerts_docs = alerts_ref.get()
            self.price_alerts = {}
            for doc in alerts_docs:
                self.price_alerts[doc.id] = doc.to_dict()
            logger.info(f"تم تحميل {len(self.price_alerts)} تنبيه من قاعدة البيانات")
        except Exception as e:
            logger.error(f"Error loading alerts: {str(e)}")
            self.price_alerts = {}
        return self.price_alerts
    
    def save_alerts(self) -> bool:
        """
        حفظ التنبيهات في Firestore
        
        Returns:
            True إذا تم الحفظ بنجاح، False خلاف ذلك
        """
        try:
            batch = self.db.batch()
            for user_id, alerts in self.price_alerts.items():
                alert_ref = self.db.collection('alerts').document(user_id)
                batch.set(alert_ref, alerts)
            batch.commit()
            logger.info(f"تم حفظ {len(self.price_alerts)} تنبيه في قاعدة البيانات")
            return True
        except Exception as e:
            logger.error(f"Error saving alerts: {str(e)}")
            return False
    
    def save_user_settings(self, user_id: str, **settings) -> bool:
        """
        حفظ إعدادات المستخدم في Firestore
        
        Args:
            user_id: معرف المستخدم
            **settings: الإعدادات المراد حفظها
            
        Returns:
            True إذا تم الحفظ بنجاح، False خلاف ذلك
        """
        try:
            settings_ref = self.db.collection('user_settings').document(user_id)
            settings_ref.set(settings, merge=True)
            logger.info(f"تم حفظ إعدادات المستخدم {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error saving user settings for {user_id}: {str(e)}")
            return False
    
    def load_user_settings(self, user_id: str) -> Dict[str, Any]:
        """
        تحميل إعدادات المستخدم من Firestore
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            قاموس يحتوي على إعدادات المستخدم
        """
        try:
            settings_ref = self.db.collection('user_settings').document(user_id)
            settings_doc = settings_ref.get()

            if settings_doc.exists:
                settings = settings_doc.to_dict()
            else:
                settings = {
                    'indicators': [],
                    'currencies': [],
                    'lang': 'ar'  # اللغة الافتراضية هي العربية
                }
                settings_ref.set(settings)

            # التأكد من وجود جميع المفاتيح الضرورية
            if 'lang' not in settings:
                settings['lang'] = 'ar'
                settings_ref.update({'lang': 'ar'})

            logger.info(f"تم تحميل إعدادات المستخدم {user_id}")
            return settings
        except Exception as e:
            logger.error(f"Error loading user settings for {user_id}: {str(e)}")
            return {'indicators': [], 'currencies': [], 'lang': 'ar'}
    
    def load_previous_stats(self) -> Dict[str, Any]:
        """
        تحميل الإحصائيات السابقة من ملف subscription_data.json
        
        Returns:
            قاموس يحتوي على الإحصائيات السابقة
        """
        try:
            if os.path.exists('subscription_data.json'):
                with open('subscription_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if 'stats' in data:
                        self.previous_stats.update(data['stats'])
                        logger.info("تم تحميل الإحصائيات السابقة من الملف")
        except Exception as e:
            logger.error(f"خطأ في تحميل الإحصائيات السابقة: {str(e)}")
        return self.previous_stats
    
    def save_previous_stats(self) -> bool:
        """
        حفظ الإحصائيات السابقة في ملف subscription_data.json
        
        Returns:
            True إذا تم الحفظ بنجاح، False خلاف ذلك
        """
        try:
            with open('subscription_data.json', 'r+', encoding='utf-8') as f:
                data = json.load(f)
                data['stats'] = self.previous_stats
                f.seek(0)
                json.dump(data, f, ensure_ascii=False, indent=4)
                f.truncate()
            logger.info("تم حفظ الإحصائيات السابقة في الملف")
            return True
        except Exception as e:
            logger.error(f"خطأ في حفظ الإحصائيات السابقة: {str(e)}")
            return False


# مثيل مدير البيانات العام - سيتم تهيئته من main.py
_data_manager_instance = None

def set_data_manager(db: firestore.Client):
    """
    تعيين مثيل مدير البيانات العام

    Args:
        db: كائن قاعدة بيانات Firestore
    """
    global _data_manager_instance
    _data_manager_instance = DataManager(db)
    logger.info("تم تهيئة مدير البيانات بنجاح")

def get_data_manager() -> Optional[DataManager]:
    """
    الحصول على مثيل مدير البيانات العام

    Returns:
        مثيل DataManager أو None إذا لم يتم تهيئته
    """
    return _data_manager_instance

# دوال مساعدة للتوافق مع الكود الحالي
def load_alerts():
    """تحميل التنبيهات من Firestore - دالة للتوافق مع الكود الحالي"""
    global price_alerts
    if _data_manager_instance:
        price_alerts = _data_manager_instance.load_alerts()
        return price_alerts
    else:
        logger.warning("مدير البيانات غير مهيأ - استخدام البيانات المحلية")
        return price_alerts

def save_alerts():
    """حفظ التنبيهات في Firestore - دالة للتوافق مع الكود الحالي"""
    global price_alerts
    if _data_manager_instance:
        _data_manager_instance.price_alerts = price_alerts
        return _data_manager_instance.save_alerts()
    else:
        logger.warning("مدير البيانات غير مهيأ - لا يمكن حفظ التنبيهات")
        return False

def save_user_settings(user_id: str, **settings):
    """حفظ إعدادات المستخدم في Firestore - دالة للتوافق مع الكود الحالي"""
    if _data_manager_instance:
        return _data_manager_instance.save_user_settings(user_id, **settings)
    else:
        logger.warning("مدير البيانات غير مهيأ - لا يمكن حفظ إعدادات المستخدم")
        return False

def load_user_settings(user_id: str):
    """تحميل إعدادات المستخدم من Firestore - دالة للتوافق مع الكود الحالي"""
    if _data_manager_instance:
        return _data_manager_instance.load_user_settings(user_id)
    else:
        logger.warning("مدير البيانات غير مهيأ - استخدام الإعدادات الافتراضية")
        return {'indicators': [], 'currencies': [], 'lang': 'ar'}

def load_previous_stats():
    """تحميل الإحصائيات السابقة من ملف subscription_data.json - دالة للتوافق مع الكود الحالي"""
    global previous_stats
    if _data_manager_instance:
        previous_stats = _data_manager_instance.load_previous_stats()
        return previous_stats
    else:
        logger.warning("مدير البيانات غير مهيأ - استخدام الإحصائيات المحلية")
        return previous_stats

def save_previous_stats():
    """حفظ الإحصائيات السابقة في ملف subscription_data.json - دالة للتوافق مع الكود الحالي"""
    global previous_stats
    if _data_manager_instance:
        _data_manager_instance.previous_stats = previous_stats
        return _data_manager_instance.save_previous_stats()
    else:
        logger.warning("مدير البيانات غير مهيأ - لا يمكن حفظ الإحصائيات")
        return False
