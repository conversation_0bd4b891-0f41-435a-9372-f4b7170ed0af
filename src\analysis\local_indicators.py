#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المؤشرات الفنية المحلي المحسن
بديل محسن لـ TA-Lib مع أداء عالي ودقة ممتازة
"""

import numpy as np
import pandas as pd
from numba import jit, njit
from typing import List, Dict, Tuple, Optional, Union
import logging
from functools import lru_cache

logger = logging.getLogger(__name__)

class LocalTechnicalIndicators:
    """مؤشرات فنية محلية محسنة - بديل TA-Lib"""
    
    @staticmethod
    @njit(cache=True)
    def sma(data: np.ndarray, period: int) -> np.ndarray:
        """متوسط متحرك بسيط محسن"""
        if len(data) < period:
            return np.full(len(data), data[-1] if len(data) > 0 else 0.0)
        
        result = np.zeros(len(data))
        result[:period-1] = data[:period-1]  # القيم الأولى
        
        # حساب المتوسط المتحرك
        for i in range(period-1, len(data)):
            result[i] = np.mean(data[i-period+1:i+1])
        
        return result
    
    @staticmethod
    @njit(cache=True)
    def ema(data: np.ndarray, period: int) -> np.ndarray:
        """متوسط متحرك أسي محسن"""
        if len(data) == 0:
            return np.zeros(0, dtype=np.float64)

        alpha = 2.0 / (period + 1.0)
        result = np.zeros(len(data))
        result[0] = data[0]

        for i in range(1, len(data)):
            result[i] = alpha * data[i] + (1 - alpha) * result[i-1]

        return result
    
    @staticmethod
    @njit(cache=True)
    def rsi(data: np.ndarray, period: int = 14) -> np.ndarray:
        """مؤشر القوة النسبية محسن"""
        if len(data) < period + 1:
            return np.full(len(data), 50.0)
        
        delta = np.diff(data)
        gain = np.where(delta > 0, delta, 0.0)
        loss = np.where(delta < 0, -delta, 0.0)
        
        # حساب المتوسطات
        avg_gain = np.zeros(len(gain))
        avg_loss = np.zeros(len(loss))
        
        # المتوسط الأولي
        avg_gain[period-1] = np.mean(gain[:period])
        avg_loss[period-1] = np.mean(loss[:period])
        
        # المتوسطات المتحركة الأسية
        for i in range(period, len(gain)):
            avg_gain[i] = (avg_gain[i-1] * (period-1) + gain[i]) / period
            avg_loss[i] = (avg_loss[i-1] * (period-1) + loss[i]) / period
        
        # حساب RSI
        rsi_values = np.zeros(len(data))
        rsi_values[:period] = 50.0  # قيم افتراضية
        
        for i in range(period, len(data)):
            if avg_loss[i-1] == 0:
                rsi_values[i] = 100.0
            else:
                rs = avg_gain[i-1] / avg_loss[i-1]
                rsi_values[i] = 100.0 - (100.0 / (1.0 + rs))
        
        return rsi_values
    
    @staticmethod
    def macd(data: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """مؤشر MACD محسن"""
        if len(data) < slow:
            zeros = np.zeros(len(data))
            return zeros, zeros, zeros

        # حساب EMAs باستخدام الدالة المحسنة
        ema_fast = _ema_numba(data, fast)
        ema_slow = _ema_numba(data, slow)

        # خط MACD
        macd_line = ema_fast - ema_slow

        # خط الإشارة
        signal_line = _ema_numba(macd_line, signal)

        # الهيستوجرام
        histogram = macd_line - signal_line

        return macd_line, signal_line, histogram
    
    @staticmethod
    @njit(cache=True)
    def bollinger_bands(data: np.ndarray, period: int = 20, std_dev: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """نطاقات بولينجر محسنة"""
        if len(data) < period:
            middle = np.full(len(data), np.mean(data) if len(data) > 0 else 0.0)
            return middle, middle, middle
        
        # المتوسط المتحرك (الخط الأوسط)
        middle = _sma_numba(data, period)
        
        # حساب الانحراف المعياري
        upper = np.zeros(len(data))
        lower = np.zeros(len(data))
        
        for i in range(period-1, len(data)):
            window = data[i-period+1:i+1]
            std = np.std(window)
            upper[i] = middle[i] + (std_dev * std)
            lower[i] = middle[i] - (std_dev * std)
        
        # ملء القيم الأولى
        for i in range(period-1):
            upper[i] = middle[i]
            lower[i] = middle[i]
        
        return upper, middle, lower
    
    @staticmethod
    @njit(cache=True)
    def stochastic(high: np.ndarray, low: np.ndarray, close: np.ndarray, 
                   k_period: int = 14, d_period: int = 3) -> Tuple[np.ndarray, np.ndarray]:
        """مؤشر ستوكاستيك محسن"""
        if len(close) < k_period:
            k_values = np.full(len(close), 50.0)
            d_values = np.full(len(close), 50.0)
            return k_values, d_values
        
        k_values = np.zeros(len(close))
        
        for i in range(k_period-1, len(close)):
            window_high = np.max(high[i-k_period+1:i+1])
            window_low = np.min(low[i-k_period+1:i+1])
            
            if window_high - window_low == 0:
                k_values[i] = 50.0
            else:
                k_values[i] = ((close[i] - window_low) / (window_high - window_low)) * 100.0
        
        # ملء القيم الأولى
        for i in range(k_period-1):
            k_values[i] = 50.0
        
        # حساب %D (متوسط متحرك لـ %K)
        d_values = _sma_numba(k_values, d_period)
        
        return k_values, d_values
    
    @staticmethod
    @njit(cache=True)
    def williams_r(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """مؤشر ويليامز %R محسن"""
        if len(close) < period:
            return np.full(len(close), -50.0)
        
        wr_values = np.zeros(len(close))
        
        for i in range(period-1, len(close)):
            window_high = np.max(high[i-period+1:i+1])
            window_low = np.min(low[i-period+1:i+1])
            
            if window_high - window_low == 0:
                wr_values[i] = -50.0
            else:
                wr_values[i] = ((window_high - close[i]) / (window_high - window_low)) * -100.0
        
        # ملء القيم الأولى
        for i in range(period-1):
            wr_values[i] = -50.0
        
        return wr_values
    
    @staticmethod
    @njit(cache=True)
    def atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """متوسط المدى الحقيقي محسن"""
        if len(close) < 2:
            return np.full(len(close), 0.0)
        
        # حساب True Range
        tr = np.zeros(len(close))
        tr[0] = high[0] - low[0]  # القيمة الأولى
        
        for i in range(1, len(close)):
            tr[i] = max(
                high[i] - low[i],
                abs(high[i] - close[i-1]),
                abs(low[i] - close[i-1])
            )
        
        # حساب ATR كمتوسط متحرك لـ TR
        return _sma_numba(tr, period)
    
    @staticmethod
    @njit(cache=True)
    def adx(high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """مؤشر ADX محسن"""
        if len(close) < period + 1:
            default_val = np.full(len(close), 25.0)
            return default_val, default_val, default_val
        
        # حساب True Range
        tr = np.zeros(len(close))
        plus_dm = np.zeros(len(close))
        minus_dm = np.zeros(len(close))
        
        tr[0] = high[0] - low[0]
        
        for i in range(1, len(close)):
            # True Range
            tr[i] = max(
                high[i] - low[i],
                abs(high[i] - close[i-1]),
                abs(low[i] - close[i-1])
            )
            
            # Directional Movement
            up_move = high[i] - high[i-1]
            down_move = low[i-1] - low[i]
            
            if up_move > down_move and up_move > 0:
                plus_dm[i] = up_move
            if down_move > up_move and down_move > 0:
                minus_dm[i] = down_move
        
        # حساب المتوسطات المتحركة
        atr_values = _sma_numba(tr, period)
        plus_dm_sma = _sma_numba(plus_dm, period)
        minus_dm_sma = _sma_numba(minus_dm, period)
        plus_di = np.zeros(len(close))
        minus_di = np.zeros(len(close))
        adx_values = np.zeros(len(close))

        # حساب DI
        for i in range(period, len(close)):
            if atr_values[i] > 0:
                plus_di[i] = (plus_dm_sma[i] / atr_values[i]) * 100
                minus_di[i] = (minus_dm_sma[i] / atr_values[i]) * 100
        
        # حساب ADX
        for i in range(period * 2, len(close)):
            dx_sum = 0.0
            count = 0
            
            for j in range(i-period+1, i+1):
                if plus_di[j] + minus_di[j] > 0:
                    dx = abs(plus_di[j] - minus_di[j]) / (plus_di[j] + minus_di[j]) * 100
                    dx_sum += dx
                    count += 1
            
            if count > 0:
                adx_values[i] = dx_sum / count
            else:
                adx_values[i] = 25.0
        
        # ملء القيم الافتراضية
        for i in range(period * 2):
            adx_values[i] = 25.0
            if i < period:
                plus_di[i] = 25.0
                minus_di[i] = 25.0
        
        return adx_values, plus_di, minus_di
    
    @classmethod
    def calculate_all_indicators(cls, high: np.ndarray, low: np.ndarray, close: np.ndarray, 
                               volume: Optional[np.ndarray] = None) -> Dict[str, Union[float, np.ndarray, Dict]]:
        """حساب جميع المؤشرات الفنية دفعة واحدة"""
        try:
            indicators = {}
            
            # المتوسطات المتحركة
            indicators['sma_20'] = cls.sma(close, 20)
            indicators['sma_50'] = cls.sma(close, 50)
            indicators['ema_12'] = cls.ema(close, 12)
            indicators['ema_26'] = cls.ema(close, 26)
            
            # مؤشرات الزخم
            indicators['rsi'] = cls.rsi(close, 14)
            macd_line, signal_line, histogram = cls.macd(close)
            indicators['macd'] = {
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }
            
            # نطاقات بولينجر
            bb_upper, bb_middle, bb_lower = cls.bollinger_bands(close)
            indicators['bollinger'] = {
                'upper': bb_upper,
                'middle': bb_middle,
                'lower': bb_lower
            }
            
            # مؤشرات التذبذب
            stoch_k, stoch_d = cls.stochastic(high, low, close)
            indicators['stochastic'] = {
                'k': stoch_k,
                'd': stoch_d
            }
            
            indicators['williams_r'] = cls.williams_r(high, low, close)
            
            # مؤشرات التقلب والاتجاه
            indicators['atr'] = cls.atr(high, low, close)
            adx_vals, plus_di, minus_di = cls.adx(high, low, close)
            indicators['adx'] = {
                'adx': adx_vals,
                'plus_di': plus_di,
                'minus_di': minus_di
            }
            
            # القيم الحالية (آخر قيمة)
            current_values = {}
            for key, value in indicators.items():
                if isinstance(value, dict):
                    current_values[key] = {k: float(v[-1]) if len(v) > 0 else 0.0 for k, v in value.items()}
                else:
                    current_values[key] = float(value[-1]) if len(value) > 0 else 0.0
            
            indicators['current'] = current_values
            
            return indicators
            
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات الفنية: {str(e)}")
            return {}

# مثيل عام للاستخدام
local_indicators = LocalTechnicalIndicators()

# دوال مساعدة للتوافق مع الكود الحالي
def SMA(data, period):
    """دالة توافق مع TA-Lib"""
    return LocalTechnicalIndicators.sma(np.array(data), period)

def EMA(data, period):
    """دالة توافق مع TA-Lib"""
    return LocalTechnicalIndicators.ema(np.array(data), period)

def RSI(data, period=14):
    """دالة توافق مع TA-Lib"""
    return LocalTechnicalIndicators.rsi(np.array(data), period)

def MACD(data, fast=12, slow=26, signal=9):
    """دالة توافق مع TA-Lib"""
    return LocalTechnicalIndicators.macd(np.array(data), fast, slow, signal)

def BBANDS(data, period=20, std_dev=2.0):
    """دالة توافق مع TA-Lib"""
    return LocalTechnicalIndicators.bollinger_bands(np.array(data), period, std_dev)

def STOCH(high, low, close, k_period=14, d_period=3):
    """دالة توافق مع TA-Lib"""
    return LocalTechnicalIndicators.stochastic(np.array(high), np.array(low), np.array(close), k_period, d_period)

def WILLR(high, low, close, period=14):
    """دالة توافق مع TA-Lib"""
    return LocalTechnicalIndicators.williams_r(np.array(high), np.array(low), np.array(close), period)

def ATR(high, low, close, period=14):
    """دالة توافق مع TA-Lib"""
    return LocalTechnicalIndicators.atr(np.array(high), np.array(low), np.array(close), period)

def ADX(high, low, close, period=14):
    """دالة توافق مع TA-Lib"""
    adx_vals, _, _ = LocalTechnicalIndicators.adx(np.array(high), np.array(low), np.array(close), period)
    return adx_vals

def PLUS_DI(high, low, close, period=14):
    """دالة توافق مع TA-Lib"""
    _, plus_di, _ = LocalTechnicalIndicators.adx(np.array(high), np.array(low), np.array(close), period)
    return plus_di

def MINUS_DI(high, low, close, period=14):
    """دالة توافق مع TA-Lib"""
    _, _, minus_di = LocalTechnicalIndicators.adx(np.array(high), np.array(low), np.array(close), period)
    return minus_di

# ===== دوال مساعدة محسنة لـ Numba =====

@njit(cache=True)
def _ema_numba(data: np.ndarray, period: int) -> np.ndarray:
    """متوسط متحرك أسي محسن لـ Numba"""
    if len(data) == 0:
        return np.zeros(0, dtype=np.float64)

    alpha = 2.0 / (period + 1.0)
    result = np.zeros(len(data))
    result[0] = data[0]

    for i in range(1, len(data)):
        result[i] = alpha * data[i] + (1 - alpha) * result[i-1]

    return result

@njit(cache=True)
def _sma_numba(data: np.ndarray, period: int) -> np.ndarray:
    """متوسط متحرك بسيط محسن لـ Numba"""
    if len(data) < period:
        return np.full(len(data), data[-1] if len(data) > 0 else 0.0)

    result = np.zeros(len(data))
    result[:period-1] = data[:period-1]  # القيم الأولى

    # حساب المتوسط المتحرك
    for i in range(period-1, len(data)):
        result[i] = np.mean(data[i-period+1:i+1])

    return result
