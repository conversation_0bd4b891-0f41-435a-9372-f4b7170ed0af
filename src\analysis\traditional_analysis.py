"""
وحدة التحليل الفني التقليدي بدون استخدام الذكاء الاصطناعي
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional

# إعداد التسجيل
logger = logging.getLogger(__name__)

def generate_direct_links(symbol: str) -> Dict[str, str]:
    """
    إنشاء الروابط المباشرة لـ TradingView و Binance

    Args:
        symbol: رمز العملة

    Returns:
        قاموس يحتوي على الروابط المباشرة
    """
    try:
        # Prepare symbols for URLs
        s_upper = symbol.upper()

        # تحديد رمز TradingView الصحيح
        # التحقق من أن الرمز هو زوج عملة أم عملة مفردة
        known_quote_assets = ["USDT", "BUSD", "TUSD", "USDC", "BTC", "ETH", "BNB", "XRP", "TRX", "DOT", "LINK", "LTC", "BCH", "ADA", "SOL"]

        # فحص ما إذا كان الرمز ينتهي بأحد أصول التسعير المعروفة
        is_pair = False
        for quote in known_quote_assets:
            if s_upper.endswith(quote) and len(s_upper) > len(quote):
                # التأكد من أن ما قبل أصل التسعير ليس فارغاً
                base_part = s_upper[:-len(quote)]
                if base_part and base_part != quote:  # تجنب حالات مثل BTCBTC
                    is_pair = True
                    break

        if is_pair:
            # رمز زوج مثل BNBUSDT -> BINANCE:BNBUSDT
            tradingview_symbol_url_part = f"BINANCE:{s_upper}"
        else:
            # رمز مفرد مثل BNB -> BNBUSDT
            tradingview_symbol_url_part = f"BINANCE:{s_upper}USDT"

        # Binance: uses format like BTC_USDT, ETH_BTC
        binance_symbol_url_part = s_upper # Default
        known_quotes_binance = [
            "USDT", "BUSD", "TUSD", "USDC", "PAX", "DAI", # Common stablecoins
            "TRY", "EUR", "GBP", "AUD", "BRL", "RUB", "IDRT", "UAH", "NGN", "VND", "BIDR", # Common fiat
            "BTC", "ETH", "BNB", "XRP", "TRX", "DOT", "LINK", "LTC", "BCH", "ADA", "SOL" # Common crypto quote assets
        ]
        # Sort by length descending to match longer quotes first (e.g., "USDT" before any hypothetical "USD")
        known_quotes_binance.sort(key=len, reverse=True)

        pair_found_for_binance = False
        for q_asset in known_quotes_binance:
            if s_upper.endswith(q_asset) and len(s_upper) > len(q_asset):
                base_asset = s_upper[:-len(q_asset)]
                # Ensure base_asset is not empty (e.g. if symbol itself was 'USDT')
                if base_asset:
                    binance_symbol_url_part = f"{base_asset}_{q_asset}"
                    pair_found_for_binance = True
                    break

        if not pair_found_for_binance:
            # If symbol is short (e.g. "BTC") and not identified as a pair, assume user wants its USDT market
            # This is a common default on Binance if you search for a ticker.
            if '_' not in binance_symbol_url_part and len(binance_symbol_url_part) <= 4: # e.g. BTC, ETH, ADA
                binance_symbol_url_part = f"{binance_symbol_url_part}_USDT"
            # If it's longer and no quote matched (e.g. "SOMEARBITRARYTOKEN"),
            # it might be unlisted or require different handling. We pass it as is for now.

        return {
            'tradingview': f"https://www.tradingview.com/chart/?symbol={tradingview_symbol_url_part}",
            'binance': f"https://www.binance.com/en/trade/{binance_symbol_url_part}?type=spot",
            'tradingview_symbol': tradingview_symbol_url_part,
            'binance_symbol': binance_symbol_url_part
        }

    except Exception as e:
        logger.error(f"خطأ في إنشاء الروابط المباشرة: {str(e)}")
        return {
            'tradingview': f"https://www.tradingview.com/chart/?symbol=BINANCE:{symbol.upper()}",
            'binance': f"https://www.binance.com/en/trade/{symbol.upper()}_USDT?type=spot",
            'tradingview_symbol': f"BINANCE:{symbol.upper()}",
            'binance_symbol': f"{symbol.upper()}_USDT"
        }


def create_traditional_analysis(symbol: str, market_data: Dict[str, Any], lang: str = 'ar') -> str:
    """
    إنشاء تحليل فني تقليدي بدون استخدام الذكاء الاصطناعي

    Args:
        symbol: رمز العملة
        market_data: بيانات السوق والمؤشرات الفنية
        lang: لغة التحليل (ar أو en)

    Returns:
        نص التحليل الفني
    """
    try:
        # استخراج البيانات
        price = market_data.get('price', 0)
        if isinstance(price, str):
            try:
                price = float(price)
                logger.info(f"تم تحويل السعر من نص إلى رقم: {price}")
            except ValueError:
                logger.warning(f"لا يمكن تحويل السعر إلى رقم: {price}")
                price = 0

        price_change = market_data.get('price_change', 0)
        if isinstance(price_change, str):
            try:
                price_change = float(price_change)
                logger.info(f"تم تحويل نسبة التغيير من نص إلى رقم: {price_change}")
            except ValueError:
                logger.warning(f"لا يمكن تحويل نسبة التغيير إلى رقم: {price_change}")
                price_change = 0

        # استخراج المؤشرات الفنية
        # استخدام القيم النصية للعرض إذا كانت متاحة
        rsi = market_data.get('rsi', 'N/A')
        ema20 = market_data.get('ema20', 'N/A')
        ema50 = market_data.get('ema50', 'N/A')
        macd = market_data.get('macd', 'N/A')
        macd_signal = market_data.get('macd_signal', 'N/A')
        bb_upper = market_data.get('bb_upper', 'N/A')
        bb_middle = market_data.get('bb_middle', 'N/A')
        bb_lower = market_data.get('bb_lower', 'N/A')

        # استخراج مؤشر Ichimoku Cloud (للمستخدمين المشتركين فقط)
        ichimoku_tenkan = market_data.get('ichimoku_tenkan', 'N/A')
        ichimoku_kijun = market_data.get('ichimoku_kijun', 'N/A')
        ichimoku_senkou_a = market_data.get('ichimoku_senkou_a', 'N/A')
        ichimoku_senkou_b = market_data.get('ichimoku_senkou_b', 'N/A')
        ichimoku_chikou = market_data.get('ichimoku_chikou', 'N/A')

        # استخراج إشارات المؤشرات النصية إذا كانت متاحة
        rsi_signal_text = market_data.get('rsi_signal_text', None)
        ema_signal_text = market_data.get('ema_signal_text', None)
        macd_signal_text = market_data.get('macd_signal_text', None)
        bb_signal_text = market_data.get('bb_signal_text', None)
        stoch_signal_text = market_data.get('stoch_signal_text', None)

        # استخراج التوصية النصية إذا كانت متاحة
        recommendation_text = market_data.get('recommendation_text', None)

        # إضافة علم العملة إذا كان متاحًا
        currency_flag = get_currency_flag(symbol)

        # تحليل المؤشرات
        # RSI تحليل
        if rsi != 'N/A':
            rsi = float(rsi)
            if rsi > 70:
                rsi_analysis = "ذروة شراء (احتمال هبوط)" if lang == 'ar' else "Overbought (potential downside)"
                rsi_emoji = "🔴"
            elif rsi < 30:
                rsi_analysis = "ذروة بيع (احتمال صعود)" if lang == 'ar' else "Oversold (potential upside)"
                rsi_emoji = "🟢"
            else:
                rsi_analysis = "منطقة محايدة" if lang == 'ar' else "Neutral zone"
                rsi_emoji = "🟡"
        else:
            rsi_analysis = "غير متوفر" if lang == 'ar' else "Not available"
            rsi_emoji = "⚪"

        # EMA تحليل
        if ema20 != 'N/A' and ema50 != 'N/A':
            ema20 = float(ema20)
            ema50 = float(ema50)
            if ema20 > ema50:
                ema_analysis = "اتجاه صعودي" if lang == 'ar' else "Bullish trend"
                ema_emoji = "🟢"
            elif ema20 < ema50:
                ema_analysis = "اتجاه هبوطي" if lang == 'ar' else "Bearish trend"
                ema_emoji = "🔴"
            else:
                ema_analysis = "اتجاه محايد" if lang == 'ar' else "Neutral trend"
                ema_emoji = "🟡"
        else:
            ema_analysis = "غير متوفر" if lang == 'ar' else "Not available"
            ema_emoji = "⚪"

        # MACD تحليل
        if macd != 'N/A' and macd_signal != 'N/A':
            macd = float(macd)
            macd_signal = float(macd_signal)
            if macd > macd_signal:
                macd_analysis = "إشارة شراء" if lang == 'ar' else "Buy signal"
                macd_emoji = "🟢"
            elif macd < macd_signal:
                macd_analysis = "إشارة بيع" if lang == 'ar' else "Sell signal"
                macd_emoji = "🔴"
            else:
                macd_analysis = "إشارة محايدة" if lang == 'ar' else "Neutral signal"
                macd_emoji = "🟡"
        else:
            macd_analysis = "غير متوفر" if lang == 'ar' else "Not available"
            macd_emoji = "⚪"

        # Bollinger Bands تحليل
        if price != 0 and bb_upper != 'N/A' and bb_lower != 'N/A':
            bb_upper = float(bb_upper)
            bb_lower = float(bb_lower)
            if price > bb_upper:
                bb_analysis = "فوق النطاق العلوي (احتمال هبوط)" if lang == 'ar' else "Above upper band (potential downside)"
                bb_emoji = "🔴"
            elif price < bb_lower:
                bb_analysis = "تحت النطاق السفلي (احتمال صعود)" if lang == 'ar' else "Below lower band (potential upside)"
                bb_emoji = "🟢"
            else:
                bb_analysis = "ضمن نطاق التداول" if lang == 'ar' else "Within trading range"
                bb_emoji = "🟡"
        else:
            bb_analysis = "غير متوفر" if lang == 'ar' else "Not available"
            bb_emoji = "⚪"

        # استخدام التوصية المحسوبة مسبقًا إذا كانت متاحة
        if recommendation_text:
            if recommendation_text == 'buy':
                recommendation = "شراء" if lang == 'ar' else "Buy"
                recommendation_emoji = "🟢"
            elif recommendation_text == 'sell':
                recommendation = "بيع" if lang == 'ar' else "Sell"
                recommendation_emoji = "🔴"
            else:  # hold
                recommendation = "انتظار" if lang == 'ar' else "Hold"
                recommendation_emoji = "🟡"
        else:
            # تحديد التوصية العامة
            if rsi != 'N/A' and ema20 != 'N/A' and ema50 != 'N/A' and macd != 'N/A' and macd_signal != 'N/A':
                try:
                    # تحويل القيم إلى أرقام إذا لم تكن كذلك
                    rsi_val = float(rsi) if not isinstance(rsi, (int, float)) else rsi
                    ema20_val = float(ema20) if not isinstance(ema20, (int, float)) else ema20
                    ema50_val = float(ema50) if not isinstance(ema50, (int, float)) else ema50
                    macd_val = float(macd) if not isinstance(macd, (int, float)) else macd
                    macd_signal_val = float(macd_signal) if not isinstance(macd_signal, (int, float)) else macd_signal

                    bullish_signals = 0
                    bearish_signals = 0

                    # RSI
                    if rsi_val < 30:
                        bullish_signals += 1
                    elif rsi_val > 70:
                        bearish_signals += 1

                    # EMA
                    if ema20_val > ema50_val:
                        bullish_signals += 1
                    elif ema20_val < ema50_val:
                        bearish_signals += 1

                    # MACD
                    if macd_val > macd_signal_val:
                        bullish_signals += 1
                    elif macd_val < macd_signal_val:
                        bearish_signals += 1

                    # Bollinger Bands
                    if price != 0 and bb_lower != 'N/A':
                        bb_lower_val = float(bb_lower) if not isinstance(bb_lower, (int, float)) else bb_lower
                        if price < bb_lower_val:
                            bullish_signals += 1
                    if price != 0 and bb_upper != 'N/A':
                        bb_upper_val = float(bb_upper) if not isinstance(bb_upper, (int, float)) else bb_upper
                        if price > bb_upper_val:
                            bearish_signals += 1

                    # تحديد التوصية
                    if bullish_signals > bearish_signals:
                        recommendation = "شراء" if lang == 'ar' else "Buy"
                        recommendation_emoji = "🟢"
                    elif bearish_signals > bullish_signals:
                        recommendation = "بيع" if lang == 'ar' else "Sell"
                        recommendation_emoji = "🔴"
                    else:
                        recommendation = "انتظار" if lang == 'ar' else "Hold"
                        recommendation_emoji = "🟡"
                except (ValueError, TypeError) as e:
                    logger.warning(f"خطأ في تحويل قيم المؤشرات: {str(e)}")
                    recommendation = "غير متوفر" if lang == 'ar' else "Not available"
                    recommendation_emoji = "⚪"
            else:
                recommendation = "غير متوفر" if lang == 'ar' else "Not available"
                recommendation_emoji = "⚪"

        # إنشاء الروابط المباشرة
        direct_links = generate_direct_links(symbol)
        tradingview_symbol_url_part = direct_links['tradingview_symbol']
        binance_symbol_url_part = direct_links['binance_symbol']

        # إنشاء نص التحليل - تم تحديثه لإصلاح مشكلة النقطتين
        if lang == 'ar':
            try:
                price_formatted = f"{price:.4f}" if isinstance(price, (int, float)) and price > 0 else "غير متوفر"
                price_change_formatted = f"{price_change:+.2f}" if isinstance(price_change, (int, float)) else "غير متوفر"
            except Exception as e:
                logger.error(f"خطأ في تنسيق السعر: {str(e)}")
                price_formatted = str(price)
                price_change_formatted = str(price_change)

            # الحصول على العملة المستهدفة من البيانات
            target_currency = market_data.get('currency', 'USD')
            currency_symbol = 'USDT' if target_currency == 'USD' else target_currency

            # تحليل Ichimoku Cloud (متاح لجميع المستخدمين)
            ichimoku_analysis = ""
            ichimoku_emoji = "⚪"

            # إضافة تحليل Ichimoku Cloud لجميع المستخدمين
            is_subscribed = market_data.get('is_subscribed', False)

            if ichimoku_tenkan != 'N/A' and ichimoku_kijun != 'N/A':
                try:
                    ichimoku_tenkan = float(ichimoku_tenkan)
                    ichimoku_kijun = float(ichimoku_kijun)

                    if ichimoku_tenkan > ichimoku_kijun:
                        ichimoku_analysis = "إشارة صعودية (خط التحويل فوق خط الأساس)" if lang == 'ar' else "Bullish signal (Tenkan-sen above Kijun-sen)"
                        ichimoku_emoji = "🟢"
                    elif ichimoku_tenkan < ichimoku_kijun:
                        ichimoku_analysis = "إشارة هبوطية (خط التحويل تحت خط الأساس)" if lang == 'ar' else "Bearish signal (Tenkan-sen below Kijun-sen)"
                        ichimoku_emoji = "🔴"
                    else:
                        ichimoku_analysis = "إشارة محايدة" if lang == 'ar' else "Neutral signal"
                        ichimoku_emoji = "🟡"
                except (ValueError, TypeError):
                    ichimoku_analysis = "غير متوفر" if lang == 'ar' else "Not available"
            else:
                ichimoku_analysis = "غير متوفر" if lang == 'ar' else "Not available"

            analysis_text = f"""📊 تحليل فني لعملة {currency_flag} {symbol}

💰 **السعر الحالي** {price_formatted} {currency_symbol}
📈 **نسبة التغير** {price_change_formatted}%

{recommendation_emoji} **التوصية** {recommendation}

📏 **المؤشرات الفنية**
{rsi_emoji} **مؤشر القوة النسبية (RSI)** {float(rsi) if rsi != 'N/A' else 'غير متوفر':.2f} - {rsi_analysis}
{ema_emoji} **المتوسط المتحرك الأسي (EMA)** EMA20 = {float(ema20) if ema20 != 'N/A' else 'غير متوفر':.4f}, EMA50 = {float(ema50) if ema50 != 'N/A' else 'غير متوفر':.4f} - {ema_analysis}
{macd_emoji} **مؤشر تقارب وتباعد المتوسطات المتحركة (MACD)** {float(macd) if macd != 'N/A' else 'غير متوفر':.4f} / {float(macd_signal) if macd_signal != 'N/A' else 'غير متوفر':.4f} - {macd_analysis}
{bb_emoji} **نطاقات بولينجر (Bollinger Bands)**
  - **العلوي** {float(bb_upper) if bb_upper != 'N/A' else 'غير متوفر':.4f}
  - **الوسط** {float(bb_middle) if bb_middle != 'N/A' else 'غير متوفر':.4f}
  - **السفلي** {float(bb_lower) if bb_lower != 'N/A' else 'غير متوفر':.4f}
  - **التحليل** {bb_analysis}
{ichimoku_emoji} **سحابة إيشيموكو (Ichimoku Cloud)** - {ichimoku_analysis}
  {f"- **خط التحويل (Tenkan-sen)** {float(ichimoku_tenkan):.4f}" if ichimoku_tenkan != 'N/A' else ""}
  {f"- **خط الأساس (Kijun-sen)** {float(ichimoku_kijun):.4f}" if ichimoku_kijun != 'N/A' else ""}

⏰ {datetime.now().strftime('%H:%M')}

ℹ️ للحصول على تحليل متقدم بالذكاء الاصطناعي، يرجى الاشتراك في النسخة المدفوعة وإضافة مفتاح Gemini API الخاص بك.

🔗 [عرض الرسم البياني على TradingView](https://www.tradingview.com/chart/?symbol={tradingview_symbol_url_part})
🔗 [رابط مباشر للعملة على Binance](https://www.binance.com/en/trade/{binance_symbol_url_part}?type=spot)
"""
        else:
            try:
                price_formatted = f"{price:.4f}" if isinstance(price, (int, float)) and price > 0 else "N/A"
                price_change_formatted = f"{price_change:+.2f}" if isinstance(price_change, (int, float)) else "N/A"
            except Exception as e:
                logger.error(f"Error formatting price: {str(e)}")
                price_formatted = str(price)
                price_change_formatted = str(price_change)

            # الحصول على العملة المستهدفة من البيانات
            target_currency = market_data.get('currency', 'USD')
            currency_symbol = 'USDT' if target_currency == 'USD' else target_currency

            # تحليل Ichimoku Cloud (متاح لجميع المستخدمين)
            ichimoku_analysis = ""
            ichimoku_emoji = "⚪"

            # إضافة تحليل Ichimoku Cloud لجميع المستخدمين
            is_subscribed = market_data.get('is_subscribed', False)

            if ichimoku_tenkan != 'N/A' and ichimoku_kijun != 'N/A':
                try:
                    ichimoku_tenkan = float(ichimoku_tenkan)
                    ichimoku_kijun = float(ichimoku_kijun)

                    if ichimoku_tenkan > ichimoku_kijun:
                        ichimoku_analysis = "Bullish signal (Tenkan-sen above Kijun-sen)"
                        ichimoku_emoji = "🟢"
                    elif ichimoku_tenkan < ichimoku_kijun:
                        ichimoku_analysis = "Bearish signal (Tenkan-sen below Kijun-sen)"
                        ichimoku_emoji = "🔴"
                    else:
                        ichimoku_analysis = "Neutral signal"
                        ichimoku_emoji = "🟡"
                except (ValueError, TypeError):
                    ichimoku_analysis = "Not available"
            else:
                ichimoku_analysis = "Not available"

            analysis_text = f"""📊 Technical Analysis for {currency_flag} {symbol}

💰 **Current Price** {price_formatted} {currency_symbol}
📈 **Price Change** {price_change_formatted}%

{recommendation_emoji} **Recommendation** {recommendation}

📏 **Technical Indicators**
{rsi_emoji} **Relative Strength Index (RSI)** {float(rsi) if rsi != 'N/A' else 'N/A':.2f} - {rsi_analysis}
{ema_emoji} **Exponential Moving Average (EMA)** EMA20 = {float(ema20) if ema20 != 'N/A' else 'N/A':.4f}, EMA50 = {float(ema50) if ema50 != 'N/A' else 'N/A':.4f} - {ema_analysis}
{macd_emoji} **Moving Average Convergence Divergence (MACD)** {float(macd) if macd != 'N/A' else 'N/A':.4f} / {float(macd_signal) if macd_signal != 'N/A' else 'N/A':.4f} - {macd_analysis}
{bb_emoji} **Bollinger Bands**
  - **Upper** {float(bb_upper) if bb_upper != 'N/A' else 'N/A':.4f}
  - **Middle** {float(bb_middle) if bb_middle != 'N/A' else 'N/A':.4f}
  - **Lower** {float(bb_lower) if bb_lower != 'N/A' else 'N/A':.4f}
  - **Analysis** {bb_analysis}
{ichimoku_emoji} **Ichimoku Cloud** - {ichimoku_analysis}
  {f"- **Tenkan-sen** {float(ichimoku_tenkan):.4f}" if ichimoku_tenkan != 'N/A' else ""}
  {f"- **Kijun-sen** {float(ichimoku_kijun):.4f}" if ichimoku_kijun != 'N/A' else ""}

⏰ {datetime.now().strftime('%H:%M')}

ℹ️ For advanced AI-powered analysis, please subscribe to the premium version and add your Gemini API key.

🔗 [View Chart on TradingView](https://www.tradingview.com/chart/?symbol={tradingview_symbol_url_part})
🔗 [Direct Link to Coin on Binance](https://www.binance.com/en/trade/{binance_symbol_url_part}?type=spot)
"""

        # تطبيق الحل المبهر للتنسيق
        try:
            from utils.utils import fix_bold_formatting
            analysis_text = fix_bold_formatting(analysis_text, lang)
        except ImportError:
            logger.warning("لم يتم العثور على دالة fix_bold_formatting للتطبيق على التحليل التقليدي")

        return analysis_text

    except Exception as e:
        logger.error(f"خطأ في إنشاء التحليل التقليدي: {str(e)}")
        if lang == 'ar':
            return f"⚠️ حدث خطأ أثناء إنشاء التحليل. يرجى المحاولة مرة أخرى."
        else:
            return f"⚠️ An error occurred while creating the analysis. Please try again."


def get_currency_flag(symbol: str) -> str:
    """
    الحصول على علم العملة بناءً على رمزها

    Args:
        symbol: رمز العملة

    Returns:
        إيموجي العلم المناسب
    """
    # قاموس العملات وأعلامها
    currency_flags = {
        # العملات المشفرة
        'BTC': '₿',
        'ETH': '⟠',
        'BNB': '🔶',
        'XRP': '✖️',
        'ADA': '₳',
        'SOL': '◎',
        'DOT': '●',
        'DOGE': '🐕',
        'SHIB': '🐕',
        'MATIC': '⬡',
        'AVAX': '🔺',
        'LINK': '⚓',
        'UNI': '🦄',
        'ATOM': '⚛️',
        'LTC': 'Ł',

        # العملات التقليدية
        'USD': '🇺🇸',
        'EUR': '🇪🇺',
        'GBP': '🇬🇧',
        'JPY': '🇯🇵',
        'CNY': '🇨🇳',
        'AUD': '🇦🇺',
        'CAD': '🇨🇦',
        'CHF': '🇨🇭',
        'HKD': '🇭🇰',
        'SGD': '🇸🇬',
        'SAR': '🇸🇦',
        'AED': '🇦🇪',
        'KWD': '🇰🇼',
        'QAR': '🇶🇦',
        'BHD': '🇧🇭',
        'OMR': '🇴🇲',
        'EGP': '🇪🇬',
        'TRY': '🇹🇷',
    }

    # التعامل مع أزواج العملات
    if 'USDT' in symbol:
        base_symbol = symbol.replace('USDT', '')
        return currency_flags.get(base_symbol, '')

    # البحث عن العملة في القاموس
    return currency_flags.get(symbol, '')
