"""
أداة تشغيل جميع أدوات نظام الأخبار الذكي
تشغل التشخيص والإصلاح والاختبار بتسلسل منطقي
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# إعداد التسجيل
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    """تشغيل جميع الأدوات بالتسلسل"""
    print("🚀 بدء تشغيل جميع أدوات نظام الأخبار الذكي")
    print("="*60)
    
    try:
        # تهيئة قاعدة البيانات
        from core.database import db
        if not db:
            print("❌ خطأ: قاعدة البيانات غير متوفرة")
            return
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # الخطوة 1: التشخيص
        print(f"\n{'='*60}")
        print("🔍 الخطوة 1: تشخيص النظام")
        print("="*60)
        
        try:
            from news_system_diagnostics import NewsSystemDiagnostics
            diagnostics = NewsSystemDiagnostics(db)
            diagnosis_results = await diagnostics.run_full_diagnosis()
            diagnostics.print_diagnosis_report(diagnosis_results)
            
            # تحديد ما إذا كان الإصلاح مطلوباً
            needs_fixing = len(diagnosis_results.get('issues', [])) > 0
            
        except Exception as e:
            print(f"❌ خطأ في التشخيص: {str(e)}")
            needs_fixing = True
        
        # الخطوة 2: الإصلاح (إذا كان مطلوباً)
        if needs_fixing:
            print(f"\n{'='*60}")
            print("🔧 الخطوة 2: إصلاح المشاكل")
            print("="*60)
            
            try:
                from fix_news_system import NewsSystemFixer
                fixer = NewsSystemFixer(db)
                fix_results = await fixer.run_auto_fix()
                fixer.print_fix_report(fix_results)
                
            except Exception as e:
                print(f"❌ خطأ في الإصلاح: {str(e)}")
        else:
            print(f"\n✅ لا توجد مشاكل تحتاج إصلاح")
        
        # الخطوة 3: الاختبار
        print(f"\n{'='*60}")
        print("🧪 الخطوة 3: اختبار النظام")
        print("="*60)
        
        try:
            # محاولة الحصول على البوت للاختبار
            bot = None
            try:
                from core.telegram_bot import TelegramBot
                telegram_bot = TelegramBot()
                await telegram_bot.setup()
                bot = telegram_bot.application.bot
                print("✅ تم تهيئة البوت للاختبار")
            except Exception as e:
                print(f"⚠️ تحذير: لم يتم تهيئة البوت للاختبار: {str(e)}")
            
            from test_news_system import NewsSystemTester
            tester = NewsSystemTester(db, bot)
            test_results = await tester.run_comprehensive_test()
            tester.print_test_report(test_results)
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
        
        # الخطوة 4: التشخيص النهائي
        print(f"\n{'='*60}")
        print("🔍 الخطوة 4: التشخيص النهائي")
        print("="*60)
        
        try:
            from news_system_diagnostics import NewsSystemDiagnostics
            final_diagnostics = NewsSystemDiagnostics(db)
            final_results = await final_diagnostics.run_full_diagnosis()
            
            print(f"📊 النتيجة النهائية: {final_results['overall_status']}")
            print(f"⚠️ المشاكل المتبقية: {len(final_results.get('issues', []))}")
            
            if final_results.get('issues'):
                print("🔍 المشاكل المتبقية:")
                for i, issue in enumerate(final_results['issues'], 1):
                    print(f"  {i}. {issue}")
            
            if final_results.get('recommendations'):
                print("💡 التوصيات:")
                for i, rec in enumerate(final_results['recommendations'], 1):
                    print(f"  {i}. {rec}")
            
        except Exception as e:
            print(f"❌ خطأ في التشخيص النهائي: {str(e)}")
        
        # الخطوة 5: ملخص النتائج
        print(f"\n{'='*60}")
        print("📋 ملخص النتائج")
        print("="*60)
        
        print("✅ تم إكمال جميع الخطوات:")
        print("  1. ✅ التشخيص الأولي")
        if needs_fixing:
            print("  2. ✅ الإصلاح التلقائي")
        else:
            print("  2. ⏭️ تم تخطي الإصلاح (غير مطلوب)")
        print("  3. ✅ اختبار النظام")
        print("  4. ✅ التشخيص النهائي")
        
        print(f"\n🎯 حالة النظام النهائية: {final_results.get('overall_status', 'غير محدد')}")
        
        # توصيات نهائية
        print(f"\n💡 التوصيات النهائية:")
        if final_results.get('overall_status') == 'excellent':
            print("  🎉 النظام يعمل بشكل ممتاز! لا توجد إجراءات مطلوبة.")
        elif final_results.get('overall_status') == 'good':
            print("  👍 النظام يعمل بشكل جيد مع بعض التحسينات الطفيفة.")
        elif final_results.get('overall_status') == 'fair':
            print("  ⚠️ النظام يعمل لكن يحتاج بعض التحسينات.")
            print("  📝 راجع المشاكل المتبقية أعلاه.")
        else:
            print("  ❌ النظام يحتاج إلى تدخل يدوي.")
            print("  📞 تواصل مع فريق التطوير.")
        
        print(f"\n⏰ تم الانتهاء في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
    except Exception as e:
        print(f"❌ خطأ عام في تشغيل الأدوات: {str(e)}")
        import traceback
        print(traceback.format_exc())

def print_usage():
    """طباعة تعليمات الاستخدام"""
    print("""
🛠️ أداة تشغيل جميع أدوات نظام الأخبار الذكي

الاستخدام:
    python run_all_tools.py

ما تفعله هذه الأداة:
    1. 🔍 تشخيص شامل للنظام
    2. 🔧 إصلاح تلقائي للمشاكل (إذا وُجدت)
    3. 🧪 اختبار شامل للنظام
    4. 🔍 تشخيص نهائي للتأكد من الإصلاح
    5. 📋 ملخص شامل للنتائج

المتطلبات:
    - Python 3.8+
    - اتصال بقاعدة البيانات
    - مفتاح Gemini API (اختياري للاختبار الكامل)

ملاحظات:
    - قد تستغرق العملية عدة دقائق
    - سيتم إنشاء ملفات سجل مفصلة
    - قد يتم إرسال رسالة اختبار للمطور
    """)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
    else:
        asyncio.run(main())
