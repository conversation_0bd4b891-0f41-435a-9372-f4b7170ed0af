"""
🌐 محسن استدعاءات API
====================

نظام متقدم لتحسين استدعاءات API وتقليل زمن الاستجابة.
يتضمن تجميع الطلبات، إعادة المحاولة الذكية، وإدارة معدل الطلبات.

المميزات:
- تجميع الطلبات المتشابهة
- إعادة المحاولة التلقائية
- إدارة معدل الطلبات
- تحسين الاتصالات المتوازية
"""

import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple, Union
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class APIRequest:
    """طلب API"""
    url: str
    method: str = 'GET'
    params: Optional[Dict[str, Any]] = None
    headers: Optional[Dict[str, str]] = None
    data: Optional[Any] = None
    timeout: int = 10
    priority: int = 1  # 1 = عالي، 2 = متوسط، 3 = منخفض

class RateLimiter:
    """محدد معدل الطلبات"""
    
    def __init__(self, max_requests: int = 10, time_window: int = 60):
        """
        تهيئة محدد معدل الطلبات
        
        Args:
            max_requests: الحد الأقصى للطلبات
            time_window: النافزة الزمنية بالثواني
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = []
        self.lock = asyncio.Lock()
    
    async def acquire(self) -> bool:
        """الحصول على إذن لإرسال طلب"""
        async with self.lock:
            now = time.time()
            
            # إزالة الطلبات القديمة
            self.requests = [req_time for req_time in self.requests 
                           if now - req_time < self.time_window]
            
            # التحقق من الحد الأقصى
            if len(self.requests) >= self.max_requests:
                # حساب وقت الانتظار
                oldest_request = min(self.requests)
                wait_time = self.time_window - (now - oldest_request)
                if wait_time > 0:
                    logger.info(f"⏳ انتظار {wait_time:.2f} ثانية بسبب حد معدل الطلبات")
                    await asyncio.sleep(wait_time)
            
            # تسجيل الطلب الجديد
            self.requests.append(now)
            return True

class RequestBatcher:
    """مجمع الطلبات"""
    
    def __init__(self, batch_size: int = 5, batch_timeout: float = 2.0):
        """
        تهيئة مجمع الطلبات
        
        Args:
            batch_size: حجم المجموعة
            batch_timeout: مهلة انتظار المجموعة
        """
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.pending_requests = []
        self.batch_lock = asyncio.Lock()
    
    async def add_request(self, request: APIRequest) -> Any:
        """إضافة طلب إلى المجموعة"""
        async with self.batch_lock:
            self.pending_requests.append(request)
            
            # إذا امتلأت المجموعة، قم بمعالجتها
            if len(self.pending_requests) >= self.batch_size:
                return await self._process_batch()
            
            # إذا لم تمتلئ، انتظر قليلاً
            await asyncio.sleep(self.batch_timeout)
            
            # معالجة المجموعة حتى لو لم تمتلئ
            if self.pending_requests:
                return await self._process_batch()
    
    async def _process_batch(self) -> List[Any]:
        """معالجة مجموعة من الطلبات"""
        if not self.pending_requests:
            return []
        
        batch = self.pending_requests.copy()
        self.pending_requests.clear()
        
        logger.info(f"🔄 معالجة مجموعة من {len(batch)} طلب")
        
        # تنفيذ الطلبات بشكل متوازي
        tasks = []
        for request in batch:
            task = asyncio.create_task(self._execute_single_request(request))
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results
    
    async def _execute_single_request(self, request: APIRequest) -> Any:
        """تنفيذ طلب واحد"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=request.method,
                    url=request.url,
                    params=request.params,
                    headers=request.headers,
                    data=request.data,
                    timeout=aiohttp.ClientTimeout(total=request.timeout)
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    else:
                        logger.warning(f"طلب API فشل: {response.status} - {request.url}")
                        return None
        except Exception as e:
            logger.error(f"خطأ في تنفيذ طلب API: {str(e)}")
            return None

class SmartRetryHandler:
    """معالج إعادة المحاولة الذكي"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        """
        تهيئة معالج إعادة المحاولة
        
        Args:
            max_retries: الحد الأقصى لإعادة المحاولة
            base_delay: التأخير الأساسي
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
    
    async def execute_with_retry(self, request: APIRequest) -> Any:
        """تنفيذ طلب مع إعادة المحاولة الذكية"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                result = await self._execute_request(request)
                if result is not None:
                    if attempt > 0:
                        logger.info(f"✅ نجح الطلب في المحاولة {attempt + 1}")
                    return result
                
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    # حساب تأخير متزايد
                    delay = self.base_delay * (2 ** attempt)
                    logger.warning(f"⚠️ فشل الطلب، إعادة المحاولة خلال {delay} ثانية (المحاولة {attempt + 1}/{self.max_retries + 1})")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"❌ فشل الطلب نهائياً بعد {self.max_retries + 1} محاولات")
        
        # إذا فشلت جميع المحاولات
        if last_exception:
            raise last_exception
        return None
    
    async def _execute_request(self, request: APIRequest) -> Any:
        """تنفيذ طلب واحد"""
        async with aiohttp.ClientSession() as session:
            async with session.request(
                method=request.method,
                url=request.url,
                params=request.params,
                headers=request.headers,
                data=request.data,
                timeout=aiohttp.ClientTimeout(total=request.timeout)
            ) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 429:  # Too Many Requests
                    raise Exception("Rate limit exceeded")
                elif response.status >= 500:  # Server errors
                    raise Exception(f"Server error: {response.status}")
                else:
                    logger.warning(f"طلب API فشل: {response.status}")
                    return None

class APIOptimizer:
    """محسن استدعاءات API الرئيسي"""
    
    def __init__(self):
        """تهيئة محسن API"""
        self.rate_limiter = RateLimiter(max_requests=20, time_window=60)
        self.request_batcher = RequestBatcher(batch_size=3, batch_timeout=1.0)
        self.retry_handler = SmartRetryHandler(max_retries=2, base_delay=0.5)
        self.connection_pool = None
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'retried_requests': 0,
            'avg_response_time': 0.0
        }
    
    async def execute_request(self, request: APIRequest, use_batching: bool = False) -> Any:
        """تنفيذ طلب API محسن"""
        start_time = time.time()
        
        try:
            # انتظار إذن من محدد معدل الطلبات
            await self.rate_limiter.acquire()
            
            # تنفيذ الطلب
            if use_batching:
                result = await self.request_batcher.add_request(request)
            else:
                result = await self.retry_handler.execute_with_retry(request)
            
            # تحديث الإحصائيات
            response_time = time.time() - start_time
            self._update_stats(True, response_time)
            
            return result
            
        except Exception as e:
            response_time = time.time() - start_time
            self._update_stats(False, response_time)
            logger.error(f"خطأ في تنفيذ طلب API محسن: {str(e)}")
            return None
    
    async def execute_multiple_requests(self, requests: List[APIRequest]) -> List[Any]:
        """تنفيذ عدة طلبات بشكل محسن"""
        # ترتيب الطلبات حسب الأولوية
        sorted_requests = sorted(requests, key=lambda r: r.priority)
        
        # تقسيم الطلبات إلى مجموعات
        high_priority = [r for r in sorted_requests if r.priority == 1]
        medium_priority = [r for r in sorted_requests if r.priority == 2]
        low_priority = [r for r in sorted_requests if r.priority == 3]
        
        results = []
        
        # تنفيذ الطلبات عالية الأولوية أولاً
        if high_priority:
            high_results = await self._execute_request_group(high_priority)
            results.extend(high_results)
        
        # ثم متوسطة الأولوية
        if medium_priority:
            medium_results = await self._execute_request_group(medium_priority)
            results.extend(medium_results)
        
        # وأخيراً منخفضة الأولوية
        if low_priority:
            low_results = await self._execute_request_group(low_priority)
            results.extend(low_results)
        
        return results
    
    async def _execute_request_group(self, requests: List[APIRequest]) -> List[Any]:
        """تنفيذ مجموعة من الطلبات"""
        tasks = []
        for request in requests:
            task = asyncio.create_task(self.execute_request(request))
            tasks.append(task)
        
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    def _update_stats(self, success: bool, response_time: float):
        """تحديث إحصائيات الأداء"""
        self.stats['total_requests'] += 1
        
        if success:
            self.stats['successful_requests'] += 1
        else:
            self.stats['failed_requests'] += 1
        
        # تحديث متوسط وقت الاستجابة
        total = self.stats['total_requests']
        current_avg = self.stats['avg_response_time']
        self.stats['avg_response_time'] = (
            (current_avg * (total - 1) + response_time) / total
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        success_rate = 0
        if self.stats['total_requests'] > 0:
            success_rate = (self.stats['successful_requests'] / self.stats['total_requests']) * 100
        
        return {
            **self.stats,
            'success_rate': round(success_rate, 2),
            'failure_rate': round(100 - success_rate, 2)
        }

# إنشاء مثيل عام من محسن API
api_optimizer = APIOptimizer()
