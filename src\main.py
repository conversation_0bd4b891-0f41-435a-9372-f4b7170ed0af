"""
🚀 Trading Telegram Bot - الملف الرئيسي المحسن
===============================================

نظام بوت تداول العملات الرقمية مع تحليل متقدم وذكاء اصطناعي.
تم تحسين نظام الاستيرادات لتحسين الأداء وسهولة الصيانة.
1
"""

# ===== الاستيرادات الأساسية المطلوبة فوراً =====
import logging
import os
import sys
import asyncio
import json
import time
from datetime import datetime, timedelta
import dotenv

# ===== نظام الاستيراد المحسن (تحميل تدريجي) =====
try:
    from core.dependency_manager import DependencyManager, get_dependency_manager
    from core.lazy_loader import LazyLoader, get_lazy_loader

    # تهيئة مدير التبعيات
    dependency_manager = get_dependency_manager()
    lazy_loader = get_lazy_loader()

    # تحميل الوحدات الأساسية
    essential_modules = dependency_manager.load_essential()

    # تسجيل الوحدات للتحميل الذكي
    telegram_module = lazy_loader.register_module('telegram')
    firebase_module = lazy_loader.register_module('firebase_admin')
    numpy_module = lazy_loader.register_module('numpy')
    pandas_module = lazy_loader.register_module('pandas')

    ENHANCED_IMPORTS_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("✅ تم تفعيل نظام الاستيراد المحسن")

    # تحميل الاستيرادات المتخصصة من النظام الجديد
    from core.imports.external_imports import (
        requests, np, pd, telegram, firebase_admin, credentials, firestore
    )
    from core.imports.service_imports import (
        create_payment_transaction, update_transaction_with_binance_id,
        cancel_transaction, verify_payment_transaction, verify_paypal_transaction,
        extend_transaction, complete_payment, verify_payment,
        cleanup_pending_transactions, notify_expiring_transactions,
        send_transaction_expiry_notification, get_transaction_manager,
        initialize_transaction_manager, load_user_settings, save_user_settings
    )

except ImportError as e:
    # التراجع للنظام القديم في حالة فشل النظام الجديد
    ENHANCED_IMPORTS_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"⚠️ فشل في تحميل النظام المحسن، التراجع للنظام القديم: {e}")

    # الاستيرادات التقليدية كبديل
    import requests
    import numpy as np
    import pandas as pd
    import telegram
    import firebase_admin
    from firebase_admin import credentials, firestore

# ===== تحميل ذكي للوحدات المطلوبة =====
if ENHANCED_IMPORTS_AVAILABLE:
    # تسجيل الوحدات للتحميل الذكي
    api_manager_module = lazy_loader.register_module('api_manager', 'api_manager')
    binance_module = lazy_loader.register_module('binance_manager', 'integrations.binance_manager')
    api_validators_module = lazy_loader.register_module('api_validators', 'api_validators')
    analysis_module = lazy_loader.register_module('analysis', 'analysis.user_market_data')
    gemini_module = lazy_loader.register_module('gemini_analysis', 'analysis.gemini_analysis')
    api_ui_module = lazy_loader.register_module('api_ui', 'api_ui')
    system_settings_module = lazy_loader.register_module('system_settings', 'services.system_settings')

    # تحميل مسبق للوحدات الحرجة
    critical_modules = [
        'api_manager', 'binance_manager', 'system_settings'
    ]
    lazy_loader.preload_critical(critical_modules)

# دالة مساعدة للوصول للوحدات المحملة
def get_module_attr(module_name, attr_name):
    """الحصول على خاصية من وحدة محملة بالتحميل الذكي"""
    try:
        if ENHANCED_IMPORTS_AVAILABLE:
            module = lazy_loader.get_module(module_name)
            if module and hasattr(module, attr_name):
                return getattr(module, attr_name)

        # تحميل مباشر كبديل - إصلاح مشكلة services.system
        import importlib
        # إصلاح: عدم تحويل اسم الوحدة إذا كان يحتوي على نقاط بالفعل
        if '.' in module_name:
            mod = importlib.import_module(module_name)
        else:
            mod = importlib.import_module(module_name.replace('_', '.') if '_' in module_name else module_name)
        return getattr(mod, attr_name)
    except Exception as e:
        logger.warning(f"فشل في الوصول لـ {attr_name} من {module_name}: {e}")
        return None
# تم نقل الاستيرادات إلى النظام المحسن أعلاه
# استيراد دوال التنبيهات من الخدمة الجديدة
from services.alert_service import (
    initialize_alert_service,
    alert_command,
    setup_price_alert,
    handle_custom_alert,
    process_custom_alert,
    check_alerts,
    send_daily_report
)

# استيراد دوال معالجة المدفوعات
from services.handle_paypal_payment import handle_paypal_payment
from services.handle_payment_verification import handle_payment_verification

from services.free_day_system import free_day_system
from services.automatic_payment_verification import AutomaticPaymentVerifier

# استيراد خدمات إدارة البيانات
from services import (
    set_data_manager,
    # استيراد خدمات المعاملات المالية
    initialize_transaction_service,
    initialize_transaction_manager
)

# استيراد النظام المحسن متعدد الإطارات الزمنية
from analysis.integration_wrapper import EnhancedAnalysisWrapper

# استيراد الأنظمة المحسنة الجديدة - الإصدار 4.4.0
from analysis.optimized_indicators import OptimizedIndicators
from analysis.enhanced_ai_analysis import EnhancedAIAnalyzer, initialize_enhanced_ai_analyzer
# استيراد الملفات البديلة الفارغة (الاستضافة تتولى هذه المهام)
from monitoring.real_time_performance import RealTimePerformanceMonitor
from utils.memory_manager import AdvancedMemoryManager
from security.api_security import AdvancedAPISecurityManager
from database.optimized_queries import OptimizedFirestoreManager

# استيراد وحدة تعليم التداول
from education.trading_education import (
    handle_learn_trading_ai,
    handle_message_for_ai_tutor,
    generate_and_send_chapter, # Needed for callback
    start_quiz, # Needed for callback
    handle_quiz_answer, # Needed for poll answers
    # تم إزالة user_education_state من الاستيراد العام - يتم استيراده محلياً في كل دالة
    check_gemini_api_key, # Needed for callback
    handle_ask_ai_tutor_button, # استيراد دالة معالجة زر اسأل الذكاء الاصطناعي
    show_quiz_results_or_next_steps, # استيراد دالة عرض نتائج الاختبار
    show_supplementary_chapters, # استيراد دالة عرض الفصول التكميلية
    generate_and_send_supplementary_chapter, # استيراد دالة إنشاء الفصول التكميلية
    set_firestore_db as set_trading_education_db # استيراد دالة تعيين قاعدة البيانات
)

# استيراد معالجات واجهة المستخدم الجديدة
from handlers import (
    # معالجات القوائم
    show_language_selection,
    show_terms_and_conditions,
    show_enhanced_analysis_explanation,
    show_analysis_comparison,
    show_upgrade_info,

    # معالجات الإعدادات
    set_trading_style,
    set_analysis_type,
    reset_enhanced_settings,
    set_language
)

# استيراد معالجات القوائم من menu_handlers
from handlers.menu_handlers import (
    show_enhanced_settings,
    show_trading_style_options,
    show_analysis_type_options
)

# استيراد خدمة معالجة الأخطاء الجديدة
from services.error_handler import (
    initialize_error_handler,
    telegram_error_handler,
    log_error,
    log_info,
    log_warning,
    log_debug,
    specialized_handlers,
    safe_send_message,
    safe_edit_message,
    safe_delete_message,
    generate_error_report
)

# استيراد دوال التحليل المساعدة الجديدة
from analysis.analysis_helpers import (
    initialize_analysis_helpers,
    create_analysis_text,
    generate_stats_report,
    get_analysis_type_name
)

# استيراد وحدة التحليل المحسن الجديدة
from analysis.enhanced_analysis import (
    initialize_enhanced_analysis,
    show_enhanced_stats,
    show_enhanced_analysis_menu,
    analyze_symbol_enhanced,
    compare_trading_styles,
    refresh_analysis,
    show_analysis_type_settings,
    get_comprehensive_analysis
)

# استيراد خدمات الإدارة الجديدة
from services.admin_service import (
    initialize_admin_service,
    cast,
    ban_user,
    unban_user,
    system_info,
    cleanup_system,
    backup_data,
    grant_free_day_command,
    remove_free_day_command,
    stop_all_scheduled_tasks
)

# استيراد خدمات إدارة المستخدمين الجديدة
from services.user_management import (
    initialize_user_management,
    start,
    add_user_to_users_collection,
    show_user_stats,
    check_expired_subscriptions,
    notify_expiring_subscriptions,
    _update_expired_subscription,
    _send_expiry_notification,
    # test_subscription, # تم نقله إلى subscription_system
    activate_subscription,
    manage_free_day_settings,
    set_free_day,
    stop
)

# استيراد معالجات الواجهة الرئيسية الجديدة
from handlers.main_handlers import (
    initialize_main_handlers,
    show_main_menu,
    help_command,
    button_click,
    handle_message,
    handle_trading_education_callback,
    handle_ai_tutor_message_wrapper
)

# استيراد خدمة النسخ الاحتياطي الجديدة
from services.backup_service import (
    initialize_backup_service,
    get_backup_instances,
    perform_backup,
    backup_subscription_data
)

# استيراد خدمة إدارة API الجديدة
from services.api_management import (
    initialize_api_management_service,
    api_setup_command,
    api_info_command,
    delete_api_command
)

dotenv.load_dotenv()
# استيراد وحدة تهيئة Firebase
from integrations.firebase_init import initialize_firebase

# تهيئة Firebase
db = initialize_firebase()
if not db:
    logger.error("❌ فشل في تهيئة Firebase، سيتم محاولة التهيئة اليدوية")
    try:
        cred = credentials.Certificate('tradingtelegram-da632-firebase-adminsdk-fbsvc-a67cf6e086.json')
        firebase_admin.initialize_app(cred)
        db = firestore.client()
        logger.info("✅ تم تهيئة Firebase يدويًا بنجاح")
    except Exception as e:
        logger.error(f"❌ فشل في تهيئة Firebase يدويًا: {str(e)}")
        raise ValueError("لم يتم تهيئة قاعدة البيانات بعد")

# حل التبعية الدائرية مع config.py
from config import set_external_references
set_external_references(database=db, log=logger)

# تعيين قاعدة بيانات Firestore في gemini_analysis
from analysis import gemini_analysis
gemini_analysis.set_firestore_db(db)

# تعيين قاعدة بيانات Firestore في trading_education
set_trading_education_db(db)

# تهيئة نظام الإعدادات العامة
system_settings_obj = get_module_attr('services.system_settings', 'system_settings')
if system_settings_obj:
    system_settings_obj.initialize_db(db)
    system_settings = system_settings_obj  # تعيين المتغير العام
else:
    # تحميل مباشر كبديل
    from services.system_settings import system_settings
    system_settings.initialize_db(db)

# التأكد من تعريف system_settings كمتغير عام
if 'system_settings' not in globals():
    from services.system_settings import system_settings

# تهيئة مدير البيانات
set_data_manager(db)

# تهيئة خدمة المعاملات المالية
transaction_service = initialize_transaction_service(db, None)  # سيتم تعيين subscription_system لاحقاً

# تهيئة معالج الأخطاء
initialize_error_handler(db)

# تهيئة خدمة الإدارة - سيتم تهيئتها لاحقاً بعد تعريف SystemConfig
# initialize_admin_service(db, SystemConfig)

# Configure logging
logging.basicConfig(format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)

# تهيئة النظام المحسن متعدد الإطارات الزمنية
enhanced_analyzer = None

# تهيئة الأنظمة المحسنة الجديدة - الإصدار 4.4.0
optimized_indicators = None
enhanced_ai_analyzer = None
# الملفات البديلة الفارغة (الاستضافة تتولى هذه المهام)
performance_monitor = None
memory_manager = None
api_security = None
optimized_db = None

# رسائل شرح ميزة التحليل المحسن
ENHANCED_ANALYSIS_EXPLANATION = {
    'ar': """
🚀 **ميزة التحليل المحسن - شرح مفصل**

## 🔍 **ما هو التحليل المحسن؟**

التحليل المحسن هو نظام تحليل متطور يستخدم **تقنيات متعددة الإطارات الزمنية** لتقديم تحليل أكثر دقة وشمولية من التحليل التقليدي.

---

## 📊 **الفرق بين التحليل العادي والمحسن**

### **التحليل العادي:**
• يحلل إطار زمني واحد فقط (عادة 4 ساعات)
• يستخدم مؤشرات أساسية (RSI, EMA, MACD)
• توصية بسيطة (شراء/بيع/انتظار)
• دقة محدودة (~60-70%)

### **التحليل المحسن:**
• يحلل **5-7 إطارات زمنية** مختلفة
• يستخدم **20+ مؤشر متقدم**
• تحليل هرمي من الأعلى للأسفل
• نظام تأكيد الإشارات متعدد المستويات
• دقة عالية (~85-95%)

---

## ⚙️ **كيف يعمل النظام المحسن؟**

### **1. جمع البيانات المتقدم:**
• إطارات زمنية متعددة (1م، 15م، 1س، 4س، 1ي، 1أ)
• مؤشرات متخصصة لكل إطار زمني
• تحليل الحجم والسيولة

### **2. التحليل الهرمي:**
• **الاتجاه طويل المدى** (أسبوعي/يومي)
• **الاتجاه متوسط المدى** (4 ساعات/ساعة)
• **نقاط الدخول** (15 دقيقة/دقيقة)

### **3. نظام تأكيد الإشارات:**
• مقارنة الإشارات بين الإطارات
• كشف التناقضات والتوافقات
• حساب مستوى الثقة الإجمالي

### **4. تقييم المخاطر متعدد الأبعاد:**
• مخاطر التقلبات
• مخاطر التناقضات
• مخاطر السيولة
• مخاطر الاتجاه

---

## 🎯 **المميزات الرئيسية:**

✅ **دقة أعلى بنسبة 40%** من التحليل العادي
✅ **توصيات مخصصة** حسب نمط التحليل
✅ **نقاط دخول وخروج دقيقة**
✅ **تقييم شامل للمخاطر**
✅ **كشف الإشارات الخاطئة**
✅ **تحليل متوافق مع جميع أنماط التحليل**

---

## 📈 **أنماط التحليل المدعومة:**

🔸 **Scalping** - تحليل للتداول السريع (دقائق)
🔸 **Day Trading** - تحليل للتداول اليومي (ساعات)
🔸 **التداول المتأرجح** - تحليل للتداول المتأرجح (أيام)
🔸 **Position Trading** - تحليل للاستثمار طويل المدى (أسابيع)

---

## 🛠️ **كيفية الاستخدام:**

1️⃣ اذهب للقائمة الرئيسية
2️⃣ اضغط **"🚀 التحليل المحسن"**
3️⃣ اختر العملة المراد تحليلها
4️⃣ حدد نمط التحليل المناسب
5️⃣ احصل على تحليل شامل ومفصل!

---

## 📋 **مثال على النتائج:**

```
🚀 BTC/USDT - تحليل محسن

📊 تحليل هرمي: صاعد قوي ✅
🛡️ مستوى الثقة: 88%
📈 التوصية: شراء قوي

💰 نقاط التداول:
• الدخول: 43,200-43,400
• الهدف الأول: 44,500
• الهدف الثاني: 45,800
• وقف الخسارة: 42,100
• نسبة المخاطرة/المكافأة: 1:3.2

📊 تفاصيل التحليل:
• الإطارات المحللة: 6
• مصادر التأكيد: 4
• توافق الإطارات: 85%
• مستوى المخاطر: منخفض
```

---

## ⚠️ **ملاحظات مهمة:**

• يتطلب اشتراك مميز
• يحتاج مفاتيح API (Binance + Gemini)
• أكثر دقة من التحليل العادي
• مناسب لجميع مستويات الخبرة
• يوفر تحليل مخاطر شامل

---

**🌟 جرب التحليل المحسن الآن واكتشف الفرق في دقة التوقعات!**
""",

    'en': """
🚀 **Enhanced Analysis Feature - Detailed Explanation**

## 🔍 **What is Enhanced Analysis?**

Enhanced Analysis is an advanced analysis system that uses **multi-timeframe techniques** to provide more accurate and comprehensive analysis than traditional analysis.

---

## 📊 **Difference Between Regular and Enhanced Analysis**

### **Regular Analysis:**
• Analyzes only one timeframe (usually 4 hours)
• Uses basic indicators (RSI, EMA, MACD)
• Simple recommendation (buy/sell/hold)
• Limited accuracy (~60-70%)

### **Enhanced Analysis:**
• Analyzes **5-7 different timeframes**
• Uses **20+ advanced indicators**
• Top-down hierarchical analysis
• Multi-level signal confirmation system
• High accuracy (~85-95%)

---

## ⚙️ **How Does the Enhanced System Work?**

### **1. Advanced Data Collection:**
• Multiple timeframes (1m, 15m, 1h, 4h, 1d, 1w)
• Specialized indicators for each timeframe
• Volume and liquidity analysis

### **2. Hierarchical Analysis:**
• **Long-term trend** (weekly/daily)
• **Medium-term trend** (4 hours/hour)
• **Entry points** (15 minutes/minute)

### **3. Signal Confirmation System:**
• Compare signals between timeframes
• Detect conflicts and alignments
• Calculate overall confidence level

### **4. Multi-dimensional Risk Assessment:**
• Volatility risks
• Conflict risks
• Liquidity risks
• Trend risks

---

## 🎯 **Key Features:**

✅ **40% higher accuracy** than regular analysis
✅ **Customized recommendations** based on analysis style
✅ **Precise entry and exit points**
✅ **Comprehensive risk assessment**
✅ **False signal detection**
✅ **Compatible with all analysis styles**

---

## 📈 **Supported Analysis Styles:**

🔸 **Scalping** - Analysis for quick trading (minutes)
🔸 **Day Trading** - Analysis for daily trading (hours)
🔸 **التداول المتأرجح** - Analysis for swing trading (days)
🔸 **Position Trading** - Analysis for long-term investment (weeks)

---

## 🛠️ **How to Use:**

1️⃣ Go to main menu
2️⃣ Press **"🚀 Enhanced Analysis"**
3️⃣ Choose the currency to analyze
4️⃣ Select appropriate analysis style
5️⃣ Get comprehensive and detailed analysis!

---

## 📋 **Example Results:**

```
🚀 BTC/USDT - Enhanced Analysis

📊 Hierarchical Analysis: Strong Bullish ✅
🛡️ Confidence Level: 88%
📈 Recommendation: Strong Buy

💰 Trading Points:
• Entry: 43,200-43,400
• First Target: 44,500
• Second Target: 45,800
• Stop Loss: 42,100
• Risk/Reward Ratio: 1:3.2

📊 Analysis Details:
• Timeframes Analyzed: 6
• Confirmation Sources: 4
• Timeframe Alignment: 85%
• Risk Level: Low
```

---

## ⚠️ **Important Notes:**

• Requires premium subscription
• Needs API keys (Binance + Gemini)
• More accurate than regular analysis
• Suitable for all experience levels
• Provides comprehensive risk analysis

---

**🌟 Try Enhanced Analysis now and discover the difference in prediction accuracy!**
"""
}

# تم نقل دالة get_env_var إلى src/utils/system_helpers.py
from utils.system_helpers import get_env_var

# تعريف المتغيرات الأساسية
class SystemConfig:
    """فئة للإعدادات الأساسية للنظام"""
    __slots__ = []

    # يتم الآن قراءة جميع القيم من المتغيرات البيئية أو قاعدة البيانات
    DEVELOPER_ID = str(get_env_var("DEVELOPER_ID", "7839527436"))  # تحويل القيمة إلى نص (str) لضمان المقارنة الصحيحة

    @classmethod
    def get_bot_token(cls):
        """الحصول على التوكن مباشرة من الإعدادات الحساسة"""
        try:
            bot_token = system_settings.get("BOT_TOKEN", None, sensitive=True)
            if not bot_token:
                bot_token = os.getenv("BOT_TOKEN", "DEFAULT_BOT_TOKEN")  # قيمة افتراضية للاختبار
            return bot_token
        except:
            return os.getenv("BOT_TOKEN", "DEFAULT_BOT_TOKEN")  # قيمة افتراضية للاختبار

    # سيتم تعيين BOT_TOKEN لاحقاً بعد تهيئة system_settings
    BOT_TOKEN = None

    @classmethod
    def initialize_bot_token(cls):
        """تهيئة BOT_TOKEN بعد تهيئة system_settings"""
        cls.BOT_TOKEN = cls.get_bot_token()
        return cls.BOT_TOKEN

    # تم تعطيل استخدام مفاتيح Binance API الخاصة بالمطور
    BINANCE_API = {
        'key': None,
        'secret': None
    }
    GITHUB = {
        'token': get_env_var("GITHUB_TOKEN", "DEFAULT_GITHUB_TOKEN"),  # قيمة افتراضية للاختبار
        'repo': get_env_var("GITHUB_REPO", "TradingTelegram"),
        'owner': get_env_var("GITHUB_OWNER", "HoySama")
    }
    # تم إزالة إعدادات Redis واستبدالها بنظام التخزين المؤقت باستخدام Firestore
    DEFAULT_VALUES = {
        'analyses_per_day': int(get_env_var("ANALYSES_PER_DAY", "3")),
        'alerts_per_day': int(get_env_var("ALERTS_PER_DAY", "1")),
        'default_lang': get_env_var("DEFAULT_LANG", "ar")
    }

# تهيئة BOT_TOKEN بعد تعريف SystemConfig
SystemConfig.initialize_bot_token()

# تعريف المتغيرات المباشرة
TOKEN = SystemConfig.BOT_TOKEN
TELEGRAM_CHAT_ID = SystemConfig.DEVELOPER_ID
# تم تعطيل استخدام مفاتيح Binance API الخاصة بالمطور
BINANCE_API_KEY = None
BINANCE_API_SECRET = None
GITHUB_TOKEN = SystemConfig.GITHUB['token']
GITHUB_REPO = SystemConfig.GITHUB['repo']
GITHUB_OWNER = SystemConfig.GITHUB['owner']

# تهيئة خدمة الإدارة الآن بعد تعريف SystemConfig
initialize_admin_service(db, SystemConfig)

# تهيئة خدمة النسخ الاحتياطي
initialize_backup_service(db, logger, SystemConfig)

# استيراد الكلاسات المطلوبة
from integrations.binance_manager import BinanceManager
from integrations.binance_verifier import BinanceTransactionVerifier
from services.auto_transaction_verifier import AutomaticTransactionVerifier

# استيراد مدير API
from api_manager import APIManager

# استيراد دوال واجهة API
from api_ui import setup_api_keys, show_api_instructions, delete_api_keys_ui, show_platform_selection

# استيراد دوال التحقق من API
from api_validators import verify_binance_api, verify_gemini_api

# استيراد مكتبة التشفير
from cryptography.fernet import Fernet

# تهيئة الأنظمة
binance_manager = BinanceManager(db)
# سيتم تهيئة binance_verifier و auto_verifier بعد تهيئة api_manager
binance_verifier = None
auto_verifier = None

# الحصول على كائنات النسخ الاحتياطي من الخدمة الجديدة
secure_backup, github_backup = get_backup_instances()

# استيراد الدوال المساعدة من utils
from utils import (
    sanitize_telegram_text,
    validate_markdown_entities,
    split_long_message,
    delete_message_after_delay
)

# إكمال تهيئة التبعيات في معالجات الإعدادات - سيتم تهيئتها لاحقاً
# set_settings_dependencies(subscription_system, db, firestore_cache)

# تهيئة مدير API
# الحصول على مفتاح التشفير من Firestore أو إنشاء مفتاح جديد
async def get_or_create_encryption_key():
    try:
        # التحقق من وجود مفتاح في Firestore
        key_ref = db.collection('system_config').document('encryption_key')
        key_data = key_ref.get()

        if key_data.exists and 'key' in key_data.to_dict() and len(key_data.to_dict()['key']) == 44:
            # استخدام المفتاح المخزن
            return key_data.to_dict()['key']

        # التحقق من وجود مفتاح في ملف .env
        import config as config_module
        env_key = config_module.ENCRYPTION_KEY
        if env_key and len(env_key) == 44:
            # تخزين المفتاح في Firestore
            key_ref.set({'key': env_key, 'created_at': datetime.now().isoformat()})
            return env_key

        # إنشاء مفتاح جديد
        new_key = Fernet.generate_key().decode()
        key_ref.set({'key': new_key, 'created_at': datetime.now().isoformat()})
        logger.info("تم إنشاء مفتاح تشفير جديد وتخزينه في Firestore. يرجى تخزينه في ملف .env للاستخدام المستقبلي.")
        # تم إزالة طباعة مفتاح التشفير هنا لأسباب أمنية
        return new_key
    except Exception as e:
        logger.error(f"خطأ في الحصول على مفتاح التشفير: {str(e)}")
        # في حالة الخطأ، نستخدم مفتاح جديد
        return Fernet.generate_key().decode()

# سيتم تعيين مفتاح التشفير لاحقًا بعد تهيئة النظام
encryption_key = None
api_manager = None

# Global state
user_states = {}
user_settings = {}

# تم نقل كلاس Config إلى src/config.py
from config import Config

# تم نقل كلاس SubscriptionSystem إلى src/services/subscription_system.py
from services.subscription_system import get_subscription_system, initialize_subscription_system







# علامة للتحقق من التهيئة لتجنب التهيئة المتكررة
_system_initialized = False
_system_components = None

# تهيئة النظام العالمي - سيتم تهيئة subscription_system في دالة initialize_system()
subscription_system = None

# تهيئة النظام
config = Config()

# تم نقل كلاس CryptoAnalysis إلى src/analysis/basic_analysis.py
# استيراد الدوال والكلاسات من الملف الجديد
from analysis.basic_analysis import (
    CryptoAnalysis,
    analyze_symbol,
    analyze_command,
    add_indicator,
    remove_indicator,
    add_custom_currency,
    customize_indicators,
    initialize_basic_analysis
)

# تم دمج CryptoAnalysisRemaining في basic_analysis.py
# استخدام CryptoAnalysis الموحد بدلاً من ذلك
from analysis.basic_analysis import CryptoAnalysis

# Initialize Crypto Analysis - إنشاء كائن للاستخدام في main.py
ca = CryptoAnalysis()

# تم نقل قاموس الترجمات إلى src/localization/translations.py
from localization.translations import get_translations
translations = get_translations()

# تم نقل دالة get_text إلى src/utils/text_helpers.py
from utils.text_helpers import get_text

# تم نقل جميع دوال التهيئة إلى src/core/system_initialization_extended.py
from core.system_initialization import (
    migrate_config_to_database,
    initialize_system_extended
)

# إعدادات البوت
BOT_CONFIG = {
    'owner_id': SystemConfig.DEVELOPER_ID,  # استخدام المعرف المركزي دائماً
    'backup_interval': int(os.getenv('BACKUP_INTERVAL', '24')),
    'stats_interval': int(os.getenv('STATS_INTERVAL', '12')),
}


async def run_bot():
    """تشغيل البوت الرئيسي"""
    try:
        global api_manager, encryption_key, subscription_system, _system_initialized, _system_components

        # التحقق من قاعدة البيانات أولاً
        if not db:
            logger.error("❌ قاعدة البيانات غير متوفرة - لا يمكن تشغيل البوت")
            return

        logger.info("✅ قاعدة البيانات متوفرة")

        # التحقق من التهيئة لتجنب التهيئة المتكررة
        if _system_initialized and _system_components:
            logger.info("ℹ️ النظام مُهيأ بالفعل، استخدام المكونات المحفوظة")
            # استخدام المكونات المحفوظة
            encryption_key = _system_components['encryption_key']
            api_manager = _system_components['api_manager']
            subscription_system = _system_components['subscription_system']
        else:
            logger.info("🔧 جاري تهيئة النظام للمرة الأولى...")

            # نقل الإعدادات من ملف التكوين إلى قاعدة البيانات
            try:
                await migrate_config_to_database(system_settings)
                logger.info("✅ تم نقل الإعدادات إلى قاعدة البيانات")
            except Exception as config_error:
                logger.warning(f"⚠️ فشل في نقل الإعدادات: {str(config_error)}")
                # المتابعة بدون نقل الإعدادات

            # تهيئة النظام الموسع أولاً لإنشاء api_manager و encryption_key
            logger.info("🔧 جاري تهيئة النظام الموسع...")

            # استيراد الدوال المطلوبة للتهيئة الموسعة
            from integrations.binance_manager import BinanceManager
            from integrations.binance_verifier import BinanceTransactionVerifier
            from services.auto_transaction_verifier import AutomaticTransactionVerifier
            from analysis.integration_wrapper import EnhancedAnalysisWrapper
            from services.subscription_system import initialize_subscription_system
            from analysis.analysis_helpers import initialize_analysis_helpers
            from services.alert_service import initialize_alert_service
            from services.api_management import initialize_api_management_service
            from services.user_management import initialize_user_management
            from services.news_system import initialize_news_system
            # استخدام transaction_service المُعرف مسبقاً في main.py

            # تهيئة النظام الموسع
            system_components = await initialize_system_extended(
                db,
                get_or_create_encryption_key,
                APIManager,
                BinanceManager(),
                BinanceTransactionVerifier,
                AutomaticTransactionVerifier,
                EnhancedAnalysisWrapper,
                initialize_subscription_system,
                free_day_system,
                initialize_analysis_helpers,
                initialize_alert_service,
                initialize_api_management_service,
                initialize_user_management,
                initialize_news_system,
                ca,
                user_states,
                get_text,
                show_main_menu,
                generate_stats_report,
                show_language_selection,
                show_terms_and_conditions,
                SystemConfig,
                transaction_service,
                create_analysis_text,
                load_user_settings,
                save_user_settings,
                specialized_handlers,
                delete_message_after_delay,
                show_api_instructions,
                analyze_symbol
            )

            if system_components:
                # تحديث المتغيرات العامة
                encryption_key = system_components['encryption_key']
                api_manager = system_components['api_manager']
                subscription_system = system_components['subscription_system']

                # حفظ المكونات للاستخدام المستقبلي
                _system_components = system_components

                # تحديث خدمة المعاملات مع نظام الاشتراكات
                transaction_service.subscription_system = subscription_system

                # تعيين علامة التهيئة
                _system_initialized = True
                logger.info("✅ تم تهيئة النظام الموسع بنجاح")

                # التأكد من تعيين حالة التهيئة في النظام الموسع أيضاً
                try:
                    from core.system_initialization_extended import force_system_initialization
                    force_system_initialization(system_components)
                    logger.info("✅ تم تعيين حالة التهيئة في النظام الموسع")
                except Exception as force_error:
                    logger.warning(f"⚠️ فشل في تعيين حالة التهيئة: {str(force_error)}")

                logger.info("🎉 تم اكتمال تهيئة النظام بالكامل")

                # تهيئة نظام الأخبار التلقائي الجديد
                logger.info("🤖 جاري تهيئة نظام الأخبار التلقائي الذكي...")
                try:
                    from services.automatic_news_integration import initialize_automatic_news_integration

                    # الحصول على bot instance من النظام
                    bot_instance = None
                    try:
                        from core.telegram_bot import TelegramBot
                        bot_instance = TelegramBot()
                        await bot_instance.setup()
                    except Exception as bot_setup_error:
                        logger.warning(f"⚠️ لم يتم إعداد البوت بعد للنظام التلقائي: {str(bot_setup_error)}")

                    # تهيئة نظام التكامل التلقائي
                    automatic_integration = await initialize_automatic_news_integration(db, bot_instance)

                    if automatic_integration:
                        # بدء تشغيل الأنظمة التلقائية
                        start_success = await automatic_integration.start_automatic_systems()

                        if start_success:
                            logger.info("✅ تم تشغيل نظام الأخبار التلقائي بنجاح!")

                            # إضافة النظام التلقائي إلى مكونات النظام
                            _system_components['automatic_news_integration'] = automatic_integration

                            # عرض حالة النظام
                            integration_status = automatic_integration.get_integration_status()
                            logger.info(f"📊 حالة التكامل: {integration_status}")

                            # فحص صحة النظام
                            system_health = await automatic_integration.get_system_health()
                            logger.info(f"🏥 صحة النظام: {system_health['overall_status']}")

                        else:
                            logger.error("❌ فشل في بدء تشغيل نظام الأخبار التلقائي")
                    else:
                        logger.error("❌ فشل في تهيئة نظام الأخبار التلقائي")

                except Exception as auto_news_error:
                    logger.error(f"❌ خطأ في تهيئة نظام الأخبار التلقائي: {str(auto_news_error)}")
                    import traceback
                    logger.error(traceback.format_exc())
                    # المتابعة بدون النظام التلقائي

            else:
                logger.error("❌ فشل في تهيئة النظام الموسع")
                # في حالة فشل التهيئة، نحاول تشغيل البوت الأساسي فقط
                logger.info("🔄 محاولة تشغيل البوت الأساسي...")
                try:
                    from core.telegram_bot import TelegramBot
                    bot = TelegramBot()
                    await bot.setup()
                    await bot.start()
                    logger.info("✅ تم تشغيل البوت الأساسي بنجاح")
                    await bot.run_forever()
                except Exception as basic_bot_error:
                    logger.error(f"❌ فشل في تشغيل البوت الأساسي: {str(basic_bot_error)}")
                return

        # تهيئة مدير المعاملات
        await initialize_transaction_manager()

        # تهيئة نظام التحقق التلقائي من المدفوعات
        # استخدام الإعدادات من قاعدة البيانات
        paypal_client_id = system_settings.get("PAYPAL_CLIENT_ID", sensitive=True)
        paypal_secret = system_settings.get("PAYPAL_CLIENT_SECRET", sensitive=True)
        is_sandbox = system_settings.get("PAYPAL_SANDBOX_MODE", False)  # تغيير القيمة الافتراضية إلى False (وضع الإنتاج)

        # إذا لم تكن الإعدادات موجودة في قاعدة البيانات، نستخدم المتغيرات البيئية
        if not paypal_client_id:
            paypal_client_id = get_env_var("PAYPAL_CLIENT_ID")
            system_settings.set("PAYPAL_CLIENT_ID", paypal_client_id, sensitive=True)

        if not paypal_secret:
            paypal_secret = get_env_var("PAYPAL_CLIENT_SECRET")
            system_settings.set("PAYPAL_CLIENT_SECRET", paypal_secret, sensitive=True)

        if is_sandbox is None:
            is_sandbox = get_env_var("PAYPAL_SANDBOX_MODE", "false").lower() == "true"  # تغيير القيمة الافتراضية إلى false
            system_settings.set("PAYPAL_SANDBOX_MODE", is_sandbox)

        payment_verifier = AutomaticPaymentVerifier(
            db=db,
            paypal_client_id=paypal_client_id,
            paypal_secret=paypal_secret,
            is_sandbox=is_sandbox,
            bot_token=TOKEN  # إضافة رمز البوت لإرسال الإشعارات
        )
 
        # بدء نظام التحقق التلقائي
        # استخدام منطقة زمنية من مكتبة pytz
        await payment_verifier.start()
        logger.info("✅ تم بدء نظام التحقق التلقائي من المدفوعات")

        # تم تعطيل مجدول تشغيل الرابط في هذا الملف لأنه يتم تنفيذه في ملف main_wrapper.py
        logger.info("ℹ️ مجدول تشغيل رابط فحص الصحة يتم إدارته بواسطة main_wrapper.py")



        # تهيئة معالجات الواجهة الرئيسية
        logger.info("🔧 جاري تهيئة معالجات الواجهة الرئيسية...")

        # الحصول على المكونات المطلوبة من النظام الموسع
        if 'system_components' in locals():
            # استخراج المكونات المطلوبة
            subscription_system = system_components.get('subscription_system')
            api_manager = system_components.get('api_manager')
            enhanced_analyzer = system_components.get('enhanced_analyzer')
            firestore_cache = system_components.get('firestore_cache')

            # تهيئة معالجات الواجهة الرئيسية مع جميع التبعيات
            initialize_main_handlers(
                db,  # firestore_db
                subscription_system,  # subscription_sys
                api_manager,  # api_mgr
                user_states,  # user_st
                {},  # user_set (إعدادات المستخدم)
                enhanced_analyzer,  # enhanced_analyzer_obj
                ca,  # crypto_analysis
                get_text,  # get_text_func
                show_main_menu,  # show_main_menu_func
                show_language_selection,  # show_language_selection_func
                show_terms_and_conditions,  # show_terms_and_conditions_func
                show_enhanced_analysis_menu,  # show_enhanced_analysis_menu_func
                show_enhanced_settings,  # show_enhanced_settings_func
                show_trading_style_options,  # show_trading_style_options_func
                show_analysis_type_options,  # show_analysis_type_options_func
                show_enhanced_analysis_explanation,  # show_enhanced_analysis_explanation_func
                show_analysis_comparison,  # show_analysis_comparison_func
                show_upgrade_info,  # show_upgrade_info_func
                show_analysis_type_settings,  # show_analysis_type_settings_func
                setup_api_keys,  # setup_api_keys_func
                show_platform_selection,  # show_platform_selection_func
                show_api_instructions,  # show_api_instructions_func
                delete_api_keys_ui,  # delete_api_keys_ui_func
                analyze_symbol,  # analyze_symbol_func
                analyze_symbol_enhanced,  # analyze_symbol_enhanced_func
                process_custom_alert,  # process_custom_alert_func
                handle_paypal_payment,  # handle_paypal_payment_func
                handle_payment_verification,  # handle_payment_verification_func
                manage_free_day_settings,  # manage_free_day_settings_func
                set_free_day,  # set_free_day_func
                verify_binance_api,  # verify_binance_api_func
                verify_gemini_api,  # verify_gemini_api_func
                api_setup_command,  # api_setup_command_func
                handle_learn_trading_ai,  # handle_learn_trading_ai_func
                handle_ask_ai_tutor_button,  # handle_ask_ai_tutor_button_func
                generate_and_send_chapter,  # generate_and_send_chapter_func
                start_quiz,  # start_quiz_func
                show_quiz_results_or_next_steps,  # show_quiz_results_or_next_steps_func
                show_supplementary_chapters,  # show_supplementary_chapters_func
                generate_and_send_supplementary_chapter,  # generate_and_send_supplementary_chapter_func
                handle_message_for_ai_tutor,  # handle_message_for_ai_tutor_func
                firestore_cache  # firestore_cache_obj
            )
            logger.info("✅ تم تهيئة معالجات الواجهة الرئيسية بنجاح")

            # تهيئة وحدة التحليل المحسن
            logger.info("🔧 جاري تهيئة وحدة التحليل المحسن...")
            initialize_enhanced_analysis(
                subscription_system,  # subscription_sys
                enhanced_analyzer,  # enhanced_analyzer_instance
                api_manager,  # api_mgr
                db,  # database
                ca,  # crypto_analysis
                user_states,  # user_states_dict
                show_main_menu,  # show_main_menu_func
                show_api_instructions,  # show_api_instructions_func
                analyze_symbol  # analyze_symbol_func
            )
            logger.info("✅ تم تهيئة وحدة التحليل المحسن بنجاح")

            # تهيئة التبعيات في معالجات القوائم
            logger.info("🔧 جاري تهيئة التبعيات في معالجات القوائم...")
            from handlers.menu_handlers import set_dependencies as set_menu_dependencies
            set_menu_dependencies(subscription_system, ENHANCED_ANALYSIS_EXPLANATION)

            # تهيئة التبعيات في معالجات الإعدادات
            from handlers.settings_handlers import set_dependencies as set_settings_dependencies
            set_settings_dependencies(subscription_system, db, firestore_cache)
            logger.info("✅ تم تهيئة التبعيات في المعالجات بنجاح")
        else:
            logger.warning("⚠️ لم يتم العثور على مكونات النظام، سيتم تأجيل تهيئة معالجات الواجهة الرئيسية")

        # تم نقل تهيئة وحدات التحليل إلى initialize_system_extended
        # لضمان تمرير جميع المعاملات المطلوبة بشكل صحيح

        # إنشاء وتشغيل البوت بعد اكتمال جميع التهيئات
        logger.info("🤖 جاري إنشاء البوت...")
        try:
            # استيراد كلاس TelegramBot من الوحدة الجديدة
            from core.telegram_bot import TelegramBot
            bot = TelegramBot()

            logger.info("🔧 جاري إعداد البوت الأساسي...")
            await bot.setup()

            # تهيئة معالجات البوت بعد تهيئة النظام
            logger.info("🔧 جاري تهيئة معالجات البوت...")
            handlers_setup = await bot.setup_handlers_after_system_init()
            if not handlers_setup:
                logger.error("❌ فشل في تهيئة معالجات البوت")
                return

            logger.info("🚀 جاري بدء تشغيل البوت...")

            # بدء تشغيل البوت
            await bot.start()

            # إشعار نهائي بأن النظام جاهز تماماً
            logger.info("🎉 النظام جاهز تماماً لاستقبال الطلبات!")
            logger.info("✅ جميع المعالجات مُهيأة والبوت يعمل بكامل طاقته")

            # تشغيل البوت إلى أجل غير مسمى
            logger.info("🔄 جاري تشغيل البوت بشكل مستمر...")
            await bot.run_forever()

        except Exception as bot_error:
            logger.error(f"❌ خطأ في تشغيل البوت: {str(bot_error)}")
            import traceback
            logger.error(traceback.format_exc())

            # في حالة فشل تشغيل البوت، نحافظ على خادم الصحة يعمل
            logger.info("🔄 الحفاظ على خادم الصحة يعمل...")

            # محاولة إعادة تشغيل البوت بعد تأخير
            retry_count = 0
            max_retries = 3

            while retry_count < max_retries:
                try:
                    retry_count += 1
                    logger.info(f"🔄 محاولة إعادة تشغيل البوت ({retry_count}/{max_retries})...")
                    await asyncio.sleep(30)  # انتظار 30 ثانية قبل المحاولة

                    # إعادة إنشاء البوت
                    bot = TelegramBot()
                    await bot.setup()
                    await bot.setup_handlers_after_system_init()
                    await bot.start()
                    await bot.run_forever()

                    # إذا وصلنا هنا، فقد نجحت إعادة التشغيل
                    break

                except Exception as retry_error:
                    logger.error(f"❌ فشلت محاولة إعادة التشغيل {retry_count}: {str(retry_error)}")
                    if retry_count >= max_retries:
                        logger.error("❌ فشلت جميع محاولات إعادة التشغيل")
                        break

            # إذا فشلت جميع المحاولات، نحافظ على خادم الصحة فقط
            logger.info("🔄 الحفاظ على خادم الصحة يعمل فقط...")
            while True:
                await asyncio.sleep(60)  # انتظار دقيقة واحدة

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {str(e)}")
        raise e

# تم نقل دالة migrate_config_to_database إلى src/core/system_initialization_extended.py

def main():
    """النقطة الرئيسية لتشغيل التطبيق"""
    try:
        # التحقق من متغيرات البيئة الأساسية
        bot_token = os.getenv('BOT_TOKEN')
        if not bot_token:
            logger.error("❌ BOT_TOKEN غير محدد في متغيرات البيئة")
            return

        logger.info(f"✅ تم العثور على BOT_TOKEN: {bot_token[:10]}...")

        # تهيئة حلقة الأحداث
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # تعيين مهلة زمنية أطول للعمليات
        logger.info("🔄 جاري تهيئة حلقة الأحداث مع مهلة زمنية أطول...")

        # بدء خادم فحص الصحة للاستضافة على Koyeb
        from server import run_health_server
        health_server_thread = run_health_server()
        logger.info("🌐 تم بدء خادم فحص الصحة للاستضافة على Koyeb")

        # تم حذف مراقبة الذاكرة لأن الاستضافة تتولى هذه المهام

        # تشغيل البوت
        logger.info("🚀 بدء تشغيل البوت...")
        # إنشاء مهمة البوت وتشغيل حلقة الأحداث إلى أجل غير مسمى
        task = loop.create_task(run_bot())
        loop.run_forever()

    except KeyboardInterrupt:
        logger.info("تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {str(e)}")
        # طباعة تتبع الاستثناء للمساعدة في تشخيص المشكلة
        import traceback
        logger.error(traceback.format_exc())

        # في حالة الخطأ، نحافظ على خادم الصحة يعمل
        logger.info("🔄 محاولة الحفاظ على خادم الصحة يعمل...")
        try:
            while True:
                time.sleep(60)  # انتظار دقيقة واحدة
        except KeyboardInterrupt:
            logger.info("تم إيقاف البرنامج")
    finally:
        if 'loop' in locals():
            # إيقاف المهام المعلقة
            if 'task' in locals() and not task.done():
                task.cancel()

            # إغلاق حلقة الأحداث بشكل آمن
            try:
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    task.cancel()
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
            except Exception as cleanup_error:
                logger.warning(f"تحذير في تنظيف المهام: {cleanup_error}")
            finally:
                loop.close()

if __name__ == "__main__":
    main()