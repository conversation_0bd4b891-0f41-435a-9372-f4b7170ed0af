"""
نظام التحليل متعدد الإطارات الزمنية المحسن
Enhanced Multi-Timeframe Analysis System

هذا الملف يحتوي على نظام تحليل متطور يستخدم:
1. التحليل الهرمي المتكامل (من الأعلى للأسفل)
2. مؤشرات متخصصة لكل إطار زمني
3. نظام تأكيد الإشارات متعدد المستويات
4. استراتيجيات مخصصة لأنماط التداول
5. تقييم المخاطر متعدد الأبعاد
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple, Optional, Union
from enum import Enum
import asyncio
import json

# إعداد السجل
logger = logging.getLogger(__name__)

class TradingStyle(Enum):
    """أنماط التداول المختلفة"""
    SCALPING = "scalping"
    DAY_TRADING = "day_trading"
    SWING_TRADING = "swing_trading"
    POSITION = "position"

class MarketCondition(Enum):
    """حالات السوق"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    LOW_VOLATILITY = "low_volatility"

class SignalStrength(Enum):
    """قوة الإشارة"""
    VERY_STRONG = 5
    STRONG = 4
    MODERATE = 3
    WEAK = 2
    VERY_WEAK = 1

class TimeframeConfig:
    """تكوين الإطارات الزمنية المتدرجة"""
    
    # إطارات زمنية متدرجة ومترابطة
    SMART_TIMEFRAMES = {
        TradingStyle.SCALPING: ['1m', '5m', '15m'],
        TradingStyle.DAY_TRADING: ['15m', '1h', '4h'],
        TradingStyle.SWING_TRADING: ['4h', '1d', '1w'],
        TradingStyle.POSITION: ['1d', '1w', '1M']
    }
    
    # مؤشرات متخصصة حسب الإطار الزمني
    TIMEFRAME_INDICATORS = {
        'short_term': {
            'primary': ['rsi', 'stoch_k', 'stoch_d', 'williams_r', 'cci'],
            'secondary': ['bb_upper', 'bb_lower', 'bb_middle'],
            'volume': ['volume_sma', 'volume_ratio']
        },
        'medium_term': {
            'primary': ['macd', 'macd_signal', 'ema20', 'ema50', 'adx'],
            'secondary': ['plus_di', 'minus_di', 'atr'],
            'trend': ['parabolic_sar', 'trend_strength']
        },
        'long_term': {
            'primary': ['ichimoku_tenkan', 'ichimoku_kijun', 'ichimoku_senkou_a', 'ichimoku_senkou_b'],
            'secondary': ['sma50', 'sma200', 'ema100'],
            'structure': ['pivot_points', 'fibonacci_levels']
        }
    }
    
    # أوزان الإطارات الزمنية حسب نمط التداول (محسنة لتمييز أكبر)
    TIMEFRAME_WEIGHTS = {
        TradingStyle.SCALPING: {'1m': 0.7, '5m': 0.2, '15m': 0.1},
        TradingStyle.DAY_TRADING: {'15m': 0.1, '1h': 0.7, '4h': 0.2},
        TradingStyle.SWING_TRADING: {'4h': 0.1, '1d': 0.7, '1w': 0.2},
        TradingStyle.POSITION: {'1d': 0.1, '1w': 0.3, '1M': 0.6}
    }

    # المؤشرات المتخصصة لكل نمط تداول
    SPECIALIZED_INDICATORS = {
        TradingStyle.SCALPING: {
            'primary': ['rsi', 'stochastic', 'williams_r', 'cci', 'momentum'],
            'secondary': ['bollinger_bands', 'rate_of_change', 'volume_spike'],
            'focus': 'momentum_oscillators',
            'entry_threshold': 0.8,  # عتبة عالية للدخول السريع
            'exit_threshold': 0.6
        },
        TradingStyle.DAY_TRADING: {
            'primary': ['macd', 'ema_cross', 'volume_analysis', 'support_resistance'],
            'secondary': ['rsi', 'adx', 'price_action', 'breakout_patterns'],
            'focus': 'trend_following',
            'entry_threshold': 0.7,
            'exit_threshold': 0.5
        },
        TradingStyle.SWING_TRADING: {
            'primary': ['ichimoku', 'moving_averages', 'fibonacci', 'chart_patterns'],
            'secondary': ['macd', 'volume_profile', 'market_structure', 'divergence'],
            'focus': 'trend_reversal',
            'entry_threshold': 0.6,
            'exit_threshold': 0.4
        },
        TradingStyle.POSITION: {
            'primary': ['long_term_ma', 'fundamental_strength', 'market_cycles', 'macro_trends'],
            'secondary': ['relative_strength', 'sector_analysis', 'correlation', 'value_metrics'],
            'focus': 'long_term_value',
            'entry_threshold': 0.5,
            'exit_threshold': 0.3
        }
    }

class AdvancedIndicators:
    """حساب المؤشرات الفنية المتقدمة"""
    
    @staticmethod
    def calculate_williams_r(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """حساب مؤشر Williams %R"""
        try:
            highest_high = high.rolling(window=period).max()
            lowest_low = low.rolling(window=period).min()
            williams_r = -100 * (highest_high - close) / (highest_high - lowest_low)
            return williams_r
        except Exception as e:
            logger.error(f"خطأ في حساب Williams %R: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_cci(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 20) -> pd.Series:
        """حساب مؤشر Commodity Channel Index"""
        try:
            typical_price = (high + low + close) / 3
            sma_tp = typical_price.rolling(window=period).mean()
            mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
            cci = (typical_price - sma_tp) / (0.015 * mad)
            return cci
        except Exception as e:
            logger.error(f"خطأ في حساب CCI: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_atr(high: pd.Series, low: pd.Series, close: pd.Series, period: int = 14) -> pd.Series:
        """حساب مؤشر Average True Range"""
        try:
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = true_range.rolling(window=period).mean()
            return atr
        except Exception as e:
            logger.error(f"خطأ في حساب ATR: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_parabolic_sar(high: pd.Series, low: pd.Series, close: pd.Series, 
                               af_start: float = 0.02, af_increment: float = 0.02, af_max: float = 0.2) -> pd.Series:
        """حساب مؤشر Parabolic SAR"""
        try:
            length = len(close)
            sar = pd.Series(index=close.index, dtype=float)
            trend = pd.Series(index=close.index, dtype=int)
            af = pd.Series(index=close.index, dtype=float)
            ep = pd.Series(index=close.index, dtype=float)
            
            # تهيئة القيم الأولى
            sar.iloc[0] = low.iloc[0]
            trend.iloc[0] = 1  # 1 للاتجاه الصاعد، -1 للاتجاه الهابط
            af.iloc[0] = af_start
            ep.iloc[0] = high.iloc[0]
            
            for i in range(1, length):
                if trend.iloc[i-1] == 1:  # اتجاه صاعد
                    sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])
                    
                    if low.iloc[i] <= sar.iloc[i]:
                        trend.iloc[i] = -1
                        sar.iloc[i] = ep.iloc[i-1]
                        ep.iloc[i] = low.iloc[i]
                        af.iloc[i] = af_start
                    else:
                        trend.iloc[i] = 1
                        if high.iloc[i] > ep.iloc[i-1]:
                            ep.iloc[i] = high.iloc[i]
                            af.iloc[i] = min(af.iloc[i-1] + af_increment, af_max)
                        else:
                            ep.iloc[i] = ep.iloc[i-1]
                            af.iloc[i] = af.iloc[i-1]
                else:  # اتجاه هابط
                    sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])
                    
                    if high.iloc[i] >= sar.iloc[i]:
                        trend.iloc[i] = 1
                        sar.iloc[i] = ep.iloc[i-1]
                        ep.iloc[i] = high.iloc[i]
                        af.iloc[i] = af_start
                    else:
                        trend.iloc[i] = -1
                        if low.iloc[i] < ep.iloc[i-1]:
                            ep.iloc[i] = low.iloc[i]
                            af.iloc[i] = min(af.iloc[i-1] + af_increment, af_max)
                        else:
                            ep.iloc[i] = ep.iloc[i-1]
                            af.iloc[i] = af.iloc[i-1]
            
            return sar
        except Exception as e:
            logger.error(f"خطأ في حساب Parabolic SAR: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_trend_strength(close: pd.Series, period: int = 20) -> pd.Series:
        """حساب قوة الاتجاه"""
        try:
            # حساب المتوسط المتحرك
            sma = close.rolling(window=period).mean()
            
            # حساب انحراف السعر عن المتوسط
            deviation = (close - sma) / sma * 100
            
            # حساب قوة الاتجاه بناءً على الانحراف والاتساق
            trend_strength = deviation.rolling(window=period).apply(
                lambda x: np.mean(x) * (1 - np.std(x) / (np.abs(np.mean(x)) + 1))
            )
            
            return trend_strength
        except Exception as e:
            logger.error(f"خطأ في حساب قوة الاتجاه: {str(e)}")
            return pd.Series([np.nan] * len(close))
    
    @staticmethod
    def calculate_volume_indicators(volume: pd.Series, close: pd.Series, period: int = 20) -> Dict[str, pd.Series]:
        """حساب مؤشرات الحجم"""
        try:
            volume_sma = volume.rolling(window=period).mean()
            volume_ratio = volume / volume_sma
            
            # حساب مؤشر On-Balance Volume
            obv = pd.Series(index=close.index, dtype=float)
            obv.iloc[0] = volume.iloc[0]
            
            for i in range(1, len(close)):
                if close.iloc[i] > close.iloc[i-1]:
                    obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
                elif close.iloc[i] < close.iloc[i-1]:
                    obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
                else:
                    obv.iloc[i] = obv.iloc[i-1]
            
            return {
                'volume_sma': volume_sma,
                'volume_ratio': volume_ratio,
                'obv': obv
            }
        except Exception as e:
            logger.error(f"خطأ في حساب مؤشرات الحجم: {str(e)}")
            return {
                'volume_sma': pd.Series([np.nan] * len(volume)),
                'volume_ratio': pd.Series([np.nan] * len(volume)),
                'obv': pd.Series([np.nan] * len(volume))
            }

class HierarchicalAnalysis:
    """نظام التحليل الهرمي المتكامل مع تخصص لكل نمط تداول"""

    def __init__(self, trading_style: TradingStyle = TradingStyle.DAY_TRADING):
        self.trading_style = trading_style
        self.timeframes = TimeframeConfig.SMART_TIMEFRAMES[trading_style]
        self.weights = TimeframeConfig.TIMEFRAME_WEIGHTS[trading_style]
        self.specialized_config = TimeframeConfig.SPECIALIZED_INDICATORS[trading_style]
        self.advanced_indicators = AdvancedIndicators()

    def analyze_top_down(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل هرمي من الأعلى للأسفل مع تخصص لنمط التداول"""
        try:
            # استخدام التحليل الموحد الجديد بدلاً من الأنماط المنفصلة
            return self._analyze_unified_strategy(timeframes_data)

        except Exception as e:
            logger.error(f"خطأ في التحليل الهرمي: {str(e)}")
            return {}

    def _analyze_unified_strategy(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        تحليل موحد يجمع جميع الأنماط الأربعة في استراتيجية واحدة شاملة

        يجمع هذا التحليل:
        - مؤشرات المضاربة السريعة (الزخم والإشارات السريعة)
        - مؤشرات التداول اليومي (اتباع الاتجاه والكسر)
        - مؤشرات التداول المتأرجح (انعكاس الاتجاه والأنماط)
        - مؤشرات الاستثمار طويل المدى (القيمة والاتجاهات الكبرى)
        """
        try:
            logger.info("🔄 بدء التحليل الموحد الشامل")

            # جمع جميع الإشارات من الأنماط الأربعة
            all_signals = []
            analysis_components = {}

            # 1. تحليل المضاربة السريعة (إشارات سريعة)
            scalping_analysis = self._get_scalping_signals(timeframes_data)
            if scalping_analysis:
                all_signals.extend(scalping_analysis.get('signals', []))
                analysis_components['scalping'] = scalping_analysis
                logger.info(f"✅ تحليل المضاربة السريعة: {len(scalping_analysis.get('signals', []))} إشارة")

            # 2. تحليل التداول اليومي (اتجاهات وكسر)
            day_trading_analysis = self._get_day_trading_signals(timeframes_data)
            if day_trading_analysis:
                all_signals.extend(day_trading_analysis.get('signals', []))
                analysis_components['day_trading'] = day_trading_analysis
                logger.info(f"✅ تحليل التداول اليومي: {len(day_trading_analysis.get('signals', []))} إشارة")

            # 3. تحليل التداول المتأرجح (انعكاسات وأنماط)
            swing_analysis = self._get_swing_signals(timeframes_data)
            if swing_analysis:
                all_signals.extend(swing_analysis.get('signals', []))
                analysis_components['swing_trading'] = swing_analysis
                logger.info(f"✅ تحليل التداول المتأرجح: {len(swing_analysis.get('signals', []))} إشارة")

            # 4. تحليل الاستثمار طويل المدى (قيمة واتجاهات كبرى)
            position_analysis = self._get_position_signals(timeframes_data)
            if position_analysis:
                all_signals.extend(position_analysis.get('signals', []))
                analysis_components['position_trading'] = position_analysis
                logger.info(f"✅ تحليل الاستثمار طويل المدى: {len(position_analysis.get('signals', []))} إشارة")

            # 5. دمج وتحليل جميع الإشارات
            unified_recommendation = self._synthesize_unified_signals(all_signals, analysis_components)

            # 6. تقييم شامل للمخاطر
            unified_risk_assessment = self._assess_unified_risks(timeframes_data, analysis_components)

            # 7. إنتاج التوصية النهائية الموحدة
            final_result = {
                'recommendation': unified_recommendation,
                'analysis_components': analysis_components,
                'risk_assessment': unified_risk_assessment,
                'total_signals': len(all_signals),
                'analysis_type': 'unified_comprehensive',
                'timeframes_covered': list(timeframes_data.keys()),
                'confidence_factors': self._calculate_unified_confidence(analysis_components)
            }

            logger.info(f"🎯 التحليل الموحد مكتمل: {len(all_signals)} إشارة إجمالية")
            return final_result

        except Exception as e:
            logger.error(f"خطأ في التحليل الموحد: {str(e)}")
            return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

    def _analyze_scalping_strategy(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل متخصص للمضاربة السريعة - التركيز على الزخم والإشارات السريعة"""
        try:
            # التركيز على الإطار الزمني الأقصر (1m)
            primary_data = timeframes_data.get('1m', {})
            secondary_data = timeframes_data.get('5m', {})

            if not primary_data:
                return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

            # مؤشرات الزخم السريعة
            momentum_signals = []

            # RSI للإشارات السريعة
            rsi = primary_data.get('rsi', 50)
            if rsi > 70:
                momentum_signals.append({'type': 'rsi_overbought', 'strength': 4, 'action': 'sell'})
            elif rsi < 30:
                momentum_signals.append({'type': 'rsi_oversold', 'strength': 4, 'action': 'buy'})

            # Stochastic للتأكيد
            stoch_k = primary_data.get('stoch_k', 50)
            stoch_d = primary_data.get('stoch_d', 50)
            if stoch_k > 80 and stoch_d > 80:
                momentum_signals.append({'type': 'stoch_overbought', 'strength': 3, 'action': 'sell'})
            elif stoch_k < 20 and stoch_d < 20:
                momentum_signals.append({'type': 'stoch_oversold', 'strength': 3, 'action': 'buy'})

            # Williams %R للإشارات الدقيقة
            williams_r = primary_data.get('williams_r', -50)
            if williams_r > -20:
                momentum_signals.append({'type': 'williams_overbought', 'strength': 3, 'action': 'sell'})
            elif williams_r < -80:
                momentum_signals.append({'type': 'williams_oversold', 'strength': 3, 'action': 'buy'})

            # تحليل الحجم للتأكيد
            volume_ratio = primary_data.get('volume_ratio', 1)
            if volume_ratio > 2:  # حجم عالي
                for signal in momentum_signals:
                    signal['strength'] += 1  # تعزيز الإشارة بالحجم

            # تحديد الإشارة الأقوى
            if momentum_signals:
                strongest_signal = max(momentum_signals, key=lambda x: x['strength'])
                confidence = min(strongest_signal['strength'] * 20, 95)

                return {
                    'recommendation': {
                        'action': strongest_signal['action'],
                        'confidence_level': 'high' if confidence > 80 else 'medium' if confidence > 60 else 'low',
                        'entry_quality': confidence,
                        'strategy_type': 'scalping_momentum'
                    },
                    'signals': momentum_signals,
                    'analysis_focus': 'momentum_oscillators'
                }

            return {'recommendation': {'action': 'hold', 'confidence_level': 'low'}}

        except Exception as e:
            logger.error(f"خطأ في تحليل المضاربة السريعة: {str(e)}")
            return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

    def _analyze_day_trading_strategy(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل متخصص للتداول اليومي - التركيز على اتباع الاتجاه والكسر"""
        try:
            # التركيز على الإطار الزمني الساعي
            primary_data = timeframes_data.get('1h', {})
            context_data = timeframes_data.get('4h', {})

            if not primary_data:
                return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

            trend_signals = []

            # MACD لاتباع الاتجاه
            macd = primary_data.get('macd', 0)
            macd_signal = primary_data.get('macd_signal', 0)
            macd_histogram = primary_data.get('macd_histogram', 0)

            if macd > macd_signal and macd_histogram > 0:
                trend_signals.append({'type': 'macd_bullish', 'strength': 4, 'action': 'buy'})
            elif macd < macd_signal and macd_histogram < 0:
                trend_signals.append({'type': 'macd_bearish', 'strength': 4, 'action': 'sell'})

            # EMA Crossover للاتجاه
            ema20 = primary_data.get('ema20', 0)
            ema50 = primary_data.get('ema50', 0)
            price = primary_data.get('price', 0)

            if ema20 > ema50 and price > ema20:
                trend_signals.append({'type': 'ema_bullish_trend', 'strength': 3, 'action': 'buy'})
            elif ema20 < ema50 and price < ema20:
                trend_signals.append({'type': 'ema_bearish_trend', 'strength': 3, 'action': 'sell'})

            # ADX لقوة الاتجاه
            adx = primary_data.get('adx', 0)
            plus_di = primary_data.get('plus_di', 0)
            minus_di = primary_data.get('minus_di', 0)

            if adx > 25:  # اتجاه قوي
                if plus_di > minus_di:
                    trend_signals.append({'type': 'adx_strong_bullish', 'strength': 3, 'action': 'buy'})
                else:
                    trend_signals.append({'type': 'adx_strong_bearish', 'strength': 3, 'action': 'sell'})

            # تحليل مستويات الدعم والمقاومة
            high_24h = primary_data.get('high_24h', 0)
            low_24h = primary_data.get('low_24h', 0)

            if price and high_24h and low_24h:
                price_position = (price - low_24h) / (high_24h - low_24h)
                if price_position > 0.8:  # قرب المقاومة
                    trend_signals.append({'type': 'near_resistance', 'strength': 2, 'action': 'sell'})
                elif price_position < 0.2:  # قرب الدعم
                    trend_signals.append({'type': 'near_support', 'strength': 2, 'action': 'buy'})

            # تحليل الحجم للتأكيد
            volume_ratio = primary_data.get('volume_ratio', 1)
            if volume_ratio > 1.5:  # حجم أعلى من المتوسط
                for signal in trend_signals:
                    signal['strength'] += 1

            # تحديد الإشارة الأقوى
            if trend_signals:
                buy_signals = [s for s in trend_signals if s['action'] == 'buy']
                sell_signals = [s for s in trend_signals if s['action'] == 'sell']

                buy_strength = sum(s['strength'] for s in buy_signals)
                sell_strength = sum(s['strength'] for s in sell_signals)

                if buy_strength > sell_strength and buy_strength > 6:
                    action = 'buy'
                    confidence = min(buy_strength * 10, 90)
                elif sell_strength > buy_strength and sell_strength > 6:
                    action = 'sell'
                    confidence = min(sell_strength * 10, 90)
                else:
                    action = 'hold'
                    confidence = 40

                return {
                    'recommendation': {
                        'action': action,
                        'confidence_level': 'high' if confidence > 75 else 'medium' if confidence > 50 else 'low',
                        'entry_quality': confidence,
                        'strategy_type': 'day_trading_trend'
                    },
                    'signals': trend_signals,
                    'analysis_focus': 'trend_following'
                }

            return {'recommendation': {'action': 'hold', 'confidence_level': 'low'}}

        except Exception as e:
            logger.error(f"خطأ في تحليل التداول اليومي: {str(e)}")
            return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

    def _analyze_swing_trading_strategy(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل متخصص للتداول المتأرجح - التركيز على انعكاس الاتجاه والأنماط"""
        try:
            # التركيز على الإطار الزمني اليومي
            primary_data = timeframes_data.get('1d', {})
            context_data = timeframes_data.get('1w', {})

            if not primary_data:
                return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

            reversal_signals = []

            # Ichimoku Cloud للاتجاه العام
            tenkan = primary_data.get('ichimoku_tenkan', 0)
            kijun = primary_data.get('ichimoku_kijun', 0)
            senkou_a = primary_data.get('ichimoku_senkou_a', 0)
            senkou_b = primary_data.get('ichimoku_senkou_b', 0)
            price = primary_data.get('price', 0)

            if all([tenkan, kijun, senkou_a, senkou_b, price]):
                cloud_top = max(senkou_a, senkou_b)
                cloud_bottom = min(senkou_a, senkou_b)

                if price > cloud_top and tenkan > kijun:
                    reversal_signals.append({'type': 'ichimoku_bullish', 'strength': 4, 'action': 'buy'})
                elif price < cloud_bottom and tenkan < kijun:
                    reversal_signals.append({'type': 'ichimoku_bearish', 'strength': 4, 'action': 'sell'})
                elif cloud_bottom < price < cloud_top:
                    reversal_signals.append({'type': 'ichimoku_neutral', 'strength': 1, 'action': 'hold'})

            # Moving Average Convergence للاتجاه طويل المدى
            sma50 = primary_data.get('sma50', 0)
            sma200 = primary_data.get('sma200', 0)
            ema20 = primary_data.get('ema20', 0)

            if sma50 and sma200 and ema20 and price:
                if sma50 > sma200 and price > ema20:  # Golden Cross + Price above EMA
                    reversal_signals.append({'type': 'golden_cross_bullish', 'strength': 4, 'action': 'buy'})
                elif sma50 < sma200 and price < ema20:  # Death Cross + Price below EMA
                    reversal_signals.append({'type': 'death_cross_bearish', 'strength': 4, 'action': 'sell'})

            # MACD للزخم متوسط المدى
            macd = primary_data.get('macd', 0)
            macd_signal = primary_data.get('macd_signal', 0)
            macd_histogram = primary_data.get('macd_histogram', 0)

            # البحث عن تقاطعات MACD
            if macd > macd_signal and macd_histogram > 0:
                if macd < 0:  # تقاطع صاعد تحت الصفر (إشارة قوية)
                    reversal_signals.append({'type': 'macd_bullish_reversal', 'strength': 4, 'action': 'buy'})
                else:
                    reversal_signals.append({'type': 'macd_bullish_continuation', 'strength': 2, 'action': 'buy'})
            elif macd < macd_signal and macd_histogram < 0:
                if macd > 0:  # تقاطع هابط فوق الصفر (إشارة قوية)
                    reversal_signals.append({'type': 'macd_bearish_reversal', 'strength': 4, 'action': 'sell'})
                else:
                    reversal_signals.append({'type': 'macd_bearish_continuation', 'strength': 2, 'action': 'sell'})

            # تحليل RSI للانعكاسات
            rsi = primary_data.get('rsi', 50)
            if rsi > 70:
                reversal_signals.append({'type': 'rsi_overbought_reversal', 'strength': 3, 'action': 'sell'})
            elif rsi < 30:
                reversal_signals.append({'type': 'rsi_oversold_reversal', 'strength': 3, 'action': 'buy'})
            elif 40 < rsi < 60:
                reversal_signals.append({'type': 'rsi_neutral', 'strength': 1, 'action': 'hold'})

            # تحليل الحجم للتأكيد
            volume_ratio = primary_data.get('volume_ratio', 1)
            if volume_ratio > 1.3:  # حجم أعلى من المتوسط
                for signal in reversal_signals:
                    if signal['action'] != 'hold':
                        signal['strength'] += 1

            # تحديد الإشارة الأقوى
            if reversal_signals:
                buy_signals = [s for s in reversal_signals if s['action'] == 'buy']
                sell_signals = [s for s in reversal_signals if s['action'] == 'sell']

                buy_strength = sum(s['strength'] for s in buy_signals)
                sell_strength = sum(s['strength'] for s in sell_signals)

                if buy_strength > sell_strength and buy_strength > 5:
                    action = 'buy'
                    confidence = min(buy_strength * 12, 85)
                elif sell_strength > buy_strength and sell_strength > 5:
                    action = 'sell'
                    confidence = min(sell_strength * 12, 85)
                else:
                    action = 'hold'
                    confidence = 35

                return {
                    'recommendation': {
                        'action': action,
                        'confidence_level': 'high' if confidence > 70 else 'medium' if confidence > 45 else 'low',
                        'entry_quality': confidence,
                        'strategy_type': 'swing_trading_reversal'
                    },
                    'signals': reversal_signals,
                    'analysis_focus': 'trend_reversal'
                }

            return {'recommendation': {'action': 'hold', 'confidence_level': 'low'}}

        except Exception as e:
            logger.error(f"خطأ في تحليل التداول المتأرجح: {str(e)}")
            return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

    def _analyze_position_trading_strategy(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل متخصص للاستثمار طويل المدى - التركيز على القيمة والاتجاهات الكبرى"""
        try:
            # التركيز على الإطار الزمني الأسبوعي والشهري
            primary_data = timeframes_data.get('1w', {})
            context_data = timeframes_data.get('1M', {})
            daily_data = timeframes_data.get('1d', {})

            if not primary_data:
                return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

            value_signals = []

            # تحليل المتوسطات المتحركة طويلة المدى
            sma50 = primary_data.get('sma50', 0)
            sma200 = primary_data.get('sma200', 0)
            ema100 = primary_data.get('ema100', 0)
            price = primary_data.get('price', 0)

            if all([sma50, sma200, ema100, price]):
                # Golden Cross طويل المدى
                if sma50 > sma200 and price > ema100:
                    value_signals.append({'type': 'long_term_golden_cross', 'strength': 5, 'action': 'buy'})
                # Death Cross طويل المدى
                elif sma50 < sma200 and price < ema100:
                    value_signals.append({'type': 'long_term_death_cross', 'strength': 5, 'action': 'sell'})

                # تحليل موقع السعر بالنسبة للمتوسطات
                ma_average = (sma50 + sma200 + ema100) / 3
                price_deviation = (price - ma_average) / ma_average * 100

                if price_deviation > 20:  # السعر أعلى بكثير من المتوسطات
                    value_signals.append({'type': 'overvalued', 'strength': 3, 'action': 'sell'})
                elif price_deviation < -20:  # السعر أقل بكثير من المتوسطات
                    value_signals.append({'type': 'undervalued', 'strength': 3, 'action': 'buy'})

            # تحليل الاتجاه الكبير باستخدام Ichimoku
            if context_data:  # بيانات شهرية
                monthly_tenkan = context_data.get('ichimoku_tenkan', 0)
                monthly_kijun = context_data.get('ichimoku_kijun', 0)
                monthly_senkou_a = context_data.get('ichimoku_senkou_a', 0)
                monthly_senkou_b = context_data.get('ichimoku_senkou_b', 0)

                if all([monthly_tenkan, monthly_kijun, monthly_senkou_a, monthly_senkou_b, price]):
                    monthly_cloud_top = max(monthly_senkou_a, monthly_senkou_b)
                    monthly_cloud_bottom = min(monthly_senkou_a, monthly_senkou_b)

                    if price > monthly_cloud_top and monthly_tenkan > monthly_kijun:
                        value_signals.append({'type': 'monthly_ichimoku_bullish', 'strength': 4, 'action': 'buy'})
                    elif price < monthly_cloud_bottom and monthly_tenkan < monthly_kijun:
                        value_signals.append({'type': 'monthly_ichimoku_bearish', 'strength': 4, 'action': 'sell'})

            # تحليل RSI طويل المدى للقيمة
            weekly_rsi = primary_data.get('rsi', 50)
            if weekly_rsi:
                if weekly_rsi < 30:  # منطقة ذروة البيع طويلة المدى
                    value_signals.append({'type': 'weekly_rsi_oversold', 'strength': 4, 'action': 'buy'})
                elif weekly_rsi > 70:  # منطقة ذروة الشراء طويلة المدى
                    value_signals.append({'type': 'weekly_rsi_overbought', 'strength': 4, 'action': 'sell'})

            # تحليل MACD طويل المدى
            weekly_macd = primary_data.get('macd', 0)
            weekly_macd_signal = primary_data.get('macd_signal', 0)

            if weekly_macd and weekly_macd_signal:
                if weekly_macd > weekly_macd_signal and weekly_macd < 0:
                    # تقاطع صاعد تحت الصفر - إشارة شراء قوية طويلة المدى
                    value_signals.append({'type': 'weekly_macd_bullish_reversal', 'strength': 4, 'action': 'buy'})
                elif weekly_macd < weekly_macd_signal and weekly_macd > 0:
                    # تقاطع هابط فوق الصفر - إشارة بيع قوية طويلة المدى
                    value_signals.append({'type': 'weekly_macd_bearish_reversal', 'strength': 4, 'action': 'sell'})

            # تحليل الحجم طويل المدى
            weekly_volume_ratio = primary_data.get('volume_ratio', 1)
            if weekly_volume_ratio > 1.5:  # حجم أسبوعي عالي
                for signal in value_signals:
                    if signal['action'] != 'hold':
                        signal['strength'] += 1

            # تحديد الإشارة الأقوى
            if value_signals:
                buy_signals = [s for s in value_signals if s['action'] == 'buy']
                sell_signals = [s for s in value_signals if s['action'] == 'sell']

                buy_strength = sum(s['strength'] for s in buy_signals)
                sell_strength = sum(s['strength'] for s in sell_signals)

                if buy_strength > sell_strength and buy_strength > 4:
                    action = 'buy'
                    confidence = min(buy_strength * 15, 80)
                elif sell_strength > buy_strength and sell_strength > 4:
                    action = 'sell'
                    confidence = min(sell_strength * 15, 80)
                else:
                    action = 'hold'
                    confidence = 30

                return {
                    'recommendation': {
                        'action': action,
                        'confidence_level': 'high' if confidence > 65 else 'medium' if confidence > 40 else 'low',
                        'entry_quality': confidence,
                        'strategy_type': 'position_trading_value'
                    },
                    'signals': value_signals,
                    'analysis_focus': 'long_term_value'
                }

            return {'recommendation': {'action': 'hold', 'confidence_level': 'low'}}

        except Exception as e:
            logger.error(f"خطأ في تحليل الاستثمار طويل المدى: {str(e)}")
            return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

    def _analyze_general_strategy(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """التحليل العام كبديل عند عدم توفر نمط محدد"""
        try:
            # استخدام التحليل الهرمي التقليدي
            long_term_analysis = self._analyze_long_term_trend(timeframes_data)
            medium_term_analysis = self._analyze_medium_term_trend(timeframes_data)
            short_term_analysis = self._analyze_short_term_signals(timeframes_data)

            # دمج التحليلات
            integrated_analysis = self._synthesize_analysis(
                long_term_analysis,
                medium_term_analysis,
                short_term_analysis
            )

            return integrated_analysis

        except Exception as e:
            logger.error(f"خطأ في التحليل العام: {str(e)}")
            return {'recommendation': {'action': 'hold', 'confidence_level': 'very_low'}}

    def _analyze_long_term_trend(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل الاتجاه طويل المدى"""
        try:
            # استخدام أطول إطار زمني متاح
            longest_timeframe = self.timeframes[-1]
            data = timeframes_data.get(longest_timeframe, {})

            if not data:
                return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

            # تحليل مؤشرات الاتجاه طويل المدى
            trend_signals = []

            # تحليل Ichimoku Cloud
            ichimoku_signal = self._analyze_ichimoku_trend(data)
            if ichimoku_signal:
                trend_signals.append(ichimoku_signal)

            # تحليل المتوسطات المتحركة طويلة المدى
            ma_signal = self._analyze_moving_averages_trend(data)
            if ma_signal:
                trend_signals.append(ma_signal)

            # تحليل هيكل السعر
            structure_signal = self._analyze_price_structure(data)
            if structure_signal:
                trend_signals.append(structure_signal)

            # دمج الإشارات
            return self._combine_trend_signals(trend_signals, 'long_term')

        except Exception as e:
            logger.error(f"خطأ في تحليل الاتجاه طويل المدى: {str(e)}")
            return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

    def _analyze_medium_term_trend(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل الاتجاه متوسط المدى"""
        try:
            # استخدام الإطار الزمني المتوسط
            medium_timeframe = self.timeframes[1] if len(self.timeframes) > 1 else self.timeframes[0]
            data = timeframes_data.get(medium_timeframe, {})

            if not data:
                return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

            trend_signals = []

            # تحليل MACD
            macd_signal = self._analyze_macd_trend(data)
            if macd_signal:
                trend_signals.append(macd_signal)

            # تحليل ADX
            adx_signal = self._analyze_adx_trend(data)
            if adx_signal:
                trend_signals.append(adx_signal)

            # تحليل Parabolic SAR
            sar_signal = self._analyze_parabolic_sar_trend(data)
            if sar_signal:
                trend_signals.append(sar_signal)

            return self._combine_trend_signals(trend_signals, 'medium_term')

        except Exception as e:
            logger.error(f"خطأ في تحليل الاتجاه متوسط المدى: {str(e)}")
            return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

    def _analyze_short_term_signals(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل إشارات قصيرة المدى"""
        try:
            # استخدام أقصر إطار زمني
            short_timeframe = self.timeframes[0]
            data = timeframes_data.get(short_timeframe, {})

            if not data:
                return {'signals': [], 'entry_quality': 0, 'timing_score': 0}

            signals = []

            # تحليل RSI
            rsi_signal = self._analyze_rsi_signals(data)
            if rsi_signal:
                signals.append(rsi_signal)

            # تحليل Stochastic
            stoch_signal = self._analyze_stochastic_signals(data)
            if stoch_signal:
                signals.append(stoch_signal)

            # تحليل Williams %R
            williams_signal = self._analyze_williams_signals(data)
            if williams_signal:
                signals.append(williams_signal)

            # تحليل CCI
            cci_signal = self._analyze_cci_signals(data)
            if cci_signal:
                signals.append(cci_signal)

            # تحليل Bollinger Bands
            bb_signal = self._analyze_bollinger_signals(data)
            if bb_signal:
                signals.append(bb_signal)

            return self._combine_entry_signals(signals)

        except Exception as e:
            logger.error(f"خطأ في تحليل الإشارات قصيرة المدى: {str(e)}")
            return {'signals': [], 'entry_quality': 0, 'timing_score': 0}

    def _synthesize_analysis(self, long_term: Dict[str, Any], medium_term: Dict[str, Any],
                           short_term: Dict[str, Any]) -> Dict[str, Any]:
        """دمج التحليلات من جميع الإطارات الزمنية مع تطبيق أوزان نمط التداول"""
        try:
            # تحديد التوافق بين الإطارات الزمنية
            trend_alignment = self._calculate_trend_alignment(long_term, medium_term)

            # تقييم جودة نقاط الدخول مع تطبيق أوزان نمط التداول
            entry_quality = self._evaluate_entry_quality_weighted(short_term, trend_alignment)

            # حساب مستوى الثقة الإجمالي مع أوزان نمط التداول
            overall_confidence = self._calculate_overall_confidence_weighted(long_term, medium_term, short_term)

            # تحديد التوصية النهائية مع مراعاة نمط التداول
            recommendation = self._generate_recommendation_weighted(
                long_term, medium_term, short_term, trend_alignment, entry_quality
            )

            return {
                'long_term_trend': long_term,
                'medium_term_trend': medium_term,
                'short_term_signals': short_term,
                'trend_alignment': trend_alignment,
                'entry_quality': entry_quality,
                'overall_confidence': overall_confidence,
                'recommendation': recommendation,
                'trading_style': self.trading_style.value,
                'analysis_timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"خطأ في دمج التحليلات: {str(e)}")
            return {}

    # دوال تحليل المؤشرات المتخصصة
    def _analyze_ichimoku_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه Ichimoku Cloud"""
        try:
            tenkan = data.get('ichimoku_tenkan')
            kijun = data.get('ichimoku_kijun')
            senkou_a = data.get('ichimoku_senkou_a')
            senkou_b = data.get('ichimoku_senkou_b')
            price = data.get('price', 0)

            if not all([tenkan, kijun, senkou_a, senkou_b, price]):
                return None

            # تحديد موقع السعر بالنسبة للسحابة
            cloud_top = max(senkou_a, senkou_b)
            cloud_bottom = min(senkou_a, senkou_b)

            if price > cloud_top:
                cloud_position = "above"
                trend_bias = "bullish"
                strength = 3
            elif price < cloud_bottom:
                cloud_position = "below"
                trend_bias = "bearish"
                strength = 3
            else:
                cloud_position = "inside"
                trend_bias = "neutral"
                strength = 1

            # تحليل تقاطع Tenkan و Kijun
            if tenkan > kijun:
                tk_signal = "bullish"
                strength += 1
            elif tenkan < kijun:
                tk_signal = "bearish"
                strength += 1
            else:
                tk_signal = "neutral"

            # تحليل اتجاه السحابة
            if senkou_a > senkou_b:
                cloud_trend = "bullish"
                strength += 1
            elif senkou_a < senkou_b:
                cloud_trend = "bearish"
                strength += 1
            else:
                cloud_trend = "neutral"

            return {
                'indicator': 'ichimoku',
                'trend': trend_bias,
                'strength': min(strength, 5),
                'confidence': min(strength * 20, 100),
                'details': {
                    'cloud_position': cloud_position,
                    'tk_signal': tk_signal,
                    'cloud_trend': cloud_trend,
                    'cloud_thickness': abs(senkou_a - senkou_b)
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Ichimoku: {str(e)}")
            return None

    def _analyze_moving_averages_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه المتوسطات المتحركة"""
        try:
            ema20 = data.get('ema20')
            ema50 = data.get('ema50')
            sma50 = data.get('sma50')
            sma200 = data.get('sma200')
            price = data.get('price', 0)

            if not price:
                return None

            signals = []
            strength = 0

            # تحليل موقع السعر بالنسبة للمتوسطات
            if ema20 and price > ema20:
                signals.append('price_above_ema20')
                strength += 1
            elif ema20 and price < ema20:
                signals.append('price_below_ema20')
                strength += 1

            # تحليل ترتيب المتوسطات المتحركة
            if ema20 and ema50:
                if ema20 > ema50:
                    signals.append('ema20_above_ema50')
                    strength += 2
                else:
                    signals.append('ema20_below_ema50')
                    strength += 2

            if sma50 and sma200:
                if sma50 > sma200:
                    signals.append('golden_cross_potential')
                    strength += 3
                else:
                    signals.append('death_cross_potential')
                    strength += 3

            # تحديد الاتجاه العام
            bullish_signals = len([s for s in signals if 'above' in s or 'golden' in s])
            bearish_signals = len([s for s in signals if 'below' in s or 'death' in s])

            if bullish_signals > bearish_signals:
                trend = 'bullish'
            elif bearish_signals > bullish_signals:
                trend = 'bearish'
            else:
                trend = 'neutral'

            return {
                'indicator': 'moving_averages',
                'trend': trend,
                'strength': min(strength, 5),
                'confidence': min(strength * 15, 100),
                'details': {
                    'signals': signals,
                    'bullish_count': bullish_signals,
                    'bearish_count': bearish_signals
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل المتوسطات المتحركة: {str(e)}")
            return None

    def _analyze_price_structure(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل هيكل السعر"""
        try:
            price = data.get('price', 0)
            high_24h = data.get('high_24h', 0)
            low_24h = data.get('low_24h', 0)

            if not all([price, high_24h, low_24h]):
                return None

            # حساب موقع السعر في النطاق اليومي
            daily_range = high_24h - low_24h
            if daily_range == 0:
                return None

            price_position = (price - low_24h) / daily_range

            # تحديد هيكل السعر
            if price_position > 0.8:
                structure = 'near_high'
                trend_bias = 'bullish'
                strength = 4
            elif price_position > 0.6:
                structure = 'upper_range'
                trend_bias = 'bullish'
                strength = 3
            elif price_position > 0.4:
                structure = 'middle_range'
                trend_bias = 'neutral'
                strength = 2
            elif price_position > 0.2:
                structure = 'lower_range'
                trend_bias = 'bearish'
                strength = 3
            else:
                structure = 'near_low'
                trend_bias = 'bearish'
                strength = 4

            return {
                'indicator': 'price_structure',
                'trend': trend_bias,
                'strength': strength,
                'confidence': strength * 20,
                'details': {
                    'structure': structure,
                    'price_position': price_position,
                    'daily_range': daily_range
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل هيكل السعر: {str(e)}")
            return None

    def _analyze_macd_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه MACD"""
        try:
            macd = data.get('macd')
            macd_signal = data.get('macd_signal')
            macd_histogram = data.get('macd_histogram')

            if not all([macd, macd_signal, macd_histogram]):
                return None

            signals = []
            strength = 0

            # تحليل تقاطع MACD مع خط الإشارة
            if macd > macd_signal:
                signals.append('macd_above_signal')
                strength += 2
                trend_bias = 'bullish'
            else:
                signals.append('macd_below_signal')
                strength += 2
                trend_bias = 'bearish'

            # تحليل موقع MACD بالنسبة للصفر
            if macd > 0:
                signals.append('macd_above_zero')
                strength += 1
            else:
                signals.append('macd_below_zero')
                strength += 1

            # تحليل الهيستوجرام
            if macd_histogram > 0:
                signals.append('histogram_positive')
                strength += 1
            else:
                signals.append('histogram_negative')
                strength += 1

            return {
                'indicator': 'macd',
                'trend': trend_bias,
                'strength': min(strength, 5),
                'confidence': min(strength * 20, 100),
                'details': {
                    'signals': signals,
                    'macd_value': macd,
                    'signal_value': macd_signal,
                    'histogram_value': macd_histogram
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل MACD: {str(e)}")
            return None

    def _analyze_adx_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه ADX"""
        try:
            adx = data.get('adx')
            plus_di = data.get('plus_di')
            minus_di = data.get('minus_di')

            if not all([adx, plus_di, minus_di]):
                return None

            # تحديد قوة الاتجاه بناءً على ADX
            if adx > 50:
                trend_strength = 'very_strong'
                strength = 5
            elif adx > 30:
                trend_strength = 'strong'
                strength = 4
            elif adx > 20:
                trend_strength = 'moderate'
                strength = 3
            else:
                trend_strength = 'weak'
                strength = 2

            # تحديد اتجاه الاتجاه بناءً على DI
            if plus_di > minus_di:
                trend_direction = 'bullish'
            else:
                trend_direction = 'bearish'

            return {
                'indicator': 'adx',
                'trend': trend_direction,
                'strength': strength,
                'confidence': min(adx * 2, 100),
                'details': {
                    'trend_strength': trend_strength,
                    'adx_value': adx,
                    'plus_di': plus_di,
                    'minus_di': minus_di
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل ADX: {str(e)}")
            return None

    def _analyze_parabolic_sar_trend(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل اتجاه Parabolic SAR"""
        try:
            sar = data.get('parabolic_sar')
            price = data.get('price', 0)

            if not all([sar, price]):
                return None

            # تحديد الاتجاه بناءً على موقع SAR بالنسبة للسعر
            if price > sar:
                trend = 'bullish'
                strength = 3
            else:
                trend = 'bearish'
                strength = 3

            # حساب المسافة بين السعر و SAR
            distance = abs(price - sar) / price * 100

            # تعديل القوة بناءً على المسافة
            if distance > 5:
                strength += 1
            elif distance < 1:
                strength -= 1

            return {
                'indicator': 'parabolic_sar',
                'trend': trend,
                'strength': max(min(strength, 5), 1),
                'confidence': min(distance * 20, 100),
                'details': {
                    'sar_value': sar,
                    'price_sar_distance': distance
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Parabolic SAR: {str(e)}")
            return None

    # دوال تحليل المؤشرات قصيرة المدى
    def _analyze_rsi_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات RSI"""
        try:
            rsi = data.get('rsi')
            if not rsi:
                return None

            # تحديد حالة RSI
            if rsi >= 80:
                signal_type = 'extremely_overbought'
                trend = 'bearish'
                strength = 5
            elif rsi >= 70:
                signal_type = 'overbought'
                trend = 'bearish'
                strength = 4
            elif rsi >= 60:
                signal_type = 'bullish_momentum'
                trend = 'bullish'
                strength = 3
            elif rsi >= 40:
                signal_type = 'neutral'
                trend = 'neutral'
                strength = 2
            elif rsi >= 30:
                signal_type = 'bearish_momentum'
                trend = 'bearish'
                strength = 3
            elif rsi >= 20:
                signal_type = 'oversold'
                trend = 'bullish'
                strength = 4
            else:
                signal_type = 'extremely_oversold'
                trend = 'bullish'
                strength = 5

            return {
                'indicator': 'rsi',
                'signal_type': signal_type,
                'trend': trend,
                'strength': strength,
                'confidence': min(abs(rsi - 50) * 2, 100),
                'details': {
                    'rsi_value': rsi,
                    'distance_from_neutral': abs(rsi - 50)
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل RSI: {str(e)}")
            return None

    def _analyze_stochastic_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات Stochastic"""
        try:
            stoch_k = data.get('stoch_k')
            stoch_d = data.get('stoch_d')

            if not all([stoch_k, stoch_d]):
                return None

            signals = []
            strength = 0

            # تحليل مستويات ذروة الشراء والبيع
            if stoch_k >= 80 and stoch_d >= 80:
                signals.append('overbought')
                trend = 'bearish'
                strength += 3
            elif stoch_k <= 20 and stoch_d <= 20:
                signals.append('oversold')
                trend = 'bullish'
                strength += 3
            else:
                trend = 'neutral'
                strength += 1

            # تحليل تقاطع %K و %D
            if stoch_k > stoch_d:
                signals.append('k_above_d')
                if trend == 'neutral':
                    trend = 'bullish'
                strength += 1
            else:
                signals.append('k_below_d')
                if trend == 'neutral':
                    trend = 'bearish'
                strength += 1

            return {
                'indicator': 'stochastic',
                'trend': trend,
                'strength': min(strength, 5),
                'confidence': min(strength * 20, 100),
                'details': {
                    'signals': signals,
                    'k_value': stoch_k,
                    'd_value': stoch_d
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Stochastic: {str(e)}")
            return None

    def _analyze_williams_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات Williams %R"""
        try:
            williams_r = data.get('williams_r')
            if not williams_r:
                return None

            # تحديد حالة Williams %R
            if williams_r >= -20:
                signal_type = 'overbought'
                trend = 'bearish'
                strength = 4
            elif williams_r >= -50:
                signal_type = 'neutral_to_bullish'
                trend = 'bullish'
                strength = 2
            elif williams_r >= -80:
                signal_type = 'neutral_to_bearish'
                trend = 'bearish'
                strength = 2
            else:
                signal_type = 'oversold'
                trend = 'bullish'
                strength = 4

            return {
                'indicator': 'williams_r',
                'signal_type': signal_type,
                'trend': trend,
                'strength': strength,
                'confidence': min(abs(williams_r + 50) * 2, 100),
                'details': {
                    'williams_value': williams_r
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Williams %R: {str(e)}")
            return None

    def _analyze_cci_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات CCI"""
        try:
            cci = data.get('cci')
            if not cci:
                return None

            # تحديد حالة CCI
            if cci >= 200:
                signal_type = 'extremely_overbought'
                trend = 'bearish'
                strength = 5
            elif cci >= 100:
                signal_type = 'overbought'
                trend = 'bearish'
                strength = 4
            elif cci >= 0:
                signal_type = 'bullish_momentum'
                trend = 'bullish'
                strength = 3
            elif cci >= -100:
                signal_type = 'bearish_momentum'
                trend = 'bearish'
                strength = 3
            elif cci >= -200:
                signal_type = 'oversold'
                trend = 'bullish'
                strength = 4
            else:
                signal_type = 'extremely_oversold'
                trend = 'bullish'
                strength = 5

            return {
                'indicator': 'cci',
                'signal_type': signal_type,
                'trend': trend,
                'strength': strength,
                'confidence': min(abs(cci) / 2, 100),
                'details': {
                    'cci_value': cci
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل CCI: {str(e)}")
            return None

    def _analyze_bollinger_signals(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """تحليل إشارات Bollinger Bands"""
        try:
            bb_upper = data.get('bb_upper')
            bb_middle = data.get('bb_middle')
            bb_lower = data.get('bb_lower')
            price = data.get('price', 0)

            if not all([bb_upper, bb_middle, bb_lower, price]):
                return None

            # حساب موقع السعر بالنسبة للنطاقات
            bb_range = bb_upper - bb_lower
            if bb_range == 0:
                return None

            # تحديد موقع السعر
            if price >= bb_upper:
                signal_type = 'touching_upper_band'
                trend = 'bearish'
                strength = 4
            elif price >= bb_middle + (bb_range * 0.3):
                signal_type = 'upper_zone'
                trend = 'bullish'
                strength = 3
            elif price >= bb_middle - (bb_range * 0.3):
                signal_type = 'middle_zone'
                trend = 'neutral'
                strength = 2
            elif price >= bb_lower:
                signal_type = 'lower_zone'
                trend = 'bearish'
                strength = 3
            else:
                signal_type = 'touching_lower_band'
                trend = 'bullish'
                strength = 4

            # حساب نسبة %B
            percent_b = (price - bb_lower) / bb_range

            return {
                'indicator': 'bollinger_bands',
                'signal_type': signal_type,
                'trend': trend,
                'strength': strength,
                'confidence': min(abs(percent_b - 0.5) * 200, 100),
                'details': {
                    'percent_b': percent_b,
                    'bb_width': bb_range,
                    'price_position': signal_type
                }
            }

        except Exception as e:
            logger.error(f"خطأ في تحليل Bollinger Bands: {str(e)}")
            return None

    # دوال دمج الإشارات والتحليلات
    def _combine_trend_signals(self, signals: List[Dict[str, Any]], timeframe_type: str) -> Dict[str, Any]:
        """دمج إشارات الاتجاه"""
        try:
            if not signals:
                return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

            # حساب الأوزان حسب نوع الإطار الزمني
            weights = {
                'long_term': {'ichimoku': 0.4, 'moving_averages': 0.4, 'price_structure': 0.2},
                'medium_term': {'macd': 0.4, 'adx': 0.4, 'parabolic_sar': 0.2}
            }

            timeframe_weights = weights.get(timeframe_type, {})

            bullish_score = 0
            bearish_score = 0
            total_weight = 0

            for signal in signals:
                indicator = signal.get('indicator', '')
                trend = signal.get('trend', 'neutral')
                strength = signal.get('strength', 0)
                weight = timeframe_weights.get(indicator, 1.0)

                weighted_strength = strength * weight
                total_weight += weight

                if trend == 'bullish':
                    bullish_score += weighted_strength
                elif trend == 'bearish':
                    bearish_score += weighted_strength

            # تحديد الاتجاه النهائي
            if bullish_score > bearish_score:
                final_trend = 'bullish'
                final_strength = min(int(bullish_score / total_weight), 5) if total_weight > 0 else 0
            elif bearish_score > bullish_score:
                final_trend = 'bearish'
                final_strength = min(int(bearish_score / total_weight), 5) if total_weight > 0 else 0
            else:
                final_trend = 'neutral'
                final_strength = 2

            # حساب مستوى الثقة
            confidence = min(abs(bullish_score - bearish_score) * 10, 100) if total_weight > 0 else 0

            return {
                'trend': final_trend,
                'strength': final_strength,
                'confidence': confidence,
                'details': {
                    'bullish_score': bullish_score,
                    'bearish_score': bearish_score,
                    'signals_count': len(signals),
                    'individual_signals': signals
                }
            }

        except Exception as e:
            logger.error(f"خطأ في دمج إشارات الاتجاه: {str(e)}")
            return {'trend': 'unknown', 'strength': 0, 'confidence': 0}

    def _combine_entry_signals(self, signals: List[Dict[str, Any]]) -> Dict[str, Any]:
        """دمج إشارات الدخول"""
        try:
            if not signals:
                return {'signals': [], 'entry_quality': 0, 'timing_score': 0}

            # أوزان المؤشرات قصيرة المدى
            weights = {
                'rsi': 0.25,
                'stochastic': 0.25,
                'williams_r': 0.15,
                'cci': 0.15,
                'bollinger_bands': 0.20
            }

            bullish_signals = []
            bearish_signals = []
            total_strength = 0

            for signal in signals:
                indicator = signal.get('indicator', '')
                trend = signal.get('trend', 'neutral')
                strength = signal.get('strength', 0)
                weight = weights.get(indicator, 0.1)

                weighted_strength = strength * weight
                total_strength += weighted_strength

                if trend == 'bullish':
                    bullish_signals.append(signal)
                elif trend == 'bearish':
                    bearish_signals.append(signal)

            # تقييم جودة نقاط الدخول
            entry_quality = min(total_strength * 20, 100)

            # حساب نقاط التوقيت
            timing_score = self._calculate_timing_score(signals)

            return {
                'signals': signals,
                'entry_quality': entry_quality,
                'timing_score': timing_score,
                'bullish_signals': bullish_signals,
                'bearish_signals': bearish_signals,
                'total_signals': len(signals)
            }

        except Exception as e:
            logger.error(f"خطأ في دمج إشارات الدخول: {str(e)}")
            return {'signals': [], 'entry_quality': 0, 'timing_score': 0}

    def _calculate_timing_score(self, signals: List[Dict[str, Any]]) -> float:
        """حساب نقاط التوقيت للدخول"""
        try:
            if not signals:
                return 0

            # عوامل التوقيت
            timing_factors = {
                'oversold_signals': 0,
                'overbought_signals': 0,
                'momentum_signals': 0,
                'reversal_signals': 0
            }

            for signal in signals:
                signal_type = signal.get('signal_type', '')

                if 'oversold' in signal_type or 'extremely_oversold' in signal_type:
                    timing_factors['oversold_signals'] += 1
                elif 'overbought' in signal_type or 'extremely_overbought' in signal_type:
                    timing_factors['overbought_signals'] += 1
                elif 'momentum' in signal_type:
                    timing_factors['momentum_signals'] += 1
                elif 'touching' in signal_type:
                    timing_factors['reversal_signals'] += 1

            # حساب نقاط التوقيت
            timing_score = 0

            # إشارات ذروة البيع تعطي نقاط إيجابية للشراء
            timing_score += timing_factors['oversold_signals'] * 25

            # إشارات ذروة الشراء تعطي نقاط إيجابية للبيع
            timing_score += timing_factors['overbought_signals'] * 25

            # إشارات الزخم تعطي نقاط متوسطة
            timing_score += timing_factors['momentum_signals'] * 15

            # إشارات الانعكاس تعطي نقاط عالية
            timing_score += timing_factors['reversal_signals'] * 30

            return min(timing_score, 100)

        except Exception as e:
            logger.error(f"خطأ في حساب نقاط التوقيت: {str(e)}")
            return 0

    def _calculate_trend_alignment(self, long_term: Dict[str, Any], medium_term: Dict[str, Any]) -> Dict[str, Any]:
        """حساب التوافق بين الاتجاهات"""
        try:
            long_trend = long_term.get('trend', 'unknown')
            medium_trend = medium_term.get('trend', 'unknown')

            long_strength = long_term.get('strength', 0)
            medium_strength = medium_term.get('strength', 0)

            # تحديد مستوى التوافق
            if long_trend == medium_trend and long_trend != 'neutral':
                alignment_type = 'strong_alignment'
                alignment_score = min((long_strength + medium_strength) * 10, 100)
            elif long_trend == 'neutral' or medium_trend == 'neutral':
                alignment_type = 'partial_alignment'
                alignment_score = max(long_strength, medium_strength) * 10
            else:
                alignment_type = 'conflicting'
                alignment_score = abs(long_strength - medium_strength) * 5

            return {
                'alignment_type': alignment_type,
                'alignment_score': alignment_score,
                'long_term_trend': long_trend,
                'medium_term_trend': medium_trend,
                'trend_consistency': long_trend == medium_trend
            }

        except Exception as e:
            logger.error(f"خطأ في حساب التوافق بين الاتجاهات: {str(e)}")
            return {'alignment_type': 'unknown', 'alignment_score': 0}

    def _evaluate_entry_quality(self, short_term: Dict[str, Any], trend_alignment: Dict[str, Any]) -> float:
        """تقييم جودة نقاط الدخول - النسخة القديمة"""
        try:
            entry_quality = short_term.get('entry_quality', 0)
            timing_score = short_term.get('timing_score', 0)
            alignment_score = trend_alignment.get('alignment_score', 0)

            # حساب الجودة الإجمالية
            base_quality = (entry_quality * 0.4) + (timing_score * 0.3) + (alignment_score * 0.3)

            # تعديل الجودة بناءً على نوع التوافق
            alignment_type = trend_alignment.get('alignment_type', 'unknown')
            if alignment_type == 'strong_alignment':
                base_quality *= 1.2
            elif alignment_type == 'conflicting':
                base_quality *= 0.7

            return min(base_quality, 100)

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة نقاط الدخول: {str(e)}")
            return 0

    def _evaluate_entry_quality_weighted(self, short_term: Dict[str, Any], trend_alignment: Dict[str, Any]) -> float:
        """تقييم جودة نقاط الدخول مع تطبيق أوزان نمط التداول"""
        try:
            entry_quality = short_term.get('entry_quality', 0)
            timing_score = short_term.get('timing_score', 0)
            alignment_score = trend_alignment.get('alignment_score', 0)

            # تحديد الأوزان حسب نمط التداول
            if self.trading_style == TradingStyle.SCALPING:
                # المضاربة السريعة: التركيز على التوقيت
                weights = {'entry': 0.2, 'timing': 0.6, 'alignment': 0.2}
                timing_multiplier = 1.5  # مضاعف إضافي للتوقيت
            elif self.trading_style == TradingStyle.DAY_TRADING:
                # التداول اليومي: توازن بين العوامل
                weights = {'entry': 0.4, 'timing': 0.3, 'alignment': 0.3}
                timing_multiplier = 1.2
            elif self.trading_style == TradingStyle.SWING_TRADING:
                # التداول المتأرجح: التركيز على التوافق
                weights = {'entry': 0.3, 'timing': 0.2, 'alignment': 0.5}
                timing_multiplier = 1.0
            else:  # POSITION
                # الاستثمار طويل المدى: التركيز على التوافق العام
                weights = {'entry': 0.2, 'timing': 0.1, 'alignment': 0.7}
                timing_multiplier = 0.8

            # حساب الجودة الإجمالية مع الأوزان المخصصة
            base_quality = (
                entry_quality * weights['entry'] +
                timing_score * weights['timing'] * timing_multiplier +
                alignment_score * weights['alignment']
            )

            # تعديل الجودة بناءً على نوع التوافق ونمط التداول
            alignment_type = trend_alignment.get('alignment_type', 'unknown')
            if alignment_type == 'strong_alignment':
                if self.trading_style in [TradingStyle.SWING_TRADING, TradingStyle.POSITION]:
                    base_quality *= 1.3  # مكافأة أكبر للأنماط طويلة المدى
                else:
                    base_quality *= 1.2
            elif alignment_type == 'conflicting':
                if self.trading_style == TradingStyle.SCALPING:
                    base_quality *= 0.8  # عقوبة أقل للمضاربة السريعة
                else:
                    base_quality *= 0.7

            return min(base_quality, 100)

        except Exception as e:
            logger.error(f"خطأ في تقييم جودة نقاط الدخول المرجحة: {str(e)}")
            return 0

    def _calculate_overall_confidence(self, long_term: Dict[str, Any], medium_term: Dict[str, Any],
                                    short_term: Dict[str, Any]) -> float:
        """حساب مستوى الثقة الإجمالي - النسخة القديمة"""
        try:
            long_confidence = long_term.get('confidence', 0)
            medium_confidence = medium_term.get('confidence', 0)
            short_confidence = short_term.get('timing_score', 0)

            # حساب الثقة الإجمالية بأوزان مختلفة
            overall_confidence = (
                long_confidence * 0.4 +
                medium_confidence * 0.4 +
                short_confidence * 0.2
            )

            return min(overall_confidence, 100)

        except Exception as e:
            logger.error(f"خطأ في حساب مستوى الثقة الإجمالي: {str(e)}")
            return 0

    def _calculate_overall_confidence_weighted(self, long_term: Dict[str, Any], medium_term: Dict[str, Any],
                                             short_term: Dict[str, Any]) -> float:
        """حساب مستوى الثقة الإجمالي مع تطبيق أوزان نمط التداول"""
        try:
            long_confidence = long_term.get('confidence', 0)
            medium_confidence = medium_term.get('confidence', 0)
            short_confidence = short_term.get('timing_score', 0)

            # تحديد الأوزان حسب نمط التداول
            if self.trading_style == TradingStyle.SCALPING:
                # المضاربة السريعة: التركيز على الإطارات القصيرة
                weights = {'long': 0.1, 'medium': 0.3, 'short': 0.6}
            elif self.trading_style == TradingStyle.DAY_TRADING:
                # التداول اليومي: توازن بين الإطارات
                weights = {'long': 0.2, 'medium': 0.5, 'short': 0.3}
            elif self.trading_style == TradingStyle.SWING_TRADING:
                # التداول المتأرجح: التركيز على الإطارات المتوسطة والطويلة
                weights = {'long': 0.4, 'medium': 0.5, 'short': 0.1}
            else:  # POSITION
                # الاستثمار طويل المدى: التركيز على الإطارات الطويلة
                weights = {'long': 0.7, 'medium': 0.2, 'short': 0.1}

            # حساب الثقة الإجمالية مع الأوزان المخصصة
            overall_confidence = (
                long_confidence * weights['long'] +
                medium_confidence * weights['medium'] +
                short_confidence * weights['short']
            )

            return min(overall_confidence, 100)

        except Exception as e:
            logger.error(f"خطأ في حساب مستوى الثقة الإجمالي المرجح: {str(e)}")
            return 0

    def _generate_recommendation(self, long_term: Dict[str, Any], medium_term: Dict[str, Any],
                               short_term: Dict[str, Any], trend_alignment: Dict[str, Any],
                               entry_quality: float) -> Dict[str, Any]:
        """إنشاء التوصية النهائية - النسخة القديمة"""
        try:
            # تحديد الاتجاه الرئيسي
            long_trend = long_term.get('trend', 'unknown')
            medium_trend = medium_term.get('trend', 'unknown')
            alignment_type = trend_alignment.get('alignment_type', 'unknown')

            # تحديد نوع التوصية
            if alignment_type == 'strong_alignment' and entry_quality > 70:
                if long_trend == 'bullish':
                    recommendation_type = 'strong_buy'
                    action = 'buy'
                else:
                    recommendation_type = 'strong_sell'
                    action = 'sell'
                confidence_level = 'high'
            elif alignment_type == 'strong_alignment' and entry_quality > 50:
                if long_trend == 'bullish':
                    recommendation_type = 'buy'
                    action = 'buy'
                else:
                    recommendation_type = 'sell'
                    action = 'sell'
                confidence_level = 'medium'
            elif entry_quality > 60:
                recommendation_type = 'weak_signal'
                action = long_trend if long_trend != 'neutral' else 'hold'
                confidence_level = 'low'
            else:
                recommendation_type = 'hold'
                action = 'hold'
                confidence_level = 'very_low'

            return {
                'recommendation_type': recommendation_type,
                'action': action,
                'confidence_level': confidence_level,
                'entry_quality': entry_quality,
                'primary_trend': long_trend,
                'secondary_trend': medium_trend,
                'alignment_quality': alignment_type
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصية النهائية: {str(e)}")
            return {
                'recommendation_type': 'hold',
                'action': 'hold',
                'confidence_level': 'very_low',
                'entry_quality': 0
            }

    def _generate_recommendation_weighted(self, long_term: Dict[str, Any], medium_term: Dict[str, Any],
                                        short_term: Dict[str, Any], trend_alignment: Dict[str, Any],
                                        entry_quality: float) -> Dict[str, Any]:
        """إنشاء التوصية النهائية مع تطبيق أوزان نمط التداول"""
        try:
            # تحديد الاتجاه الرئيسي حسب نمط التداول
            long_trend = long_term.get('trend', 'unknown')
            medium_trend = medium_term.get('trend', 'unknown')
            short_signals = short_term.get('signals', [])
            alignment_type = trend_alignment.get('alignment_type', 'unknown')

            # تحديد الاتجاه المرجعي حسب نمط التداول
            if self.trading_style == TradingStyle.SCALPING:
                # المضاربة السريعة: التركيز على الإشارات قصيرة المدى
                primary_trend = self._get_dominant_short_term_trend(short_signals)
                reference_trend = primary_trend
                quality_threshold_high = 60  # عتبة أقل للمضاربة السريعة
                quality_threshold_medium = 40
            elif self.trading_style == TradingStyle.DAY_TRADING:
                # التداول اليومي: التركيز على الاتجاه المتوسط
                primary_trend = medium_trend
                reference_trend = medium_trend
                quality_threshold_high = 70
                quality_threshold_medium = 50
            elif self.trading_style == TradingStyle.SWING_TRADING:
                # التداول المتأرجح: التوازن بين الطويل والمتوسط
                primary_trend = long_trend if long_trend != 'neutral' else medium_trend
                reference_trend = long_trend
                quality_threshold_high = 75
                quality_threshold_medium = 55
            else:  # POSITION
                # الاستثمار طويل المدى: التركيز على الاتجاه طويل المدى
                primary_trend = long_trend
                reference_trend = long_trend
                quality_threshold_high = 80
                quality_threshold_medium = 60

            # تحديد نوع التوصية مع مراعاة نمط التداول
            if alignment_type == 'strong_alignment' and entry_quality > quality_threshold_high:
                if reference_trend == 'bullish':
                    recommendation_type = 'strong_buy'
                    action = 'buy'
                elif reference_trend == 'bearish':
                    recommendation_type = 'strong_sell'
                    action = 'sell'
                else:
                    recommendation_type = 'hold'
                    action = 'hold'
                confidence_level = 'high'
            elif alignment_type == 'strong_alignment' and entry_quality > quality_threshold_medium:
                if reference_trend == 'bullish':
                    recommendation_type = 'buy'
                    action = 'buy'
                elif reference_trend == 'bearish':
                    recommendation_type = 'sell'
                    action = 'sell'
                else:
                    recommendation_type = 'hold'
                    action = 'hold'
                confidence_level = 'medium'
            elif entry_quality > quality_threshold_medium:
                recommendation_type = 'weak_signal'
                action = reference_trend if reference_trend != 'neutral' else 'hold'
                confidence_level = 'low'
            else:
                recommendation_type = 'hold'
                action = 'hold'
                confidence_level = 'very_low'

            return {
                'recommendation_type': recommendation_type,
                'action': action,
                'confidence_level': confidence_level,
                'entry_quality': entry_quality,
                'primary_trend': primary_trend,
                'secondary_trend': medium_trend,
                'alignment_quality': alignment_type,
                'trading_style': self.trading_style.value,
                'quality_thresholds': {
                    'high': quality_threshold_high,
                    'medium': quality_threshold_medium
                }
            }

        except Exception as e:
            logger.error(f"خطأ في إنشاء التوصية النهائية المرجحة: {str(e)}")
            return {
                'recommendation_type': 'hold',
                'action': 'hold',
                'confidence_level': 'very_low',
                'entry_quality': 0,
                'trading_style': self.trading_style.value
            }

    def _get_dominant_short_term_trend(self, signals: List[Dict[str, Any]]) -> str:
        """تحديد الاتجاه المهيمن من الإشارات قصيرة المدى"""
        try:
            if not signals:
                return 'neutral'

            bullish_count = 0
            bearish_count = 0
            bullish_strength = 0
            bearish_strength = 0

            for signal in signals:
                trend = signal.get('trend', 'neutral')
                strength = signal.get('strength', 0)

                if trend == 'bullish':
                    bullish_count += 1
                    bullish_strength += strength
                elif trend == 'bearish':
                    bearish_count += 1
                    bearish_strength += strength

            # تحديد الاتجاه بناءً على العدد والقوة
            if bullish_count > bearish_count and bullish_strength > bearish_strength:
                return 'bullish'
            elif bearish_count > bullish_count and bearish_strength > bullish_strength:
                return 'bearish'
            else:
                return 'neutral'

        except Exception as e:
            logger.error(f"خطأ في تحديد الاتجاه المهيمن: {str(e)}")
            return 'neutral'

    # ===== دوال التحليل الموحد الجديدة =====

    def _get_scalping_signals(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """استخراج إشارات المضاربة السريعة من البيانات"""
        try:
            # التركيز على الإطارات القصيرة
            primary_data = timeframes_data.get('1m', {})
            secondary_data = timeframes_data.get('5m', {})

            if not primary_data:
                return {}

            signals = []

            # مؤشرات الزخم السريعة
            rsi = primary_data.get('rsi', 50)
            if rsi > 70:
                signals.append({'type': 'scalping_rsi_overbought', 'strength': 4, 'action': 'sell', 'timeframe': '1m'})
            elif rsi < 30:
                signals.append({'type': 'scalping_rsi_oversold', 'strength': 4, 'action': 'buy', 'timeframe': '1m'})

            # Stochastic للتأكيد
            stoch_k = primary_data.get('stoch_k', 50)
            if stoch_k > 80:
                signals.append({'type': 'scalping_stoch_overbought', 'strength': 3, 'action': 'sell', 'timeframe': '1m'})
            elif stoch_k < 20:
                signals.append({'type': 'scalping_stoch_oversold', 'strength': 3, 'action': 'buy', 'timeframe': '1m'})

            # Williams %R
            williams_r = primary_data.get('williams_r', -50)
            if williams_r > -20:
                signals.append({'type': 'scalping_williams_overbought', 'strength': 3, 'action': 'sell', 'timeframe': '1m'})
            elif williams_r < -80:
                signals.append({'type': 'scalping_williams_oversold', 'strength': 3, 'action': 'buy', 'timeframe': '1m'})

            # تعزيز بالحجم
            volume_ratio = primary_data.get('volume_ratio', 1)
            if volume_ratio > 2:
                for signal in signals:
                    signal['strength'] += 1
                    signal['volume_confirmed'] = True

            return {
                'signals': signals,
                'focus': 'momentum_oscillators',
                'timeframes': ['1m', '5m'],
                'total_strength': sum(s['strength'] for s in signals)
            }

        except Exception as e:
            logger.error(f"خطأ في استخراج إشارات المضاربة السريعة: {str(e)}")
            return {}

    def _get_day_trading_signals(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """استخراج إشارات التداول اليومي من البيانات"""
        try:
            # التركيز على الإطارات المتوسطة
            primary_data = timeframes_data.get('1h', {})
            context_data = timeframes_data.get('4h', {})

            if not primary_data:
                return {}

            signals = []

            # MACD لاتباع الاتجاه
            macd = primary_data.get('macd', 0)
            macd_signal = primary_data.get('macd_signal', 0)
            if macd > macd_signal:
                signals.append({'type': 'day_trading_macd_bullish', 'strength': 4, 'action': 'buy', 'timeframe': '1h'})
            elif macd < macd_signal:
                signals.append({'type': 'day_trading_macd_bearish', 'strength': 4, 'action': 'sell', 'timeframe': '1h'})

            # EMA Crossover
            ema20 = primary_data.get('ema20', 0)
            ema50 = primary_data.get('ema50', 0)
            price = primary_data.get('price', 0)

            if ema20 and ema50 and price:
                if ema20 > ema50 and price > ema20:
                    signals.append({'type': 'day_trading_ema_bullish', 'strength': 3, 'action': 'buy', 'timeframe': '1h'})
                elif ema20 < ema50 and price < ema20:
                    signals.append({'type': 'day_trading_ema_bearish', 'strength': 3, 'action': 'sell', 'timeframe': '1h'})

            # ADX لقوة الاتجاه
            adx = primary_data.get('adx', 0)
            plus_di = primary_data.get('plus_di', 0)
            minus_di = primary_data.get('minus_di', 0)

            if adx > 25:
                if plus_di > minus_di:
                    signals.append({'type': 'day_trading_adx_bullish', 'strength': 3, 'action': 'buy', 'timeframe': '1h'})
                else:
                    signals.append({'type': 'day_trading_adx_bearish', 'strength': 3, 'action': 'sell', 'timeframe': '1h'})

            # تعزيز بالحجم
            volume_ratio = primary_data.get('volume_ratio', 1)
            if volume_ratio > 1.5:
                for signal in signals:
                    signal['strength'] += 1
                    signal['volume_confirmed'] = True

            return {
                'signals': signals,
                'focus': 'trend_following',
                'timeframes': ['1h', '4h'],
                'total_strength': sum(s['strength'] for s in signals)
            }

        except Exception as e:
            logger.error(f"خطأ في استخراج إشارات التداول اليومي: {str(e)}")
            return {}

    def _get_swing_signals(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """استخراج إشارات التداول المتأرجح من البيانات"""
        try:
            # التركيز على الإطارات الطويلة
            primary_data = timeframes_data.get('1d', {})
            context_data = timeframes_data.get('1w', {})

            if not primary_data:
                return {}

            signals = []

            # Ichimoku Cloud للاتجاه العام
            tenkan = primary_data.get('ichimoku_tenkan', 0)
            kijun = primary_data.get('ichimoku_kijun', 0)
            senkou_a = primary_data.get('ichimoku_senkou_a', 0)
            senkou_b = primary_data.get('ichimoku_senkou_b', 0)
            price = primary_data.get('price', 0)

            if all([tenkan, kijun, senkou_a, senkou_b, price]):
                cloud_top = max(senkou_a, senkou_b)
                cloud_bottom = min(senkou_a, senkou_b)

                if price > cloud_top and tenkan > kijun:
                    signals.append({'type': 'swing_ichimoku_bullish', 'strength': 4, 'action': 'buy', 'timeframe': '1d'})
                elif price < cloud_bottom and tenkan < kijun:
                    signals.append({'type': 'swing_ichimoku_bearish', 'strength': 4, 'action': 'sell', 'timeframe': '1d'})

            # Moving Average Convergence
            sma50 = primary_data.get('sma50', 0)
            sma200 = primary_data.get('sma200', 0)

            if sma50 and sma200:
                if sma50 > sma200:
                    signals.append({'type': 'swing_golden_cross', 'strength': 4, 'action': 'buy', 'timeframe': '1d'})
                elif sma50 < sma200:
                    signals.append({'type': 'swing_death_cross', 'strength': 4, 'action': 'sell', 'timeframe': '1d'})

            # MACD للزخم متوسط المدى
            macd = primary_data.get('macd', 0)
            macd_signal = primary_data.get('macd_signal', 0)

            if macd and macd_signal:
                if macd > macd_signal and macd < 0:
                    signals.append({'type': 'swing_macd_bullish_reversal', 'strength': 4, 'action': 'buy', 'timeframe': '1d'})
                elif macd < macd_signal and macd > 0:
                    signals.append({'type': 'swing_macd_bearish_reversal', 'strength': 4, 'action': 'sell', 'timeframe': '1d'})

            return {
                'signals': signals,
                'focus': 'trend_reversal',
                'timeframes': ['1d', '1w'],
                'total_strength': sum(s['strength'] for s in signals)
            }

        except Exception as e:
            logger.error(f"خطأ في استخراج إشارات التداول المتأرجح: {str(e)}")
            return {}

    def _get_position_signals(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """استخراج إشارات الاستثمار طويل المدى من البيانات"""
        try:
            # التركيز على الإطارات الطويلة جداً
            primary_data = timeframes_data.get('1w', {})
            context_data = timeframes_data.get('1M', {})

            if not primary_data:
                return {}

            signals = []

            # تحليل المتوسطات المتحركة طويلة المدى
            sma50 = primary_data.get('sma50', 0)
            sma200 = primary_data.get('sma200', 0)
            ema100 = primary_data.get('ema100', 0)
            price = primary_data.get('price', 0)

            if all([sma50, sma200, ema100, price]):
                # Golden Cross طويل المدى
                if sma50 > sma200 and price > ema100:
                    signals.append({'type': 'position_golden_cross', 'strength': 5, 'action': 'buy', 'timeframe': '1w'})
                elif sma50 < sma200 and price < ema100:
                    signals.append({'type': 'position_death_cross', 'strength': 5, 'action': 'sell', 'timeframe': '1w'})

            # تحليل RSI طويل المدى للقيمة
            weekly_rsi = primary_data.get('rsi', 50)
            if weekly_rsi:
                if weekly_rsi < 30:
                    signals.append({'type': 'position_rsi_oversold', 'strength': 4, 'action': 'buy', 'timeframe': '1w'})
                elif weekly_rsi > 70:
                    signals.append({'type': 'position_rsi_overbought', 'strength': 4, 'action': 'sell', 'timeframe': '1w'})

            # تحليل MACD طويل المدى
            weekly_macd = primary_data.get('macd', 0)
            weekly_macd_signal = primary_data.get('macd_signal', 0)

            if weekly_macd and weekly_macd_signal:
                if weekly_macd > weekly_macd_signal and weekly_macd < 0:
                    signals.append({'type': 'position_macd_bullish_reversal', 'strength': 4, 'action': 'buy', 'timeframe': '1w'})
                elif weekly_macd < weekly_macd_signal and weekly_macd > 0:
                    signals.append({'type': 'position_macd_bearish_reversal', 'strength': 4, 'action': 'sell', 'timeframe': '1w'})

            return {
                'signals': signals,
                'focus': 'long_term_value',
                'timeframes': ['1w', '1M'],
                'total_strength': sum(s['strength'] for s in signals)
            }

        except Exception as e:
            logger.error(f"خطأ في استخراج إشارات الاستثمار طويل المدى: {str(e)}")
            return {}

    def _synthesize_unified_signals(self, all_signals: List[Dict[str, Any]],
                                   analysis_components: Dict[str, Any]) -> Dict[str, Any]:
        """دمج وتحليل جميع الإشارات من الأنماط الأربعة"""
        try:
            if not all_signals:
                return {'action': 'hold', 'confidence_level': 'very_low', 'entry_quality': 0}

            # تصنيف الإشارات حسب الإجراء
            buy_signals = [s for s in all_signals if s.get('action') == 'buy']
            sell_signals = [s for s in all_signals if s.get('action') == 'sell']

            # حساب القوة الإجمالية
            buy_strength = sum(s.get('strength', 0) for s in buy_signals)
            sell_strength = sum(s.get('strength', 0) for s in sell_signals)

            # تحليل التوافق بين الأنماط
            pattern_agreement = self._calculate_pattern_agreement(analysis_components)

            # تحديد الإجراء الموصى به
            if buy_strength > sell_strength and buy_strength > 8:
                action = 'buy'
                confidence = min((buy_strength / (buy_strength + sell_strength + 1)) * 100, 95)
                confidence *= pattern_agreement  # تعديل الثقة بناءً على التوافق
            elif sell_strength > buy_strength and sell_strength > 8:
                action = 'sell'
                confidence = min((sell_strength / (buy_strength + sell_strength + 1)) * 100, 95)
                confidence *= pattern_agreement
            else:
                action = 'hold'
                confidence = 40

            # تحديد مستوى الثقة
            if confidence > 80:
                confidence_level = 'very_high'
            elif confidence > 65:
                confidence_level = 'high'
            elif confidence > 50:
                confidence_level = 'medium'
            elif confidence > 35:
                confidence_level = 'low'
            else:
                confidence_level = 'very_low'

            return {
                'action': action,
                'confidence_level': confidence_level,
                'entry_quality': confidence,
                'buy_signals_count': len(buy_signals),
                'sell_signals_count': len(sell_signals),
                'buy_strength': buy_strength,
                'sell_strength': sell_strength,
                'pattern_agreement': pattern_agreement,
                'strategy_type': 'unified_comprehensive'
            }

        except Exception as e:
            logger.error(f"خطأ في دمج الإشارات الموحدة: {str(e)}")
            return {'action': 'hold', 'confidence_level': 'very_low', 'entry_quality': 0}

    def _calculate_pattern_agreement(self, analysis_components: Dict[str, Any]) -> float:
        """حساب مستوى التوافق بين الأنماط المختلفة"""
        try:
            if not analysis_components:
                return 0.5

            # استخراج التوصيات من كل نمط
            recommendations = {}
            for pattern_name, component in analysis_components.items():
                signals = component.get('signals', [])
                if signals:
                    buy_count = len([s for s in signals if s.get('action') == 'buy'])
                    sell_count = len([s for s in signals if s.get('action') == 'sell'])

                    if buy_count > sell_count:
                        recommendations[pattern_name] = 'buy'
                    elif sell_count > buy_count:
                        recommendations[pattern_name] = 'sell'
                    else:
                        recommendations[pattern_name] = 'hold'

            if not recommendations:
                return 0.5

            # حساب التوافق
            total_patterns = len(recommendations)
            buy_patterns = len([r for r in recommendations.values() if r == 'buy'])
            sell_patterns = len([r for r in recommendations.values() if r == 'sell'])

            # نسبة التوافق
            max_agreement = max(buy_patterns, sell_patterns)
            agreement_ratio = max_agreement / total_patterns

            # تعديل التوافق بناءً على عدد الأنماط المتفقة
            if agreement_ratio >= 0.75:  # 3 من 4 أو أكثر
                return 1.0
            elif agreement_ratio >= 0.5:  # 2 من 4
                return 0.8
            else:  # أقل من النصف
                return 0.6

        except Exception as e:
            logger.error(f"خطأ في حساب التوافق بين الأنماط: {str(e)}")
            return 0.5

    def _assess_unified_risks(self, timeframes_data: Dict[str, Dict[str, Any]],
                             analysis_components: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم شامل للمخاطر من جميع الأنماط"""
        try:
            risk_factors = []
            total_risk_score = 0

            # مخاطر التقلبات (من المضاربة السريعة)
            volatility_risk = self._assess_volatility_risk(timeframes_data)
            risk_factors.append(volatility_risk)
            total_risk_score += volatility_risk.get('score', 50)

            # مخاطر الاتجاه (من التداول اليومي)
            trend_risk = self._assess_trend_risk(timeframes_data)
            risk_factors.append(trend_risk)
            total_risk_score += trend_risk.get('score', 50)

            # مخاطر الانعكاس (من التداول المتأرجح)
            reversal_risk = self._assess_reversal_risk(timeframes_data)
            risk_factors.append(reversal_risk)
            total_risk_score += reversal_risk.get('score', 50)

            # مخاطر القيمة (من الاستثمار طويل المدى)
            value_risk = self._assess_value_risk(timeframes_data)
            risk_factors.append(value_risk)
            total_risk_score += value_risk.get('score', 50)

            # حساب المتوسط
            average_risk = total_risk_score / len(risk_factors) if risk_factors else 50

            # تحديد مستوى المخاطر
            if average_risk < 30:
                risk_level = 'low'
            elif average_risk < 50:
                risk_level = 'medium'
            elif average_risk < 70:
                risk_level = 'high'
            else:
                risk_level = 'very_high'

            return {
                'overall_risk_level': risk_level,
                'risk_score': average_risk,
                'risk_factors': risk_factors,
                'assessment_type': 'unified_comprehensive'
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم المخاطر الموحدة: {str(e)}")
            return {'overall_risk_level': 'medium', 'risk_score': 50}

    def _calculate_unified_confidence(self, analysis_components: Dict[str, Any]) -> Dict[str, Any]:
        """حساب عوامل الثقة الموحدة"""
        try:
            confidence_factors = {}

            # عامل قوة الإشارات
            total_signals = 0
            total_strength = 0

            for component in analysis_components.values():
                signals = component.get('signals', [])
                total_signals += len(signals)
                total_strength += sum(s.get('strength', 0) for s in signals)

            signal_strength_factor = min(total_strength / max(total_signals, 1) / 5, 1.0) if total_signals > 0 else 0
            confidence_factors['signal_strength'] = signal_strength_factor

            # عامل التوافق بين الأنماط
            pattern_agreement = self._calculate_pattern_agreement(analysis_components)
            confidence_factors['pattern_agreement'] = pattern_agreement

            # عامل تنوع الإطارات الزمنية
            unique_timeframes = set()
            for component in analysis_components.values():
                timeframes = component.get('timeframes', [])
                unique_timeframes.update(timeframes)

            timeframe_diversity = min(len(unique_timeframes) / 6, 1.0)  # 6 إطارات زمنية كحد أقصى
            confidence_factors['timeframe_diversity'] = timeframe_diversity

            # عامل شمولية التحليل
            analysis_coverage = len(analysis_components) / 4  # 4 أنماط
            confidence_factors['analysis_coverage'] = analysis_coverage

            # الثقة الإجمالية
            overall_confidence = (
                signal_strength_factor * 0.3 +
                pattern_agreement * 0.3 +
                timeframe_diversity * 0.2 +
                analysis_coverage * 0.2
            )

            confidence_factors['overall_confidence'] = overall_confidence

            return confidence_factors

        except Exception as e:
            logger.error(f"خطأ في حساب عوامل الثقة الموحدة: {str(e)}")
            return {'overall_confidence': 0.5}

    def _assess_volatility_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم مخاطر التقلبات"""
        try:
            # استخدام البيانات قصيرة المدى
            data_1m = timeframes_data.get('1m', {})
            data_5m = timeframes_data.get('5m', {})

            atr_1m = data_1m.get('atr', 0)
            volume_ratio = data_1m.get('volume_ratio', 1)

            # حساب مخاطر التقلبات
            volatility_score = 0
            if atr_1m > 0:
                volatility_score += min(atr_1m * 100, 40)  # ATR كمؤشر للتقلبات

            if volume_ratio > 3:  # حجم عالي جداً
                volatility_score += 30
            elif volume_ratio > 2:
                volatility_score += 20

            return {
                'type': 'volatility_risk',
                'score': min(volatility_score, 100),
                'level': 'high' if volatility_score > 60 else 'medium' if volatility_score > 30 else 'low'
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر التقلبات: {str(e)}")
            return {'type': 'volatility_risk', 'score': 50, 'level': 'medium'}

    def _assess_trend_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم مخاطر الاتجاه"""
        try:
            # استخدام البيانات متوسطة المدى
            data_1h = timeframes_data.get('1h', {})
            data_4h = timeframes_data.get('4h', {})

            adx_1h = data_1h.get('adx', 0)
            macd_1h = data_1h.get('macd', 0)
            macd_signal_1h = data_1h.get('macd_signal', 0)

            # حساب مخاطر الاتجاه
            trend_risk_score = 0

            if adx_1h < 20:  # اتجاه ضعيف
                trend_risk_score += 40
            elif adx_1h < 30:
                trend_risk_score += 20

            # تناقض MACD
            if abs(macd_1h - macd_signal_1h) < 0.1:  # تقارب خطوط MACD
                trend_risk_score += 30

            return {
                'type': 'trend_risk',
                'score': min(trend_risk_score, 100),
                'level': 'high' if trend_risk_score > 60 else 'medium' if trend_risk_score > 30 else 'low'
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر الاتجاه: {str(e)}")
            return {'type': 'trend_risk', 'score': 50, 'level': 'medium'}

    def _assess_reversal_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم مخاطر الانعكاس"""
        try:
            # استخدام البيانات طويلة المدى
            data_1d = timeframes_data.get('1d', {})

            rsi_1d = data_1d.get('rsi', 50)
            price = data_1d.get('price', 0)
            high_24h = data_1d.get('high_24h', 0)
            low_24h = data_1d.get('low_24h', 0)

            # حساب مخاطر الانعكاس
            reversal_risk_score = 0

            # RSI في المناطق الحرجة
            if rsi_1d > 70 or rsi_1d < 30:
                reversal_risk_score += 40

            # موقع السعر في النطاق اليومي
            if price and high_24h and low_24h:
                daily_range = high_24h - low_24h
                if daily_range > 0:
                    price_position = (price - low_24h) / daily_range
                    if price_position > 0.9 or price_position < 0.1:
                        reversal_risk_score += 30

            return {
                'type': 'reversal_risk',
                'score': min(reversal_risk_score, 100),
                'level': 'high' if reversal_risk_score > 60 else 'medium' if reversal_risk_score > 30 else 'low'
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر الانعكاس: {str(e)}")
            return {'type': 'reversal_risk', 'score': 50, 'level': 'medium'}

    def _assess_value_risk(self, timeframes_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """تقييم مخاطر القيمة"""
        try:
            # استخدام البيانات طويلة المدى جداً
            data_1w = timeframes_data.get('1w', {})
            data_1M = timeframes_data.get('1M', {})

            sma200_1w = data_1w.get('sma200', 0)
            price = data_1w.get('price', 0)

            # حساب مخاطر القيمة
            value_risk_score = 0

            # انحراف السعر عن المتوسط طويل المدى
            if sma200_1w and price:
                deviation = abs(price - sma200_1w) / sma200_1w * 100
                if deviation > 50:  # انحراف كبير
                    value_risk_score += 50
                elif deviation > 30:
                    value_risk_score += 30
                elif deviation > 20:
                    value_risk_score += 20

            return {
                'type': 'value_risk',
                'score': min(value_risk_score, 100),
                'level': 'high' if value_risk_score > 60 else 'medium' if value_risk_score > 30 else 'low'
            }

        except Exception as e:
            logger.error(f"خطأ في تقييم مخاطر القيمة: {str(e)}")
            return {'type': 'value_risk', 'score': 50, 'level': 'medium'}
