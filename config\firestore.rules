rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد المستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد إعدادات المستخدمين
    match /user_settings/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد تفضيلات المستخدمين
    match /user_preferences/{userId} {
      allow read, write: if request.auth != null;
    }
    
    // قواعد الإشعارات
    match /notifications/{notificationId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // قواعد إحصائيات الإشعارات
    match /user_notification_stats/{userId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // قواعد قواعد الإشعارات
    match /notification_rules/{ruleId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // قواعد تفضيلات الإشعارات
    match /notification_preferences/{userId} {
      allow read, write: if request.auth != null;
      allow create: if request.auth != null;
    }
    
    // قواعد المستخدمين المحظورين
    match /banned_users/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // قواعد عامة للمجموعات الأخرى
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}