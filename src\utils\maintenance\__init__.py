"""
أدوات صيانة وإصلاح النظام

هذا المجلد يحتوي على أداة شاملة لتشخيص وإصلاح مشاكل النظام.
تم توحيد جميع الأدوات في أداة واحدة لضمان الأمان والفعالية.

الملفات المتوفرة:
- fix_notifications_issue.py: أداة شاملة للتشخيص والإصلاح
- README.md: دليل مفصل للاستخدام

الاستخدام:
    python src/utils/maintenance/fix_notifications_issue.py

الفلسفة الأمنية:
- أداة واحدة شاملة = نقطة دخول واحدة آمنة
- تشخيص شامل قبل أي تعديل
- معالجة أخطاء متقدمة
- تسجيل مفصل لجميع العمليات
"""

__version__ = "2.0.0"
__author__ = "TradingTelegram Team"
__description__ = "أدوات صيانة وإصلاح النظام الموحدة"

# تصدير الوظائف الرئيسية
__all__ = [
    "fix_notifications_issue",
]