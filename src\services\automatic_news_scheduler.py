"""
نظام الجدولة التلقائية للأخبار الذكي
يدير جلب الأخبار تلقائياً مع مراعاة حدود API وتوزيع الطلبات بذكاء
"""

import asyncio
import logging
from datetime import datetime, timedelta, time
from typing import Dict, List, Optional, Any, Callable
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor
import pytz

logger = logging.getLogger(__name__)

class APIRateManager:
    """مدير معدلات API للتحكم في استخدام الطلبات"""
    
    def __init__(self):
        # حدود API لكل منصة (طلبات/دقيقة)
        # gemini-2.0-flash له حدود أكثر سخاءً من النماذج القديمة
        self.api_limits = {
            'binance': {'limit': 60, 'used': 0, 'reset_time': datetime.now()},
            'coindesk': {'limit': 30, 'used': 0, 'reset_time': datetime.now()},
            'coingecko': {'limit': 50, 'used': 0, 'reset_time': datetime.now()},
            'gemini': {'limit': 60, 'used': 0, 'reset_time': datetime.now()}  # gemini-2.0-flash أكثر سخاءً
        }
        
        # إحصائيات الاستخدام اليومي
        self.daily_stats = {
            'binance': {'total_requests': 0, 'successful': 0, 'failed': 0},
            'coindesk': {'total_requests': 0, 'successful': 0, 'failed': 0},
            'coingecko': {'total_requests': 0, 'successful': 0, 'failed': 0},
            'gemini': {'total_requests': 0, 'successful': 0, 'failed': 0}
        }
    
    def can_make_request(self, platform: str) -> bool:
        """التحقق من إمكانية إجراء طلب API"""
        if platform not in self.api_limits:
            return False
        
        limit_info = self.api_limits[platform]
        current_time = datetime.now()
        
        # إعادة تعيين العداد كل دقيقة
        if current_time - limit_info['reset_time'] >= timedelta(minutes=1):
            limit_info['used'] = 0
            limit_info['reset_time'] = current_time
        
        return limit_info['used'] < limit_info['limit']
    
    def record_request(self, platform: str, success: bool = True):
        """تسجيل طلب API"""
        if platform in self.api_limits:
            self.api_limits[platform]['used'] += 1
            
        if platform in self.daily_stats:
            self.daily_stats[platform]['total_requests'] += 1
            if success:
                self.daily_stats[platform]['successful'] += 1
            else:
                self.daily_stats[platform]['failed'] += 1
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الاستخدام"""
        stats = {}
        for platform, limits in self.api_limits.items():
            stats[platform] = {
                'current_usage': limits['used'],
                'limit': limits['limit'],
                'remaining': limits['limit'] - limits['used'],
                'daily_stats': self.daily_stats.get(platform, {})
            }
        return stats

class AutomaticNewsScheduler:
    """نظام الجدولة التلقائية للأخبار"""
    
    def __init__(self, news_system=None, db=None, bot=None):
        self.news_system = news_system
        self.db = db
        self.bot = bot  # Telegram bot instance
        self.scheduler = None
        self.rate_manager = APIRateManager()
        self.is_running = False
        
        # إعدادات الجدولة حسب الوقت
        self.schedule_config = {
            'morning': {  # 6-12
                'interval_minutes': 10,
                'priority': 'high',
                'sources': ['binance', 'coindesk', 'coingecko']
            },
            'afternoon': {  # 12-18
                'interval_minutes': 15,
                'priority': 'medium',
                'sources': ['binance', 'coindesk']
            },
            'evening': {  # 18-24
                'interval_minutes': 30,
                'priority': 'low',
                'sources': ['binance']
            },
            'night': {  # 0-6
                'interval_minutes': 60,
                'priority': 'minimal',
                'sources': ['binance']
            }
        }
        
        # قائمة المستخدمين المشتركين في التنبيهات
        self.subscribers = set()
        
    def _setup_scheduler(self):
        """إعداد المجدول"""
        jobstores = {
            'default': MemoryJobStore()
        }
        executors = {
            'default': AsyncIOExecutor()
        }
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }
        
        self.scheduler = AsyncIOScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone=pytz.timezone('UTC')
        )
    
    async def start(self):
        """بدء نظام الجدولة التلقائية"""
        if self.is_running:
            logger.warning("نظام الجدولة يعمل بالفعل")
            return
        
        logger.info("🚀 بدء نظام الجدولة التلقائية للأخبار...")
        
        self._setup_scheduler()
        
        # جدولة المهام الأساسية
        await self._schedule_news_fetching()
        await self._schedule_ai_analysis()
        await self._schedule_notifications()
        await self._schedule_maintenance()

        # تفعيل الإشعارات التلقائية لجميع المستخدمين
        await self._enable_notifications_for_all_users()

        self.scheduler.start()
        self.is_running = True

        logger.info("✅ تم بدء نظام الجدولة التلقائية بنجاح")
    
    async def stop(self):
        """إيقاف نظام الجدولة"""
        if not self.is_running:
            return
        
        logger.info("⏹️ إيقاف نظام الجدولة التلقائية...")
        
        if self.scheduler:
            self.scheduler.shutdown(wait=False)
        
        self.is_running = False
        logger.info("✅ تم إيقاف نظام الجدولة بنجاح")

    async def _enable_notifications_for_all_users(self):
        """تفعيل الإشعارات التلقائية لجميع المستخدمين"""
        try:
            logger.info("🔔 تفعيل الإشعارات التلقائية لجميع المستخدمين...")

            from services.automatic_news_notifications import AutomaticNewsNotifications
            notifications_system = AutomaticNewsNotifications(db=self.db, bot=self.bot)

            # تحميل قواعد المستخدمين الحالية
            await notifications_system.load_user_rules()

            # تفعيل الإشعارات لجميع المستخدمين
            success = await notifications_system.enable_automatic_notifications_for_all_users()

            if success:
                logger.info("✅ تم تفعيل الإشعارات التلقائية لجميع المستخدمين بنجاح")
            else:
                logger.warning("⚠️ حدثت مشاكل في تفعيل الإشعارات لبعض المستخدمين")

        except Exception as e:
            logger.error(f"خطأ في تفعيل الإشعارات التلقائية: {str(e)}")

    async def _schedule_news_fetching(self):
        """جدولة جلب الأخبار حسب الأوقات المختلفة"""
        
        # الصباح (6-12) - كل 10 دقائق
        self.scheduler.add_job(
            self._fetch_morning_news,
            CronTrigger(hour='6-11', minute='*/10'),
            id='morning_news',
            name='جلب أخبار الصباح'
        )
        
        # بعد الظهر (12-18) - كل 15 دقيقة
        self.scheduler.add_job(
            self._fetch_afternoon_news,
            CronTrigger(hour='12-17', minute='*/15'),
            id='afternoon_news',
            name='جلب أخبار بعد الظهر'
        )
        
        # المساء (18-24) - كل 30 دقيقة
        self.scheduler.add_job(
            self._fetch_evening_news,
            CronTrigger(hour='18-23', minute='*/30'),
            id='evening_news',
            name='جلب أخبار المساء'
        )
        
        # الليل (0-6) - كل ساعة
        self.scheduler.add_job(
            self._fetch_night_news,
            CronTrigger(hour='0-5', minute='0'),
            id='night_news',
            name='جلب أخبار الليل'
        )
    
    async def _schedule_ai_analysis(self):
        """جدولة التحليل بالذكاء الاصطناعي"""
        
        # تحليل الأخبار المهمة كل 30 دقيقة
        self.scheduler.add_job(
            self._analyze_important_news,
            IntervalTrigger(minutes=30),
            id='ai_analysis',
            name='تحليل الأخبار بالذكاء الاصطناعي'
        )
        
        # تحليل العملات الجديدة كل ساعة
        self.scheduler.add_job(
            self._analyze_new_coins,
            IntervalTrigger(hours=1),
            id='new_coins_analysis',
            name='تحليل العملات الجديدة'
        )
    
    async def _schedule_notifications(self):
        """جدولة الإشعارات"""
        
        # إرسال تقرير يومي
        self.scheduler.add_job(
            self._send_daily_report,
            CronTrigger(hour=9, minute=0),
            id='daily_report',
            name='التقرير اليومي'
        )
        
        # فحص الأخبار العاجلة كل 5 دقائق
        self.scheduler.add_job(
            self._check_breaking_news,
            IntervalTrigger(minutes=5),
            id='breaking_news',
            name='فحص الأخبار العاجلة'
        )
    
    async def _schedule_maintenance(self):
        """جدولة مهام الصيانة"""
        
        # تنظيف البيانات القديمة يومياً
        self.scheduler.add_job(
            self._cleanup_old_data,
            CronTrigger(hour=2, minute=0),
            id='cleanup',
            name='تنظيف البيانات القديمة'
        )
        
        # إعادة تعيين إحصائيات API يومياً
        self.scheduler.add_job(
            self._reset_daily_stats,
            CronTrigger(hour=0, minute=0),
            id='reset_stats',
            name='إعادة تعيين الإحصائيات'
        )
    
    async def _fetch_morning_news(self):
        """جلب أخبار الصباح"""
        await self._fetch_news_with_rate_limit(['binance', 'coindesk', 'coingecko'], 'morning')
    
    async def _fetch_afternoon_news(self):
        """جلب أخبار بعد الظهر"""
        await self._fetch_news_with_rate_limit(['binance', 'coindesk'], 'afternoon')
    
    async def _fetch_evening_news(self):
        """جلب أخبار المساء"""
        await self._fetch_news_with_rate_limit(['binance'], 'evening')
    
    async def _fetch_night_news(self):
        """جلب أخبار الليل"""
        await self._fetch_news_with_rate_limit(['binance'], 'night')
    
    async def _fetch_news_with_rate_limit(self, sources: List[str], period: str):
        """جلب الأخبار مع مراعاة حدود API"""
        if not self.news_system:
            logger.warning("نظام الأخبار غير متوفر")
            return
        
        logger.info(f"🔄 جلب أخبار {period} من المصادر: {sources}")
        
        for source in sources:
            if self.rate_manager.can_make_request(source):
                try:
                    # جلب الأخبار حسب المصدر
                    if source == 'binance':
                        news = await self.news_system.fetch_news_from_binance()
                    elif source == 'coindesk':
                        news = await self.news_system.fetch_news_from_coindesk()
                    elif source == 'coingecko':
                        news = await self.news_system.fetch_news_from_coingecko()
                    else:
                        continue
                    
                    self.rate_manager.record_request(source, True)
                    logger.info(f"✅ تم جلب {len(news) if news else 0} خبر من {source}")
                    
                    # حفظ الأخبار في قاعدة البيانات
                    if news and self.db:
                        await self._save_news_to_db(news, source)
                    
                except Exception as e:
                    self.rate_manager.record_request(source, False)
                    logger.error(f"❌ خطأ في جلب الأخبار من {source}: {str(e)}")
            else:
                logger.warning(f"⚠️ تم تجاوز حد API لـ {source}")
    
    async def _save_news_to_db(self, news_items: List, source: str):
        """حفظ الأخبار في قاعدة البيانات"""
        if not self.db:
            return

        try:
            for news_item in news_items:
                # إنشاء معرف فريد للخبر
                news_id = f"{source}_{hash(news_item.title)}_{int(news_item.published_at.timestamp())}"

                news_data = {
                    'id': news_id,
                    'title': news_item.title,
                    'content': news_item.content,
                    'source': source,
                    'published_at': news_item.published_at.isoformat(),
                    'url': news_item.url,
                    'symbols': news_item.symbols or [],
                    'sentiment': news_item.sentiment,
                    'ai_analysis': news_item.ai_analysis,
                    'created_at': datetime.now().isoformat(),
                    'is_processed': False,
                    'interested_users': [],
                    'sent_to_users': [],
                    'total_interested_users': 0,
                    'is_sent_to_all': False
                }

                # حفظ في مجموعة الأخبار
                self.db.collection('news').document(news_id).set(news_data, merge=True)

        except Exception as e:
            logger.error(f"خطأ في حفظ الأخبار: {str(e)}")

    async def _analyze_important_news(self):
        """تحليل الأخبار المهمة بالذكاء الاصطناعي"""
        if not self.news_system or not self.rate_manager.can_make_request('gemini'):
            return

        try:
            logger.info("🤖 بدء تحليل الأخبار المهمة بالذكاء الاصطناعي...")

            # جلب الأخبار غير المحللة من قاعدة البيانات
            unprocessed_news = await self._get_unprocessed_news(limit=5)

            if not unprocessed_news:
                logger.info("✅ لا توجد أخبار جديدة تحتاج للتحليل")
                return

            logger.info(f"📊 تم العثور على {len(unprocessed_news)} خبر يحتاج للتحليل")

            processed_count = 0
            analyzed_news_items = []  # قائمة الأخبار المحللة لإرسالها للإشعارات
            for news_item in unprocessed_news:
                if self.rate_manager.can_make_request('gemini'):
                    try:
                        # تحليل الخبر بالذكاء الاصطناعي
                        analyzed_news = await self.news_system.analyze_news_with_ai(news_item)

                        # تحديث الخبر في قاعدة البيانات
                        if analyzed_news and self.db:
                            await self._update_news_analysis(news_item.id, analyzed_news)
                            analyzed_news_items.append(analyzed_news)  # إضافة للقائمة
                            processed_count += 1
                            logger.info(f"✅ تم تحليل الخبر: {news_item.title[:50]}...")

                        self.rate_manager.record_request('gemini', True)

                        # تأخير لتجنب تجاوز حدود API
                        await asyncio.sleep(2)

                    except Exception as e:
                        self.rate_manager.record_request('gemini', False)
                        logger.error(f"خطأ في تحليل الخبر {news_item.id}: {str(e)}")
                else:
                    logger.warning("تم تجاوز حد API لـ Gemini")
                    break

            logger.info(f"🎯 تم تحليل {processed_count} خبر من أصل {len(unprocessed_news)}")

            # إرسال الأخبار المحللة لنظام الإشعارات
            if analyzed_news_items:
                await self._send_analyzed_news_to_notifications(analyzed_news_items)

        except Exception as e:
            logger.error(f"خطأ في تحليل الأخبار المهمة: {str(e)}")

    async def _analyze_new_coins(self):
        """تحليل العملات الجديدة"""
        if not self.news_system:
            return

        try:
            logger.info("🆕 فحص العملات الجديدة...")

            # البحث عن أخبار العملات الجديدة
            new_coin_news = await self.news_system.get_new_coin_alerts(limit=3)

            if new_coin_news:
                logger.info(f"تم العثور على {len(new_coin_news)} خبر عن عملات جديدة")

                # إرسال تنبيهات للمشتركين
                await self._notify_subscribers_about_new_coins(new_coin_news)

        except Exception as e:
            logger.error(f"خطأ في تحليل العملات الجديدة: {str(e)}")

    async def _check_breaking_news(self):
        """فحص الأخبار العاجلة"""
        try:
            # جلب آخر الأخبار
            recent_news = await self._get_recent_news(minutes=10)

            # البحث عن كلمات مفتاحية للأخبار العاجلة
            breaking_keywords = [
                'breaking', 'urgent', 'alert', 'crash', 'surge', 'pump', 'dump',
                'عاجل', 'تحذير', 'انهيار', 'ارتفاع', 'هبوط'
            ]

            breaking_news = []
            for news in recent_news:
                title_lower = news.title.lower()
                content_lower = news.content.lower()

                if any(keyword in title_lower or keyword in content_lower for keyword in breaking_keywords):
                    breaking_news.append(news)

            if breaking_news:
                logger.info(f"🚨 تم العثور على {len(breaking_news)} خبر عاجل")
                logger.info(f"🚨 إرسال {len(breaking_news)} خبر عاجل للمستخدمين")
                await self._notify_all_users_about_breaking_news(breaking_news)

        except Exception as e:
            logger.error(f"خطأ في فحص الأخبار العاجلة: {str(e)}")

    async def _send_daily_report(self):
        """إرسال التقرير اليومي"""
        try:
            logger.info("📊 إنشاء التقرير اليومي...")

            # جمع إحصائيات اليوم
            usage_stats = self.rate_manager.get_usage_stats()

            # جلب أهم الأخبار لليوم
            today_news = await self._get_today_news()

            # إنشاء التقرير
            report = self._generate_daily_report(usage_stats, today_news)

            # إرسال التقرير للمشتركين
            await self._send_report_to_subscribers(report)

        except Exception as e:
            logger.error(f"خطأ في إرسال التقرير اليومي: {str(e)}")

    async def _cleanup_old_data(self):
        """تنظيف البيانات القديمة"""
        if not self.db:
            return

        try:
            logger.info("🧹 تنظيف البيانات القديمة...")

            # حذف الأخبار الأقدم من 3 أيام
            cutoff_date = datetime.now() - timedelta(days=3)

            old_news_query = self.db.collection('news').where(
                'created_at', '<', cutoff_date.isoformat()
            )

            old_news = old_news_query.get()
            deleted_count = 0

            for doc in old_news:
                doc.reference.delete()
                deleted_count += 1

            logger.info(f"✅ تم حذف {deleted_count} خبر قديم")

        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات: {str(e)}")

    async def _reset_daily_stats(self):
        """إعادة تعيين الإحصائيات اليومية"""
        logger.info("🔄 إعادة تعيين الإحصائيات اليومية...")

        for platform in self.rate_manager.daily_stats:
            self.rate_manager.daily_stats[platform] = {
                'total_requests': 0,
                'successful': 0,
                'failed': 0
            }

        logger.info("✅ تم إعادة تعيين الإحصائيات")

    # وظائف مساعدة
    async def _get_unprocessed_news(self, limit: int = 10) -> List:
        """جلب الأخبار غير المحللة"""
        if not self.db:
            return []

        try:
            # استخدام استعلام بسيط بدون ترتيب لتجنب مشكلة الفهرسة
            unprocessed_query = self.db.collection('news').where(
                'is_processed', '==', False
            ).limit(limit * 2)  # جلب عدد أكبر للفلترة اليدوية

            docs = unprocessed_query.get()
            news_items = []

            # تحويل البيانات وترتيبها يدوياً
            temp_items = []
            for doc in docs:
                data = doc.to_dict()
                # تحويل البيانات إلى NewsItem
                from services.news_system import NewsItem, NewsSource

                try:
                    # تحويل آمن لـ NewsSource
                    source_str = data.get('source', 'crypto_news')
                    try:
                        source = NewsSource(source_str)
                    except ValueError:
                        # إذا لم يكن المصدر معرف، استخدم CRYPTO_NEWS كافتراضي
                        logger.warning(f"مصدر غير معروف: {source_str}, استخدام CRYPTO_NEWS كافتراضي")
                        source = NewsSource.CRYPTO_NEWS

                    news_item = NewsItem(
                        id=data.get('id'),
                        title=data.get('title'),
                        content=data.get('content'),
                        source=source,
                        published_at=datetime.fromisoformat(data.get('published_at')),
                        url=data.get('url'),
                        symbols=data.get('symbols', []),
                        sentiment=data.get('sentiment'),
                        ai_analysis=data.get('ai_analysis')
                    )
                    temp_items.append(news_item)
                except Exception as item_error:
                    logger.warning(f"خطأ في تحويل عنصر الأخبار: {str(item_error)}")
                    continue

            # ترتيب الأخبار حسب تاريخ النشر (الأحدث أولاً)
            temp_items.sort(key=lambda x: x.published_at, reverse=True)

            # أخذ العدد المطلوب فقط
            news_items = temp_items[:limit]

            logger.info(f"تم جلب {len(news_items)} خبر غير محلل من أصل {len(temp_items)} خبر")
            return news_items

        except Exception as e:
            logger.error(f"خطأ في جلب الأخبار غير المحللة: {str(e)}")
            return []

    async def _update_news_analysis(self, news_id: str, analyzed_news):
        """تحديث تحليل الخبر في قاعدة البيانات"""
        if not self.db:
            logger.warning("قاعدة البيانات غير متوفرة لتحديث تحليل الخبر")
            return

        try:
            update_data = {
                'ai_analysis': analyzed_news.ai_analysis,
                'sentiment': analyzed_news.sentiment,
                'trading_recommendation': analyzed_news.trading_recommendation,
                'confidence_score': analyzed_news.confidence_score,
                'is_processed': True,
                'analyzed_at': datetime.now().isoformat()
            }

            # التحقق من وجود الخبر قبل التحديث
            doc_ref = self.db.collection('news').document(news_id)
            if doc_ref.get().exists:
                doc_ref.update(update_data)
                logger.debug(f"تم تحديث تحليل الخبر {news_id} بنجاح")
            else:
                logger.warning(f"الخبر {news_id} غير موجود في قاعدة البيانات")

        except Exception as e:
            logger.error(f"خطأ في تحديث تحليل الخبر {news_id}: {str(e)}")

    async def _get_recent_news(self, minutes: int = 30) -> List:
        """جلب الأخبار الحديثة"""
        if not self.db:
            return []

        try:
            cutoff_time = datetime.now() - timedelta(minutes=minutes)

            recent_query = self.db.collection('news').where(
                'created_at', '>=', cutoff_time.isoformat()
            ).order_by('created_at', direction='DESCENDING')

            docs = recent_query.get()
            news_items = []

            for doc in docs:
                data = doc.to_dict()
                from services.news_system import NewsItem, NewsSource

                # تحويل آمن لـ NewsSource
                source_str = data.get('source', 'crypto_news')
                try:
                    source = NewsSource(source_str)
                except ValueError:
                    # إذا لم يكن المصدر معرف، استخدم CRYPTO_NEWS كافتراضي
                    logger.warning(f"مصدر غير معروف: {source_str}, استخدام CRYPTO_NEWS كافتراضي")
                    source = NewsSource.CRYPTO_NEWS

                news_item = NewsItem(
                    id=data.get('id'),
                    title=data.get('title'),
                    content=data.get('content'),
                    source=source,
                    published_at=datetime.fromisoformat(data.get('published_at')),
                    url=data.get('url'),
                    symbols=data.get('symbols', []),
                    sentiment=data.get('sentiment'),
                    ai_analysis=data.get('ai_analysis')
                )
                news_items.append(news_item)

            return news_items

        except Exception as e:
            logger.error(f"خطأ في جلب الأخبار الحديثة: {str(e)}")
            return []

    async def _get_today_news(self) -> List:
        """جلب أخبار اليوم"""
        if not self.db:
            return []

        try:
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

            today_query = self.db.collection('news').where(
                'created_at', '>=', today_start.isoformat()
            ).order_by('created_at', direction='DESCENDING')

            docs = today_query.get()
            return [doc.to_dict() for doc in docs]

        except Exception as e:
            logger.error(f"خطأ في جلب أخبار اليوم: {str(e)}")
            return []

    def _generate_daily_report(self, usage_stats: Dict, today_news: List) -> str:
        """إنشاء التقرير اليومي"""
        report = "📊 **التقرير اليومي للأخبار**\n\n"

        # إحصائيات API
        report += "🔧 **إحصائيات استخدام API:**\n"
        for platform, stats in usage_stats.items():
            daily = stats.get('daily_stats', {})
            report += f"• {platform.title()}: {daily.get('successful', 0)} طلب ناجح، {daily.get('failed', 0)} فاشل\n"

        report += f"\n📰 **إجمالي الأخبار اليوم:** {len(today_news)}\n"

        # أهم الأخبار
        if today_news:
            report += "\n🔥 **أهم الأخبار:**\n"
            for i, news in enumerate(today_news[:5], 1):
                report += f"{i}. {news.get('title', 'بدون عنوان')[:100]}...\n"

        return report

    async def _notify_subscribers_about_new_coins(self, new_coin_news: List):
        """إرسال تنبيهات العملات الجديدة للمشتركين"""
        # سيتم تنفيذ هذه الوظيفة لاحقاً مع نظام الإشعارات
        logger.info(f"📢 إرسال تنبيهات {len(new_coin_news)} عملة جديدة للمشتركين")

    async def _notify_subscribers_about_breaking_news(self, breaking_news: List):
        """إرسال تنبيهات الأخبار العاجلة للمشتركين"""
        # سيتم تنفيذ هذه الوظيفة لاحقاً مع نظام الإشعارات
        logger.info(f"🚨 إرسال {len(breaking_news)} خبر عاجل للمشتركين")

    async def _send_analyzed_news_to_notifications(self, analyzed_news_items: List):
        """إرسال الأخبار المحللة لنظام الإشعارات - محسن"""
        try:
            if not analyzed_news_items:
                logger.info("📢 لا توجد أخبار محللة للإرسال")
                return

            logger.info(f"📢 إرسال {len(analyzed_news_items)} خبر محلل لنظام الإشعارات...")

            from services.automatic_news_notifications import AutomaticNewsNotifications
            notifications_system = AutomaticNewsNotifications(db=self.db, bot=self.bot)

            # تحميل قواعد المستخدمين
            await notifications_system.load_user_rules()
            logger.info(f"📋 تم تحميل قواعد المستخدمين")

            # معالجة الأخبار المحللة لإنشاء الإشعارات
            await notifications_system.process_news_for_notifications(analyzed_news_items)

            # إحصائيات الإرسال
            notification_stats = await notifications_system.get_notification_stats()
            logger.info(f"📊 إحصائيات الإشعارات: {notification_stats}")

            logger.info("✅ تم إرسال الأخبار المحللة لنظام الإشعارات بنجاح")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال الأخبار المحللة لنظام الإشعارات: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())

    async def _notify_all_users_about_breaking_news(self, breaking_news: List):
        """إرسال تنبيهات الأخبار العاجلة لجميع المستخدمين (وليس المشتركين فقط)"""
        try:
            logger.info(f"🚨 إرسال {len(breaking_news)} خبر عاجل لجميع المستخدمين...")

            from services.automatic_news_notifications import AutomaticNewsNotifications
            notifications_system = AutomaticNewsNotifications(db=self.db, bot=self.bot)

            # تحميل قواعد المستخدمين
            await notifications_system.load_user_rules()

            # معالجة الأخبار العاجلة لإنشاء الإشعارات لجميع المستخدمين
            await notifications_system.process_news_for_notifications(breaking_news)

            logger.info("✅ تم إرسال الأخبار العاجلة لجميع المستخدمين بنجاح")

        except Exception as e:
            logger.error(f"خطأ في إرسال الأخبار العاجلة لجميع المستخدمين: {str(e)}")

    async def _send_report_to_subscribers(self, report: str):
        """إرسال التقرير للمشتركين"""
        # سيتم تنفيذ هذه الوظيفة لاحقاً مع نظام الإشعارات
        logger.info("📊 إرسال التقرير اليومي للمشتركين")

    def add_subscriber(self, user_id: str):
        """إضافة مشترك للتنبيهات"""
        self.subscribers.add(user_id)
        logger.info(f"✅ تم إضافة المستخدم {user_id} للتنبيهات التلقائية")

    def remove_subscriber(self, user_id: str):
        """إزالة مشترك من التنبيهات"""
        self.subscribers.discard(user_id)
        logger.info(f"❌ تم إزالة المستخدم {user_id} من التنبيهات التلقائية")

    def get_scheduler_status(self) -> Dict[str, Any]:
        """الحصول على حالة المجدول"""
        if not self.scheduler:
            return {'status': 'not_initialized'}

        jobs = self.scheduler.get_jobs()
        return {
            'status': 'running' if self.is_running else 'stopped',
            'total_jobs': len(jobs),
            'jobs': [{'id': job.id, 'name': job.name, 'next_run': str(job.next_run_time)} for job in jobs],
            'subscribers_count': len(self.subscribers),
            'api_usage': self.rate_manager.get_usage_stats()
        }

# إنشاء نسخة عامة من النظام
automatic_news_scheduler = None

def initialize_automatic_news_scheduler(news_system=None, db=None, bot=None):
    """تهيئة نظام الجدولة التلقائية"""
    global automatic_news_scheduler
    automatic_news_scheduler = AutomaticNewsScheduler(news_system, db, bot)
    logger.info("✅ تم تهيئة نظام الجدولة التلقائية للأخبار")
    return automatic_news_scheduler
