# -*- coding: utf-8 -*-
"""
وحدة الأدوات المساعدة للمشروع
"""

# استيراد الدوال من ملف utils.py
from .utils import (
    get_text,
    fix_bold_formatting,
    clean_markdown_content,
    sanitize_telegram_text,
    validate_markdown_entities,
    split_long_message,
    delete_message_after_delay
)

# استيراد الدوال من ملف text_helpers.py
from .text_helpers import (
    get_main_menu_text,
    get_main_menu_keyboard
)

# تصدير الدوال للاستخدام المباشر عند استيراد وحدة utils
__all__ = [
    'get_text',
    'fix_bold_formatting',
    'clean_markdown_content',
    'sanitize_telegram_text',
    'validate_markdown_entities',
    'split_long_message',
    'delete_message_after_delay',
    'get_main_menu_text',
    'get_main_menu_keyboard'
]