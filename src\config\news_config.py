"""
إعدادات نظام الأخبار الذكي
"""

import os
from typing import Dict, Any

class NewsConfig:
    """إعدادات نظام الأخبار"""
    
    # مفتاح Gemini AI API
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', None)
    
    # إعدادات مصادر الأخبار
    NEWS_SOURCES = {
        'binance': {
            'enabled': True,
            'url': 'https://www.binance.com/bapi/composite/v1/public/cms/article/list/query',
            'rate_limit': 60,  # طلب في الدقيقة
            'timeout': 10  # تقليل timeout لتحسين الأداء
        },
        'coindesk': {
            'enabled': True,
            'url': 'https://feeds.coindesk.com/rss',
            'rate_limit': 30,
            'timeout': 10  # تقليل timeout لتحسين الأداء
        },
        'coingecko': {
            'enabled': True,
            'url': 'https://api.coingecko.com/api/v3/news',
            'rate_limit': 50,
            'timeout': 10  # تقليل timeout لتحسين الأداء
        }
    }
    
    # إعدادات التحليل بالذكاء الاصطناعي
    AI_ANALYSIS = {
        'enabled': True,
        'max_retries': 3,
        'timeout': 60,
        'temperature': 0.7,
        'max_tokens': 1024
    }
    
    # إعدادات التخزين المؤقت
    CACHE_SETTINGS = {
        'news_cache_duration': 300,  # 5 دقائق
        'price_cache_duration': 60,  # دقيقة واحدة
        'analysis_cache_duration': 1800  # 30 دقيقة
    }
    
    # إعدادات الأخبار
    NEWS_SETTINGS = {
        'max_news_per_request': 20,
        'max_analysis_per_request': 5,
        'default_language': 'ar',
        'supported_languages': ['ar', 'en']
    }
    
    # كلمات مفتاحية للعملات الجديدة
    NEW_COIN_KEYWORDS = {
        'ar': [
            'إدراج جديد', 'عملة جديدة', 'إطلاق', 'توكن جديد', 
            'قائمة جديدة', 'عملة مشفرة جديدة', 'مشروع جديد'
        ],
        'en': [
            'new listing', 'new token', 'launch', 'debut', 'new coin',
            'new cryptocurrency', 'new project', 'listing announcement'
        ]
    }
    
    # كلمات مفتاحية لتوقعات الأسعار
    PRICE_PREDICTION_KEYWORDS = {
        'ar': [
            'توقع', 'تحليل فني', 'هدف سعر', 'مقاومة', 'دعم',
            'اتجاه صاعد', 'اتجاه هابط', 'تنبؤ'
        ],
        'en': [
            'prediction', 'technical analysis', 'price target', 'resistance',
            'support', 'bullish', 'bearish', 'forecast', 'outlook'
        ]
    }
    
    @classmethod
    def get_gemini_config(cls) -> Dict[str, Any]:
        """الحصول على إعدادات Gemini AI"""
        return {
            'api_key': cls.GEMINI_API_KEY,
            'model': 'gemini-pro',
            'temperature': cls.AI_ANALYSIS['temperature'],
            'max_tokens': cls.AI_ANALYSIS['max_tokens'],
            'timeout': cls.AI_ANALYSIS['timeout']
        }
    
    @classmethod
    def get_news_source_config(cls, source_name: str) -> Dict[str, Any]:
        """الحصول على إعدادات مصدر أخبار محدد"""
        return cls.NEWS_SOURCES.get(source_name, {})
    
    @classmethod
    def is_source_enabled(cls, source_name: str) -> bool:
        """التحقق من تفعيل مصدر أخبار"""
        source_config = cls.get_news_source_config(source_name)
        return source_config.get('enabled', False)
    
    @classmethod
    def get_cache_duration(cls, cache_type: str) -> int:
        """الحصول على مدة التخزين المؤقت"""
        return cls.CACHE_SETTINGS.get(f'{cache_type}_cache_duration', 300)
    
    @classmethod
    def get_keywords(cls, keyword_type: str, language: str = 'ar') -> list:
        """الحصول على الكلمات المفتاحية"""
        if keyword_type == 'new_coin':
            return cls.NEW_COIN_KEYWORDS.get(language, [])
        elif keyword_type == 'price_prediction':
            return cls.PRICE_PREDICTION_KEYWORDS.get(language, [])
        return []
    
    @classmethod
    def validate_config(cls) -> Dict[str, bool]:
        """التحقق من صحة الإعدادات"""
        validation_results = {
            'gemini_api_key': cls.GEMINI_API_KEY is not None,
            'news_sources': any(
                source.get('enabled', False) 
                for source in cls.NEWS_SOURCES.values()
            ),
            'cache_settings': all(
                isinstance(duration, int) and duration > 0
                for duration in cls.CACHE_SETTINGS.values()
            )
        }
        
        return validation_results
    
    @classmethod
    def get_validation_report(cls) -> str:
        """الحصول على تقرير التحقق من الإعدادات"""
        validation = cls.validate_config()
        
        report = "📋 تقرير إعدادات نظام الأخبار:\n\n"
        
        if validation['gemini_api_key']:
            report += "✅ مفتاح Gemini AI متوفر\n"
        else:
            report += "❌ مفتاح Gemini AI غير متوفر\n"
        
        if validation['news_sources']:
            enabled_sources = [
                name for name, config in cls.NEWS_SOURCES.items()
                if config.get('enabled', False)
            ]
            report += f"✅ مصادر الأخبار المفعلة: {', '.join(enabled_sources)}\n"
        else:
            report += "❌ لا توجد مصادر أخبار مفعلة\n"
        
        if validation['cache_settings']:
            report += "✅ إعدادات التخزين المؤقت صحيحة\n"
        else:
            report += "❌ إعدادات التخزين المؤقت غير صحيحة\n"
        
        return report

# إنشاء نسخة عامة من الإعدادات
news_config = NewsConfig()
