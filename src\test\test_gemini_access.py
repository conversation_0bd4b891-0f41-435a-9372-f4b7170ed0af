#!/usr/bin/env python3
"""
اختبار إتاحة Gemini لجميع المستخدمين
"""

import asyncio
import logging
import re
from unittest.mock import Mock, AsyncMock

# إعداد السجل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestGeminiAccess:
    """فئة اختبار إتاحة Gemini لجميع المستخدمين"""
    
    def __init__(self):
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """إعداد بيئة الاختبار"""
        # إنشاء mock objects
        self.mock_subscription_system = Mock()
        self.mock_api_manager = Mock()
        
        logger.info("✅ تم إعداد بيئة الاختبار")
    
    def test_gemini_callback_pattern(self):
        """اختبار أن setup_gemini_api يطابق pattern"""
        try:
            # Pattern المستخدم في telegram_bot.py
            api_pattern = r'^(setup_api_keys|select_platform|setup_.*_api|delete_api_keys|delete_.*_api)$'
            
            # اختبار callback_data لـ Gemini
            gemini_callback = 'setup_gemini_api'
            
            pattern = re.compile(api_pattern)
            if pattern.match(gemini_callback):
                logger.info(f"✅ {gemini_callback} يطابق pattern")
                return True
            else:
                logger.error(f"❌ {gemini_callback} لا يطابق pattern")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار pattern: {str(e)}")
            return False
    
    def test_gemini_access_for_subscribers(self):
        """اختبار وصول المشتركين لـ Gemini"""
        try:
            # محاكاة مستخدم مشترك
            self.mock_subscription_system.is_subscribed_sync.return_value = True
            
            # التحقق من أن المشترك يمكنه الوصول لـ Gemini
            is_subscribed = self.mock_subscription_system.is_subscribed_sync("123456")
            
            if is_subscribed:
                logger.info("✅ المشتركون يمكنهم الوصول لـ Gemini")
                return True
            else:
                logger.error("❌ المشتركون لا يمكنهم الوصول لـ Gemini")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار وصول المشتركين: {str(e)}")
            return False
    
    def test_gemini_access_for_non_subscribers(self):
        """اختبار وصول غير المشتركين لـ Gemini"""
        try:
            # محاكاة مستخدم غير مشترك
            self.mock_subscription_system.is_subscribed_sync.return_value = False
            
            # التحقق من أن غير المشترك يمكنه الوصول لـ Gemini الآن
            is_subscribed = self.mock_subscription_system.is_subscribed_sync("789012")
            
            # بعد التعديل، يجب أن يكون Gemini متاحاً لجميع المستخدمين
            gemini_available_for_all = True  # هذا ما نريد تحقيقه
            
            if not is_subscribed and gemini_available_for_all:
                logger.info("✅ غير المشتركين يمكنهم الآن الوصول لـ Gemini")
                return True
            else:
                logger.error("❌ غير المشتركين لا يمكنهم الوصول لـ Gemini")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار وصول غير المشتركين: {str(e)}")
            return False
    
    def test_gemini_button_availability(self):
        """اختبار توفر زر Gemini في واجهة اختيار المنصة"""
        try:
            # محاكاة إنشاء أزرار المنصات
            platforms = ['binance', 'gemini', 'kucoin', 'coinbase', 'bybit', 'okx', 'kraken']
            
            # التحقق من أن Gemini موجود في قائمة المنصات
            if 'gemini' in platforms:
                logger.info("✅ زر Gemini متوفر في قائمة المنصات")
                
                # التحقق من أن Gemini متاح لجميع المستخدمين
                # (بعد التعديل، لا يوجد قيد على الاشتراك)
                gemini_available_for_all = True
                
                if gemini_available_for_all:
                    logger.info("✅ زر Gemini متاح لجميع المستخدمين")
                    return True
                else:
                    logger.error("❌ زر Gemini مقيد للمشتركين فقط")
                    return False
            else:
                logger.error("❌ زر Gemini غير موجود في قائمة المنصات")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار توفر زر Gemini: {str(e)}")
            return False
    
    def test_gemini_api_info_display(self):
        """اختبار عرض معلومات Gemini API لجميع المستخدمين"""
        try:
            # محاكاة معلومات API
            api_info = {
                'has_gemini': True,
                'gemini_updated_at': '2025-06-21 12:00:00'
            }
            
            # محاكاة مستخدم غير مشترك
            is_subscribed = False
            
            # بعد التعديل، يجب أن تظهر معلومات Gemini لجميع المستخدمين
            should_show_gemini = True  # لا يوجد قيد على الاشتراك بعد الآن
            
            if should_show_gemini:
                logger.info("✅ معلومات Gemini API تظهر لجميع المستخدمين")
                
                # التحقق من وجود المعلومات
                if api_info.get('has_gemini', False):
                    logger.info("✅ معلومات Gemini API متوفرة")
                    return True
                else:
                    logger.info("ℹ️ معلومات Gemini API غير متوفرة (لم يتم إعداد المفتاح)")
                    return True
            else:
                logger.error("❌ معلومات Gemini API مخفية عن غير المشتركين")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في اختبار عرض معلومات API: {str(e)}")
            return False
    
    async def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        logger.info("🚀 بدء تشغيل اختبارات إتاحة Gemini لجميع المستخدمين...")
        
        tests = [
            ("اختبار pattern لـ Gemini", self.test_gemini_callback_pattern),
            ("اختبار وصول المشتركين", self.test_gemini_access_for_subscribers),
            ("اختبار وصول غير المشتركين", self.test_gemini_access_for_non_subscribers),
            ("اختبار توفر زر Gemini", self.test_gemini_button_availability),
            ("اختبار عرض معلومات Gemini", self.test_gemini_api_info_display)
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            logger.info(f"🔄 تشغيل {test_name}...")
            try:
                if asyncio.iscoroutinefunction(test_func):
                    result = await test_func()
                else:
                    result = test_func()
                
                if result:
                    logger.info(f"✅ {test_name}: نجح")
                    passed += 1
                else:
                    logger.error(f"❌ {test_name}: فشل")
                    failed += 1
            except Exception as e:
                logger.error(f"❌ {test_name}: خطأ - {str(e)}")
                failed += 1
        
        logger.info(f"📊 نتائج الاختبار: {passed} نجح، {failed} فشل")
        
        if failed == 0:
            logger.info("🎉 جميع اختبارات إتاحة Gemini نجحت!")
            return True
        else:
            logger.error("❌ بعض اختبارات إتاحة Gemini فشلت")
            return False

async def main():
    """الدالة الرئيسية للاختبار"""
    tester = TestGeminiAccess()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎉 جميع اختبارات إتاحة Gemini نجحت!")
        print("✅ Gemini متاح الآن لجميع المستخدمين للتعلم مع الذكاء الاصطناعي")
        print("🤖 يمكن لجميع المستخدمين (مشتركين وغير مشتركين) إضافة مفتاح Gemini API")
    else:
        print("\n❌ بعض اختبارات إتاحة Gemini فشلت")
        print("⚠️ يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    asyncio.run(main())
