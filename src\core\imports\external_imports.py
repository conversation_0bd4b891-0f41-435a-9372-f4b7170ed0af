"""
🌐 استيرادات المكتبات الخارجية
==============================

جميع المكتبات الخارجية المطلوبة للنظام.
هذه المكتبات تحتاج تثبيت عبر pip.

الفئات:
- معالجة البيانات والحوسبة العلمية
- Telegram Bot API
- Firebase وقواعد البيانات
- الشبكات والاتصالات
- التشفير والأمان
- المراقبة والأداء
- المجدولة والمهام
"""

# ===== معالجة البيانات والحوسبة العلمية =====
try:
    import requests
    import numpy as np
    import pandas as pd
    import aiohttp
    SCIENTIFIC_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد مكتبات الحوسبة العلمية: {e}")
    SCIENTIFIC_AVAILABLE = False

# ===== Telegram Bot API =====
try:
    import telegram
    from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, Poll
    from telegram.constants import ParseMode
    from telegram.ext import (
        Application, CommandHandler, ContextTypes, MessageHandler, 
        filters, CallbackQueryHandler, CallbackContext, PollAnswerHandler
    )
    TELEGRAM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد Telegram API: {e}")
    TELEGRAM_AVAILABLE = False

# ===== Firebase وقواعد البيانات =====
try:
    import firebase_admin
    from firebase_admin import credentials, firestore
    from google.cloud.firestore_v1 import FieldFilter
    FIREBASE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد Firebase: {e}")
    FIREBASE_AVAILABLE = False

# ===== Blockchain والعملات الرقمية =====
try:
    from web3 import Web3
    WEB3_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد Web3: {e}")
    WEB3_AVAILABLE = False

# ===== التشفير والأمان =====
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.backends import default_backend
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد مكتبات التشفير: {e}")
    CRYPTOGRAPHY_AVAILABLE = False

# ===== المراقبة والأداء =====
# تم إزالة memory_profiler - غير مطلوب للإنتاج
MEMORY_PROFILER_AVAILABLE = False

# ===== المجدولة والمهام =====
try:
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    SCHEDULER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد APScheduler: {e}")
    SCHEDULER_AVAILABLE = False

# ===== متغيرات البيئة =====
try:
    import dotenv
    DOTENV_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: فشل في استيراد python-dotenv: {e}")
    DOTENV_AVAILABLE = False

# ===== تجميع الاستيرادات للتصدير =====
telegram_imports = {
    'telegram': telegram if TELEGRAM_AVAILABLE else None,
    'Update': Update if TELEGRAM_AVAILABLE else None,
    'InlineKeyboardButton': InlineKeyboardButton if TELEGRAM_AVAILABLE else None,
    'InlineKeyboardMarkup': InlineKeyboardMarkup if TELEGRAM_AVAILABLE else None,
    'Poll': Poll if TELEGRAM_AVAILABLE else None,
    'ParseMode': ParseMode if TELEGRAM_AVAILABLE else None,
    'Application': Application if TELEGRAM_AVAILABLE else None,
    'CommandHandler': CommandHandler if TELEGRAM_AVAILABLE else None,
    'ContextTypes': ContextTypes if TELEGRAM_AVAILABLE else None,
    'MessageHandler': MessageHandler if TELEGRAM_AVAILABLE else None,
    'filters': filters if TELEGRAM_AVAILABLE else None,
    'CallbackQueryHandler': CallbackQueryHandler if TELEGRAM_AVAILABLE else None,
    'CallbackContext': CallbackContext if TELEGRAM_AVAILABLE else None,
    'PollAnswerHandler': PollAnswerHandler if TELEGRAM_AVAILABLE else None
}

firebase_imports = {
    'firebase_admin': firebase_admin if FIREBASE_AVAILABLE else None,
    'credentials': credentials if FIREBASE_AVAILABLE else None,
    'firestore': firestore if FIREBASE_AVAILABLE else None,
    'FieldFilter': FieldFilter if FIREBASE_AVAILABLE else None
}

cryptography_imports = {
    'Fernet': Fernet if CRYPTOGRAPHY_AVAILABLE else None,
    'Cipher': Cipher if CRYPTOGRAPHY_AVAILABLE else None,
    'algorithms': algorithms if CRYPTOGRAPHY_AVAILABLE else None,
    'modes': modes if CRYPTOGRAPHY_AVAILABLE else None,
    'default_backend': default_backend if CRYPTOGRAPHY_AVAILABLE else None
}

# إعادة تصدير المتغيرات المتاحة
if SCIENTIFIC_AVAILABLE:
    numpy = np
    pandas = pd
else:
    numpy = None
    pandas = None

if WEB3_AVAILABLE:
    web3 = Web3
else:
    web3 = None

# تم إزالة memory_profiler - غير مطلوب للإنتاج
memory_profiler = None

if SCHEDULER_AVAILABLE:
    apscheduler = AsyncIOScheduler
else:
    apscheduler = None

# قائمة جميع الاستيرادات الخارجية للتصدير
__all__ = [
    # معالجة البيانات
    'requests', 'numpy', 'pandas', 'aiohttp',

    # Telegram
    'telegram_imports',

    # Firebase
    'firebase_imports',

    # Blockchain
    'web3',

    # التشفير
    'cryptography_imports',

    # المراقبة
    'memory_profiler',

    # المجدولة
    'apscheduler',

    # متغيرات البيئة
    'dotenv',

    # متغيرات التوفر
    'SCIENTIFIC_AVAILABLE', 'TELEGRAM_AVAILABLE', 'FIREBASE_AVAILABLE',
    'WEB3_AVAILABLE', 'CRYPTOGRAPHY_AVAILABLE', 'MEMORY_PROFILER_AVAILABLE',
    'SCHEDULER_AVAILABLE', 'DOTENV_AVAILABLE'
]

def get_external_imports_status():
    """
    حالة توفر المكتبات الخارجية
    
    Returns:
        dict: حالة كل مكتبة
    """
    return {
        'scientific_computing': {
            'available': SCIENTIFIC_AVAILABLE,
            'modules': ['requests', 'numpy', 'pandas', 'aiohttp'],
            'critical': True
        },
        'telegram_api': {
            'available': TELEGRAM_AVAILABLE,
            'modules': ['telegram', 'telegram.ext'],
            'critical': True
        },
        'firebase': {
            'available': FIREBASE_AVAILABLE,
            'modules': ['firebase_admin', 'google.cloud.firestore_v1'],
            'critical': True
        },
        'blockchain': {
            'available': WEB3_AVAILABLE,
            'modules': ['web3'],
            'critical': False
        },
        'cryptography': {
            'available': CRYPTOGRAPHY_AVAILABLE,
            'modules': ['cryptography'],
            'critical': True
        },
        'monitoring': {
            'available': False,
            'modules': [],
            'critical': False
        },
        'scheduling': {
            'available': SCHEDULER_AVAILABLE,
            'modules': ['apscheduler'],
            'critical': True
        },
        'environment': {
            'available': DOTENV_AVAILABLE,
            'modules': ['python-dotenv'],
            'critical': True
        }
    }

def validate_critical_imports():
    """
    التحقق من توفر المكتبات الحرجة
    
    Returns:
        tuple: (success: bool, missing_critical: list)
    """
    status = get_external_imports_status()
    missing_critical = []
    
    for category, info in status.items():
        if info['critical'] and not info['available']:
            missing_critical.extend(info['modules'])
    
    return len(missing_critical) == 0, missing_critical

# اختبار فوري للاستيرادات الحرجة
if __name__ == "__main__":
    success, missing = validate_critical_imports()
    if success:
        print("✅ جميع المكتبات الحرجة متوفرة")
    else:
        print(f"❌ مكتبات حرجة مفقودة: {missing}")
        
    # عرض حالة جميع المكتبات
    status = get_external_imports_status()
    print("\n📊 حالة المكتبات الخارجية:")
    for category, info in status.items():
        status_icon = "✅" if info['available'] else "❌"
        critical_icon = "🔴" if info['critical'] else "🟡"
        print(f"{status_icon} {critical_icon} {category}: {info['modules']}")
