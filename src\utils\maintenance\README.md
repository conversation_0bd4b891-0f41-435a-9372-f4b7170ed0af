# أدوات صيانة النظام

هذا المجلد يحتوي على أدوات الصيانة والإصلاح للنظام.

## 📁 الملفات المتوفرة

### 🔍 أداة التشخيص والإصلاح الشاملة
**الملف:** `fix_notifications_issue.py`

**الوصف:** أداة شاملة لتشخيص وإصلاح جميع مشاكل الإشعارات مع خيارات الإصلاح السريع والشامل

**الاستخدام:**
```bash
python src/utils/maintenance/fix_notifications_issue.py
```

**الوظائف الشاملة:**
- 🔍 **التشخيص:** فحص شامل لجميع مكونات النظام
- 🔧 **الإصلاح:** تطبيق الإصلاحات المناسبة تلقائياً
- 📊 **التحليل:** تحليل إحصائيات الإشعارات والأداء
- 🧪 **الاختبار:** اختبار شامل للنظام بعد الإصلاح

**المكونات المشمولة:**
- فحص فهارس Firestore
- تشخيص إعدادات المستخدمين
- فحص قواعد الإشعارات
- تحليل إحصائيات الإشعارات
- اختبار اتصال البوت
- تطبيق الإصلاحات المناسبة
- اختبار شامل للنظام

---

## 🛠️ متطلبات التشغيل

### البيئة المطلوبة:
- Python 3.8+
- Firebase Admin SDK
- Telegram Bot API
- جميع المتطلبات في `src/requirements.txt`

### المتغيرات المطلوبة:
- `TELEGRAM_BOT_TOKEN` - رمز البوت
- Firebase credentials file

### الأذونات المطلوبة:
- قراءة/كتابة Firestore
- إرسال رسائل Telegram
- تعديل إعدادات المستخدمين

---

## 📋 دليل الاستخدام

### الأداة الموحدة (الموصى بها)
```bash
cd /path/to/TradingTelegram
python src/utils/maintenance/fix_notifications_issue.py
```

**مميزات الأداة الموحدة:**
- ✅ تشخيص شامل قبل التعديل
- ✅ إصلاحات آمنة ومدروسة
- ✅ اختبار شامل بعد الإصلاح
- ✅ تقارير مفصلة عن العمليات
- ✅ معالجة أخطاء متقدمة

### بعد الإصلاح
```bash
# تطبيق فهارس Firestore
firebase deploy --only firestore:indexes

# تطبيق قواعد Firestore
firebase deploy --only firestore:rules

# إعادة تشغيل البوت
python src/main.py
```

---

## 🔒 الأمان والأفضليات

### لماذا أداة واحدة؟
1. **أمان أعلى:** تقليل نقاط الدخول للنظام
2. **تشخيص شامل:** فحص كامل قبل التعديل
3. **صيانة أسهل:** ملف واحد للصيانة والتحديث
4. **أخطاء أقل:** تجنب التضارب بين أدوات متعددة

### الممارسات الآمنة:
- ✅ النسخ الاحتياطية قبل التشغيل
- ✅ التشغيل في بيئة الاختبار أولاً
- ✅ مراقبة السجلات أثناء التشغيل
- ✅ التحقق من النتائج بعد الإصلاح

---

## ⚠️ تحذيرات مهمة

1. **النسخ الاحتياطية:** تأكد من وجود نسخة احتياطية من قاعدة البيانات قبل التشغيل
2. **البيئة:** تشغيل الأدوات في بيئة الاختبار أولاً
3. **الأذونات:** تأكد من وجود الأذونات المناسبة
4. **المراقبة:** راقب السجلات أثناء وبعد التشغيل
5. **التدرج:** ابدأ بالتشخيص قبل تطبيق الإصلاحات

---

## 📊 مؤشرات النجاح

بعد تشغيل الأداة، يجب أن ترى:

### في السجلات:
```
✅ تم تهيئة نظام الإشعارات التلقائية للأخبار
✅ تم العثور على X مستخدم نشط
✅ تم إرسال X إشعار
```

### في Firebase Console:
- فهارس Firestore نشطة
- لا توجد أخطاء في الاستعلامات
- إحصائيات الإشعارات محدثة

### للمستخدمين:
- وصول الأخبار العاجلة
- وصول إشعارات العملات الجديدة
- احترام حدود الإرسال

---

## 🔧 استكشاف الأخطاء

### خطأ في الاستيراد:
```bash
# تأكد من المسار الصحيح
export PYTHONPATH="${PYTHONPATH}:/path/to/TradingTelegram/src"
```

### خطأ في Firebase:
```bash
# تحقق من ملف الاعتمادات
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/firebase-key.json"
```

### خطأ في البوت:
```bash
# تحقق من رمز البوت
echo $TELEGRAM_BOT_TOKEN
```

---

## 📞 الدعم

للحصول على المساعدة:
1. راجع السجلات للأخطاء التفصيلية
2. تحقق من التوثيق في `docs/02-technical-reports/`
3. تشغيل التشخيص الشامل للحصول على تقرير مفصل

---

## 📝 سجل التحديثات

- **v1.0** - إنشاء أدوات الصيانة الأساسية
- **v1.1** - تحسين التشخيص وإضافة الإصلاح السريع
- **v1.2** - تنظيم الملفات وتحديث المسارات
- **v2.0** - توحيد الأدوات في أداة شاملة واحدة لتحسين الأمان والصيانة

---

## 🎯 الفلسفة الأمنية

تم اعتماد نهج "أداة واحدة شاملة" بدلاً من أدوات متعددة للأسباب التالية:

1. **مبدأ الحد الأدنى من الامتيازات:** أداة واحدة = نقطة دخول واحدة
2. **التحكم المركزي:** سهولة مراقبة ومراجعة العمليات
3. **تقليل سطح الهجوم:** عدد أقل من الملفات القابلة للتنفيذ
4. **الشفافية:** جميع العمليات في مكان واحد قابل للمراجعة