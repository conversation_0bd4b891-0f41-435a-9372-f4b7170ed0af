"""
نظام تخزين مؤقت باستخدام Firestore
يوفر واجهة بسيطة لتخزين واسترجاع البيانات المؤقتة في Firestore
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

from firebase_admin import firestore
from google.cloud.firestore_v1 import FieldFilter

# إعداد السجل
logger = logging.getLogger(__name__)

class FirestoreCache:
    """فئة لإدارة التخزين المؤقت باستخدام Firestore"""

    def __init__(self, db: firestore.Client, collection_prefix: str = "cache"):
        """
        تهيئة نظام التخزين المؤقت

        Args:
            db: مثيل قاعدة بيانات Firestore
            collection_prefix: بادئة اسم المجموعة للتخزين المؤقت
        """
        self.db = db
        self.collection_prefix = collection_prefix
        self._initialize_collections()

    def _initialize_collections(self):
        """تهيئة مجموعات التخزين المؤقت"""
        self.collections = {
            "market_data": f"{self.collection_prefix}_market_data",
            "user_data": f"{self.collection_prefix}_user_data",
            "system_data": f"{self.collection_prefix}_system_data"
        }

        # التحقق من وجود المجموعات وإنشائها إذا لم تكن موجودة
        for collection_name in self.collections.values():
            # إنشاء وثيقة تهيئة للمجموعة إذا لم تكن موجودة
            init_doc = {
                '_metadata': {
                    'created_at': datetime.now().isoformat(),
                    'collection_type': 'cache',
                    'description': 'مجموعة تخزين مؤقت'
                }
            }

            # التحقق من وجود المجموعة
            docs = self.db.collection(collection_name).limit(1).get()
            if not list(docs):
                # إنشاء وثيقة تهيئة
                self.db.collection(collection_name).document('_init').set(init_doc)
                logger.info(f"تم إنشاء مجموعة التخزين المؤقت: {collection_name}")

    def _get_collection(self, cache_type: str) -> str:
        """
        الحصول على اسم المجموعة المناسب لنوع التخزين المؤقت

        Args:
            cache_type: نوع التخزين المؤقت (market_data, user_data, system_data)

        Returns:
            اسم المجموعة
        """
        return self.collections.get(cache_type, self.collections["system_data"])

    def _serialize_value(self, value: Any) -> str:
        """
        تحويل القيمة إلى سلسلة JSON

        Args:
            value: القيمة المراد تحويلها

        Returns:
            سلسلة JSON
        """
        try:
            return json.dumps(value)
        except (TypeError, ValueError):
            # إذا لم يمكن تحويل القيمة إلى JSON، نعيد تمثيلها كسلسلة
            return str(value)

    def _deserialize_value(self, value: str) -> Any:
        """
        تحويل سلسلة JSON إلى قيمة

        Args:
            value: سلسلة JSON

        Returns:
            القيمة المحولة
        """
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            # إذا لم يمكن تحويل السلسلة إلى JSON، نعيدها كما هي
            return value

    def set(self, key: str, value: Any, ex: int = 3600, cache_type: str = "system_data") -> bool:
        """
        تخزين قيمة في التخزين المؤقت

        Args:
            key: مفتاح القيمة
            value: القيمة المراد تخزينها
            ex: مدة الصلاحية بالثواني (الافتراضي: ساعة واحدة)
            cache_type: نوع التخزين المؤقت (market_data, user_data, system_data)

        Returns:
            True إذا تم التخزين بنجاح، False خلاف ذلك
        """
        try:
            collection_name = self._get_collection(cache_type)
            expiry = datetime.now() + timedelta(seconds=ex)

            # تحويل القيمة إلى سلسلة JSON
            serialized_value = self._serialize_value(value)

            # تخزين القيمة في Firestore
            self.db.collection(collection_name).document(key).set({
                'value': serialized_value,
                'expiry': expiry.isoformat(),
                'created_at': datetime.now().isoformat()
            })

            return True
        except Exception as e:
            logger.error(f"خطأ في تخزين القيمة: {str(e)}")
            return False

    def get(self, key: str, cache_type: str = "system_data") -> Optional[Any]:
        """
        استرجاع قيمة من التخزين المؤقت

        Args:
            key: مفتاح القيمة
            cache_type: نوع التخزين المؤقت (market_data, user_data, system_data)

        Returns:
            القيمة المخزنة أو None إذا لم تكن موجودة أو منتهية الصلاحية
        """
        try:
            collection_name = self._get_collection(cache_type)
            doc_ref = self.db.collection(collection_name).document(key)
            doc = doc_ref.get()

            if not doc.exists:
                return None

            data = doc.to_dict()
            expiry = datetime.fromisoformat(data['expiry'])

            # التحقق من انتهاء الصلاحية
            if expiry < datetime.now():
                # حذف القيمة منتهية الصلاحية
                doc_ref.delete()
                return None

            # تحويل القيمة من سلسلة JSON
            return self._deserialize_value(data['value'])
        except Exception as e:
            logger.error(f"خطأ في استرجاع القيمة: {str(e)}")
            return None

    def delete(self, key: str, cache_type: str = "system_data") -> bool:
        """
        حذف قيمة من التخزين المؤقت

        Args:
            key: مفتاح القيمة
            cache_type: نوع التخزين المؤقت (market_data, user_data, system_data)

        Returns:
            True إذا تم الحذف بنجاح، False خلاف ذلك
        """
        try:
            collection_name = self._get_collection(cache_type)
            self.db.collection(collection_name).document(key).delete()
            return True
        except Exception as e:
            logger.error(f"خطأ في حذف القيمة: {str(e)}")
            return False

    def clear_expired(self, cache_type: str = None) -> int:
        """
        حذف جميع القيم منتهية الصلاحية

        Args:
            cache_type: نوع التخزين المؤقت (market_data, user_data, system_data)
                       إذا كانت None، يتم حذف القيم منتهية الصلاحية من جميع المجموعات

        Returns:
            عدد القيم التي تم حذفها
        """
        try:
            now = datetime.now().isoformat()
            deleted_count = 0

            if cache_type:
                collections = [self._get_collection(cache_type)]
            else:
                collections = self.collections.values()

            for collection_name in collections:
                # استعلام عن القيم منتهية الصلاحية
                from google.cloud.firestore import FieldFilter
                expired_docs = self.db.collection(collection_name).where(filter=FieldFilter('expiry', '<', now)).get()

                # حذف القيم منتهية الصلاحية
                for doc in expired_docs:
                    if doc.id != '_init':  # تجنب حذف وثيقة التهيئة
                        self.db.collection(collection_name).document(doc.id).delete()
                        deleted_count += 1

            return deleted_count
        except Exception as e:
            logger.error(f"خطأ في حذف القيم منتهية الصلاحية: {str(e)}")
            return 0

    def clear_all(self, cache_type: str = None) -> int:
        """
        حذف جميع القيم

        Args:
            cache_type: نوع التخزين المؤقت (market_data, user_data, system_data)
                       إذا كانت None، يتم حذف جميع القيم من جميع المجموعات

        Returns:
            عدد القيم التي تم حذفها
        """
        try:
            deleted_count = 0

            if cache_type:
                collections = [self._get_collection(cache_type)]
            else:
                collections = self.collections.values()

            for collection_name in collections:
                # استعلام عن جميع القيم
                docs = self.db.collection(collection_name).get()

                # حذف جميع القيم
                for doc in docs:
                    if doc.id != '_init':  # تجنب حذف وثيقة التهيئة
                        self.db.collection(collection_name).document(doc.id).delete()
                        deleted_count += 1

            return deleted_count
        except Exception as e:
            logger.error(f"خطأ في حذف جميع القيم: {str(e)}")
            return 0
