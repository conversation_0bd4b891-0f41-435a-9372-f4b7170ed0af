"""
إصلاح سريع لمشكلة عدم إرسال الأخبار الملخصة حسب اللغة
يركز على المشاكل الأساسية فقط
"""

import asyncio
import logging
import sys
import os
from datetime import datetime

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# إعداد التسجيل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def quick_fix_news_system():
    """إصلاح سريع لنظام الأخبار"""
    print("🚀 بدء الإصلاح السريع لنظام الأخبار الذكي")
    print("="*50)
    
    try:
        # تهيئة قاعدة البيانات
        from core.database import db
        if not db:
            print("❌ خطأ: قاعدة البيانات غير متوفرة")
            return False
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # الخطوة 1: تهيئة الأنظمة الأساسية
        print("\n🔧 الخطوة 1: تهيئة الأنظمة الأساسية...")
        
        # تهيئة نظام الأخبار
        try:
            from services.news_system import news_system, initialize_news_system
            if not news_system:
                # محاولة الحصول على مفتاح Gemini
                gemini_key = None
                try:
                    system_settings = db.collection('system_settings').document('config').get()
                    if system_settings.exists:
                        settings_data = system_settings.to_dict()
                        gemini_key = settings_data.get('gemini_api_key')
                except Exception:
                    pass
                
                if not gemini_key:
                    import os
                    gemini_key = os.getenv('GEMINI_API_KEY')
                
                initialize_news_system(db, gemini_key)
                print("  ✅ تم تهيئة نظام الأخبار")
            else:
                print("  ✅ نظام الأخبار مهيأ مسبقاً")
        except Exception as e:
            print(f"  ❌ خطأ في تهيئة نظام الأخبار: {str(e)}")
        
        # تهيئة نظام الإشعارات
        try:
            from services.automatic_news_notifications import automatic_news_notifications, initialize_automatic_news_notifications
            if not automatic_news_notifications:
                initialize_automatic_news_notifications(db, None)
                print("  ✅ تم تهيئة نظام الإشعارات")
            else:
                print("  ✅ نظام الإشعارات مهيأ مسبقاً")
        except Exception as e:
            print(f"  ❌ خطأ في تهيئة نظام الإشعارات: {str(e)}")
        
        # تهيئة نظام الجدولة
        try:
            from services.automatic_news_scheduler import automatic_news_scheduler, initialize_automatic_news_scheduler
            from services.news_system import news_system
            if not automatic_news_scheduler:
                initialize_automatic_news_scheduler(news_system, db, None)
                print("  ✅ تم تهيئة نظام الجدولة")
            else:
                print("  ✅ نظام الجدولة مهيأ مسبقاً")
        except Exception as e:
            print(f"  ❌ خطأ في تهيئة نظام الجدولة: {str(e)}")
        
        # الخطوة 2: إصلاح إعدادات اللغة
        print("\n🌐 الخطوة 2: إصلاح إعدادات اللغة...")
        
        fixed_users = 0
        try:
            # البحث عن المستخدمين
            users_collection = db.collection('users')
            users_docs = list(users_collection.stream())
            
            for user_doc in users_docs:
                user_id = user_doc.id
                
                # فحص إعدادات اللغة
                settings_doc = db.collection('user_settings').document(user_id).get()
                
                if not settings_doc.exists:
                    # إنشاء إعدادات افتراضية
                    default_settings = {
                        'lang': 'ar',
                        'language': 'ar',
                        'lang_selected': False,
                        'notifications_enabled': True,
                        'created_at': datetime.now().isoformat(),
                        'fixed_by_quick_repair': True
                    }
                    
                    db.collection('user_settings').document(user_id).set(default_settings)
                    fixed_users += 1
                    
                else:
                    # فحص وإصلاح الإعدادات الموجودة
                    settings_data = settings_doc.to_dict()
                    needs_update = False
                    
                    if not settings_data.get('lang') and not settings_data.get('language'):
                        settings_data['lang'] = 'ar'
                        settings_data['language'] = 'ar'
                        needs_update = True
                    
                    if 'lang_selected' not in settings_data:
                        settings_data['lang_selected'] = False
                        needs_update = True
                    
                    if needs_update:
                        settings_data['updated_at'] = datetime.now().isoformat()
                        settings_data['fixed_by_quick_repair'] = True
                        db.collection('user_settings').document(user_id).set(settings_data, merge=True)
                        fixed_users += 1
            
            print(f"  ✅ تم إصلاح إعدادات {fixed_users} مستخدم")
            
        except Exception as e:
            print(f"  ❌ خطأ في إصلاح إعدادات اللغة: {str(e)}")
        
        # الخطوة 3: إنشاء تفضيلات الإشعارات
        print("\n🔔 الخطوة 3: إنشاء تفضيلات الإشعارات...")
        
        created_prefs = 0
        try:
            from services.automatic_news_notifications import NotificationType
            
            users_collection = db.collection('users')
            users_docs = list(users_collection.stream())
            
            for user_doc in users_docs:
                user_id = user_doc.id
                
                # فحص تفضيلات الإشعارات
                prefs_doc = db.collection('notification_preferences').document(user_id).get()
                
                if not prefs_doc.exists:
                    # إنشاء تفضيلات افتراضية
                    default_prefs = {
                        'enabled': True,
                        'language': 'ar',
                        'types': [
                            NotificationType.BREAKING_NEWS.value,
                            NotificationType.NEW_COIN.value
                        ],
                        'max_daily': {
                            NotificationType.BREAKING_NEWS.value: 5,
                            NotificationType.NEW_COIN.value: 3,
                            NotificationType.MARKET_ANALYSIS.value: 2,
                            NotificationType.DAILY_SUMMARY.value: 1
                        },
                        'created_at': datetime.now().isoformat(),
                        'created_by_quick_repair': True
                    }
                    
                    db.collection('notification_preferences').document(user_id).set(default_prefs)
                    created_prefs += 1
            
            print(f"  ✅ تم إنشاء تفضيلات لـ {created_prefs} مستخدم")
            
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء تفضيلات الإشعارات: {str(e)}")
        
        # الخطوة 4: تشغيل النظام التلقائي
        print("\n🤖 الخطوة 4: تشغيل النظام التلقائي...")
        
        try:
            from services.automatic_news_integration import automatic_news_integration, initialize_automatic_news_integration
            
            if not automatic_news_integration:
                # تهيئة النظام التلقائي
                integration = await initialize_automatic_news_integration(db, None)
                if integration:
                    success = await integration.start_automatic_systems()
                    if success:
                        print("  ✅ تم تشغيل النظام التلقائي")
                    else:
                        print("  ⚠️ تم تهيئة النظام لكن فشل في التشغيل")
                else:
                    print("  ❌ فشل في تهيئة النظام التلقائي")
            else:
                # إعادة تشغيل النظام الموجود
                try:
                    await automatic_news_integration.stop_automatic_systems()
                    success = await automatic_news_integration.start_automatic_systems()
                    if success:
                        print("  ✅ تم إعادة تشغيل النظام التلقائي")
                    else:
                        print("  ⚠️ فشل في إعادة تشغيل النظام التلقائي")
                except Exception as e:
                    print(f"  ⚠️ خطأ في إعادة التشغيل: {str(e)}")
                    
        except Exception as e:
            print(f"  ❌ خطأ في النظام التلقائي: {str(e)}")
        
        # الخطوة 5: اختبار سريع
        print("\n🧪 الخطوة 5: اختبار سريع...")
        
        try:
            # اختبار تحديد اللغة
            from services.automatic_news_notifications import automatic_news_notifications
            if automatic_news_notifications:
                test_lang = await automatic_news_notifications._get_user_language("7839527436")
                print(f"  ✅ اختبار تحديد اللغة: {test_lang}")
            
            # اختبار الترجمة
            if automatic_news_notifications:
                test_text = "🚨 خبر عاجل عن البيتكوين"
                translated = automatic_news_notifications._translate_to_english(test_text)
                print(f"  ✅ اختبار الترجمة: {test_text} -> {translated}")
            
        except Exception as e:
            print(f"  ⚠️ خطأ في الاختبار: {str(e)}")
        
        print("\n" + "="*50)
        print("✅ تم إكمال الإصلاح السريع!")
        print("📋 ملخص الإجراءات:")
        print("  1. ✅ تهيئة الأنظمة الأساسية")
        print(f"  2. ✅ إصلاح إعدادات {fixed_users} مستخدم")
        print(f"  3. ✅ إنشاء تفضيلات لـ {created_prefs} مستخدم")
        print("  4. ✅ تشغيل النظام التلقائي")
        print("  5. ✅ اختبار سريع")
        
        print("\n💡 التوصيات:")
        print("  - راقب السجلات للتأكد من عمل النظام")
        print("  - اختبر إرسال الأخبار يدوياً")
        print("  - تشغيل الاختبار الشامل لاحقاً")
        
        print(f"\n⏰ تم الانتهاء في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*50)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح السريع: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

async def main():
    """تشغيل الإصلاح السريع"""
    success = await quick_fix_news_system()
    
    if success:
        print("\n🎉 تم الإصلاح بنجاح!")
        print("🔍 لتشخيص شامل، شغل: python news_system_diagnostics.py")
        print("🧪 لاختبار شامل، شغل: python test_news_system.py")
    else:
        print("\n❌ فشل الإصلاح!")
        print("🛠️ جرب الإصلاح الشامل: python fix_news_system.py")

if __name__ == "__main__":
    asyncio.run(main())
