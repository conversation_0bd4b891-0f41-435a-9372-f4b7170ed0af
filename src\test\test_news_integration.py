#!/usr/bin/env python3
"""
اختبار تكامل نظام الأخبار مع البوت
"""

import asyncio
import logging
import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_news_integration():
    """اختبار تكامل نظام الأخبار"""
    print("🔗 اختبار تكامل نظام الأخبار...")
    print("=" * 60)
    
    try:
        # اختبار 1: تهيئة قاعدة البيانات
        print("\n📊 تهيئة قاعدة البيانات...")
        db = None
        try:
            from integrations.firebase_init import db as firebase_db
            db = firebase_db
            if db:
                print("✅ تم الاتصال بقاعدة البيانات Firebase")
            else:
                print("⚠️ قاعدة البيانات غير متوفرة")
        except Exception as db_error:
            print(f"⚠️ لم يتم الاتصال بقاعدة البيانات: {str(db_error)}")
        
        # اختبار 2: تهيئة البوت
        print("\n🤖 تهيئة البوت...")
        bot = None
        try:
            from core.telegram_bot import TelegramBot
            bot = TelegramBot()
            await bot.setup()
            print("✅ تم إعداد البوت بنجاح")
        except Exception as bot_error:
            print(f"⚠️ لم يتم إعداد البوت: {str(bot_error)}")
        
        # اختبار 3: تهيئة نظام الأخبار
        print("\n📰 تهيئة نظام الأخبار...")
        from services.news_system import NewsSystem
        news_system = NewsSystem(db=db)
        print("✅ تم تهيئة نظام الأخبار")
        
        # اختبار 4: تهيئة نظام الجدولة مع البوت
        print("\n⏰ تهيئة نظام الجدولة مع البوت...")
        from services.automatic_news_scheduler import AutomaticNewsScheduler
        scheduler = AutomaticNewsScheduler(news_system=news_system, db=db, bot=bot)
        print("✅ تم تهيئة نظام الجدولة مع البوت")
        
        # اختبار 5: تهيئة نظام الإشعارات مع البوت
        print("\n🔔 تهيئة نظام الإشعارات مع البوت...")
        from services.automatic_news_notifications import AutomaticNewsNotifications
        notifications = AutomaticNewsNotifications(db=db, bot=bot)
        print("✅ تم تهيئة نظام الإشعارات مع البوت")
        
        # اختبار 6: جلب وتحليل الأخبار
        print("\n📊 اختبار جلب وتحليل الأخبار...")
        
        # جلب أخبار من مصدر واحد للاختبار
        news_items = await news_system.fetch_news_from_coindesk()
        print(f"✅ تم جلب {len(news_items)} خبر من CoinDesk")
        
        if news_items and len(news_items) > 0:
            # اختبار تحليل خبر واحد
            sample_news = news_items[0]
            print(f"📝 اختبار تحليل الخبر: {sample_news.title[:50]}...")
            
            # محاولة تحليل الخبر
            analyzed_news = await news_system.analyze_news_with_ai(sample_news)
            if analyzed_news and analyzed_news.ai_analysis:
                print("✅ تم تحليل الخبر بالذكاء الاصطناعي")
                
                # اختبار إرسال الخبر المحلل للإشعارات
                print("📢 اختبار إرسال الخبر للإشعارات...")
                await notifications.process_news_for_notifications([analyzed_news])
                print("✅ تم إرسال الخبر لنظام الإشعارات")
                
            else:
                print("⚠️ لم يتم تحليل الخبر (مفتاح Gemini غير متوفر)")
                
                # اختبار إرسال الخبر بدون تحليل
                print("📢 اختبار إرسال الخبر بدون تحليل...")
                await notifications.process_news_for_notifications([sample_news])
                print("✅ تم إرسال الخبر لنظام الإشعارات")
        
        # اختبار 7: حالة الأنظمة
        print("\n📊 فحص حالة الأنظمة...")
        scheduler_status = scheduler.get_scheduler_status()
        print(f"  • نظام الجدولة: {scheduler_status['status']}")
        print(f"  • عدد المهام: {scheduler_status['total_jobs']}")
        print(f"  • عدد المشتركين: {scheduler_status['subscribers_count']}")
        
        # عرض إحصائيات API
        api_stats = scheduler_status.get('api_usage', {})
        print(f"  • إحصائيات API:")
        for platform, stats in api_stats.items():
            print(f"    - {platform}: {stats.get('current_usage', 0)}/{stats.get('limit', 0)}")
        
        print("\n" + "=" * 60)
        print("🎉 تم اختبار التكامل بنجاح!")
        print("\n📋 ملخص النتائج:")
        print(f"  • قاعدة البيانات: {'متصلة' if db else 'غير متوفرة'}")
        print(f"  • البوت: {'متصل' if bot else 'غير متوفر'}")
        print(f"  • نظام الأخبار: متاح")
        print(f"  • نظام الجدولة: متاح")
        print(f"  • نظام الإشعارات: متاح")
        print(f"  • الأخبار المجلبة: {len(news_items) if news_items else 0}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في اختبار التكامل: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False

async def main():
    """الدالة الرئيسية"""
    print("🔧 اختبار تكامل نظام الأخبار الذكي")
    print("=" * 60)
    
    success = await test_news_integration()
    
    if success:
        print(f"\n✅ تم اختبار التكامل بنجاح!")
        print("🎯 النظام جاهز للعمل مع البوت")
    else:
        print(f"\n❌ فشل في اختبار التكامل")

if __name__ == "__main__":
    asyncio.run(main())
