# -*- coding: utf-8 -*-
"""
وحدة التحليل الأساسي للعملات المشفرة
تحتوي على كلاس CryptoAnalysis والدوال الأساسية للتحليل
"""

import logging
import requests
import re
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from telegram.constants import ParseMode

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

# متغيرات عامة للتبعيات - سيتم تعيينها من main.py
subscription_system = None
api_manager = None
enhanced_analyzer = None
db = None
user_states = None

# استيراد الدوال المساعدة - سيتم تعيينها من main.py
get_text = None
create_analysis_text = None
load_user_settings = None
save_user_settings = None
specialized_handlers = None
delete_message_after_delay = None


class CryptoAnalysis:
    """كلاس التحليل الأساسي للعملات المشفرة"""

    def __init__(self):
        self.binance_api = "https://api.binance.com/api/v3"
        self.exchange_api = "https://api.exchangerate-api.com/v4/latest/USD"
        self.exchange_rates = self.get_exchange_rates()
        self.custom_indicators = {}  # تخزين المؤشرات المخصصة لكل مستخدم
        self.market_data_cache = {}  # ذاكرة محلية لتخزين بيانات السوق
        self.market_data_expiry = {}  # تواريخ انتهاء صلاحية البيانات المخزنة
        self.analysis_cache = {}  # تخزين التحليل النهائي
        self.analysis_expiry = {}  # تواريخ انتهاء صلاحية التحليل
        self.cache_timeout = 180  # 3 minutes - تقليل وقت التخزين المؤقت لتحسين الأداء
        self.analysis_cache_timeout = 120  # 2 minutes للتحليل النهائي

    def get_exchange_rates(self):
        """الحصول على أسعار صرف العملات"""
        try:
            response = requests.get(self.exchange_api, timeout=5)  # تقليل timeout لتحسين الأداء
            if response.status_code == 200:
                return response.json()['rates']
            return None
        except:
            return None

    def convert_price(self, price_usd, target_currency='USD'):
        """تحويل السعر من الدولار إلى العملة المطلوبة"""
        if target_currency == 'USD' or not self.exchange_rates:
            return price_usd, 'USD'

        try:
            rate = self.exchange_rates.get(target_currency)
            if rate:
                return price_usd * rate, target_currency
        except:
            pass

        return price_usd, 'USD'

    def calculate_ema(self, prices, period):
        """حساب المتوسط المتحرك الأسي (EMA)"""
        return prices.ewm(span=period, adjust=False).mean()

    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """حساب مؤشر الماكد (MACD)"""
        exp1 = prices.ewm(span=fast, adjust=False).mean()
        exp2 = prices.ewm(span=slow, adjust=False).mean()
        macd = exp1 - exp2
        signal_line = macd.ewm(span=signal, adjust=False).mean()
        histogram = macd - signal_line
        return macd.iloc[-1], signal_line.iloc[-1], histogram.iloc[-1]

    def calculate_bollinger_bands(self, prices, period=20, std=2):
        """حساب نطاقات بولينجر (Bollinger Bands)"""
        middle_band = prices.rolling(window=period).mean()
        std_dev = prices.rolling(window=period).std()
        upper_band = middle_band + (std_dev * std)
        lower_band = middle_band - (std_dev * std)
        return upper_band.iloc[-1], middle_band.iloc[-1], lower_band.iloc[-1]

    def calculate_ichimoku_cloud(self, high, low, close, conversion_period=9, base_period=26, span_period=52, displacement=26):
        """
        حساب مؤشر سحابة إيشيموكو (Ichimoku Cloud)

        Args:
            high: سلسلة القيم العليا
            low: سلسلة القيم الدنيا
            close: سلسلة قيم الإغلاق
            conversion_period: فترة خط التحويل (Tenkan-sen)
            base_period: فترة خط الأساس (Kijun-sen)
            span_period: فترة السبان B (Senkou Span B)
            displacement: فترة الإزاحة

        Returns:
            خمس قيم: خط التحويل، خط الأساس، السبان A، السبان B، خط التشيكو
        """
        # حساب خط التحويل (Tenkan-sen)
        tenkan_sen = (high.rolling(window=conversion_period).max() + low.rolling(window=conversion_period).min()) / 2

        # حساب خط الأساس (Kijun-sen)
        kijun_sen = (high.rolling(window=base_period).max() + low.rolling(window=base_period).min()) / 2

        # حساب خط السبان A (Senkou Span A)
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(displacement)

        # حساب خط السبان B (Senkou Span B)
        senkou_span_b = ((high.rolling(window=span_period).max() + low.rolling(window=span_period).min()) / 2).shift(displacement)

        # حساب خط التشيكو (Chikou Span)
        chikou_span = close.shift(-displacement)

        # إرجاع القيم الحالية
        return (
            tenkan_sen.iloc[-1] if not tenkan_sen.empty else float('nan'),
            kijun_sen.iloc[-1] if not kijun_sen.empty else float('nan'),
            senkou_span_a.iloc[-displacement] if len(senkou_span_a) > displacement else float('nan'),
            senkou_span_b.iloc[-displacement] if len(senkou_span_b) > displacement else float('nan'),
            chikou_span.iloc[-1] if not chikou_span.empty else float('nan')
        )

    def calculate_stoch_rsi(self, prices, period=14, smooth_k=3, smooth_d=3):
        """حساب مؤشر Stochastic RSI"""
        # Calculate RSI
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Stochastic RSI
        stoch_rsi = (rsi - rsi.rolling(period).min()) / (rsi.rolling(period).max() - rsi.rolling(period).min())
        k = stoch_rsi.rolling(smooth_k).mean()
        d = k.rolling(smooth_d).mean()
        return k.iloc[-1], d.iloc[-1]

    def calculate_adx(self, high, low, close, period=14):
        """حساب مؤشر ADX (Average Directional Index)"""
        # True Range
        tr1 = high - low
        tr2 = abs(high - close.shift())
        tr3 = abs(low - close.shift())
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(period).mean()

        # Plus Directional Movement
        up_move = high - high.shift()
        down_move = low.shift() - low
        plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
        plus_di = 100 * pd.Series(plus_dm).rolling(period).mean() / atr

        # Minus Directional Movement
        minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
        minus_di = 100 * pd.Series(minus_dm).rolling(period).mean() / atr

        # ADX
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(period).mean()
        return adx.iloc[-1], plus_di.iloc[-1], minus_di.iloc[-1]

    def calculate_rsi(self, prices, period=14):
        """حساب مؤشر القوة النسبية (RSI)"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))

    def get_recommendation(self, market_data, lang='ar'):
        """الحصول على التوصية بناءً على تحليل المؤشرات"""
        try:
            signals = []
            recommendation = None

            # تحليل RSI
            if market_data['rsi'] > 70:
                signals.append(
                    "RSI indicates overbought - Consider selling" if lang == 'en' else
                    "مؤشر القوة النسبية يشير إلى تشبع شراء - فرصة للبيع"
                )
                recommendation = "Sell" if lang == 'en' else "بيع"
            elif market_data['rsi'] < 30:
                signals.append(
                    "RSI indicates oversold - Consider buying" if lang == 'en' else
                    "مؤشر القوة النسبية يشير إلى تشبع بيع - فرصة للشراء"
                )
                recommendation = "Buy" if lang == 'en' else "شراء"

            # تحليل MACD
            if market_data['macd'] > market_data['macd_signal']:
                signals.append(
                    "MACD above signal line - Bullish signal" if lang == 'en' else
                    "مؤشر الماكد فوق خط الإشارة - إشارة إيجابية"
                )
                if not recommendation:
                    recommendation = "Buy" if lang == 'en' else "شراء"
            elif market_data['macd'] < market_data['macd_signal']:
                signals.append(
                    "MACD below signal line - Bearish signal" if lang == 'en' else
                    "مؤشر الماكد تحت خط الإشارة - إشارة سلبية"
                )
                if not recommendation:
                    recommendation = "Sell" if lang == 'en' else "بيع"

            # تحليل EMA
            if market_data['ema20'] > market_data['ema50']:
                signals.append(
                    "EMA20 above EMA50 - Upward trend" if lang == 'en' else
                    "المتوسط المتحرك 20 فوق 50 - اتجاه صعودي"
                )
                if not recommendation:
                    recommendation = "Buy" if lang == 'en' else "شراء"
            elif market_data['ema20'] < market_data['ema50']:
                signals.append(
                    "EMA20 below EMA50 - Downward trend" if lang == 'en' else
                    "المتوسط المتحرك 20 تحت 50 - اتجاه هبوطي"
                )
                if not recommendation:
                    recommendation = "Sell" if lang == 'en' else "بيع"

            # إذا لم يتم تحديد توصية واضحة
            if not recommendation:
                recommendation = "Hold" if lang == 'en' else "انتظار"
                signals.append(
                    "Mixed signals - Consider holding" if lang == 'en' else
                    "إشارات متضاربة - يفضل الانتظار"
                )

            return {
                'signals': signals,
                'recommendation': recommendation
            }

        except Exception as e:
            logger.error(f"Error in get_recommendation: {str(e)}")
            return {
                'signals': [
                    "Error analyzing indicators" if lang == 'en' else
                    "خطأ في تحليل المؤشرات"
                ],
                'recommendation': "Hold" if lang == 'en' else "انتظار"
            }

    async def calculate_indicators(self, df, analysis_type='traditional'):
        """حساب المؤشرات الفنية بشكل محسن ومتوازي"""
        try:
            # تحسين الأداء: للتحليل التقليدي، استخدم المؤشرات الأساسية فقط
            if analysis_type == 'traditional':
                logger.info("⚡ استخدام المؤشرات الأساسية للتحليل التقليدي")
                return await self._calculate_basic_indicators_fast(df)

            # استخدام المؤشرات المحسنة للحصول على أفضل أداء
            from analysis.optimized_indicators import optimized_indicators

            # حساب المؤشرات بشكل متوازي
            indicators_result = await optimized_indicators.calculate_all_indicators_parallel(df)
            
            if indicators_result:
                # استخراج القيم الحالية من النتائج
                current_price = df['close'].iloc[-1]
                
                market_data = {
                    'symbol': df.name if hasattr(df, 'name') else "UNKNOWN",
                    'price': current_price,
                    'price_change': ((current_price - df['close'].iloc[-2]) / df['close'].iloc[-2]) * 100,
                    'volume': df['volume'].iloc[-1],
                    'rsi': indicators_result.get('rsi', 50.0),
                    'ema20': indicators_result.get('ema20', current_price),
                    'ema50': indicators_result.get('ema50', current_price),
                    'ema200': indicators_result.get('ema200', current_price),
                    'macd': indicators_result.get('macd', 0.0),
                    'macd_signal': indicators_result.get('macd_signal', 0.0),
                    'macd_hist': indicators_result.get('macd_histogram', 0.0),
                    'bb_upper': indicators_result.get('bb_upper', current_price),
                    'bb_middle': indicators_result.get('bb_middle', current_price),
                    'bb_lower': indicators_result.get('bb_lower', current_price),
                    'stoch_k': indicators_result.get('stoch_k', 50.0),
                    'stoch_d': indicators_result.get('stoch_d', 50.0),
                    'adx': indicators_result.get('adx', 25.0),
                    'plus_di': indicators_result.get('plus_di', 25.0),
                    'minus_di': indicators_result.get('minus_di', 25.0),
                    'ichimoku_tenkan': indicators_result.get('ichimoku_tenkan', current_price),
                    'ichimoku_kijun': indicators_result.get('ichimoku_kijun', current_price),
                    'ichimoku_senkou_a': indicators_result.get('ichimoku_senkou_a', current_price),
                    'ichimoku_senkou_b': indicators_result.get('ichimoku_senkou_b', current_price),
                    'ichimoku_chikou': indicators_result.get('ichimoku_chikou', current_price),
                    'vwap': indicators_result.get('vwap', current_price),
                    'obv': indicators_result.get('obv', 0.0),
                    'volume_ratio': indicators_result.get('volume_ratio', 1.0)
                }
                
                # Get recommendations
                recommendations = self.get_recommendation(market_data)
                market_data['recommendations'] = recommendations
                
                return market_data
            
            # Fallback إلى الطريقة التقليدية في حالة فشل المؤشرات المحسنة
            logger.warning("فشل في استخدام المؤشرات المحسنة، استخدام الطريقة التقليدية")
            return await self._calculate_indicators_traditional(df)
            
        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات المحسنة: {str(e)}")
            # Fallback إلى الطريقة التقليدية
            return await self._calculate_indicators_traditional(df)

    async def _calculate_basic_indicators_fast(self, df):
        """حساب المؤشرات الأساسية بسرعة للتحليل التقليدي"""
        try:
            logger.info("⚡ حساب المؤشرات الأساسية السريعة")

            # حساب المؤشرات الأساسية فقط (RSI, EMA, MACD, Bollinger Bands)
            current_price = df['close'].iloc[-1]

            # RSI (مبسط)
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            current_rsi = rsi.iloc[-1] if not rsi.empty else 50

            # EMA 20 و 50
            ema20 = df['close'].ewm(span=20).mean().iloc[-1]
            ema50 = df['close'].ewm(span=50).mean().iloc[-1]

            # MACD مبسط
            ema12 = df['close'].ewm(span=12).mean()
            ema26 = df['close'].ewm(span=26).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9).mean()
            current_macd = macd.iloc[-1]
            current_signal = signal.iloc[-1]

            # Bollinger Bands
            sma20 = df['close'].rolling(window=20).mean()
            std20 = df['close'].rolling(window=20).std()
            bb_upper = (sma20 + (std20 * 2)).iloc[-1]
            bb_middle = sma20.iloc[-1]
            bb_lower = (sma20 - (std20 * 2)).iloc[-1]

            # تحديد التوصية البسيطة
            recommendation = 'hold'
            if current_rsi < 30 and ema20 > ema50 and current_macd > current_signal:
                recommendation = 'buy'
            elif current_rsi > 70 and ema20 < ema50 and current_macd < current_signal:
                recommendation = 'sell'

            return {
                'price': current_price,
                'rsi': current_rsi,
                'ema20': ema20,
                'ema50': ema50,
                'macd': current_macd,
                'macd_signal': current_signal,
                'bb_upper': bb_upper,
                'bb_middle': bb_middle,
                'bb_lower': bb_lower,
                'recommendation': recommendation,
                'volume': df['volume'].iloc[-1] if 'volume' in df.columns else 0
            }

        except Exception as e:
            logger.error(f"خطأ في حساب المؤشرات الأساسية السريعة: {str(e)}")
            # إرجاع قيم افتراضية
            return {
                'price': df['close'].iloc[-1] if not df.empty else 0,
                'rsi': 50,
                'ema20': df['close'].iloc[-1] if not df.empty else 0,
                'ema50': df['close'].iloc[-1] if not df.empty else 0,
                'macd': 0,
                'macd_signal': 0,
                'bb_upper': df['close'].iloc[-1] if not df.empty else 0,
                'bb_middle': df['close'].iloc[-1] if not df.empty else 0,
                'bb_lower': df['close'].iloc[-1] if not df.empty else 0,
                'recommendation': 'hold',
                'volume': 0
            }
    
    async def _calculate_indicators_traditional(self, df):
        """حساب المؤشرات بالطريقة التقليدية (fallback)"""
        try:
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            # Calculate other indicators
            ema20 = self.calculate_ema(df['close'], 20)
            ema50 = self.calculate_ema(df['close'], 50)
            macd, signal, hist = self.calculate_macd(df['close'])
            bb_upper, bb_middle, bb_lower = self.calculate_bollinger_bands(df['close'])
            stoch_k, stoch_d = self.calculate_stoch_rsi(df['close'])
            adx, plus_di, minus_di = self.calculate_adx(df['high'], df['low'], df['close'])

            # حساب مؤشرات إيشيموكو
            try:
                ichimoku_tenkan, ichimoku_kijun, ichimoku_senkou_a, ichimoku_senkou_b, ichimoku_chikou = self.calculate_ichimoku_cloud(
                    df['high'], df['low'], df['close']
                )
                logger.info(f"تم حساب مؤشرات إيشيموكو بنجاح: tenkan={ichimoku_tenkan}, kijun={ichimoku_kijun}")
            except Exception as ichimoku_error:
                logger.error(f"خطأ في حساب مؤشرات إيشيموكو: {str(ichimoku_error)}")
                ichimoku_tenkan = ichimoku_kijun = ichimoku_senkou_a = ichimoku_senkou_b = ichimoku_chikou = 'N/A'

            current_price = df['close'].iloc[-1]

            market_data = {
                'symbol': df.name if hasattr(df, 'name') else "UNKNOWN",
                'price': current_price,
                'price_change': ((current_price - df['close'].iloc[-2]) / df['close'].iloc[-2]) * 100,
                'volume': df['volume'].iloc[-1],
                'rsi': rsi.iloc[-1],
                'ema20': ema20.iloc[-1],
                'ema50': ema50.iloc[-1],
                'macd': macd,
                'macd_signal': signal,
                'macd_hist': hist,
                'bb_upper': bb_upper,
                'bb_middle': bb_middle,
                'bb_lower': bb_lower,
                'stoch_k': stoch_k,
                'stoch_d': stoch_d,
                'adx': adx,
                'plus_di': plus_di,
                'minus_di': minus_di,
                # إضافة مؤشرات إيشيموكو
                'ichimoku_tenkan': ichimoku_tenkan,
                'ichimoku_kijun': ichimoku_kijun,
                'ichimoku_senkou_a': ichimoku_senkou_a,
                'ichimoku_senkou_b': ichimoku_senkou_b,
                'ichimoku_chikou': ichimoku_chikou
            }

            # Get recommendations
            recommendations = self.get_recommendation(market_data)
            market_data['recommendations'] = recommendations

            return market_data

        except Exception as e:
            logger.error(f"Error calculating indicators: {str(e)}")
            return None

    async def get_market_data(self, symbol: str, target_currency='USD', user_id=None, lang='ar', interval='4h'):
        """
        الحصول على بيانات السوق مع استخدام الذاكرة المحلية

        Args:
            symbol: رمز العملة
            target_currency: العملة المستهدفة للتحويل
            user_id: معرف المستخدم
            lang: لغة التحليل
            interval: الإطار الزمني (1h, 4h, 1d, 1w)
        """
        try:
            # التحقق من صحة الرمز
            if not symbol or len(symbol) < 2:
                logger.error("Invalid symbol")
                return None

            # تنظيف وتحقق من رمز العملة
            def clean_symbol(symbol: str) -> str:
                original_symbol = symbol
                symbol = symbol.upper().strip()

                # إزالة أي أحرف غير مرغوب فيها
                symbol = ''.join(c for c in symbol if c.isalnum())

                # إذا كان الرمز لا ينتهي بـ USDT، أضفه
                if not symbol.endswith('USDT'):
                    symbol = f"{symbol}USDT"

                # التحقق من صحة الرمز
                if len(symbol) < 5:
                    raise ValueError(f"رمز عملة غير صالح: {original_symbol}")

                # التحقق من أن الجزء الأساسي من الرمز يحتوي على أحرف
                base_symbol = symbol[:-4]  # إزالة USDT
                if len(base_symbol) < 1 or not any(c.isalpha() for c in base_symbol):
                    raise ValueError(f"رمز عملة غير صالح: {original_symbol}")

                logger.info(f"تنظيف رمز العملة: {original_symbol} -> {symbol}")
                return symbol

            symbol = clean_symbol(symbol)

            # التحقق من الذاكرة المحلية - إضافة الإطار الزمني للمفتاح
            cache_key = f"market_data_{symbol}_{interval}_{lang}"
            current_time = datetime.now()

            if cache_key in self.market_data_cache and cache_key in self.market_data_expiry:
                if current_time < self.market_data_expiry[cache_key]:
                    # استخدام البيانات المخزنة
                    cached_data = self.market_data_cache[cache_key]
                    logger.info(f"⚡ استخدام البيانات المخزنة مؤقتاً لـ {symbol}")
                    return cached_data

            # محاولة استخدام مفاتيح API الخاصة بالمستخدم إذا كان متاحًا
            if user_id and api_manager:
                try:
                    # التحقق من وجود مفاتيح API للمستخدم
                    has_api_keys = await api_manager.has_api_keys(user_id, 'binance')

                    if has_api_keys:
                        logger.info(f"استخدام مفاتيح API الخاصة بالمستخدم {user_id} للحصول على بيانات {symbol}")

                        # الحصول على مفاتيح API الخاصة بالمستخدم
                        api_key, api_secret = await api_manager.get_api_keys(user_id, 'binance')

                        if api_key and api_secret:
                            # استخدام مكتبة python-binance مع مفاتيح API المستخدم
                            from binance.client import Client
                            client = Client(api_key, api_secret)

                            # الحصول على بيانات السوق باستخدام API المستخدم
                            from analysis.user_market_data import get_market_data_with_user_api
                            user_market_data = await get_market_data_with_user_api(client, symbol, interval, 100, target_currency)

                            if user_market_data:
                                logger.info(f"تم الحصول على بيانات السوق للعملة {symbol} باستخدام API المستخدم {user_id}")
                                return user_market_data
                except Exception as user_api_error:
                    logger.warning(f"فشل في استخدام مفاتيح API الخاصة بالمستخدم {user_id}: {str(user_api_error)}")

            # استخدام BinanceManager للحصول على البيانات بالإطار الزمني المحدد
            # نحتاج للوصول إلى binance_manager من المتغيرات العامة
            from integrations.binance_manager import BinanceManager
            binance_manager = BinanceManager()

            klines = await binance_manager.get_klines(symbol, interval=interval, user_id=user_id)
            if klines:
                # تحويل البيانات إلى DataFrame
                df = pd.DataFrame(klines, columns=[
                    'timestamp', 'open', 'high', 'low', 'close', 'volume',
                    'close_time', 'quote_volume', 'trades', 'taker_buy_base',
                    'taker_buy_quote', 'ignore'
                ])

                # تحويل أعمدة الأسعار والحجم إلى float
                numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
                df[numeric_columns] = df[numeric_columns].astype(float)

                # تحويل timestamp إلى datetime
                df['Date'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('Date', inplace=True)

                # حساب المؤشرات الفنية مع تمرير نوع التحليل
                analysis_type_for_calc = 'traditional'  # افتراضي للتحليل السريع
                market_data = await self.calculate_indicators(df, analysis_type_for_calc)
                if market_data:
                    market_data['symbol'] = symbol
                    market_data['currency'] = target_currency

                    # إضافة التوصيات مع تحديد اللغة
                    market_data['recommendations'] = self.get_recommendation(market_data, lang)

                    # تخزين في الذاكرة المحلية
                    self.market_data_cache[cache_key] = market_data
                    self.market_data_expiry[cache_key] = current_time + timedelta(seconds=self.cache_timeout)

                    return market_data

            # تجميع البيانات الأساسية في حالة فشل الحصول على البيانات
            market_data = {
                'symbol': symbol,
                'price': 0,
                'price_change': 0,
                'currency': target_currency,
                'chart_data': None,
                'chart_content_type': None
            }

            return market_data

        except Exception as e:
            logger.error(f"Error getting market data: {str(e)}")
            return None


def initialize_basic_analysis(sub_system, api_mgr, enhanced_analyzer_instance, database, user_states_dict,
                             get_text_func, create_analysis_text_func, load_user_settings_func,
                             save_user_settings_func, specialized_handlers_module, delete_message_after_delay_func):
    """تهيئة وحدة التحليل الأساسي مع التبعيات المطلوبة"""
    global subscription_system, api_manager, enhanced_analyzer, db, user_states
    global get_text, create_analysis_text, load_user_settings, save_user_settings, specialized_handlers, delete_message_after_delay

    subscription_system = sub_system
    api_manager = api_mgr
    enhanced_analyzer = enhanced_analyzer_instance
    db = database
    user_states = user_states_dict

    # تعيين الدوال المساعدة
    get_text = get_text_func
    create_analysis_text = create_analysis_text_func
    load_user_settings = load_user_settings_func
    save_user_settings = save_user_settings_func
    specialized_handlers = specialized_handlers_module
    delete_message_after_delay = delete_message_after_delay_func

    logger.info("✅ تم تهيئة وحدة التحليل الأساسي بنجاح")


# إنشاء كائن التحليل الأساسي
ca = CryptoAnalysis()


async def analyze_symbol(update: Update, context: CallbackContext, symbol: str, message=None, target_currency=None, analysis_type=None):
    """تحليل رمز العملة"""
    try:
        user_id = str(update.effective_user.id)

        # التحقق من توفر subscription_system
        if subscription_system is None:
            logger.error("subscription_system غير متوفر في analyze_symbol")
            error_text = "❌ حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً."
            if message:
                await message.edit_text(error_text)
            else:
                await update.message.reply_text(error_text)
            return

        settings = subscription_system.get_user_settings(user_id)
        if not settings:
            logger.warning(f"لم يتم العثور على إعدادات للمستخدم {user_id}")
            settings = {'lang': 'ar'}  # إعدادات افتراضية

        lang = settings.get('lang', 'ar')

        # استخدام العملة المخصصة إذا كانت متوفرة
        if target_currency is None:
            # استخدام أول عملة من قائمة العملات المخصصة للمستخدم
            custom_currencies = settings.get('currencies', [])
            if custom_currencies:
                target_currency = custom_currencies[0]
            else:
                target_currency = 'USD'  # العملة الافتراضية

        # تنظيف رمز العملة
        cleaned_symbol = symbol.upper().replace('/', '').replace('-', '').replace('_', '').replace('.', '')

        # التحقق من الاشتراك والاستخدام المجاني
        if not subscription_system.is_subscribed_sync(user_id):
            if not subscription_system.can_use_free_analysis(user_id):
                # إنشاء رسالة خطأ واضحة عند نفاذ التحليلات المجانية
                error_text = (
                    "⏰ **لقد استنفدت تحليلاتك المجانية اليوم!**\n\n"
                    "📊 **الحد اليومي:** 3 تحليلات مجانية\n"
                    "🔄 **إعادة التعيين:** منتصف الليل (بتوقيت الخادم)\n\n"
                    "💎 **للحصول على تحليلات غير محدودة:**\n"
                    "• اشترك في الخدمة المميزة\n"
                    "• أو انتظر حتى اليوم المجاني القادم\n\n"
                    "🎁 **اليوم المجاني القادم:** الاثنين"
                    if lang == 'ar' else
                    "⏰ **You've used up your free analyses for today!**\n\n"
                    "📊 **Daily Limit:** 3 free analyses\n"
                    "🔄 **Reset Time:** Midnight (server time)\n\n"
                    "💎 **For unlimited analyses:**\n"
                    "• Subscribe to premium service\n"
                    "• Or wait for the next free day\n\n"
                    "🎁 **Next Free Day:** Monday"
                )

                # إضافة أزرار للاشتراك والعودة للقائمة الرئيسية
                keyboard = [
                    [InlineKeyboardButton(
                        "💎 الاشتراك الآن" if lang == 'ar' else "💎 Subscribe Now",
                        callback_data='upgrade_account'
                    )],
                    [InlineKeyboardButton(
                        "🏠 القائمة الرئيسية" if lang == 'ar' else "🏠 Main Menu",
                        callback_data='back_to_main'
                    )]
                ]

                if message:
                    await message.edit_text(
                        error_text,
                        reply_markup=InlineKeyboardMarkup(keyboard),
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    await update.message.reply_text(
                        error_text,
                        reply_markup=InlineKeyboardMarkup(keyboard),
                        parse_mode=ParseMode.MARKDOWN
                    )
                return

        # إنشاء رسالة انتظار محسنة
        if message:
            wait_message = message
            # تحديث الرسالة الموجودة بمؤشر تقدم
            try:
                await wait_message.edit_text(
                    f"⏳ {get_text('analyzing_market', lang)}\n🔍 جاري تحليل المؤشرات..." if lang == 'ar' else
                    f"⏳ {get_text('analyzing_market', lang)}\n🔍 Analyzing indicators..."
                )
            except:
                pass  # تجاهل أخطاء التحديث
        else:
            wait_message = await update.message.reply_text(
                f"⏳ {get_text('analyzing_market', lang)}\n🔍 جاري تحليل المؤشرات..." if lang == 'ar' else
                f"⏳ {get_text('analyzing_market', lang)}\n🔍 Analyzing indicators..."
            )

        # التحقق من التخزين المؤقت للتحليل النهائي أولاً
        analysis_cache_key = f"analysis_{cleaned_symbol}_{analysis_type}_{lang}_{user_id}"
        current_time = datetime.now()

        if analysis_cache_key in ca.analysis_cache and analysis_cache_key in ca.analysis_expiry:
            if current_time < ca.analysis_expiry[analysis_cache_key]:
                cached_analysis_text = ca.analysis_cache[analysis_cache_key]
                logger.info(f"⚡ استخدام التحليل المخزن مؤقتاً لـ {cleaned_symbol}")

                # تحديث رسالة الانتظار بالنتيجة المخزنة
                try:
                    await wait_message.edit_text(cached_analysis_text, parse_mode='Markdown')
                except:
                    await wait_message.edit_text(cached_analysis_text)
                return

        # قياس وقت التحليل للمراقبة
        analysis_start_time = time.time()

        # استخدام محسن الأداء الجديد
        performance_optimizer = None
        try:
            from analysis.performance_optimizer import performance_optimizer
            logger.info(f"🚀 استخدام محسن الأداء للمستخدم {user_id}")
        except ImportError:
            logger.warning("محسن الأداء غير متاح")

        # تسجيل بداية التحليل في مراقب الأداء
        performance_monitor = None
        tracking_id = None
        try:
            from core.system_initialization_extended import _system_components_cache
            if _system_components_cache and 'performance_monitor' in _system_components_cache:
                performance_monitor = _system_components_cache['performance_monitor']
                tracking_id = await performance_monitor.start_analysis_tracking(user_id, cleaned_symbol, analysis_type or 'traditional')
        except Exception as perf_error:
            logger.warning(f"فشل في تسجيل بداية التحليل: {str(perf_error)}")

        # تحسين الأداء: التحقق من التخزين المؤقت أولاً
        market_data = None

        if performance_optimizer:
            # محاولة الحصول على البيانات من التخزين المؤقت المحسن
            cached_analysis = performance_optimizer.cache.get(cleaned_symbol)
            if cached_analysis:
                logger.info(f"⚡ تم الحصول على تحليل {cleaned_symbol} من التخزين المؤقت المحسن")

                # للتحليل التقليدي، يمكن استخدام التحليل المخزن مباشرة
                if analysis_type == 'traditional' and cached_analysis.get('analysis_text'):
                    cached_text = cached_analysis.get('analysis_text')
                    logger.info(f"⚡ استخدام التحليل التقليدي المخزن مؤقتاً لـ {cleaned_symbol}")

                    # تحديث رسالة الانتظار بالنتيجة
                    await wait_message.edit_text(cached_text, parse_mode='Markdown')
                    return

                # استخدام بيانات السوق المخزنة
                market_data = cached_analysis.get('market_data')
                if market_data:
                    logger.info(f"✅ استخدام بيانات السوق المخزنة مؤقتاً لـ {cleaned_symbol}")

        # إذا لم توجد بيانات مخزنة، احصل على بيانات جديدة
        if not market_data:
            logger.info(f"🔄 جلب بيانات جديدة لـ {cleaned_symbol}")

            # تحديث رسالة الانتظار
            try:
                await wait_message.edit_text(
                    f"⏳ {get_text('analyzing_market', lang)}\n📊 جاري جلب بيانات السوق..." if lang == 'ar' else
                    f"⏳ {get_text('analyzing_market', lang)}\n📊 Fetching market data..."
                )
            except:
                pass

            market_data = await ca.get_market_data(cleaned_symbol, target_currency=target_currency, user_id=user_id, lang=lang)

        # تحسين التحليل باستخدام الذكاء الاصطناعي المحسن إذا كان متاحًا
        if market_data and analysis_type == 'enhanced_ai':
            try:
                # الحصول على المحلل المحسن
                from analysis.enhanced_ai_analysis import get_enhanced_ai_analyzer
                enhanced_ai_analyzer = get_enhanced_ai_analyzer()

                if enhanced_ai_analyzer:
                    logger.info(f"🤖 استخدام المحلل المحسن للذكاء الاصطناعي للمستخدم {user_id}")
                    enhanced_result = await enhanced_ai_analyzer.analyze_symbol(
                        cleaned_symbol, market_data, user_id, 'comprehensive'
                    )

                    if enhanced_result:
                        # دمج النتائج المحسنة مع البيانات الأساسية
                        market_data.update(enhanced_result)
                        logger.info(f"✅ تم تحسين التحليل باستخدام الذكاء الاصطناعي")

            except Exception as ai_error:
                logger.warning(f"فشل في استخدام المحلل المحسن: {str(ai_error)}")
                # المتابعة بالتحليل العادي

        if market_data:
            # تحديث الاستخدام المجاني
            if not await subscription_system.is_subscribed(user_id):
                subscription_system.use_free_analysis(user_id)

            # تحديث رسالة الانتظار قبل إنشاء النص
            try:
                await wait_message.edit_text(
                    f"⏳ {get_text('analyzing_market', lang)}\n✍️ جاري إنشاء التحليل..." if lang == 'ar' else
                    f"⏳ {get_text('analyzing_market', lang)}\n✍️ Generating analysis..."
                )
            except:
                pass

            # إنشاء نص التحليل - تمرير معرف المستخدم مع نوع التحليل المحدد
            final_analysis_type = analysis_type if analysis_type else 'traditional'
            logger.info(f"🔍 استدعاء create_analysis_text للمستخدم {user_id} مع analysis_type={final_analysis_type}")
            analysis_text = await create_analysis_text(cleaned_symbol, market_data, lang, user_id, final_analysis_type)
            
            # تسجيل وقت التحليل للمراقبة
            analysis_time = time.time() - analysis_start_time
            logger.info(f"⚡ تم إكمال التحليل في {analysis_time:.3f} ثانية")

            # حفظ التحليل في التخزين المؤقت
            try:
                ca.analysis_cache[analysis_cache_key] = analysis_text
                ca.analysis_expiry[analysis_cache_key] = current_time + timedelta(seconds=ca.analysis_cache_timeout)
                logger.info(f"💾 تم حفظ التحليل في التخزين المؤقت لـ {cleaned_symbol}")
            except Exception as cache_error:
                logger.warning(f"خطأ في حفظ التحليل في التخزين المؤقت: {str(cache_error)}")

            # حفظ النتيجة في التخزين المؤقت المحسن
            if performance_optimizer and market_data:
                try:
                    cache_data = {
                        'analysis_text': analysis_text,
                        'market_data': market_data,
                        'symbol': cleaned_symbol,
                        'analysis_type': analysis_type or 'traditional',
                        'timestamp': datetime.now().isoformat(),
                        'analysis_time': analysis_time,
                        'user_id': user_id,
                        'lang': lang
                    }
                    performance_optimizer.cache.set(cleaned_symbol, cache_data)
                    logger.info(f"💾 تم حفظ تحليل {cleaned_symbol} في التخزين المؤقت المحسن")
                except Exception as cache_error:
                    logger.warning(f"خطأ في حفظ التحليل في التخزين المؤقت: {str(cache_error)}")

            # تسجيل نهاية التحليل في مراقب الأداء
            try:
                if performance_monitor:
                    await performance_monitor.end_analysis_tracking(
                        user_id, cleaned_symbol, True, analysis_time,
                        {'market_data_available': True, 'analysis_type': analysis_type or 'traditional'}
                    )
            except Exception as perf_error:
                logger.warning(f"فشل في تسجيل نهاية التحليل: {str(perf_error)}")

            # إضافة أزرار التحكم مع الميزات المتقدمة
            keyboard = [
                [
                    InlineKeyboardButton("🔄 تحديث" if lang == 'ar' else "🔄 Refresh", callback_data=f'refresh_{cleaned_symbol}'),
                    InlineKeyboardButton("📊 تحليل عملة أخرى" if lang == 'ar' else "📊 Analyze Another", callback_data='analyze')
                ],
                [
                    InlineKeyboardButton("⏰ إعداد تنبيه" if lang == 'ar' else "⏰ Set Alert", callback_data=f'quick_alert_{cleaned_symbol}')
                ]
            ]

            # إضافة أزرار الميزات المتقدمة للمشتركين
            if await subscription_system.is_subscribed(user_id):
                keyboard.append([
                    InlineKeyboardButton("🎯 تخصيص المؤشرات" if lang == 'ar' else "🎯 Customize Indicators",
                                       callback_data=f'customize_indicators_{cleaned_symbol}')
                ])

            # إضافة زر العودة للقائمة الرئيسية
            keyboard.append([
                InlineKeyboardButton("🏠 القائمة الرئيسية" if lang == 'ar' else "🏠 Main Menu", callback_data='back_to_main')
            ])

            # تطبيق حل التنسيق الشامل قبل الإرسال
            try:
                from utils.utils import fix_bold_formatting
                analysis_text = fix_bold_formatting(analysis_text, lang)
            except ImportError:
                logger.warning("لم يتم العثور على دالة fix_bold_formatting")

            # إرسال التحليل مع معالجة أخطاء Markdown المتدرجة (الحل المبهر)
            try:
                await wait_message.edit_text(
                    analysis_text,
                    reply_markup=InlineKeyboardMarkup(keyboard),
                    parse_mode=ParseMode.MARKDOWN,  # استخدام ParseMode.MARKDOWN بدلاً من 'Markdown'
                    disable_web_page_preview=True
                )
            except Exception as markdown_error:
                logger.warning(f"فشل إرسال التحليل مع Markdown: {str(markdown_error)}")
                # محاولة إرسال بدون تنسيق Markdown
                try:
                    await wait_message.edit_text(
                        analysis_text,
                        reply_markup=InlineKeyboardMarkup(keyboard),
                        disable_web_page_preview=True
                    )
                except Exception as plain_error:
                    logger.error(f"فشل إرسال التحليل حتى بدون Markdown: {str(plain_error)}")
                    # إرسال رسالة خطأ بسيطة
                    await wait_message.edit_text(
                        "عذراً، حدث خطأ في عرض التحليل. يرجى المحاولة مرة أخرى." if lang == 'ar' else
                        "Sorry, an error occurred displaying the analysis. Please try again.",
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )

            # تسجيل نجاح التحليل في مراقب الأداء
            try:
                if performance_monitor:
                    analysis_time = time.time() - analysis_start_time
                    await performance_monitor.end_analysis_tracking(
                        user_id, cleaned_symbol, True, analysis_time,
                        {'analysis_type': analysis_type or 'traditional', 'symbol': cleaned_symbol}
                    )
            except Exception as perf_error:
                logger.warning(f"فشل في تسجيل نجاح التحليل: {str(perf_error)}")

            # مسح حالة المستخدم
            user_states.pop(user_id, None)
        else:
            error_text = (
                f"Could not find data for {cleaned_symbol}. Please check the symbol and try again." if lang == 'en' else
                f"لم يتم العثور على بيانات لـ {cleaned_symbol}. الرجاء التحقق من الرمز والمحاولة مرة أخرى."
            )
            await wait_message.edit_text(error_text)

            # تسجيل فشل التحليل في مراقب الأداء
            try:
                if performance_monitor:
                    analysis_time = time.time() - analysis_start_time
                    await performance_monitor.end_analysis_tracking(
                        user_id, cleaned_symbol, False, analysis_time,
                        {'error': 'no_market_data', 'analysis_type': analysis_type or 'traditional'}
                    )
            except Exception as perf_error:
                logger.warning(f"فشل في تسجيل فشل التحليل: {str(perf_error)}")

    except Exception as e:
        # استخدام معالج الأخطاء المتخصص للتحليل
        try:
            # تحديد اللغة بشكل آمن
            lang = 'ar'  # افتراضي
            try:
                if subscription_system is not None:
                    settings = subscription_system.get_user_settings(user_id)
                    if settings:
                        lang = settings.get('lang', 'ar')
            except:
                pass

            if specialized_handlers is not None:
                await specialized_handlers.handle_analysis_error(update, context, symbol, e, lang)
            else:
                # معالجة احتياطية للأخطاء
                logger.error(f"خطأ في تحليل العملة {symbol}: {str(e)}")
                error_text = (
                    f"❌ حدث خطأ أثناء تحليل العملة {symbol}. يرجى المحاولة مرة أخرى لاحقاً." if lang == 'ar' else
                    f"❌ An error occurred while analyzing {symbol}. Please try again later."
                )
                if hasattr(update, 'message') and update.message:
                    await update.message.reply_text(error_text)
                elif hasattr(update, 'callback_query') and update.callback_query:
                    await update.callback_query.answer(error_text, show_alert=True)
        except Exception as handler_error:
            logger.error(f"خطأ في معالج الأخطاء: {str(handler_error)}")
            # معالجة احتياطية نهائية
            try:
                error_text = "❌ حدث خطأ في النظام. يرجى المحاولة مرة أخرى لاحقاً."
                if hasattr(update, 'message') and update.message:
                    await update.message.reply_text(error_text)
                elif hasattr(update, 'callback_query') and update.callback_query:
                    await update.callback_query.answer(error_text, show_alert=True)
            except:
                pass


async def analyze_command(update: Update, context: CallbackContext):
    """تحليل زوج عملات مع المخطط البياني"""
    user_id = str(update.effective_user.id)
    lang = context.bot_data.get('user_settings', {}).get(user_id, {}).get('language', 'ar')

    if not context.args:
        await update.message.reply_text(
            get_text('enter_symbol', lang),
            reply_markup=InlineKeyboardMarkup([[
                InlineKeyboardButton(get_text('back_to_main', lang), callback_data='back_to_main')
            ]])
        )
        return

    symbol = context.args[0].upper()
    await analyze_symbol(update, context, symbol)


async def add_indicator(update: Update, context: CallbackContext, symbol: str, indicator_id: str):
    """إضافة مؤشر مخصص"""
    user_id = str(update.effective_user.id)

    # تحميل الإعدادات الحالية
    settings = load_user_settings(user_id)
    indicators = settings['indicators']

    # إضافة المؤشر إذا لم يكن موجوداً
    if indicator_id not in [ind['id'] for ind in indicators]:
        indicators.append({'id': indicator_id})

        # حفظ التغييرات
        if save_user_settings(user_id, indicators=indicators):
            await update.callback_query.message.reply_text(f"تم إضافة المؤشر {indicator_id} بنجاح")
        else:
            await update.callback_query.message.reply_text("حدث خطأ أثناء حفظ المؤشر")
    else:
        await update.callback_query.message.reply_text("المؤشر موجود بالفعل")

    # تحديث التحليل
    await analyze_symbol(update, context, symbol)


async def add_custom_currency(update: Update, context: CallbackContext, symbol: str, currency: str):
    """إضافة عملة مخصصة"""
    user_id = str(update.effective_user.id)

    # تحميل الإعدادات الحالية
    settings = load_user_settings(user_id)
    currencies = settings['currencies']

    # إضافة العملة إذا لم تكن موجودة
    if currency not in currencies:
        currencies.append(currency)

        # حفظ التغييرات
        if save_user_settings(user_id, currencies=currencies):
            await update.message.reply_text(f"تم إضافة العملة {currency} بنجاح")
        else:
            await update.message.reply_text("حدث خطأ أثناء حفظ العملة")
    else:
        await update.message.reply_text("العملة موجودة بالفعل")

    # تحديث التحليل
    await analyze_symbol(update, context, symbol)


async def remove_indicator(update: Update, context: CallbackContext, symbol: str, indicator_id: str):
    """إزالة مؤشر مخصص"""
    try:
        user_id = str(update.effective_user.id)
        lang = context.bot_data.get('user_settings', {}).get(user_id, {}).get('language', 'ar')

        # تحميل الإعدادات الحالية
        settings = load_user_settings(user_id)
        indicators = settings['indicators']

        # إزالة المؤشر
        indicators = [ind for ind in indicators if ind['id'] != indicator_id]

        # حفظ التغييرات
        if save_user_settings(user_id, indicators=indicators):
            confirmation_message = await update.callback_query.message.reply_text(
                get_text('indicator_removed', lang).format(
                    name=get_text(f'{indicator_id}_indicator', lang)
                )
            )

            # حذف رسالة التأكيد بعد 3 ثوانٍ
            from utils import delete_message_after_delay
            await delete_message_after_delay(confirmation_message, 3)

            # تحديث التحليل
            await analyze_symbol(update, context, symbol)
        else:
            await update.callback_query.message.reply_text(
                get_text('error_saving_settings', lang)
            )

    except Exception as e:
        logger.error(f"خطأ في إزالة المؤشر: {str(e)}")
        await update.callback_query.message.reply_text(
            "حدث خطأ أثناء إزالة المؤشر" if lang == 'ar' else "Error removing indicator"
        )


async def customize_indicators(update: Update, context: CallbackContext, symbol: str):
    """تخصيص المؤشرات الفنية"""
    try:
        user_id = str(update.effective_user.id)
        lang = subscription_system.get_user_settings(user_id).get('lang', 'ar')

        # التحقق من الاشتراك
        if not await subscription_system.is_subscribed(user_id):
            await update.callback_query.answer(
                "عذراً، هذه الميزة متوفرة فقط للمشتركين" if lang == 'ar' else
                "Sorry, this feature is only available for subscribers",
                show_alert=True
            )
            return

        # إنشاء أزرار المؤشرات
        keyboard = []

        # إضافة زر الرجوع والتحديث
        keyboard.append([
            InlineKeyboardButton(
                "🔄 تحديث التحليل" if lang == 'ar' else "🔄 Refresh Analysis",
                callback_data=f'refresh_{symbol}'
            ),
            InlineKeyboardButton(
                "🏠 القائمة الرئيسية" if lang == 'ar' else "🏠 Main Menu",
                callback_data='back_to_main'
            )
        ])

        # إرسال قائمة تخصيص المؤشرات
        text = (
            f"🎯 **تخصيص المؤشرات الفنية لـ {symbol}**\n\n"
            f"يمكنك إضافة أو إزالة المؤشرات الفنية حسب تفضيلاتك.\n"
            f"المؤشرات المتاحة:\n\n"
            f"📈 **المؤشرات الأساسية:**\n"
            f"• RSI (مؤشر القوة النسبية)\n"
            f"• MACD (تقارب وتباعد المتوسطات)\n"
            f"• Bollinger Bands (نطاقات بولينجر)\n"
            f"• Moving Averages (المتوسطات المتحركة)\n\n"
            f"📊 **مؤشرات متقدمة:**\n"
            f"• Stochastic (العشوائي)\n"
            f"• Williams %R\n"
            f"• CCI (مؤشر القناة السلعية)\n"
            f"• ADX (مؤشر الاتجاه المتوسط)"
            if lang == 'ar' else
            f"🎯 **Customize Technical Indicators for {symbol}**\n\n"
            f"You can add or remove technical indicators according to your preferences.\n"
            f"Available indicators:\n\n"
            f"📈 **Basic Indicators:**\n"
            f"• RSI (Relative Strength Index)\n"
            f"• MACD (Moving Average Convergence Divergence)\n"
            f"• Bollinger Bands\n"
            f"• Moving Averages\n\n"
            f"📊 **Advanced Indicators:**\n"
            f"• Stochastic\n"
            f"• Williams %R\n"
            f"• CCI (Commodity Channel Index)\n"
            f"• ADX (Average Directional Index)"
        )

        await update.callback_query.message.edit_text(
            text,
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode='Markdown'
        )

    except Exception as e:
        logger.error(f"خطأ في تخصيص المؤشرات: {str(e)}")
        await update.callback_query.answer(
            "حدث خطأ أثناء تحميل قائمة المؤشرات" if lang == 'ar' else "Error loading indicators menu",
            show_alert=True
        )
