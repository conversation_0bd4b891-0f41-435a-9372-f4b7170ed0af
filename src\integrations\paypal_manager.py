"""
وحدة إدارة PayPal API للتحقق من المدفوعات وإنشاء روابط الدفع
"""

import requests
import json
import logging
import pytz
from datetime import datetime, timedelta
from typing import Tuple, Optional, Dict, Any, List

# إعداد التسجيل
logger = logging.getLogger(__name__)

class PayPalManager:
    """
    فئة لإدارة التفاعلات مع PayPal API
    """
    def __init__(self, client_id: str, client_secret: str, is_sandbox: bool = True):
        """
        تهيئة مدير PayPal

        Args:
            client_id: معرف العميل من PayPal
            client_secret: المفتاح السري من PayPal
            is_sandbox: استخدام بيئة الاختبار (True) أو البيئة الحية (False)
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.is_sandbox = is_sandbox
        self.base_url = "https://api-m.sandbox.paypal.com" if is_sandbox else "https://api-m.paypal.com"
        self.access_token = None
        self.token_expiry = None

        logger.info(f"تم تهيئة PayPalManager في وضع {'الاختبار' if is_sandbox else 'الإنتاج'}")

    def get_access_token(self) -> str:
        """
        الحصول على رمز الوصول من PayPal API

        Returns:
            رمز الوصول

        Raises:
            Exception: إذا فشل الحصول على رمز الوصول
        """
        # التحقق من وجود رمز وصول صالح
        if self.access_token and self.token_expiry and datetime.now() < self.token_expiry:
            return self.access_token

        url = f"{self.base_url}/v1/oauth2/token"
        headers = {
            "Accept": "application/json",
            "Accept-Language": "en_US"
        }
        data = {
            "grant_type": "client_credentials"
        }

        try:
            logger.info("جاري الحصول على رمز الوصول من PayPal API")
            response = requests.post(
                url,
                auth=(self.client_id, self.client_secret),
                headers=headers,
                data=data
            )

            if response.status_code == 200:
                response_data = response.json()
                self.access_token = response_data["access_token"]
                self.token_expiry = datetime.now() + timedelta(seconds=response_data["expires_in"])
                logger.info("تم الحصول على رمز الوصول بنجاح")
                return self.access_token
            else:
                error_msg = f"فشل في الحصول على رمز الوصول: {response.status_code}"
                logger.error(error_msg + " - راجع استجابة PayPal لمزيد من التفاصيل.")
                raise Exception(error_msg)
        except Exception as e:
            logger.error(f"خطأ في الاتصال بـ PayPal API: {str(e)}")
            raise

    def create_payment_link(self, amount: float, currency: str = "USD",
                           description: str = "اشتراك في بوت التحليل الفني",
                           return_url: str = None, cancel_url: str = None,
                           user_id: str = None, bot_username: str = None,
                           subscription_type: str = "weekly") -> Tuple[Optional[str], Optional[str]]:
        """
        إنشاء رابط دفع جديد

        Args:
            amount: المبلغ المطلوب دفعه
            currency: رمز العملة (الافتراضي: USD)
            description: وصف الدفع
            return_url: عنوان URL للعودة بعد الدفع الناجح
            cancel_url: عنوان URL للعودة بعد إلغاء الدفع
            user_id: معرف المستخدم
            bot_username: اسم المستخدم للبوت
            subscription_type: نوع الاشتراك (weekly, monthly, yearly)

        Returns:
            زوج من (معرف الطلب، رابط الدفع) أو (None، رسالة الخطأ)
        """
        try:
            token = self.get_access_token()
            url = f"{self.base_url}/v2/checkout/orders"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }

            # إعداد عناوين URL الافتراضية إذا لم يتم توفيرها
            if not bot_username:
                bot_username = "your_bot_username"

            if not return_url:
                return_url = f"https://t.me/{bot_username}?start=payment_success"
            if not cancel_url:
                cancel_url = f"https://t.me/{bot_username}?start=payment_cancel"

            # تحديد وصف الاشتراك بناءً على النوع
            subscription_description = description
            if subscription_type == "weekly":
                subscription_description = "اشتراك أسبوعي في بوت التحليل الفني"
            elif subscription_type == "monthly":
                subscription_description = "اشتراك شهري في بوت التحليل الفني"
            elif subscription_type == "yearly":
                subscription_description = "اشتراك سنوي في بوت التحليل الفني"

            # إعداد وحدة الشراء مع إضافة معرف المستخدم إذا كان متاحًا
            purchase_unit = {
                "amount": {
                    "currency_code": currency,
                    "value": str(amount)
                },
                "description": subscription_description
            }

            # إضافة معرف المستخدم كمعرف مخصص إذا كان متاحًا
            if user_id:
                purchase_unit["custom_id"] = str(user_id)
                # إضافة معلومات إضافية في الوصف
                purchase_unit["description"] += f" - User ID: {user_id}"

            # إضافة معلومات الاشتراك
            purchase_unit["reference_id"] = f"{subscription_type}_{datetime.now().strftime('%Y%m%d%H%M%S')}"

            payload = {
                "intent": "CAPTURE",
                "purchase_units": [purchase_unit],
                "application_context": {
                    "return_url": return_url,
                    "cancel_url": cancel_url,
                    "brand_name": "تحليل العملات الرقمية",
                    "locale": "ar-SA",
                    "landing_page": "BILLING",  # صفحة الفواتير تسمح بالدفع كضيف
                    "user_action": "PAY_NOW",
                    "shipping_preference": "NO_SHIPPING",
                    "payment_method": {
                        "payer_selected": "PAYPAL",
                        "payee_preferred": "UNRESTRICTED"  # السماح بجميع طرق الدفع
                    }
                }
            }

            logger.info(f"جاري إنشاء رابط دفع جديد بقيمة {amount} {currency}")
            response = requests.post(url, headers=headers, data=json.dumps(payload))

            if response.status_code == 201:
                order_data = response.json()
                # استخراج رابط الدفع
                payment_link = None
                order_id = order_data["id"]

                for link in order_data["links"]:
                    if link["rel"] == "approve":
                        payment_link = link["href"]
                        break

                if payment_link:
                    logger.info(f"تم إنشاء رابط دفع بنجاح: {order_id}")

                    # حفظ معلومات الطلب في قاعدة البيانات (يمكن تنفيذ هذا في الدالة الخارجية)
                    order_info = {
                        "order_id": order_id,
                        "user_id": user_id,
                        "amount": amount,
                        "currency": currency,
                        "subscription_type": subscription_type,
                        "created_at": datetime.now().isoformat(),
                        "status": "created"
                    }

                    return order_id, payment_link
                else:
                    logger.error("لم يتم العثور على رابط الدفع في استجابة PayPal")
                    return None, "لم يتم العثور على رابط الدفع"
            else:
                error_msg = f"فشل في إنشاء رابط الدفع: {response.status_code}"
                logger.error(error_msg + " - راجع استجابة PayPal لمزيد من التفاصيل.")
                return None, error_msg
        except Exception as e:
            logger.error(f"خطأ في إنشاء رابط الدفع: {str(e)}")
            return None, f"خطأ في إنشاء رابط الدفع: {str(e)}"

    def verify_payment(self, order_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        التحقق من حالة الدفع باستخدام معرف الطلب

        Args:
            order_id: معرف طلب PayPal

        Returns:
            زوج من (نجاح التحقق، بيانات الطلب أو رسالة الخطأ)
        """
        try:
            token = self.get_access_token()
            url = f"{self.base_url}/v2/checkout/orders/{order_id}"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }

            logger.info(f"جاري التحقق من حالة الدفع للطلب: {order_id}")
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                order_data = response.json()
                # التحقق من حالة الطلب
                status = order_data["status"]

                if status == "COMPLETED":
                    # التحقق من المبلغ والعملة
                    try:
                        amount = float(order_data["purchase_units"][0]["amount"]["value"])
                        currency = order_data["purchase_units"][0]["amount"]["currency_code"]

                        logger.info(f"تم التحقق من الدفع بنجاح: {amount} {currency}")
                        return True, order_data
                    except (KeyError, ValueError) as e:
                        logger.error(f"خطأ في استخراج بيانات المبلغ: {str(e)}")
                        return False, {"error": "خطأ في استخراج بيانات المبلغ", "details": str(e)}
                else:
                    logger.warning(f"حالة الطلب غير مكتملة: {status}")
                    return False, {"error": f"حالة الطلب: {status}", "order_data": order_data}
            else:
                error_msg = f"فشل في التحقق من الدفع: {response.status_code}"
                logger.error(error_msg + " - راجع استجابة PayPal لمزيد من التفاصيل.")
                return False, {"error": error_msg}
        except Exception as e:
            logger.error(f"خطأ في التحقق من الدفع: {str(e)}")
            return False, {"error": f"خطأ في التحقق من الدفع: {str(e)}"}

    def capture_payment(self, order_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        تحصيل الدفع بعد الموافقة عليه من قبل المستخدم

        Args:
            order_id: معرف طلب PayPal

        Returns:
            زوج من (نجاح التحصيل، بيانات التحصيل أو رسالة الخطأ)
        """
        try:
            token = self.get_access_token()
            url = f"{self.base_url}/v2/checkout/orders/{order_id}/capture"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }

            logger.info(f"جاري تحصيل الدفع للطلب: {order_id}")
            response = requests.post(url, headers=headers)

            if response.status_code in [200, 201]:
                capture_data = response.json()
                status = capture_data.get("status", "")

                if status == "COMPLETED":
                    logger.info(f"تم تحصيل الدفع بنجاح للطلب: {order_id}")
                    return True, capture_data
                else:
                    logger.warning(f"حالة تحصيل الدفع غير مكتملة: {status}")
                    return False, {"error": f"حالة التحصيل: {status}", "capture_data": capture_data}
            else:
                error_msg = f"فشل في تحصيل الدفع: {response.status_code}"
                logger.error(error_msg + " - راجع استجابة PayPal لمزيد من التفاصيل.")
                return False, {"error": error_msg}
        except Exception as e:
            logger.error(f"خطأ في تحصيل الدفع: {str(e)}")
            return False, {"error": f"خطأ في تحصيل الدفع: {str(e)}"}

    def get_payment_details(self, order_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        الحصول على تفاصيل الدفع

        Args:
            order_id: معرف طلب PayPal

        Returns:
            زوج من (نجاح العملية، بيانات الدفع أو رسالة الخطأ)
        """
        return self.verify_payment(order_id)

    def verify_transaction_by_id(self, transaction_id: str, user_id: str = None, amount: float = None) -> Tuple[bool, Dict[str, Any]]:
        """
        التحقق من معاملة PayPal باستخدام معرف المعاملة

        Args:
            transaction_id: معرف معاملة PayPal
            user_id: معرف المستخدم (اختياري للتحقق)
            amount: المبلغ المتوقع (اختياري للتحقق)

        Returns:
            زوج من (نجاح التحقق، بيانات المعاملة أو رسالة الخطأ)
        """
        try:
            token = self.get_access_token()
            url = f"{self.base_url}/v1/payments/payment/{transaction_id}"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }

            logger.info(f"جاري التحقق من المعاملة: {transaction_id}")
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                payment_data = response.json()

                # التحقق من حالة المعاملة
                state = payment_data.get("state", "")

                if state == "approved":
                    # التحقق من المبلغ والعملة إذا تم توفيرهما
                    if amount is not None:
                        try:
                            transactions = payment_data.get("transactions", [])
                            if transactions:
                                payment_amount = float(transactions[0].get("amount", {}).get("total", 0))
                                if abs(payment_amount - amount) > 0.01:
                                    logger.warning(f"المبلغ غير متطابق: {payment_amount} != {amount}")
                                    return False, {"error": "المبلغ غير متطابق", "payment_data": payment_data}
                        except (KeyError, ValueError, IndexError) as e:
                            logger.error(f"خطأ في استخراج بيانات المبلغ: {str(e)}")

                    # التحقق من معرف المستخدم إذا تم توفيره
                    if user_id is not None:
                        try:
                            # محاولة العثور على معرف المستخدم في المعاملة
                            found_user_id = None

                            # البحث في الملاحظات المخصصة
                            transactions = payment_data.get("transactions", [])
                            if transactions:
                                custom = transactions[0].get("custom", "")
                                if custom and str(user_id) == custom.strip():
                                    found_user_id = custom.strip()

                            if not found_user_id:
                                logger.warning("معرف المستخدم غير متطابق")
                                return False, {"error": "معرف المستخدم غير متطابق", "payment_data": payment_data}
                        except (KeyError, IndexError) as e:
                            logger.error(f"خطأ في استخراج معرف المستخدم: {str(e)}")

                    logger.info(f"تم التحقق من المعاملة بنجاح: {transaction_id}")
                    return True, payment_data
                else:
                    logger.warning(f"حالة المعاملة غير مكتملة: {state}")
                    return False, {"error": f"حالة المعاملة: {state}", "payment_data": payment_data}
            else:
                error_msg = f"فشل في التحقق من المعاملة: {response.status_code}"
                logger.error(error_msg + " - راجع استجابة PayPal لمزيد من التفاصيل.")
                return False, {"error": error_msg}
        except Exception as e:
            logger.error(f"خطأ في التحقق من المعاملة: {str(e)}")
            return False, {"error": f"خطأ في التحقق من المعاملة: {str(e)}"}

    def get_transactions(self, start_date: datetime = None, end_date: datetime = None, limit: int = 100) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        الحصول على قائمة المعاملات في فترة زمنية محددة

        Args:
            start_date: تاريخ البداية (افتراضي: قبل 24 ساعة)
            end_date: تاريخ النهاية (افتراضي: الوقت الحالي)
            limit: الحد الأقصى لعدد المعاملات (افتراضي: 100)

        Returns:
            زوج من (نجاح العملية، قائمة المعاملات أو رسالة الخطأ)
        """
        try:
            # تعيين القيم الافتراضية
            if end_date is None:
                end_date = datetime.now(pytz.UTC)
            if start_date is None:
                start_date = end_date - timedelta(days=1)

            # تنسيق التواريخ بشكل صحيح
            start_date_str = start_date.strftime('%Y-%m-%dT%H:%M:%S-0000')
            end_date_str = end_date.strftime('%Y-%m-%dT%H:%M:%S-0000')

            token = self.get_access_token()
            url = f"{self.base_url}/v1/reporting/transactions"
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }

            params = {
                "start_date": start_date_str,
                "end_date": end_date_str,
                "fields": "all",
                "page_size": min(limit, 500),  # الحد الأقصى المسموح به هو 500
                "page": 1
            }

            logger.info(f"جاري الحصول على المعاملات من {start_date_str} إلى {end_date_str}")
            response = requests.get(url, headers=headers, params=params)

            if response.status_code == 200:
                transactions_data = response.json()
                transactions = transactions_data.get("transaction_details", [])

                logger.info(f"تم الحصول على {len(transactions)} معاملة")
                return True, transactions
            else:
                error_msg = f"فشل في الحصول على المعاملات: {response.status_code}"
                logger.error(error_msg + " - راجع استجابة PayPal لمزيد من التفاصيل.")
                return False, {"error": error_msg}
        except Exception as e:
            logger.error(f"خطأ في الحصول على المعاملات: {str(e)}")
            return False, {"error": f"خطأ في الحصول على المعاملات: {str(e)}"}

    def process_webhook_event(self, event_data: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], Optional[str]]:
        """
        معالجة أحداث Webhook من PayPal

        Args:
            event_data: بيانات الحدث من PayPal

        Returns:
            ثلاثي من (نجاح المعالجة، بيانات مفيدة أو رسالة الخطأ، معرف المستخدم إذا وجد)
        """
        try:
            event_type = event_data.get("event_type", "")
            logger.info(f"معالجة حدث PayPal من نوع: {event_type}")

            # معالجة أنواع الأحداث المختلفة
            if event_type == "PAYMENT.CAPTURE.COMPLETED":
                # تم اكتمال الدفع بنجاح
                resource = event_data.get("resource", {})
                transaction_id = resource.get("id", "")

                # استخراج معرف المستخدم من الملاحظة (note)
                custom_note = resource.get("custom_note", "")
                user_id = None

                # محاولة استخراج معرف المستخدم من الملاحظة
                if custom_note:
                    user_id = custom_note.strip()

                # البحث عن معرف الطلب
                order_id = resource.get("supplementary_data", {}).get("related_ids", {}).get("order_id", "")

                if order_id:
                    # التحقق من تفاصيل الطلب
                    success, order_data = self.verify_payment(order_id)
                    return success, order_data, user_id
                elif transaction_id and user_id:
                    # إذا لم نجد معرف الطلب ولكن لدينا معرف المعاملة ومعرف المستخدم
                    return True, {"transaction_id": transaction_id, "user_id": user_id}, user_id
                else:
                    logger.warning("لم يتم العثور على معرف الطلب أو معرف المستخدم في بيانات الحدث")
                    return False, {"error": "لم يتم العثور على معرف الطلب أو معرف المستخدم"}, None

            elif event_type == "CHECKOUT.ORDER.APPROVED":
                # تمت الموافقة على الطلب من قبل المستخدم
                resource = event_data.get("resource", {})
                order_id = resource.get("id", "")

                # استخراج معرف المستخدم من الملاحظة (note)
                purchase_units = resource.get("purchase_units", [])
                user_id = None

                if purchase_units and len(purchase_units) > 0:
                    custom_id = purchase_units[0].get("custom_id", "")
                    if custom_id:
                        user_id = custom_id.strip()

                if order_id:
                    # تحصيل الدفع
                    success, capture_data = self.capture_payment(order_id)
                    return success, capture_data, user_id
                else:
                    logger.warning("لم يتم العثور على معرف الطلب في بيانات الحدث")
                    return False, {"error": "لم يتم العثور على معرف الطلب"}, None

            elif event_type == "PAYMENT.SALE.COMPLETED":
                # تم اكتمال عملية البيع
                resource = event_data.get("resource", {})
                transaction_id = resource.get("id", "")

                # استخراج معرف المستخدم من الملاحظة (note)
                custom_field = resource.get("custom", "")
                user_id = None

                if custom_field:
                    user_id = custom_field.strip()

                if transaction_id and user_id:
                    return True, {"transaction_id": transaction_id, "user_id": user_id}, user_id
                else:
                    logger.warning("لم يتم العثور على معرف المعاملة أو معرف المستخدم في بيانات الحدث")
                    return False, {"error": "لم يتم العثور على معرف المعاملة أو معرف المستخدم"}, None

            else:
                # نوع حدث غير معالج
                logger.info(f"نوع الحدث {event_type} غير معالج حاليًا")
                return True, {"message": f"نوع الحدث {event_type} غير معالج حاليًا", "event_data": event_data}, None

        except Exception as e:
            logger.error(f"خطأ في معالجة حدث PayPal: {str(e)}")
            return False, {"error": f"خطأ في معالجة حدث PayPal: {str(e)}"}, None
