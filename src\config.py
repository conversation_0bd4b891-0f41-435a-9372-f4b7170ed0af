"""
ملف التكوين للبوت
"""

import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

# معلومات البوت
BOT_TOKEN = os.getenv('BOT_TOKEN')
# تم تثبيت معرف المطور ليكون دائماً 7839527436 (نفس قيمة SystemConfig.DEVELOPER_ID في main.py)
DEVELOPER_ID = '7839527436'  # استخدام نفس الاسم كما في SystemConfig
OWNER_ID = DEVELOPER_ID  # للتوافق مع الكود القديم

# معلومات API - تم إزالة المفاتيح الافتراضية
DEFAULT_BINANCE_API_KEY = None
DEFAULT_BINANCE_API_SECRET = None
DEFAULT_GEMINI_API_KEY = None

# إعدادات API المستخدم
REQUIRE_API_KEYS_FOR_AI_ANALYSIS = True  # إلزام المستخدمين بتوفير مفاتيح API للتحليل بالذكاء الاصطناعي
REQUIRE_API_KEYS_FOR_MARKET_DATA = True  # إلزام المستخدمين بتوفير مفاتيح API للحصول على بيانات السوق

# إعدادات الميزات المتقدمة
ENABLE_ADVANCED_FEATURES = True  # تمكين الميزات المتقدمة
ADVANCED_FEATURES = {
    'trading_strategy': True,  # استراتيجيات تداول آلية
    'price_prediction': True,  # تنبؤات سعرية
    'multi_timeframe': True    # تحليل متعدد الإطارات الزمنية
}

# معلومات PayPal
PAYPAL_CLIENT_ID = os.getenv('PAYPAL_CLIENT_ID')
PAYPAL_CLIENT_SECRET = os.getenv('PAYPAL_CLIENT_SECRET')
PAYPAL_SANDBOX_MODE = os.getenv('PAYPAL_SANDBOX_MODE', 'True').lower() == 'true'  # استخدام بيئة الاختبار
PAYPAL_LINK = os.getenv('PAYPAL_LINK')  # رابط الدفع اليدوي (سيتم استبداله بالرابط الآلي)

# معلومات التشفير
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')

# معلومات GitHub
GITHUB_TOKEN = os.getenv('GITHUB_TOKEN')
GITHUB_REPO = os.getenv('GITHUB_REPO')
GITHUB_OWNER = os.getenv('GITHUB_OWNER')

# إعدادات الاشتراك
SUBSCRIPTION_PRICE = 5.0  # USD
SUBSCRIPTION_DURATION = 7  # أيام

# إعدادات التخزين المؤقت
CACHE_TIMEOUT = 3600  # ثانية

# إعدادات التحليل
MAX_FREE_ANALYSES = 3  # عدد التحليلات المجانية يوميًا
MAX_FREE_ALERTS = 1  # عدد التنبيهات المجانية

# إعدادات API المستخدم
ENABLE_USER_API = True  # تمكين استخدام API المستخدم

# إزالة التبعية الدائرية - استخدام القيم المحلية
TOKEN = BOT_TOKEN
SystemConfig = None
db = None
logger = None

# دالة لتعيين المراجع الخارجية (يتم استدعاؤها من main.py)
def set_external_references(token=None, system_config=None, database=None, log=None):
    """تعيين المراجع الخارجية لتجنب التبعية الدائرية"""
    global TOKEN, SystemConfig, db, logger
    if token:
        TOKEN = token
    if system_config:
        SystemConfig = system_config
    if database:
        db = database
    if log:
        logger = log

class Config:
    """فئة لإدارة إعدادات النظام"""
    _instance = None
    _data = {
        'bot_token': TOKEN if TOKEN else BOT_TOKEN,
        'owner_id': '7839527436',  # استخدام المعرف المركزي
        'backup_interval': 24,
        'stats_interval': 12,
        'payment_methods': {
            'paypal': {
                'amount': 5.0,
                'currency': 'USD'
            }
        },
        'PAYMENT_AMOUNT': 5.0  # تحديث السعر هنا
    }

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._initialized = True
            self._load_config()

    def _load_config(self):
        """تحميل الإعدادات من Firestore"""
        try:
            if db:
                config_ref = db.collection('config').document('settings')
                config_data = config_ref.get()
                if config_data.exists:
                    self._data.update(config_data.to_dict())
        except Exception as e:
            if logger:
                logger.error(f"Error loading config from Firestore: {str(e)}")

    def update_config(self, new_data: dict):
        """تحديث الإعدادات"""
        try:
            self._data.update(new_data)
            if db:
                config_ref = db.collection('config').document('settings')
                config_ref.set(self._data)
        except Exception as e:
            if logger:
                logger.error(f"Error updating config in Firestore: {str(e)}")

    @property
    def bot_token(self) -> str:
        return self._data.get('bot_token', TOKEN if TOKEN else BOT_TOKEN)

    @property
    def owner_id(self) -> str:
        if SystemConfig:
            return SystemConfig.DEVELOPER_ID  # استخدام المعرف المركزي دائماً
        return '7839527436'  # القيمة الافتراضية

    @property
    def backup_interval(self) -> int:
        return self._data.get('backup_interval', 24)

    @property
    def stats_interval(self) -> int:
        return self._data.get('stats_interval', 12)

    @property
    def payment_methods(self) -> dict:
        return self._data.get('payment_methods', {})
