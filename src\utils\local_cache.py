#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التخزين المؤقت المحلي المحسن
بديل محسن لـ Redis مع أداء عالي وإدارة ذكية للذاكرة
"""

import json
import time
import threading
import pickle
import hashlib
import os
import gc
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
from pathlib import Path
import logging
from functools import wraps
from collections import OrderedDict

logger = logging.getLogger(__name__)

class LocalCache:
    """نظام تخزين مؤقت محلي محسن - بديل Redis"""
    
    def __init__(self, max_memory_mb: int = 100, cleanup_interval: int = 300):
        """
        تهيئة نظام التخزين المؤقت
        
        Args:
            max_memory_mb: الحد الأقصى لاستخدام الذاكرة بالميجابايت
            cleanup_interval: فترة التنظيف التلقائي بالثواني
        """
        self._cache = OrderedDict()
        self._expiry = {}
        self._access_times = {}
        self._lock = threading.RLock()
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.cleanup_interval = cleanup_interval
        self._last_cleanup = time.time()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'cleanups': 0
        }
        
        # إنشاء مجلد التخزين المؤقت الدائم
        self.cache_dir = Path("cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        logger.info(f"تم تهيئة نظام التخزين المؤقت المحلي - الحد الأقصى: {max_memory_mb}MB")
    
    def _serialize_key(self, key: str) -> str:
        """تحويل المفتاح إلى hash آمن"""
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    
    def _get_memory_usage(self) -> int:
        """حساب استخدام الذاكرة الحالي"""
        try:
            total_size = 0
            for key, value in self._cache.items():
                total_size += len(pickle.dumps(value))
            return total_size
        except Exception:
            return 0
    
    def _cleanup_expired(self):
        """تنظيف البيانات المنتهية الصلاحية"""
        current_time = time.time()
        expired_keys = []
        
        with self._lock:
            for key, expiry_time in self._expiry.items():
                if current_time > expiry_time:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._delete_key(key)
            
            self._stats['cleanups'] += 1
            self._last_cleanup = current_time
            
            if expired_keys:
                logger.debug(f"تم تنظيف {len(expired_keys)} عنصر منتهي الصلاحية")
    
    def _cleanup_memory(self):
        """تنظيف الذاكرة عند الحاجة"""
        current_memory = self._get_memory_usage()
        
        if current_memory > self.max_memory_bytes:
            # حذف العناصر الأقل استخداماً (LRU)
            items_to_remove = max(1, len(self._cache) // 4)  # حذف 25%
            
            # ترتيب حسب آخر وصول
            sorted_items = sorted(
                self._access_times.items(),
                key=lambda x: x[1]
            )
            
            with self._lock:
                for key, _ in sorted_items[:items_to_remove]:
                    if key in self._cache:
                        self._delete_key(key)
            
            # تشغيل garbage collection
            gc.collect()
            
            new_memory = self._get_memory_usage()
            freed_mb = (current_memory - new_memory) / (1024 * 1024)
            logger.info(f"تم تحرير {freed_mb:.2f}MB من الذاكرة")
    
    def _delete_key(self, key: str):
        """حذف مفتاح من جميع الهياكل"""
        self._cache.pop(key, None)
        self._expiry.pop(key, None)
        self._access_times.pop(key, None)
        self._stats['deletes'] += 1
    
    def _auto_cleanup(self):
        """تنظيف تلقائي دوري"""
        current_time = time.time()
        if current_time - self._last_cleanup > self.cleanup_interval:
            self._cleanup_expired()
            self._cleanup_memory()
    
    def set(self, key: str, value: Any, ex: Optional[int] = None, px: Optional[int] = None) -> bool:
        """
        تعيين قيمة في التخزين المؤقت
        
        Args:
            key: المفتاح
            value: القيمة
            ex: انتهاء الصلاحية بالثواني
            px: انتهاء الصلاحية بالميلي ثانية
        """
        try:
            self._auto_cleanup()
            
            with self._lock:
                # تحديد وقت انتهاء الصلاحية
                if px:
                    expiry_time = time.time() + (px / 1000)
                elif ex:
                    expiry_time = time.time() + ex
                else:
                    expiry_time = None
                
                # تخزين البيانات
                self._cache[key] = value
                self._access_times[key] = time.time()
                
                if expiry_time:
                    self._expiry[key] = expiry_time
                
                self._stats['sets'] += 1
                
                # نقل المفتاح إلى النهاية (LRU)
                self._cache.move_to_end(key)
                
                return True
                
        except Exception as e:
            logger.error(f"خطأ في تعيين القيمة في التخزين المؤقت: {e}")
            return False
    
    def get(self, key: str) -> Optional[Any]:
        """الحصول على قيمة من التخزين المؤقت"""
        try:
            self._auto_cleanup()
            
            with self._lock:
                # فحص انتهاء الصلاحية
                if key in self._expiry and time.time() > self._expiry[key]:
                    self._delete_key(key)
                    self._stats['misses'] += 1
                    return None
                
                if key in self._cache:
                    # تحديث وقت الوصول
                    self._access_times[key] = time.time()
                    # نقل إلى النهاية (LRU)
                    self._cache.move_to_end(key)
                    self._stats['hits'] += 1
                    return self._cache[key]
                
                self._stats['misses'] += 1
                return None
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على القيمة من التخزين المؤقت: {e}")
            return None
    
    def delete(self, key: str) -> bool:
        """حذف قيمة من التخزين المؤقت"""
        try:
            with self._lock:
                if key in self._cache:
                    self._delete_key(key)
                    return True
                return False
        except Exception as e:
            logger.error(f"خطأ في حذف القيمة من التخزين المؤقت: {e}")
            return False
    
    def exists(self, key: str) -> bool:
        """فحص وجود مفتاح"""
        try:
            with self._lock:
                # فحص انتهاء الصلاحية
                if key in self._expiry and time.time() > self._expiry[key]:
                    self._delete_key(key)
                    return False
                
                return key in self._cache
        except Exception as e:
            logger.error(f"خطأ في فحص وجود المفتاح: {e}")
            return False
    
    def clear(self):
        """مسح جميع البيانات"""
        try:
            with self._lock:
                self._cache.clear()
                self._expiry.clear()
                self._access_times.clear()
                logger.info("تم مسح جميع بيانات التخزين المؤقت")
        except Exception as e:
            logger.error(f"خطأ في مسح التخزين المؤقت: {e}")
    
    def keys(self, pattern: str = "*") -> List[str]:
        """الحصول على قائمة المفاتيح"""
        try:
            self._auto_cleanup()
            
            with self._lock:
                if pattern == "*":
                    return list(self._cache.keys())
                else:
                    # تطبيق نمط بسيط
                    import fnmatch
                    return [key for key in self._cache.keys() if fnmatch.fnmatch(key, pattern)]
        except Exception as e:
            logger.error(f"خطأ في الحصول على المفاتيح: {e}")
            return []
    
    def ttl(self, key: str) -> int:
        """الحصول على الوقت المتبقي لانتهاء الصلاحية"""
        try:
            with self._lock:
                if key not in self._cache:
                    return -2  # المفتاح غير موجود
                
                if key not in self._expiry:
                    return -1  # لا يوجد انتهاء صلاحية
                
                remaining = self._expiry[key] - time.time()
                return max(0, int(remaining))
        except Exception as e:
            logger.error(f"خطأ في حساب TTL: {e}")
            return -2
    
    def expire(self, key: str, seconds: int) -> bool:
        """تعيين انتهاء صلاحية لمفتاح موجود"""
        try:
            with self._lock:
                if key in self._cache:
                    self._expiry[key] = time.time() + seconds
                    return True
                return False
        except Exception as e:
            logger.error(f"خطأ في تعيين انتهاء الصلاحية: {e}")
            return False
    
    def incr(self, key: str, amount: int = 1) -> Optional[int]:
        """زيادة قيمة رقمية"""
        try:
            with self._lock:
                current_value = self.get(key)
                if current_value is None:
                    new_value = amount
                else:
                    new_value = int(current_value) + amount
                
                self.set(key, new_value)
                return new_value
        except Exception as e:
            logger.error(f"خطأ في زيادة القيمة: {e}")
            return None
    
    def decr(self, key: str, amount: int = 1) -> Optional[int]:
        """تقليل قيمة رقمية"""
        return self.incr(key, -amount)
    
    def get_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات التخزين المؤقت"""
        with self._lock:
            hit_rate = 0
            total_requests = self._stats['hits'] + self._stats['misses']
            if total_requests > 0:
                hit_rate = (self._stats['hits'] / total_requests) * 100
            
            return {
                'total_keys': len(self._cache),
                'memory_usage_mb': self._get_memory_usage() / (1024 * 1024),
                'max_memory_mb': self.max_memory_bytes / (1024 * 1024),
                'hit_rate': f"{hit_rate:.2f}%",
                'stats': self._stats.copy()
            }
    
    def save_to_disk(self, filename: str = "cache_backup.pkl"):
        """حفظ التخزين المؤقت إلى القرص"""
        try:
            backup_file = self.cache_dir / filename
            
            with self._lock:
                backup_data = {
                    'cache': dict(self._cache),
                    'expiry': self._expiry.copy(),
                    'access_times': self._access_times.copy(),
                    'timestamp': time.time()
                }
                
                with open(backup_file, 'wb') as f:
                    pickle.dump(backup_data, f)
                
                logger.info(f"تم حفظ التخزين المؤقت إلى {backup_file}")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في حفظ التخزين المؤقت: {e}")
            return False
    
    def load_from_disk(self, filename: str = "cache_backup.pkl"):
        """تحميل التخزين المؤقت من القرص"""
        try:
            backup_file = self.cache_dir / filename
            
            if not backup_file.exists():
                logger.warning(f"ملف النسخ الاحتياطي غير موجود: {backup_file}")
                return False
            
            with open(backup_file, 'rb') as f:
                backup_data = pickle.load(f)
            
            current_time = time.time()
            
            with self._lock:
                # تحميل البيانات مع فحص انتهاء الصلاحية
                for key, value in backup_data['cache'].items():
                    expiry_time = backup_data['expiry'].get(key)
                    
                    if expiry_time is None or current_time < expiry_time:
                        self._cache[key] = value
                        if expiry_time:
                            self._expiry[key] = expiry_time
                        self._access_times[key] = backup_data['access_times'].get(key, current_time)
                
                logger.info(f"تم تحميل {len(self._cache)} عنصر من النسخة الاحتياطية")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في تحميل التخزين المؤقت: {e}")
            return False

# مثيل عام للاستخدام
local_cache = LocalCache()

# دوال مساعدة للتوافق مع Redis
def cache_decorator(expiration: int = 300, key_prefix: str = ""):
    """Decorator للتخزين المؤقت التلقائي للدوال"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # إنشاء مفتاح فريد للدالة والمعاملات
            key_parts = [key_prefix, func.__name__]
            key_parts.extend([str(arg) for arg in args])
            key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
            cache_key = ":".join(filter(None, key_parts))
            
            # محاولة الحصول على النتيجة من التخزين المؤقت
            cached_result = local_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # تنفيذ الدالة وحفظ النتيجة
            result = func(*args, **kwargs)
            local_cache.set(cache_key, result, ex=expiration)
            
            return result
        return wrapper
    return decorator

# دوال توافق مع Redis API
def redis_set(key: str, value: Any, ex: Optional[int] = None):
    """دالة توافق مع Redis SET"""
    return local_cache.set(key, value, ex=ex)

def redis_get(key: str):
    """دالة توافق مع Redis GET"""
    return local_cache.get(key)

def redis_delete(key: str):
    """دالة توافق مع Redis DELETE"""
    return local_cache.delete(key)

def redis_exists(key: str):
    """دالة توافق مع Redis EXISTS"""
    return local_cache.exists(key)

def redis_expire(key: str, seconds: int):
    """دالة توافق مع Redis EXPIRE"""
    return local_cache.expire(key, seconds)

def redis_ttl(key: str):
    """دالة توافق مع Redis TTL"""
    return local_cache.ttl(key)

def redis_incr(key: str, amount: int = 1):
    """دالة توافق مع Redis INCR"""
    return local_cache.incr(key, amount)

def redis_keys(pattern: str = "*"):
    """دالة توافق مع Redis KEYS"""
    return local_cache.keys(pattern)
