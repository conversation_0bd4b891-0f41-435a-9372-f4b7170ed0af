#!/usr/bin/env python3
"""
إصلاح مشكلة توقف البوت عن الاستجابة على الاستضافة
هذا الملف يحتوي على الإصلاحات الأساسية للمشكلة
"""

import asyncio
import logging
import signal
import sys
import os
from typing import Optional

# إعداد السجلات
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class BotHostingFix:
    """فئة لإصلاح مشاكل الاستضافة"""
    
    def __init__(self):
        self.running = True
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.bot_task: Optional[asyncio.Task] = None
        
    def setup_signal_handlers(self):
        """إعداد معالجات الإشارات"""
        def signal_handler(sig, frame):
            logger.info(f"تم استلام إشارة {sig}، جاري إيقاف البوت...")
            self.running = False
            if self.loop and not self.loop.is_closed():
                self.loop.call_soon_threadsafe(self.stop_bot)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
    def stop_bot(self):
        """إيقاف البوت بشكل آمن"""
        self.running = False
        if self.bot_task and not self.bot_task.done():
            self.bot_task.cancel()
            
    async def run_bot_with_restart(self):
        """تشغيل البوت مع إعادة التشغيل التلقائي"""
        restart_count = 0
        max_restarts = 5
        
        while self.running and restart_count < max_restarts:
            try:
                logger.info(f"🚀 بدء تشغيل البوت (المحاولة {restart_count + 1})")
                
                # استيراد وتشغيل البوت
                import main
                await main.run_bot()
                
                # إذا وصلنا هنا، فقد توقف البوت بشكل طبيعي
                break
                
            except Exception as e:
                restart_count += 1
                logger.error(f"❌ خطأ في تشغيل البوت (المحاولة {restart_count}): {str(e)}")
                
                if restart_count < max_restarts:
                    logger.info(f"🔄 إعادة تشغيل البوت خلال 30 ثانية...")
                    await asyncio.sleep(30)
                else:
                    logger.error("❌ تم الوصول للحد الأقصى من محاولات إعادة التشغيل")
                    break
                    
        logger.info("🛑 تم إيقاف البوت")
        
    def run(self):
        """تشغيل البوت الرئيسي"""
        try:
            # إعداد معالجات الإشارات
            self.setup_signal_handlers()
            
            # إنشاء حلقة أحداث جديدة
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)
            
            # بدء خادم فحص الصحة
            from server import run_health_server
            health_server_thread = run_health_server()
            logger.info("✅ تم بدء خادم فحص الصحة")
            
            # تشغيل البوت
            self.bot_task = self.loop.create_task(self.run_bot_with_restart())
            
            # تشغيل حلقة الأحداث
            self.loop.run_forever()
            
        except KeyboardInterrupt:
            logger.info("تم إيقاف البوت بواسطة المستخدم")
        except Exception as e:
            logger.error(f"❌ خطأ غير متوقع: {str(e)}")
        finally:
            self.cleanup()
            
    def cleanup(self):
        """تنظيف الموارد"""
        try:
            if self.bot_task and not self.bot_task.done():
                self.bot_task.cancel()
                
            if self.loop and not self.loop.is_closed():
                # إيقاف جميع المهام المعلقة
                pending = asyncio.all_tasks(self.loop)
                for task in pending:
                    task.cancel()
                    
                if pending:
                    self.loop.run_until_complete(
                        asyncio.gather(*pending, return_exceptions=True)
                    )
                    
                self.loop.close()
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف الموارد: {str(e)}")

def main():
    """النقطة الرئيسية للتطبيق"""
    # التحقق من متغيرات البيئة
    bot_token = os.getenv('BOT_TOKEN')
    if not bot_token:
        logger.error("❌ BOT_TOKEN غير محدد في متغيرات البيئة")
        sys.exit(1)
        
    logger.info(f"✅ تم العثور على BOT_TOKEN: {bot_token[:10]}...")
    
    # تشغيل البوت مع الإصلاحات
    bot_fix = BotHostingFix()
    bot_fix.run()

if __name__ == "__main__":
    main()
