#!/usr/bin/env python3
"""
🧪 اختبار النظام الجديد للاستيرادات
=====================================

اختبار مستقل للتأكد من عمل نظام الاستيراد المحسن
بدون التأثر بالتبعيات الدائرية في main.py

المؤلف: Augment Agent
التاريخ: ديسمبر 2024
"""

def test_standard_imports():
    """اختبار الاستيرادات الأساسية"""
    try:
        from core.imports.standard_imports import validate_standard_imports
        success, missing = validate_standard_imports()
        print('📚 الاستيرادات الأساسية:', '✅ نجح' if success else '❌ فشل')
        if missing:
            print('   مفقود:', missing)
        return success
    except Exception as e:
        print('❌ خطأ في الاستيرادات الأساسية:', str(e))
        return False

def test_dependency_manager():
    """اختبار مدير التبعيات"""
    try:
        from core.dependency_manager import get_dependency_manager
        dm = get_dependency_manager()
        essential = dm.load_essential()
        print('🧠 مدير التبعيات: ✅ نجح - تم تحميل', len(essential), 'وحدة')
        
        # اختبار تقرير الأداء
        report = dm.get_performance_report()
        success_rate = report['modules_summary']['success_rate']
        print('📊 معدل نجاح التحميل:', f'{success_rate:.1f}%')
        
        return True
    except Exception as e:
        print('❌ خطأ في مدير التبعيات:', str(e))
        return False

def test_lazy_loader():
    """اختبار التحميل الذكي"""
    try:
        from core.lazy_loader import get_lazy_loader
        ll = get_lazy_loader()
        
        # تسجيل وحدة اختبار
        test_module = ll.register_module('test_json', 'json')
        print('⚡ التحميل الذكي: ✅ نجح - تم تسجيل وحدة اختبار')
        
        # اختبار إحصائيات الاستخدام
        stats = ll.get_usage_stats()
        print('📈 إجمالي الوحدات المسجلة:', stats['summary']['total_modules'])
        
        return True
    except Exception as e:
        print('❌ خطأ في التحميل الذكي:', str(e))
        return False

def test_imports_structure():
    """اختبار هيكل الاستيرادات"""
    try:
        import os
        
        # التحقق من وجود الملفات
        imports_dir = 'src/core/imports' if os.path.exists('src/core/imports') else 'core/imports'
        required_files = [
            '__init__.py',
            'standard_imports.py',
            'external_imports.py',
            'service_imports.py',
            'analysis_imports.py',
            'handler_imports.py',
            'utils_imports.py',
            'integration_imports.py'
        ]
        
        missing_files = []
        for file in required_files:
            file_path = os.path.join(imports_dir, file)
            if not os.path.exists(file_path):
                missing_files.append(file)
        
        if missing_files:
            print('❌ ملفات مفقودة:', missing_files)
            return False
        else:
            print('📁 هيكل الملفات: ✅ جميع الملفات موجودة')
            return True
            
    except Exception as e:
        print('❌ خطأ في فحص الهيكل:', str(e))
        return False

def test_external_imports():
    """اختبار الاستيرادات الخارجية"""
    try:
        from core.imports.external_imports import get_external_imports_status
        status = get_external_imports_status()
        
        critical_available = 0
        total_critical = 0
        
        for category, info in status.items():
            if info['critical']:
                total_critical += 1
                if info['available']:
                    critical_available += 1
        
        print(f'🌐 الاستيرادات الخارجية: {critical_available}/{total_critical} حرجة متاحة')
        return True
        
    except Exception as e:
        print('❌ خطأ في الاستيرادات الخارجية:', str(e))
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print('🔄 بدء اختبار النظام الجديد للاستيرادات...')
    print('=' * 50)
    
    tests = [
        ('هيكل الملفات', test_imports_structure),
        ('الاستيرادات الأساسية', test_standard_imports),
        ('الاستيرادات الخارجية', test_external_imports),
        ('مدير التبعيات', test_dependency_manager),
        ('التحميل الذكي', test_lazy_loader),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f'\n🧪 اختبار {test_name}:')
        try:
            if test_func():
                passed += 1
            else:
                print(f'   ❌ فشل اختبار {test_name}')
        except Exception as e:
            print(f'   ❌ خطأ في اختبار {test_name}: {e}')
    
    print('\n' + '=' * 50)
    print(f'📊 النتائج النهائية: {passed}/{total} اختبارات نجحت')
    
    if passed == total:
        print('🎉 جميع الاختبارات نجحت! النظام الجديد يعمل بنجاح!')
        print('💡 التظليل في main.py طبيعي - النظام في مرحلة انتقالية')
        print('🔧 يمكن إزالة الاستيرادات القديمة تدريجياً')
    else:
        print(f'⚠️ {total - passed} اختبارات فشلت - يحتاج مراجعة')
    
    return passed == total

if __name__ == '__main__':
    main()
