"""
نظام التحقق من معاملات Binance
"""
import aiohttp
import hmac
import hashlib
import time
import logging
from datetime import datetime, timedelta
from urllib.parse import urlencode

logger = logging.getLogger(__name__)


class BinanceTransactionVerifier:
    """نظام متكامل للتحقق من معاملات Binance"""
    
    def __init__(self, db=None, api_manager=None):
        self.base_url = "https://api.binance.com"
        self.network_required = "BEP20"
        self.min_confirmations = 2
        self.tx_cache = {}  # ذاكرة محلية لتخزين نتائج التحقق من المعاملات
        self.tx_cache_expiry = {}  # تواريخ انتهاء صلاحية البيانات المخزنة
        self.cache_timeout = 300  # 5 minutes
        self.db = db  # استخدام قاعدة البيانات Firestore
        self.api_manager = api_manager

    def _generate_signature(self, params: dict, api_secret: str) -> str:
        """إنشاء التوقيع الآمن لطلبات Binance API"""
        query_string = urlencode(params)
        return hmac.new(
            api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()

    async def _get_transaction_details(self, txid: str, user_id: str = None) -> dict:
        """
        الحصول على تفاصيل المعاملة من Binance

        Args:
            txid: معرف المعاملة
            user_id: معرف المستخدم (اختياري) - إذا تم توفيره، سيتم استخدام مفاتيح API الخاصة بالمستخدم
        """
        # محاولة استخدام مفاتيح API الخاصة بالمستخدم إذا كان متاحًا
        api_key = None
        api_secret = None

        if user_id and self.api_manager:
            try:
                # الحصول على مفاتيح API الخاصة بالمستخدم
                api_key, api_secret = await self.api_manager.get_api_keys(user_id, 'binance')
            except Exception as e:
                logger.warning(f"فشل في الحصول على مفاتيح API للمستخدم {user_id}: {str(e)}")

        # التحقق من وجود مفاتيح API
        if not api_key or not api_secret:
            logger.warning(f"لا توجد مفاتيح API متاحة للتحقق من المعاملة {txid}")
            return None

        timestamp = int(time.time() * 1000)
        params = {
            'timestamp': timestamp,
            'txId': txid
        }
        signature = self._generate_signature(params, api_secret)
        params['signature'] = signature

        headers = {'X-MBX-APIKEY': api_key}

        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/v3/deposit/transaction",
                headers=headers,
                params=params
            ) as response:
                if response.status == 200:
                    return await response.json()
                logger.warning(f"فشل في الحصول على تفاصيل المعاملة {txid}: {response.status}")
                return None

    async def verify_transaction_complete(self, txid: str, user_id: str, amount: float = 5.0) -> bool:
        """
        التحقق من اكتمال المعاملة

        Args:
            txid: معرف المعاملة
            user_id: معرف المستخدم
            amount: المبلغ المتوقع

        Returns:
            True إذا كانت المعاملة صالحة، False خلاف ذلك
        """
        cache_key = f"tx_verify_{txid}"
        current_time = datetime.now()

        # التحقق من الذاكرة المحلية
        if cache_key in self.tx_cache and cache_key in self.tx_cache_expiry:
            if current_time < self.tx_cache_expiry[cache_key]:
                return self.tx_cache[cache_key]

        try:
            # التحقق من وجود مفاتيح API للمستخدم
            if self.api_manager:
                has_binance_api = await self.api_manager.has_api_keys(user_id, 'binance')
                if not has_binance_api:
                    logger.warning(f"المستخدم {user_id} ليس لديه مفاتيح Binance API للتحقق من المعاملة {txid}")
                    # إرسال إشعار للمستخدم بضرورة إضافة مفاتيح API
                    return False

            # الحصول على تفاصيل المعاملة باستخدام مفاتيح API الخاصة بالمستخدم
            tx_details = await self._get_transaction_details(txid, user_id)
            if not tx_details:
                logger.warning(f"لم يتم العثور على تفاصيل المعاملة {txid} للمستخدم {user_id}")
                return False

            is_valid = (
                tx_details.get('network') == self.network_required and
                abs(float(tx_details.get('amount', 0)) - amount) < 0.01 and
                int(tx_details.get('confirmations', 0)) >= self.min_confirmations and
                tx_details.get('memo', '').strip() == user_id
            )

            # تخزين في الذاكرة المحلية
            self.tx_cache[cache_key] = is_valid
            self.tx_cache_expiry[cache_key] = current_time + timedelta(seconds=self.cache_timeout)

            if is_valid:
                logger.info(f"تم التحقق من صحة المعاملة {txid} للمستخدم {user_id}")
            else:
                logger.warning(f"المعاملة {txid} للمستخدم {user_id} غير صالحة")

            return is_valid

        except Exception as e:
            logger.error(f"خطأ في التحقق من المعاملة {txid}: {str(e)}")
            return False

    def set_api_manager(self, api_manager):
        """تعيين مدير API"""
        self.api_manager = api_manager

    def set_db(self, db):
        """تعيين قاعدة البيانات"""
        self.db = db
