#!/usr/bin/env python3
"""
سكريبت مبسط لإصلاح مشاكل اللغة في نظام الأخبار الذكي
يعمل مع Python 3.11.9 ولا يحتاج تبعيات معقدة
"""

import asyncio
import logging
import sys
import os
from datetime import datetime
import json

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('language_fix_simple.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إضافة مسار src للاستيراد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

class SimpleLanguageFixer:
    """فئة مبسطة لإصلاح مشاكل اللغة"""
    
    def __init__(self):
        self.stats = {
            'users_processed': 0,
            'users_fixed': 0,
            'errors': 0,
            'collections_synced': 0
        }
        self.db = None
        self.firebase_available = False
        
    def initialize_firebase(self):
        """تهيئة Firebase إذا كان متوفراً"""
        try:
            from integrations.firebase_init import initialize_firebase
            self.db = initialize_firebase()
            if self.db:
                self.firebase_available = True
                logger.info("✅ تم تهيئة Firebase بنجاح")
                return True
            else:
                logger.warning("⚠️ فشل في تهيئة Firebase")
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة Firebase: {str(e)}")
            return False
    
    async def check_user_language_consistency(self, user_id: str) -> dict:
        """فحص تناسق لغة المستخدم عبر المجموعات المختلفة"""
        if not self.firebase_available:
            logger.warning("Firebase غير متوفر - لا يمكن فحص البيانات")
            return {}
        
        try:
            languages_found = {}
            
            # فحص نظام الاشتراكات
            try:
                subscription_ref = self.db.collection('subscription_system').document(user_id)
                subscription_doc = subscription_ref.get()
                if subscription_doc.exists:
                    data = subscription_doc.to_dict()
                    if 'lang' in data:
                        languages_found['subscription_lang'] = data['lang']
                    if 'language' in data:
                        languages_found['subscription_language'] = data['language']
            except Exception as e:
                logger.error(f"خطأ في فحص subscription_system للمستخدم {user_id}: {str(e)}")
            
            # فحص user_settings
            try:
                settings_ref = self.db.collection('user_settings').document(user_id)
                settings_doc = settings_ref.get()
                if settings_doc.exists:
                    data = settings_doc.to_dict()
                    if 'lang' in data:
                        languages_found['settings_lang'] = data['lang']
                    if 'language' in data:
                        languages_found['settings_language'] = data['language']
            except Exception as e:
                logger.error(f"خطأ في فحص user_settings للمستخدم {user_id}: {str(e)}")
            
            # فحص users
            try:
                users_ref = self.db.collection('users').document(user_id)
                users_doc = users_ref.get()
                if users_doc.exists:
                    data = users_doc.to_dict()
                    if 'lang' in data:
                        languages_found['users_lang'] = data['lang']
                    if 'language' in data:
                        languages_found['users_language'] = data['language']
            except Exception as e:
                logger.error(f"خطأ في فحص users للمستخدم {user_id}: {str(e)}")
            
            return languages_found
            
        except Exception as e:
            logger.error(f"خطأ عام في فحص المستخدم {user_id}: {str(e)}")
            self.stats['errors'] += 1
            return {}
    
    async def fix_user_language_consistency(self, user_id: str, target_lang: str = None) -> bool:
        """إصلاح تناسق لغة المستخدم"""
        if not self.firebase_available:
            logger.warning("Firebase غير متوفر - لا يمكن إصلاح البيانات")
            return False
        
        try:
            # فحص اللغات الموجودة
            languages_found = await self.check_user_language_consistency(user_id)
            
            if not languages_found:
                logger.info(f"لا توجد بيانات لغة للمستخدم {user_id}")
                return False
            
            # تحديد اللغة المستهدفة
            if not target_lang:
                # اختيار اللغة الأكثر شيوعاً أو الأولوية
                lang_values = list(languages_found.values())
                if 'en' in lang_values:
                    target_lang = 'en'
                elif 'ar' in lang_values:
                    target_lang = 'ar'
                else:
                    target_lang = lang_values[0] if lang_values else 'ar'
            
            # التأكد من أن اللغة صالحة
            if target_lang not in ['ar', 'en']:
                target_lang = 'ar'
            
            logger.info(f"إصلاح لغة المستخدم {user_id} إلى: {target_lang}")
            
            # تحديث جميع المجموعات
            collections_updated = 0
            
            # تحديث subscription_system
            try:
                subscription_ref = self.db.collection('subscription_system').document(user_id)
                subscription_ref.update({
                    'lang': target_lang,
                    'updated_at': datetime.now()
                })
                collections_updated += 1
                logger.debug(f"تم تحديث subscription_system للمستخدم {user_id}")
            except Exception as e:
                logger.error(f"خطأ في تحديث subscription_system للمستخدم {user_id}: {str(e)}")
            
            # تحديث user_settings
            try:
                settings_ref = self.db.collection('user_settings').document(user_id)
                settings_ref.set({
                    'lang': target_lang,
                    'updated_at': datetime.now()
                }, merge=True)
                collections_updated += 1
                logger.debug(f"تم تحديث user_settings للمستخدم {user_id}")
            except Exception as e:
                logger.error(f"خطأ في تحديث user_settings للمستخدم {user_id}: {str(e)}")
            
            # تحديث users
            try:
                users_ref = self.db.collection('users').document(user_id)
                users_ref.update({
                    'lang': target_lang,
                    'updated_at': datetime.now()
                })
                collections_updated += 1
                logger.debug(f"تم تحديث users للمستخدم {user_id}")
            except Exception as e:
                logger.error(f"خطأ في تحديث users للمستخدم {user_id}: {str(e)}")
            
            self.stats['collections_synced'] += collections_updated
            
            if collections_updated > 0:
                self.stats['users_fixed'] += 1
                logger.info(f"✅ تم إصلاح لغة المستخدم {user_id} في {collections_updated} مجموعات")
                return True
            else:
                logger.warning(f"⚠️ لم يتم تحديث أي مجموعة للمستخدم {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في إصلاح المستخدم {user_id}: {str(e)}")
            self.stats['errors'] += 1
            return False
    
    async def get_all_users_with_language_issues(self) -> list:
        """الحصول على قائمة بالمستخدمين الذين لديهم مشاكل في اللغة"""
        if not self.firebase_available:
            logger.warning("Firebase غير متوفر - لا يمكن فحص المستخدمين")
            return []
        
        users_with_issues = []
        
        try:
            # فحص subscription_system
            subscription_docs = self.db.collection('subscription_system').stream()
            for doc in subscription_docs:
                user_id = doc.id
                self.stats['users_processed'] += 1
                
                languages = await self.check_user_language_consistency(user_id)
                
                if languages:
                    # فحص التناقضات
                    unique_langs = set(languages.values())
                    if len(unique_langs) > 1:
                        users_with_issues.append({
                            'user_id': user_id,
                            'languages': languages,
                            'issue': 'inconsistent_languages'
                        })
                        logger.info(f"تناقض في اللغة للمستخدم {user_id}: {languages}")
                    
                    # فحص اللغات غير الصالحة
                    invalid_langs = [lang for lang in unique_langs if lang not in ['ar', 'en']]
                    if invalid_langs:
                        users_with_issues.append({
                            'user_id': user_id,
                            'languages': languages,
                            'issue': 'invalid_language',
                            'invalid_langs': invalid_langs
                        })
                        logger.info(f"لغة غير صالحة للمستخدم {user_id}: {invalid_langs}")
                
                # تقرير التقدم كل 100 مستخدم
                if self.stats['users_processed'] % 100 == 0:
                    logger.info(f"تم فحص {self.stats['users_processed']} مستخدم...")
            
        except Exception as e:
            logger.error(f"خطأ في فحص المستخدمين: {str(e)}")
            self.stats['errors'] += 1
        
        return users_with_issues
    
    async def fix_all_language_issues(self):
        """إصلاح جميع مشاكل اللغة"""
        logger.info("🔧 بدء إصلاح مشاكل اللغة...")
        
        if not self.initialize_firebase():
            logger.error("❌ لا يمكن المتابعة بدون Firebase")
            return
        
        # الحصول على المستخدمين الذين لديهم مشاكل
        users_with_issues = await self.get_all_users_with_language_issues()
        
        logger.info(f"تم العثور على {len(users_with_issues)} مستخدم لديهم مشاكل في اللغة")
        
        # إصلاح كل مستخدم
        for user_issue in users_with_issues:
            user_id = user_issue['user_id']
            languages = user_issue['languages']
            
            # تحديد اللغة المستهدفة
            target_lang = 'ar'  # افتراضي
            if 'en' in languages.values():
                target_lang = 'en'
            elif 'ar' in languages.values():
                target_lang = 'ar'
            
            # إصلاح المستخدم
            success = await self.fix_user_language_consistency(user_id, target_lang)
            if success:
                logger.info(f"✅ تم إصلاح المستخدم {user_id}")
            else:
                logger.warning(f"⚠️ فشل في إصلاح المستخدم {user_id}")
        
        # طباعة الإحصائيات النهائية
        self.print_final_stats()
    
    def print_final_stats(self):
        """طباعة الإحصائيات النهائية"""
        logger.info("📊 الإحصائيات النهائية:")
        logger.info(f"   👥 المستخدمين المفحوصين: {self.stats['users_processed']}")
        logger.info(f"   ✅ المستخدمين المُصلحين: {self.stats['users_fixed']}")
        logger.info(f"   🔄 المجموعات المُحدثة: {self.stats['collections_synced']}")
        logger.info(f"   ❌ الأخطاء: {self.stats['errors']}")
        
        # حفظ الإحصائيات في ملف
        stats_file = f"language_fix_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, ensure_ascii=False, indent=2)
            logger.info(f"📄 تم حفظ الإحصائيات في: {stats_file}")
        except Exception as e:
            logger.error(f"خطأ في حفظ الإحصائيات: {str(e)}")

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء سكريبت إصلاح مشاكل اللغة المبسط")
    print("=" * 60)
    
    fixer = SimpleLanguageFixer()
    
    try:
        await fixer.fix_all_language_issues()
        print("\n✅ تم إكمال السكريبت بنجاح!")
        
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف السكريبت بواسطة المستخدم")
        fixer.print_final_stats()
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل السكريبت: {str(e)}")
        logger.error(f"خطأ عام: {str(e)}")
        fixer.print_final_stats()

if __name__ == "__main__":
    asyncio.run(main())
